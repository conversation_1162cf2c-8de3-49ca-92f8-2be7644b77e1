package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.inventory.InventoryCheckConfig;
import com.hxdi.nmjl.service.inventory.InventoryCheckConfigService;
import com.hxdi.nmjl.condition.inventory.InventoryCheckConfigCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 盘点任务管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@RestController
@RequestMapping("/inventoryCheckConfig")
@Api(tags = "盘点任务管理")
public class InventoryCheckConfigController extends BaseController<InventoryCheckConfigService, InventoryCheckConfig> {

    @ApiOperation(value = "新增或编辑")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody InventoryCheckConfig config) {
        if (CommonUtils.isEmpty(config.getId())) {
            return create(config);
        } else {
            return update(config);
        }
    }

    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public ResultBody create(@RequestBody InventoryCheckConfig config) {
        bizService.create(config);
        return ResultBody.ok();
    }

    @ApiOperation(value = "更新")
    @PostMapping("/update")
    public ResultBody update(@RequestBody InventoryCheckConfig config) {
        bizService.update(config);
        return ResultBody.ok();
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<InventoryCheckConfig> getDetail(@RequestParam String id) {
        InventoryCheckConfig config = bizService.getByUniqueKey(id);
        return ResultBody.ok().data(config);
    }

    @ApiOperation(value = "启用/禁用")
    @PostMapping("/changeState")
    public ResultBody changeState(@RequestParam String id, @RequestParam Integer state) {
        bizService.changeState(id, state);
        return ResultBody.ok();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResultBody delete(@RequestParam String id) {
        // 使用状态值7表示逻辑删除
        bizService.changeState(id, 7);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<InventoryCheckConfig>> page(InventoryCheckConfigCondition condition) {
        Page<InventoryCheckConfig> page = bizService.pages(condition);
        return ResultBody.ok().data(page);
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<InventoryCheckConfig>> list(InventoryCheckConfigCondition condition) {
        List<InventoryCheckConfig> list = bizService.lists(condition);
        return ResultBody.ok().data(list);
    }
}
