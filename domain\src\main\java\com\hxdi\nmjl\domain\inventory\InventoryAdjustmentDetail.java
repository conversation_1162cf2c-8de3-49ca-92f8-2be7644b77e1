package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 库存调整明细
 */
@ApiModel(description = "库存调整明细")
@Getter
@Setter
@TableName(value = "B_INVENTORY_ADJUSTMENT_DETAIL")
public class InventoryAdjustmentDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField(value = "PID")
    private String pid;
    /**
     * 库存ID
     */
    @TableField(value = "INVENTORY_ID")
    @ApiModelProperty(value = "库存ID")
    private String inventoryId;
    /**
     * 调整前数量
     */
    @TableField(value = "BEFORE_QTY")
    @ApiModelProperty(value = "调整前数量")
    private BigDecimal beforeQty;

    /**
     * 盘点数量
     */
    @TableField(value = "CHECK_QTY")
    @ApiModelProperty(value = "盘点数量")
    private BigDecimal checkQty;

    /**
     * 调整数量
     */
    @TableField(value = "ADJUST_QTY")
    @ApiModelProperty(value = "调整数量")
    private BigDecimal adjustQty;
    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    /**
     * ----------------以下为非数据库字段-
     */
    /**
     * 库存静态数据
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "库存静态数据")
    private InventoryBase inventoryBase;
}
