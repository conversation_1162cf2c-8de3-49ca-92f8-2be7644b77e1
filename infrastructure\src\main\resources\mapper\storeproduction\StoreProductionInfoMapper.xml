<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.storeproduction.StoreProductionInfoMapper">

    <sql id="Base_Column_List">
        ID, PLAN_NO, STORE_ID, STORE_NAME,
        CATALOG_ID, CATALOG_NAME, PRODUCTION_QUANTITY, SPECIFICATION,
        PRODUCTION_DATE, CREATE_TIME, UPDATE_TIME, CREATE_ID,
        UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.storeproduction.StoreProductionInfo">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
        <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId" />
        <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
        <result column="PRODUCTION_QUANTITY" jdbcType="DECIMAL" property="productionQuantity" />
        <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification" />
        <result column="PRODUCTION_DATE" jdbcType="TIMESTAMP" property="productionDate" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_PRODUCTION_INFO
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.id)">
                AND ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planNo)">
                AND PLAN_NO LIKE CONCAT('%', #{condition.planNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeName)">
                AND STORE_NAME = #{condition.storeName}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogName)">
                AND CATALOG_NAME = #{condition.catalogName}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_PRODUCTION_INFO
        <where>
            <if test="condition.id != null and condition.id != ''">
                AND ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planNo)">
                AND PLAN_NO LIKE CONCAT('%', #{condition.planNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeName)">
                AND STORE_NAME = #{condition.storeName}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogName)">
                AND CATALOG_NAME = #{condition.catalogName}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
