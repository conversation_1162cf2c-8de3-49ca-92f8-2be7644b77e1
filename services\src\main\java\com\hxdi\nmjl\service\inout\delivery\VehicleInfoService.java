package com.hxdi.nmjl.service.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.delivery.VehicleInfo;
import com.hxdi.nmjl.condition.inout.VehicleInfoCondition;

import java.util.List;

/**
 * 车辆管理服务接口
 *
 * <AUTHOR>
 * @since 2025-04-22 14:52:10
 */
public interface VehicleInfoService extends IBaseService<VehicleInfo> {
    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<VehicleInfo> pages(VehicleInfoCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<VehicleInfo> lists(VehicleInfoCondition condition);

    /**
     * 保存或更新车辆信息
     *
     * @param vehicleInfo 车辆信息
     */
    void saveOrUpdateV1(VehicleInfo vehicleInfo);

    /**
     * 查询车辆详情
     *
     * @param id 车辆ID
     * @return 车辆详情
     */
    VehicleInfo getDetail(String id);

    /**
     * 删除车辆信息（逻辑删除）
     *
     * @param id 车辆ID
     */
    void remove(String id);

    /**
     * 根据车牌号更新车辆状态
     *
     * @param
     */
    void changeState(Integer deliveryState, String vehicleNo);
}
