package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 库容转换记录
 * @TableName B_STORAGE_CAPACITY_CONVERSION
 */
@ApiModel(description = "库容转换记录表")
@TableName("B_STORAGE_CAPACITY_CONVERSION")
@Getter
@Setter
public class StorageCapacityConversion implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "库容ID")
    @TableField(value = "STORAGE_CAP_ID")
    private String storageCapId;

    @ApiModelProperty(value = "调配容量")
    @TableField(value = "ALLOCATE_CAP")
    private BigDecimal allocateCap;

    @ApiModelProperty(value = "调配前已使用容量")
    @TableField(value = "USED_CAP")
    private BigDecimal usedCap;

    @ApiModelProperty(value = "调配前可用容量")
    @TableField(value = "AVAILABLE_CAP")
    private BigDecimal availableCap;

    @ApiModelProperty(value = "客户ID")
    @TableField(value = "CLIENT_ID")
    private String clientId;

    @ApiModelProperty(value = "客户名称")
    @TableField(value = "CLIENT_NAME")
    private String clientName;

    @ApiModelProperty(value = "有效期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "EXPIRATION_DATE")
    private Date expirationDate;

    @ApiModelProperty(value = "调配日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "OPT_TIME")
    private Date optTime;

    @ApiModelProperty(value = "操作人")
    @TableField(value = "OPT_EMP")
    private String optEmp;

    @ApiModelProperty(value = "业务状态：1-闲置，2-租用，3-政策性占用")
    @TableField(value = "STATE")
    private Integer state;

    @ApiModelProperty(value = "附件")
    @TableField(value = "ATTACHEMENTS")
    private String attachments;

    @ApiModelProperty(value = "状态：0-无效，1-有效")
    @TableField(value = "ENABLED")
    private Integer enabled;

    @ApiModelProperty(value = "审批人")
    @TableField(value = "APPROVER")
    private String approver;

    @ApiModelProperty(value = "审批时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "APPROVE_TIME")
    private Date approveTime;

    @ApiModelProperty(value = "审批意见")
    @TableField(value = "APPROVE_OPINION")
    private String approveOpinion;

    /**
     * 审批状态：0-未审核，1-已审核，2-驳回
     */
    @ApiModelProperty(value = "审批状态：0-未审核，1-已审核，2-驳回")
    @TableField(value = "APPROVE_STATUS")
    private Integer approveStatus;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID")
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}

