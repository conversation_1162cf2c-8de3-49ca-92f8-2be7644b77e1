package com.hxdi.nmjl.controller.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.portal.ResourceCondition;
import com.hxdi.nmjl.domain.portal.CmsResources;
import com.hxdi.nmjl.service.portal.CmsResourcesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;


/**
 * 包含文章、资料库、供应商信息等(CMS_RESOURCES)表控制层
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/19 09:31
 */
@RestController
@RequestMapping("/cmsResource")
@Api(tags = "门户-内容资源")
public class CmsResourcesController extends BaseController<CmsResourcesService, CmsResources> {

    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody CmsResources resource) {
        if (CommonUtils.isEmpty(resource.getId())) {
            bizService.create(resource);
        } else {
            bizService.update(resource);
        }
        return ResultBody.ok();
    }

    @ApiOperation("发布/撤销")
    @PostMapping("/changeState")
    public ResultBody changeState(@RequestParam String resourceId, @RequestParam Integer state) {
        bizService.changeState(resourceId, state);
        return ResultBody.ok();
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResultBody delete(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<CmsResources> getDetail(String resourceId) {
        return ResultBody.<CmsResources>OK().data(bizService.getDetail(resourceId));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<CmsResources>> pages(ResourceCondition condition) {
        return ResultBody.<Page<CmsResources>>OK().data(bizService.pages(condition));
    }

}
