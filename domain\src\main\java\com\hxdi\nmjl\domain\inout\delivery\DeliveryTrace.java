package com.hxdi.nmjl.domain.inout.delivery;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 配送状态跟踪表实体类
 *
 * <AUTHOR>
 * @since 2025-04-23 10:23:26
 */
@Getter
@Setter
@TableName("B_DELIVERY_TRACE")
@ApiModel(description = "配送状态跟踪")
public class DeliveryTrace implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键Id")
    private String id;
    /**
     * 配送单ID
     */
    @TableField(value = "DELIVERY_ID")
    @ApiModelProperty(value = "配送单ID")
    private String deliveryId;
    /**
     * 配送状态: 字典PSDZT
     */
    @TableField(value = "DELIVERY_STATE")
    @ApiModelProperty(value = "配送状态: 字典PSDZT")
    private Integer deliveryState;
    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Object createTime;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;


}

