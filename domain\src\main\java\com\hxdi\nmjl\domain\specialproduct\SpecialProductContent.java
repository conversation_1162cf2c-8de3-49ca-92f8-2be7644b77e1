package com.hxdi.nmjl.domain.specialproduct;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 地方特色产品描述
 */
@Getter
@Setter
@TableName(value = "B_SPECIAL_PRODUCT_CONTENT")
public class SpecialProductContent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 特色产品id（主键）
     */
    @TableId(value = "PRODUCT_ID", type = IdType.ASSIGN_ID)
    private String productId;

    /**
     * 产品描述
     */
    @TableField(value = "CONTENT")
    private String content;
}

