package com.hxdi.nmjl.service.inout.duty.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.inout.DutyCondition;
import com.hxdi.nmjl.domain.inout.duty.DutyPlan;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inout.duty.DutyPlanMapper;
import com.hxdi.nmjl.service.inout.duty.DutyEmpService;
import com.hxdi.nmjl.service.inout.duty.DutyPlanService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class DutyPlanServiceImpl extends BaseServiceImpl<DutyPlanMapper, DutyPlan> implements DutyPlanService {

    @Resource
    private DutyEmpService dutyEmpService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public DutyPlan detail(String dutyId) {
        DutyPlan dutyPlan = this.getById(dutyId);
        dutyPlan.setDutyEmpList(dutyEmpService.getList(dutyId));
        return dutyPlan;
    }

    @Override
    public void create(DutyPlan dutyPlan) {
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("DUTY_PLAN_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        dutyPlan.setDutyPlanCode((String) businessCode.getValue());

        this.save(dutyPlan);
        dutyPlan.getDutyEmpList().forEach(dutyEmp -> {
            dutyEmp.setDutyPlanId(dutyPlan.getId());
            dutyEmpService.save(dutyEmp);
        });
    }

    @Override
    public void updating(DutyPlan dutyPlan) {
        if (dutyPlan.getApproveStatus() == 2) {
            dutyPlan.setApproveStatus(0);
        }
        this.updateById(dutyPlan);

        dutyEmpService.remove(dutyPlan.getId());
        dutyPlan.getDutyEmpList().forEach(dutyEmp -> dutyEmp.setDutyPlanId(dutyPlan.getId()));
        dutyEmpService.saveBatch(dutyPlan.getDutyEmpList());
    }

    @Override
    public Page<DutyPlan> getPage(DutyCondition dutyCondition) {
        Page<DutyPlan> page = dutyCondition.newPage();
        baseMapper.selectPageV1(page, dutyCondition);
        return page;
    }

    @Override
    public List<DutyPlan> getList(DutyCondition dutyCondition) {
        List<DutyPlan> planList = baseMapper.selectListV1(dutyCondition);
        planList.forEach(dutyPlan -> dutyPlan.setDutyEmpList(dutyEmpService.getList(dutyPlan.getId())));
        return planList;
    }

    @Override
    public void approve(String dutyId, String approveOpinion) {
        DutyPlan dutyPlan = this.getById(dutyId);
        dutyPlan.setApproveTime(new Date());
        dutyPlan.setApprover(SecurityHelper.obtainUser().getNickName());
        dutyPlan.setApproveStatus(1);
        dutyPlan.setApproveOpinion(approveOpinion);
        this.updateById(dutyPlan);
    }

    @Override
    public void reject(String dutyId, String approveOpinion) {
        DutyPlan dutyPlan = this.getById(dutyId);
        dutyPlan.setApproveTime(new Date());
        dutyPlan.setApprover(SecurityHelper.obtainUser().getNickName());
        dutyPlan.setApproveStatus(2);
        dutyPlan.setApproveOpinion(approveOpinion);
        this.updateById(dutyPlan);
    }

    @Override
    public void delete(String dutyId) {
        DutyPlan dutyPlan = this.getById(dutyId);
        if (dutyPlan.getApproveStatus() == 0) {
            this.removeById(dutyId);
            dutyEmpService.remove(dutyId);
        } else {
            BizExp.pop("当前值班计划正在审批中，无法删除");
        }
    }

    @Override
    public void approved(String dutyId) {
        DutyPlan dutyPlan = this.getById(dutyId);
        dutyPlan.setApproveTime(new Date());
        dutyPlan.setApprover(SecurityHelper.obtainUser().getNickName());
        dutyPlan.setApproveStatus(3);
        this.updateById(dutyPlan);
    }
}
