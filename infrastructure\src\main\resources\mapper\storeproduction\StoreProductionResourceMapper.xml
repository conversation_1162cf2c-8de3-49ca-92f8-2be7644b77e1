<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.storeproduction.StoreProductionResourceMapper">

    <sql id="Base_Column_List">
        ID, PLAN_NO, RESOURCE_TYPE, RESOURCE_NAME, ALLOCATION_QUANTITY,
        RESOURCE_DESC, ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID,
        UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.storeproduction.StoreProductionResource">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo"/>
        <result column="RESOURCE_TYPE" jdbcType="INTEGER" property="resourceType"/>
        <result column="RESOURCE_NAME" jdbcType="VARCHAR" property="resourceName"/>
        <result column="ALLOCATION_QUANTITY" jdbcType="DECIMAL" property="allocationQuantity"/>
        <result column="RESOURCE_DESC" jdbcType="VARCHAR" property="resourceDesc"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_PRODUCTION_RESOURCE
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.id)">
                AND ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planNo)">
                AND PLAN_NO LIKE CONCAT('%', #{condition.planNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.resourceType)">
                AND RESOURCE_TYPE = #{condition.resourceType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_PRODUCTION_RESOURCE
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.id)">
                AND ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planNo)">
                AND PLAN_NO LIKE CONCAT('%', #{condition.planNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.resourceType)">
                AND RESOURCE_TYPE = #{condition.resourceType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
