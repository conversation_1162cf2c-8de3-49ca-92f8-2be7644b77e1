package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inventory.StorageCapacityConversionCondition;
import com.hxdi.nmjl.domain.inventory.StorageCapacityConversion;
import com.hxdi.nmjl.service.inventory.StorageCapacityConversionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库容转换记录管理
 *
 * @version 1.0
 * <AUTHOR>
 * @since 2025/07/19 20:02
 */
@Api(tags = "库容转换记录管理")
@RestController
@RequestMapping("/storageCapacityConversion")
public class StorageCapacityConversionController extends BaseController<StorageCapacityConversionService, StorageCapacityConversion> {
    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<StorageCapacityConversion>> getPages(StorageCapacityConversionCondition condition) {
        return ResultBody.<Page<StorageCapacityConversion>>OK().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<StorageCapacityConversion>> getList(StorageCapacityConversionCondition condition) {
        return ResultBody.<List<StorageCapacityConversion>>OK().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<StorageCapacityConversion> getDetail(@RequestParam String id) {
        return ResultBody.<StorageCapacityConversion>OK().data(bizService.getDetail(id));
    }

    @ApiOperation("删除库容信息")
    @GetMapping("/delete")
    public ResultBody<Void> delete(@RequestParam String id) {
        bizService.delete(id);
        return ResultBody.OK();
    }

    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody StorageCapacityConversion storageCapacity) {
        if (CommonUtils.isEmpty(storageCapacity.getId())) {
            bizService.add(storageCapacity);
        } else {
            bizService.update(storageCapacity);
        }
        return ResultBody.OK();
    }

    @ApiOperation(value = "审核")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id, @RequestParam Integer approveStatus, @RequestParam String approveOpinion) {
        bizService.changeApproveStatus(id, approveStatus, approveOpinion);
        return ResultBody.OK();
    }

    @ApiOperation(value = "提交")
    @GetMapping("/submit")
    public ResultBody<Void> approve(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.OK();
    }

    @ApiOperation(value = "回收租界库容")
    @PostMapping("/recycle")
    public ResultBody<Void> recycle(@RequestParam String id) {
        bizService.recycle(id);
        return ResultBody.OK();
    }
}
