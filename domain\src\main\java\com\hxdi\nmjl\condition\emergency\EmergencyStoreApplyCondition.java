package com.hxdi.nmjl.condition.emergency;

import com.hxdi.common.core.mybatis.base.support.OrderItem;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.model.Query;
import com.hxdi.common.core.mybatis.annotation.Between;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.annotation.ORDER;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class EmergencyStoreApplyCondition extends Query {
    private static final long serialVersionUID = 1L;


    @LIKE(value = "APPLY_CODE,CLASSIFICATION_NAME", logic = "OR", join = true)
    private String keywords;


    @EQ
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @EQ
    @ApiModelProperty(value = "业务状态：0-未提交，1-进行中，2-已完成")
    private Integer state;

    @EQ
    @ApiModelProperty(value="结算状态：0-未结算，1-已结算")
    private Integer accountState;

    @EQ
    @ApiModelProperty(value = "审核状态")
    private Integer approveState;

    @ApiModelProperty(value="创建时间")
    @Between
    private RangeBean<Date> createTime;

    @EQ
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private final Integer enabled = 1;

    @ORDER
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private final OrderItem orderItem = OrderItem.desc("create_time");

}
