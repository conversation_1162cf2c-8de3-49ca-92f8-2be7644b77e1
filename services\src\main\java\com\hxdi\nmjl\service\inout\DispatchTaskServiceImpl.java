package com.hxdi.nmjl.service.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.enums.TaskStatus;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.utils.RedisKeys;
import com.hxdi.nmjl.condition.inout.DispatchCondition;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import com.hxdi.nmjl.mapper.inout.DispatchTaskMapper;
import com.hxdi.nmjl.domain.inout.DispatchTask;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Transactional(rollbackFor = Exception.class)
public class DispatchTaskServiceImpl extends BaseServiceImpl<DispatchTaskMapper, DispatchTask> implements DispatchTaskService{

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private DispatchInfoService dispatchInfoService;

    @Override
    public void createOrUpdate(DispatchTask dispatchTask) {
        if(CommonUtils.isNotEmpty(dispatchTask.getId())){
            if(!Objects.equals(TaskStatus.UNEXECUTED.getCode(), dispatchTask.getState())){
                throw new BaseException("无法更新已经开始作业的拆分调度计划！");
            }
            baseMapper.updateById(dispatchTask);
        }else {
            // 生成拆分调度计划编号
            BusinessCodeParams params = new BusinessCodeParams();
            params.setCode("DISPATCH_TASK_CODE");
            params.setDt(DataType.STRING);
            BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
            dispatchTask.setTaskCode((String) businessCode.getValue());
            //新增拆分调度计划
            baseMapper.insert(dispatchTask);
        }
    }

    @Override
    public void removeById(String id) {
        DispatchTask task = baseMapper.selectById(id);
        if(CommonUtils.isNotEmpty(task)){
            if(!Objects.equals(TaskStatus.UNEXECUTED.getCode(), task.getState())){
                throw new BaseException("无法删除已经开始作业的拆分调度计划！");
            }
            //删除拆分调度作业记录
            dispatchInfoService.removeByTaskId(id);
            //删除拆分调度计划
            task.setEnabled(StrPool.State.DISABLE);
            baseMapper.updateById(task);
        }
    }

    @Override
    public DispatchTask getMore(String id) {
        DispatchTask task;
        task = baseMapper.selectById(id);
        if(CommonUtils.isNotEmpty(task)){
           task.setInventory(inventoryService.getById(task.getInventoryId()));
           if(CommonUtils.isNotEmpty(task.getInventory())){
               Catalog catalog = CacheProvider.getCacheObject(RedisKeys.CATALOG.key(), task.getInventory().getCatalogId());
               if(CommonUtils.isNotEmpty(catalog)){
                   task.setCatalog(catalog);
               }
           }
        }
        return task;
    }

    @Override
    @DataPermission
    public List<DispatchTask> listV1(DispatchCondition condition) {
        return baseMapper.listV1(condition);
    }

    @Override
    public Page<DispatchTask> PageV1(DispatchCondition condition) {
        Page<DispatchTask> page = condition.newPage();
        return baseMapper.PageV1(condition, page);
    }
}
