package com.hxdi.nmjl.controller.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.emergency.EmergencyScheduleCondition;
import com.hxdi.nmjl.domain.emergency.EmergencySchedule;
import com.hxdi.nmjl.service.emergency.EmergencyScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急调度管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/21
 */
@Api(tags = "应急调度管理")
@RestController
@RequestMapping("/emergencySchedule")
public class EmergencyScheduleController extends BaseController<EmergencyScheduleService, EmergencySchedule> {

    @ApiOperation("分页查询应急调度")
    @GetMapping("/page")
    public ResultBody<Page<EmergencySchedule>> page(EmergencyScheduleCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询应急调度")
    @GetMapping("/list")
    public ResultBody<List<EmergencySchedule>> list(EmergencyScheduleCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("保存/修改应急调度")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody EmergencySchedule schedule) {
        if (CommonUtils.isEmpty(schedule.getId())) {
            bizService.create(schedule);
        } else {
            bizService.update(schedule);
        }
        return ResultBody.ok();
    }

    @ApiOperation("查看应急调度详情")
    @GetMapping("/getDetail")
    public ResultBody<EmergencySchedule> getDetail(@RequestParam String scheduleId) {
        return ResultBody.ok().data(bizService.getDetail(scheduleId));
    }

    @ApiOperation("标记调度为已完成")
    @PostMapping("/complete")
    public ResultBody complete(@RequestParam String scheduleId) {
        bizService.complete(scheduleId);
        return ResultBody.ok();
    }

    @ApiOperation("删除应急调度")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }
}