<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.plan.BrandCrisisMapper">

    <sql id="Base_Column_List">
        ID, BRAND_ID, BRAND_NAME, EVENT_NO, EVENT_TITLE,
        EVNET_TYPE, PROPAGATION_MODE, EVENT_LEVEL, CRISIS_DESC,
        DEAL_DESC, ENABLED, ATTACHMENTS, CREATE_TIME, UPDATE_TIME,
        CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.BrandCrisis">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="BRAND_ID" jdbcType="VARCHAR" property="brandId"/>
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
        <result column="EVENT_NO" jdbcType="VARCHAR" property="eventNo"/>
        <result column="EVENT_TITLE" jdbcType="VARCHAR" property="eventTitle"/>
        <result column="EVNET_TYPE" jdbcType="VARCHAR" property="evnetType"/>
        <result column="PROPAGATION_MODE" jdbcType="VARCHAR" property="propagationMode"/>
        <result column="EVENT_LEVEL" jdbcType="INTEGER" property="eventLevel"/>
        <result column="CRISIS_DESC" jdbcType="VARCHAR" property="crisisDesc"/>
        <result column="DEAL_DESC" jdbcType="VARCHAR" property="dealDesc"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="ATTACHMENTS" jdbcType="VARCHAR" property="attachments"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_BRAND_CRISIS
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.brandId)">
                AND BRAND_ID = #{condition.brandId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.brandName)">
                AND BRAND_NAME LIKE CONCAT('%', #{condition.brandName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.evnetType)">
                AND EVNET_TYPE = #{condition.evnetType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.propagationMode)">
                AND PROPAGATION_MODE = #{condition.propagationMode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.eventLevel)">
                AND EVENT_LEVEL = #{condition.eventLevel}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeStart)">
                AND CREATE_TIME >= #{condition.createTimeStart}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeEnd)">
                AND CREATE_TIME &lt;= #{condition.createTimeEnd}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_BRAND_CRISIS
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.brandId)">
                AND BRAND_ID = #{condition.brandId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.brandName)">
                AND BRAND_NAME LIKE CONCAT('%', #{condition.brandName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.evnetType)">
                AND EVNET_TYPE = #{condition.evnetType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.propagationMode)">
                AND PROPAGATION_MODE = #{condition.propagationMode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.eventLevel)">
                AND EVENT_LEVEL = #{condition.eventLevel}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeStart)">
                AND CREATE_TIME >= #{condition.createTimeStart}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeEnd)">
                AND CREATE_TIME &lt;= #{condition.createTimeEnd}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>