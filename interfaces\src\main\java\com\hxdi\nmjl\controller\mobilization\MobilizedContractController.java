package com.hxdi.nmjl.controller.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.mobilization.MobilizedContract;
import com.hxdi.nmjl.service.mobilization.MobilizationContractService;
import com.hxdi.nmjl.condition.mobilization.MobilizedContractCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动员协议管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/2
 */
@Api(tags = "动员协议管理")
@RestController
@RequestMapping("/mobilization")
public class MobilizedContractController extends BaseController<MobilizationContractService, MobilizedContract> {

    @ApiOperation("分页查询动员协议")
    @GetMapping("/page")
    public ResultBody<Page<MobilizedContract>> page(MobilizedContractCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询动员协议")
    @GetMapping("/list")
    public ResultBody<List<MobilizedContract>> list(MobilizedContractCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("保存/修改动员协议")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody MobilizedContract contract) {
        if (CommonUtils.isEmpty(contract.getId())) {
            bizService.create(contract);
        } else {
            bizService.update(contract);
        }
        return ResultBody.ok();
    }

    @ApiOperation("查看动员协议详情")
    @GetMapping("/get")
    public ResultBody<MobilizedContract> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除动员协议")
    @DeleteMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation("提交动员协议")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }

    @ApiOperation("审核动员协议")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String contractId,@RequestParam Integer approveStatus,@RequestParam(required = false) String approveOpinion) {
        bizService.approve(contractId, approveStatus, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation("结束动员协议")
    @GetMapping("/finish")
    public ResultBody<Void> finish(@RequestParam String contractId) {
        bizService.finish(contractId);
        return ResultBody.ok();
    }

    @ApiOperation("终止动员协议")
    @GetMapping("/terminate")
    public ResultBody<Void> terminate(@RequestParam String contractId) {
        bizService.terminate(contractId);
        return ResultBody.ok();
    }


}
