<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hxdi.nmjl</groupId>
        <artifactId>nmjl-service</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <groupId>com.hxdi.nmjl</groupId>
    <artifactId>domain</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hx-common-data-starter</artifactId>
                    <groupId>com.hx.arch</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>system-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi.nmjl</groupId>
            <artifactId>client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>msg-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>file-client</artifactId>
            <version>1.0.0</version>
        </dependency>

    </dependencies>

</project>
