package com.hxdi.nmjl.service.store;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.store.StoreAssessment;
import com.hxdi.nmjl.condition.store.StoreAssessmentCondition;

import java.util.List;

/**

 评估服务接口
 <AUTHOR> 1.0
 @since 2025/8/8
 */
public interface StoreAssessmentService extends IBaseService<StoreAssessment> {
    /**
     新增评估
     @param assessment 评估实体
     */
    void create (StoreAssessment assessment);
    /**
     修改评估
     @param assessment 评估实体
     */
    void update (StoreAssessment assessment);
    /**
     获取评估详情
     @param id 评估 ID
     @return 评估实体
     */
    StoreAssessment getDetail (String id);
    /**
     分页查询评估
     @param condition 查询条件
     @return 分页结果
     */
    Page<StoreAssessment> pages(StoreAssessmentCondition condition);
    /**
     列表查询评估
     @param condition 查询条件
     @return 评估列表
     */
    List<StoreAssessment> lists(StoreAssessmentCondition condition);
    /**
     审批评估（状态变更）
     @param id 评估 ID
     @param approveStatus 审批状态
     @param opinion 审批意见
     */
    void approve (String id, Integer approveStatus, String opinion);
    /**
     删除评估
     @param id 评估 ID
     */
    void remove (String id);

    /**
     提交评估
     @param id 评估 ID
     */
    void submit(String id);
}

