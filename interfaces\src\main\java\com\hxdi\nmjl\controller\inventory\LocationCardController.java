package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inventory.LocationCard;
import com.hxdi.nmjl.service.inventory.LocationCardService;
import com.hxdi.nmjl.condition.inventory.LocationCardCondition;
import com.hxdi.nmjl.vo.inventory.LocationCardDetailResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;

/**
 * @program: nmjl-service
 * @description: 货位卡控制器
 * @author: 王贝强
 * @create: 2025-04-18 11:00
 */
@RestController
@Api(tags = "货位卡")
@RequestMapping("/locationCard")
public class LocationCardController extends BaseController<LocationCardService, LocationCard> {
    @GetMapping("/get")
    @ApiOperation(value = "获取货位卡详情")
    public LocationCard get(Serializable id) {
        return bizService.getById(id);
    }

    @GetMapping("/getPage")
    @ApiOperation(value = "根据查询条件获取货位卡分页列表")
    public ResultBody<Page<LocationCard>> getPage(LocationCardCondition condition) {
        return ResultBody.ok().data(bizService.getPage(condition));
    }

    @GetMapping("/getList")
    @ApiOperation(value = "根据查询条件获取货位卡列表")
    public ResultBody<Page<LocationCard>> getList(LocationCardCondition condition) {
        return ResultBody.ok().data(bizService.getPage(condition));
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "根据货位ID获取货位卡详情（包含库存相关信息、库存变更记录等 ）")
    public ResultBody<LocationCardDetailResVO> getDetailByLocId(String locId) {
        return ResultBody.ok().data(bizService.getDetail(locId));
    }
}
