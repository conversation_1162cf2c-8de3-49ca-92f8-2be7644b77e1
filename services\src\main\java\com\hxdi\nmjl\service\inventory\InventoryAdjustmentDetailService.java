package com.hxdi.nmjl.service.inventory;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.InventoryAdjustmentDetail;

import java.util.List;

public interface InventoryAdjustmentDetailService extends IBaseService<InventoryAdjustmentDetail> {

    /**
     * 根据主表ID获取明细列表
     *
     * @param parentId 主表ID
     * @return 明细列表
     */
    List<InventoryAdjustmentDetail> getListByPid(String parentId);

    /**
     * 删除明细
     * @param mainId
     */
    void removeByMainId(String mainId);
}
