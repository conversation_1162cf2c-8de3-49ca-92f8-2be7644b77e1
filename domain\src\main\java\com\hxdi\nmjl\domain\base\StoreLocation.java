package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 货位信息
 */
@Getter
@Setter
@TableName(value = "C_STORE_LOCATION")
public class StoreLocation extends Entity<StoreLocation> {

    private static final long serialVersionUID = 2912188924065581526L;

    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    /**
     * 库点ID
     */
    private String storeId;

    /**
     * 仓房ID
     */
    @NotNull(message = "仓房ID不能为空")
    @TableField(value = "ST_ID")
    private String stId;

    /**
     * 货位名
     */
    @NotEmpty(message = "货位名不能为空")
    @TableField(value = "NAME")
    private String name;

    /**
     * 编码
     */
    @TableField(value = "LOC_CODE")
    private String locCode;

    /**
     * 容量
     */
    @TableField(value = "CAPACITY")
    private BigDecimal capacity;

    /**
     * 启用日期
     */
    @TableField(value = "USED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date usedDate;

    /**
     * 货位状态
     */
    @TableField(value = "STATE")
    private Integer state;

    /**
     * 描述
     */
    @TableField(value = "REMARKS")
    private String remarks;

    /**
     * 是否启用:0禁用，1-启用，7-删除
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * 图片地址
     */
    @TableField(value = "IMAGE_PATH")
    private String imagePath;

    /**
     * 出入库频率
     */
    @TableField(value = "FREQUENCE")
    private Integer frequence;

    /**
     * 货物重量
     */
    @TableField(value = "WEIGHT")
    private Integer weight;

    /**
     * 货位体积：新增后固定
     */
    @TableField(value = "VOLUME")
    private Integer volume;

    /**
     * 货物名称
     */
    @TableField(value = "GOODS_NAME")
    private String goodsName;

    @TableField(value = "ASSIGN_STATE")
    private Integer assignState;


    /**
     * ----------------扩展
     */

    /**
     * 库点名称
     */
    @TableField(exist = false)
    private String storeName;

    /**
     * 仓房名称
     */
    @TableField(exist = false)
    private String stName;

    /**
     * 是否进行货位分配
     */
    @TableField(exist = false)
    private Integer isChange = 0;
}
