package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.domain.base.StoreLocation;
import com.hxdi.nmjl.domain.inventory.InventoryBase;
import com.hxdi.nmjl.mapper.inventory.InventoryBaseMapper;
import com.hxdi.nmjl.service.inventory.InventoryBaseService;
import com.hxdi.nmjl.utils.RedisKeys;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <库存基础信息服务实现>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/16 20:19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryBaseServiceImpl extends ServiceImpl<InventoryBaseMapper, InventoryBase> implements InventoryBaseService{

    @Override
    public void saveBaseInfo(InventoryBase inventoryBase) {
        InventoryBase  savedInventoryBase = getById(inventoryBase.getId());
        if (savedInventoryBase != null) {
            // 一般情况不会更新基础信息
            baseMapper.updateById(inventoryBase);
        }  else {
            long count = baseMapper.selectCount(Wrappers.<InventoryBase>lambdaQuery()
                    .eq(InventoryBase::getStId, inventoryBase.getStId())
                    .eq(InventoryBase::getLocId, inventoryBase.getLocId())
                    .eq(InventoryBase::getHisFlg, 0)
                    .eq(InventoryBase::getEnabled, StrPool.State.ENABLE));

            if(count > 0){
                baseMapper.updateHistory(inventoryBase.getLocId());
            }

            inventoryBase.setHisFlg(0);
            baseMapper.insert(inventoryBase);
        }
    }

    @Override
    public InventoryBase get(String inventoryId) {
        InventoryBase inventoryBase = baseMapper.selectById(inventoryId);
        inventoryBase.setStoreName(CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), inventoryBase.getStoreId(), Organization::getOrgName));
        inventoryBase.setStName(CacheProvider.getValue(RedisKeys.STORE_HOUSE.key(), inventoryBase.getStId(), StoreHouse::getName));
        inventoryBase.setLocName(CacheProvider.getValue(RedisKeys.STORE_LOCATION.key(), inventoryBase.getLocId(), StoreLocation::getName));
        inventoryBase.setStoreName(CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), inventoryBase.getManageUnitId(), Organization::getOrgName));
        return inventoryBase;
    }

}
