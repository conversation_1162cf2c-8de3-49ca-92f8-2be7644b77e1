<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ProcurementPlanMapper">

    <sql id="Base_Column_List">
        ID,PID,PLAN_CODE,YEAR,PLAN_TYPE,MANAGE_UNIT_ID,MANAGE_NAME,STORE_ID,STORE_NAME,ENTRUSTED_UNIT_ID,ENTRUSTED_UNIT_NAME,
        STATE,REPORTER,REPORT_TIME,APPROVE_STATUS,APPROVER,APPROVE_TIME,APPROVE_OPINION,NOTES,RCV_ADDR,RCV_DETAIL_ADDR,
        ENABLED,ATTACHEMENTS,CREATE_TIME,UPDATE_TIME,CREATE_ID,UPDATE_ID,TENANT_ID,DATA_HIERARCHY_ID,PARENT_FLAG,P_CODE,AREA_CODE
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.GrainProcurementPlan">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="PID" jdbcType="VARCHAR" property="pid" />
        <result column="P_CODE" jdbcType="VARCHAR" property="pCode" />
        <result column="PLAN_CODE" jdbcType="VARCHAR" property="planCode" />
        <result column="YEAR" jdbcType="INTEGER" property="year" />
        <result column="PLAN_TYPE" jdbcType="INTEGER" property="planType" />
        <result column="MANAGE_UNIT_ID" jdbcType="VARCHAR" property="manageUnitId" />
        <result column="MANAGE_NAME" jdbcType="VARCHAR" property="manageName" />
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
        <result column="ENTRUSTED_UNIT_ID" jdbcType="VARCHAR" property="entrustedUnitId" />
        <result column="ENTRUSTED_UNIT_NAME" jdbcType="VARCHAR" property="entrustedUnitName" />
        <result column="STATE" jdbcType="INTEGER" property="state" />
        <result column="REPORTER" jdbcType="VARCHAR" property="reporter" />
        <result column="REPORT_TIME" jdbcType="TIMESTAMP" property="reportTime" />
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus" />
        <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion" />
        <result column="NOTES" jdbcType="VARCHAR" property="notes" />
        <result column="RCV_ADDR" jdbcType="VARCHAR" property="rcvAddr" />
        <result column="RCV_DETAIL_ADDR" jdbcType="VARCHAR" property="rcvDetailAddr" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="ATTACHEMENTS" jdbcType="VARCHAR" property="attachements" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
        <result column="PARENT_FLAG" jdbcType="INTEGER" property="parentFlag" />
        <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_GRAIN_PROCUREMENT_PLAN
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.year)">
                and YEAR = #{condition.year}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planTypes)">
                and PLAN_TYPE = #{condition.planTypes}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.states)">
                and STATE = #{condition.states}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.manageUnitId)">
                and MANAGE_UNIT_ID = #{condition.manageUnitId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                and STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.reportStartTime)">
                and REPORT_TIME between #{condition.reportStartTime} and #{condition.reportEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.parentFlag)">
                and PARENT_FLAG = #{condition.parentFlag}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.pid)">
                and PID = #{condition.pid}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.pCode)">
                and P_CODE = #{condition.pCode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.search)">
                and (PLAN_CODE like concat('%',#{condition.search},'%') or MANAGE_NAME like concat('%',#{condition.search},'%'))
            </if>
        </where>
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/> from B_GRAIN_PROCUREMENT_PLAN
      <where>
        enabled = 1
        <if test="@plugins.OGNL@isNotEmpty(condition.year)">
          and YEAR = #{condition.year}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.planTypes)">
          and PLAN_TYPE = #{condition.planTypes}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.states)">
          and STATE = #{condition.states}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
          and APPROVE_STATUS = #{condition.approveStatus}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.manageUnitId)">
          and MANAGE_UNIT_ID = #{condition.manageUnitId}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
          and STORE_ID = #{condition.storeId}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.reportStartTime)">
          and REPORT_TIME between #{condition.reportStartTime} and #{condition.reportEndTime}
        </if>
          <if test="@plugins.OGNL@isNotEmpty(condition.parentFlag)">
              and PARENT_FLAG = #{condition.parentFlag}
          </if>
          <if test="@plugins.OGNL@isNotEmpty(condition.pid)">
              and PID = #{condition.pid}
          </if>
          <if test="@plugins.OGNL@isNotEmpty(condition.pCode)">
              and P_CODE = #{condition.pCode}
          </if>
          <if test="@plugins.OGNL@isNotEmpty(condition.search)">
              and (PLAN_CODE like concat('%',#{condition.search},'%') or MANAGE_NAME like concat('%',#{condition.search},'%'))
          </if>
      </where>
    </select>

    <select id="getPlanSummary" resultType="com.hxdi.nmjl.vo.bigscreen.ProcurementPlanSummaryVO">
        SELECT Round(SUM(pd.MIN_LIMIT) / 1000, 3)                  as planQty,
               Round(SUM(COALESCE(cd.COMPLETED_QTY, 0)) / 1000, 3) as completedQty
        FROM B_GRAIN_PROCUREMENT_PLAN p
                 LEFT JOIN B_GRAIN_PROCUREMENT_PLAN_DETAIL pd ON p.ID = pd.PLAN_ID
                 left join B_CONTRACT_INFO c on c.PLAN_ID = p.ID and c.ENABLED = 1
                 left join B_CONTRACT_DETAIL cd on cd.CONTRACT_ID = c.ID
        <where>
            p.ENABLED = 1
            AND p.parent_flag = 0
            AND p.state != 0
            AND p.year = YEAR(SYSDATE)
            <if test="@plugins.OGNL@isNotEmpty(areaCodeList)">
                AND p.AREA_CODE IN
                <foreach item="item" index="index" collection="areaCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getPlanSummaryPage" resultType="com.hxdi.nmjl.vo.bigscreen.ProcurementPlanSummaryVO">
        SELECT p.AREA_CODE                                         as areaCode,
               p.STORE_ID                                          as storeId,
               Max(p.STORE_NAME)                                   as storeName,
               pd.CLASSIFICATION_ID                                as classificationId,
               Max(pd.CLASSIFICATION_NAME)                         as classificationName,
               Round(SUM(pd.MIN_LIMIT) / 1000, 3)                  as planQty,
               Round(SUM(COALESCE(cd.COMPLETED_QTY, 0)) / 1000, 3) as completedQty
        FROM B_GRAIN_PROCUREMENT_PLAN p
                 LEFT JOIN B_GRAIN_PROCUREMENT_PLAN_DETAIL pd ON p.ID = pd.PLAN_ID
                 left join B_CONTRACT_INFO c on c.PLAN_ID = p.ID and c.ENABLED = 1
                 left join B_CONTRACT_DETAIL cd on cd.CONTRACT_ID = c.ID
        <where>
            p.ENABLED = 1
              AND p.parent_flag = 0
              AND p.state != 0
            <if test="@plugins.OGNL@isNotEmpty(condition.year)">
                AND p.year = #{condition.year}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.classificationId)">
                AND pd.CLASSIFICATION_ID = #{condition.classificationId}
            </if>
            <choose>
                <when test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                    AND p.STORE_ID = #{condition.storeId}
                </when>
                <when test="@plugins.OGNL@isNotEmpty(condition.areaCode)">
                    AND p.AREA_CODE IN
                    <foreach item="item" index="index"
                             collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.areaCode)" open="("
                             separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
        </where>
        GROUP BY p.AREA_CODE, p.STORE_ID, pd.CLASSIFICATION_ID
    </select>
</mapper>
