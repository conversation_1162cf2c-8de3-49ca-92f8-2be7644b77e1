package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.plan.BrandCrisis;
import com.hxdi.nmjl.service.plan.BrandCrisisService;
import com.hxdi.nmjl.condition.plan.BrandCrisisCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * 品牌危机管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/7
 */
@Api(tags = "品牌危机管理")
@RestController
@RequestMapping("/brand/crisis")
public class BrandCrisisController extends BaseController<BrandCrisisService, BrandCrisis> {

    @ApiOperation("分页查询品牌危机")
    @GetMapping("/page")
    public ResultBody<Page<BrandCrisis>> page(BrandCrisisCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询品牌危机")
    @GetMapping("/list")
    public ResultBody<Page<BrandCrisis>> list(BrandCrisisCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("查询品牌危机详情")
    @GetMapping("/getDetail")
    public ResultBody<BrandCrisis> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("保存/修改企业危机")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Boolean> saveOrUpdate(@RequestBody BrandCrisis crisis) {
        if (CommonUtils.isEmpty(crisis.getId())) {
            bizService.create(crisis);
        } else {
            bizService.update(crisis);
        }
        return ResultBody.ok();
    }

    @ApiOperation("删除品牌危机")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }


}
