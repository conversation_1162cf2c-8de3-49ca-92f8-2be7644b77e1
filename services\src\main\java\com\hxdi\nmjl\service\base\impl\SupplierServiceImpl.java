package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.clientrelated.SupplierCondition;
import com.hxdi.nmjl.domain.base.ClientInfo;
import com.hxdi.nmjl.domain.base.ProductionSupplier;
import com.hxdi.nmjl.domain.clientrelated.SupplierCredit;
import com.hxdi.nmjl.domain.clientrelated.SupplierProduct;
import com.hxdi.nmjl.linker.rule.BusinessRules;
import com.hxdi.nmjl.mapper.base.SupplierMapper;
import com.hxdi.nmjl.service.base.ClientInfoService;
import com.hxdi.nmjl.service.clientrelated.SupplierCreditService;
import com.hxdi.nmjl.service.clientrelated.SupplierProductService;
import com.hxdi.nmjl.service.base.SupplierService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 供应商实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/04/21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SupplierServiceImpl extends BaseServiceImpl<SupplierMapper, ProductionSupplier> implements SupplierService {

    @Resource
    private SupplierCreditService supplierCreditService;

    @Resource
    private SupplierProductService supplierProductService;

    @Resource
    @Lazy
    private ClientInfoService clientInfoService;

    @Resource
    private BusinessRules businessRules;

    @Override
    public void saveOrUpdateV1(ProductionSupplier supplier) {
        if (CommonUtils.isEmpty(supplier.getId())) {
            //新增
            verifySupplier(supplier);
            baseMapper.insert(supplier);
            //企业信用
            List<SupplierCredit> creditList = supplier.getSupplierCreditList();
            if (creditList != null && !creditList.isEmpty()) {
                creditList.forEach(credit -> credit.setSupplierId(supplier.getId()));
                supplierCreditService.saveBatch(creditList);
            }
            //企业产品
            List<SupplierProduct> productList = supplier.getSupplierProductList();
            if (productList != null && !productList.isEmpty()) {
                productList.forEach(product -> product.setSupplierId(supplier.getId()));
                supplierProductService.saveBatch(productList);
            }
            //审核通过之后，同步客户信息
            if (supplier.getApproveStatus() == 1) {
                syncClientInfo(supplier, false);
            }
        } else {
            //更新
            baseMapper.updateById(supplier);
            //企业信用
            supplierCreditService.removeByMainId(supplier.getId());
            List<SupplierCredit> creditList = supplier.getSupplierCreditList();
            if (creditList != null && !creditList.isEmpty()) {
                creditList.forEach(credit -> credit.setSupplierId(supplier.getId()));
                supplierCreditService.saveBatch(creditList);
            }
            //企业产品
            supplierProductService.removeByMainId(supplier.getId());
            List<SupplierProduct> productList = supplier.getSupplierProductList();
            if (productList != null && !productList.isEmpty()) {
                productList.forEach(product -> product.setSupplierId(supplier.getId()));
                supplierProductService.saveBatch(productList);
            }
            //审核通过之后，同步客户信息
            if (supplier.getApproveStatus() == 1) {
                syncClientInfo(supplier, true);
            }
        }
    }

    @Override
    public void changeState(String id, Integer enabled) {
        baseMapper.changeStatus(id, enabled);
        ProductionSupplier supplier = baseMapper.selectById(id);

        //如果为审核通过的供应商，则删除对应的客户信息
        if (supplier.getApproveStatus() == 1) {
            if (Integer.valueOf(0).equals(enabled) || Integer.valueOf(7).equals(enabled)) {
                if (businessRules.removeVerify(getById(id))) {
                    clientInfoService.removeByRefId(id, enabled);
                } else {
                    BizExp.pop("无法删除该供应商信息记录");
                }
            }
        }
    }

    @Override
    public Page<ProductionSupplier> pages(SupplierCondition condition) {
        Page<ProductionSupplier> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<ProductionSupplier> lists(SupplierCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public ProductionSupplier getDetail(String id) {
        ProductionSupplier supplier = baseMapper.selectOne(Wrappers.<ProductionSupplier>lambdaQuery()
                .eq(ProductionSupplier::getId, id)
                .eq(ProductionSupplier::getEnabled, 1));
        //查询对应的企业信用及企业产品
        supplier.setSupplierCreditList(supplierCreditService.getDetail(id));
        supplier.setSupplierProductList(supplierProductService.getDetail(id));
        return supplier;
    }


    /**
     * 验证数据有效性
     */
    private void verifySupplier(ProductionSupplier supplier) {
        if (StringUtils.isNotBlank(supplier.getSupplierCode())) {
            long count = baseMapper.selectCount(Wrappers.<ProductionSupplier>lambdaQuery()
                    .eq(ProductionSupplier::getSupplierCode, supplier.getSupplierCode()));
            if (count > 0) {
                throw new BaseException("已存在相同的供应商编号，请检查录入数据");
            }
        }
    }

    private void syncClientInfo(ProductionSupplier supplier, boolean isUpdate) {
        //同步客户信息
        ClientInfo clientInfo = new ClientInfo();
        BeanUtils.copyProperties(supplier, clientInfo, "id", "createId", "dataHierarchyId");
        clientInfo.setRefId(supplier.getId());
        clientInfo.setOrigin(2);
        clientInfo.setClientType("1");
        if (isUpdate) {
            clientInfoService.updateV1(clientInfo, true);
        } else {
            clientInfoService.createV1(clientInfo);
        }
    }

    public void updateAvgScore(ProductionSupplier supplier) {
        baseMapper.updateById(supplier);
    }

    @Override
    public void approve(String planId, Integer approveStatus, String opinion) {
        ProductionSupplier plan = this.getById(planId);
        if (plan == null) {
            throw new BaseException("待审核供应商不存在！");
        }

        if (plan.getApproveStatus() != null && plan.getApproveStatus() == 1) {
            throw new BaseException("该供应商已审核！");
        }

        // 设置审核状态
        updatePlanApprovalInfo(plan, approveStatus, opinion);

    }

    /**
     * 设置审批状态
     *
     * @param plan
     * @param approveStatus
     * @param opinion
     */
    private void updatePlanApprovalInfo(ProductionSupplier plan, Integer approveStatus, String opinion) {
        plan.setApproveStatus(approveStatus);
        plan.setApproveOpinion(opinion);
        plan.setApprover(SecurityHelper.obtainUser().getNickName());
        plan.setApproveTime(new Date());

        this.updateById(plan);

        // 审核通过之后，同步客户信息
        if (approveStatus == 1) {
            syncClientInfo(plan, false);
        }
    }

}

