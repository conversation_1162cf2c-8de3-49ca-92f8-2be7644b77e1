package com.hxdi.nmjl.service.quality.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.service.quality.QualityInspectionDetailService;
import org.springframework.stereotype.Service;
import com.hxdi.nmjl.domain.quality.QualityInspectionDetail;
import com.hxdi.nmjl.mapper.quality.QualityInspectionDetailMapper;

import java.io.Serializable;
import java.util.List;

@Service
public class QualityInspectionDetailServiceImpl extends BaseServiceImpl<QualityInspectionDetailMapper, QualityInspectionDetail> implements QualityInspectionDetailService {

    @Override
    public List<QualityInspectionDetail> getDetail(Serializable InspectionId) {
        LambdaQueryWrapper<QualityInspectionDetail> wrapper = new LambdaQueryWrapper<QualityInspectionDetail>()
                .eq(QualityInspectionDetail::getInspectId,InspectionId);
        return list(wrapper);
    }
}
