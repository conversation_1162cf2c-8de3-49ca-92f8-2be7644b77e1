package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.base.Trademarks;
import com.hxdi.nmjl.mapper.base.TrademarksMapper;
import com.hxdi.nmjl.service.base.TrademarksService;
import com.hxdi.nmjl.condition.base.TrademarkCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/9 4:57 下午
 * @description 品牌
 * @version 1.0
 */
@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class TrademarksServiceImpl extends BaseServiceImpl<TrademarksMapper, Trademarks> implements TrademarksService {

    @Override
    public void saveOrUpdates(Trademarks trademark) {
        saveOrUpdate(trademark);
    }

    @Override
    public void remove(String brandId) {
        baseMapper.deleteById(brandId);
    }

    @Override
    public Page<Trademarks> pages(TrademarkCondition condition) {
        Page<Trademarks> page = condition.newPage();
        baseMapper.selectPage(page, Wrappers.<Trademarks>lambdaQuery()
                .like(CommonUtils.isNotEmpty(condition.getBrandName()), Trademarks::getBrandName, condition.getBrandName())
                .eq(Trademarks::getClassificationId, condition.getClassificationId())
                .orderByDesc(Trademarks::getUpdateTime));

        return page;
    }

    @Override
    public List<Trademarks> lists(TrademarkCondition condition) {
        if (CommonUtils.isBlank(condition.getClassificationId())){
            return Collections.EMPTY_LIST;
        }

        List<Trademarks> trademarks = baseMapper.selectList(Wrappers.<Trademarks>lambdaQuery()
                .eq(Trademarks::getClassificationId, condition.getClassificationId())
                .orderByDesc(Trademarks::getUpdateTime));

        return trademarks;
    }
}
