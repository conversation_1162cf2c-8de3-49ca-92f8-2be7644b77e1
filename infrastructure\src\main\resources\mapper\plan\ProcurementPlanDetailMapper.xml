<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ProcurementPlanDetailMapper">

    <sql id="Base_Column_List">
        ID, PLAN_ID, CLASSIFICATION_ID, CLASSIFICATION_NAME, SPECIFICATIONS, GRADE, LIMIT_MIN, LIMIT_MAX, REQUIREMENT, FIRST_DELIVERY_TIME, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.GrainProcurementPlanDetail">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="PLAN_ID" jdbcType="VARCHAR" property="planId" />
        <result column="CLASSIFICATION_ID" jdbcType="VARCHAR" property="classificationId" />
        <result column="CLASSIFICATION_NAME" jdbcType="VARCHAR" property="classificationName" />
        <result column="SPECIFICATIONS" jdbcType="VARCHAR" property="specifications" />
        <result column="GRADE" jdbcType="VARCHAR" property="grade" />
        <result column="MAX_LIMIT" jdbcType="DECIMAL" property="maxLimit" />
        <result column="MIN_LIMIT" jdbcType="DECIMAL" property="minLimit" />
        <result column="REQUIREMENT" jdbcType="VARCHAR" property="requirement" />
        <result column="SUPPLY_FREQUENCY" jdbcType="VARCHAR" property="supplyFrequency" />
        <result column="FIRST_DELIVERY_TIME" jdbcType="DATE" property="firstDeliveryTime" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>
    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO B_GRAIN_PROCUREMENT_PLAN_DETAIL
        <include refid="Base_Column_List"/>
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.planId},
            #{item.classificationId},
            #{item.classificationName},
            #{item.specifications},
            #{item.grade},
            #{item.maxLimit,jdbcType=DECIMAL},
            #{item.minLimit,jdbcType=DECIMAL},
            #{item.requirement},
            #{item.supplyFrequency},
            #{item.firstDeliveryTime,jdbcType=DATE},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.createId},
            #{item.updateId},
            #{item.tenantId},
            #{item.dataHierarchyId}
            )
        </foreach>
    </insert>
    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from B_GRAIN_PROCUREMENT_PLAN_DETAIL
        <where>
            <if test="id != null and id != ''">
                AND ID = #{id}
            </if>
            <if test="planId != null and planId != ''">
                AND PLAN_ID = #{planId}
            </if>
            <if test="classificationName != null and classificationName != ''">
                AND CLASSIFICATION_NAME LIKE CONCAT('%', #{classificationName}, '%')
            </if>
            <if test="specifications != null and specifications != ''">
                AND SPECIFICATIONS LIKE CONCAT('%', #{specifications}, '%')
            </if>
            <if test="grade != null and grade != ''">
                AND GRADE = #{grade}
            </if>
            <if test="minLimit != null">
                AND MIN_LIMIT &gt;= #{minLimit}
            </if>
            <if test="maxLimit != null">
                AND MAX_LIMIT &lt;= #{maxLimit}
            </if>
            <if test="requirement != null and requirement != ''">
                AND REQUIREMENT LIKE CONCAT('%', #{requirement}, '%')
            </if>
            <if test="firstDeliveryTimeStart != null">
                AND FIRST_DELIVERY_TIME &gt;= #{firstDeliveryTimeStart}
            </if>
            <if test="firstDeliveryTimeEnd != null">
                AND FIRST_DELIVERY_TIME &lt;= #{firstDeliveryTimeEnd}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
