package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.nmjl.domain.plan.BidInfo;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.plan.BidInfoCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BidInfoMapper extends SuperMapper<BidInfo> {

    @DataPermission
    List<BidInfo> getList(@Param("condition") BidInfoCondition condition);

    @DataPermission
    Page<BidInfo> getPages(@Param("condition") BidInfoCondition condition, @Param("page") Page<BidInfo> page);
}
