package com.hxdi.nmjl.vo.inventory;

import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: nmjl-service
 * @description: 库存关键数据返回VO
 * @author: 王贝强
 * @create: 2025-08-19 10:46
 */
@Setter
@Getter
@ApiModel(value = "库存关键数据返回VO")
public class InventoryCoreDataVO extends Entity<InventoryCoreDataVO> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "军供站名称")
    private String storeName;

    @ApiModelProperty(value = "仓房/油罐名称")
    private String stName;

    @ApiModelProperty(value = "货位名称")
    private String locName;

    @ApiModelProperty(value = "货位状态")
    private Integer locState;

    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value="品牌")
    private String brand;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "生产批次")
    private String batchNum;

    @ApiModelProperty(value = "质量等级")
    private String grade;

    @ApiModelProperty(value = "储备性质")
    private Integer reserveLevel;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal inventoryQty;
}
