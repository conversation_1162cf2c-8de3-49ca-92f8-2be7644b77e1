package com.hxdi.nmjl.service.plan.impl;

import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.plan.InspectionRecord;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.InspectionRecordMapper;
import com.hxdi.nmjl.service.plan.InspectionItemService;
import com.hxdi.nmjl.service.plan.InspectionPlanService;
import com.hxdi.nmjl.service.plan.InspectionRecordService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * <监督检查记录服务实现>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/13 18:51
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InspectionRecordServiceImpl extends BaseServiceImpl<InspectionRecordMapper, InspectionRecord> implements InspectionRecordService {

    @Resource
    private InspectionPlanService inspectionPlanService;

    @Resource
    private InspectionItemService inspectionItemService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public InspectionRecord getById(Serializable id) {
        InspectionRecord inspectionRecode = baseMapper.selectById(id);
        inspectionRecode.setDetailList(inspectionItemService.getListByPid((String) id));
        return inspectionRecode;
    }

    @Override
    public boolean saveOrUpdate(InspectionRecord entity) {
        if (CommonUtils.isEmpty(entity.getId())) {
            create(entity);
        } else {
            update(entity);
        }

        return true;
    }

    public void create(InspectionRecord entity) {
        //生成计划编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("INSPECTION_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        entity.setInspectCode((String) businessCode.getValue());

        checkResult(entity);
        baseMapper.insert(entity);

        inspectionItemService.updateList(entity.getId(), entity.getDetailList());

        // 更新计划状态
        updatePlanState(entity);
    }



    public void update(InspectionRecord entity) {
        checkResult(entity);
        baseMapper.updateById(entity);
        inspectionItemService.updateList(entity.getId(), entity.getDetailList());

        // 更新计划状态
        updatePlanState(entity);
    }


    /**
     * 检查和更新结果与整改状态
     * @param entity
     */
    private void checkResult(InspectionRecord entity) {
        Integer resultState = 0;
        Integer dealState = 0;
        if (!CollectionUtils.isEmpty(entity.getDetailList())) {
            boolean result_1 = entity.getDetailList().stream().anyMatch(item -> item.getResult() == 1);
            if (result_1) {
                resultState = 1;
            }

            boolean result_2 = entity.getDetailList().stream().anyMatch(item -> item.getResult() == 2);
            if (result_2) {
                resultState = 2;
            }

            //----
            // 检查结果状态不为0-正常的情况下，需更新整改状态
            if (resultState != 0) {
                boolean state_1 = entity.getDetailList().stream().anyMatch(item -> item.getState() == 1);
                if (state_1) {
                    dealState = 1;
                }

                boolean state_2 = entity.getDetailList().stream().allMatch(item -> item.getState() == 2 || item.getState() == 0);
                if (state_2) {
                    dealState = 2;
                }
            }
            entity.setResultState(resultState);
            entity.setDealState(dealState);
        }
    }

    /**
     * 触发更新计划状态
     * @param entity
     */
    private void updatePlanState(InspectionRecord entity) {
        if (entity.getDealState() == 1) {
            // 整改中，更新计划状态为执行中
            inspectionPlanService.updateState(entity.getPlanCode(), 1);
        } else if (entity.getDealState() == 2 || entity.getDealState() == 0) {
            // 已整改|无需整改，更新计划状态为已完成
            inspectionPlanService.updateState(entity.getPlanCode(), 2);
        }
    }
}
