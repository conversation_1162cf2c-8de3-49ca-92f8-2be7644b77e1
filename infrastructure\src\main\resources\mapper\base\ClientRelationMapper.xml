<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.ClientRelationMapper">

    <delete id="deleteByClientIdAndOrgId">
        delete from B_CLIENT_RELATION where CLIENT_ID = #{cid} and DATA_HIERARCHY_ID = #{oid}
    </delete>

    <delete id="deleteAll">
        delete from B_CLIENT_RELATION where CLIENT_ID = #{cid}
    </delete>
</mapper>
