package com.hxdi.nmjl.controller.emergency;

import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.emergency.EmergencyStoreApplyCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStat;
import com.hxdi.nmjl.domain.emergency.EmergencyStoreApply;
import com.hxdi.nmjl.service.emergency.EmergencyLossStatService;
import com.hxdi.nmjl.service.emergency.EmergencyStoreApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <应急补库申请管理>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:15
 */
@RestController
@RequestMapping("/emergency/store/apply")
@Api(tags = "应急补库申请管理")
public class EmergencyStoreApplyController extends CommonApiController<EmergencyStoreApplyService, EmergencyStoreApply, EmergencyStoreApplyCondition> {

    @ApiOperation(value = "审核")
    @PostMapping("/approve")
    @Log("审核")
    public ResultBody<Void> approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @PostMapping("/reject")
    @Log("驳回")
    public ResultBody<Void> reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "提交")
    @PostMapping("/submit")
    @Log("提交")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "完成")
    @PostMapping("/complete")
    @Log("完成")
    public ResultBody<Void> complete(@RequestParam String id) {
        bizService.complete(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "结算")
    @PostMapping("/accounted")
    @Log("结算")
    public ResultBody<Void> accounted(@RequestBody EmergencyStoreApply emergencyStoreApply) {
        bizService.accounted(emergencyStoreApply);
        return ResultBody.ok();
    }

    @ApiOperation(value = "查询应急损耗统计列表")
    @GetMapping("/lossStats/list")
    public ResultBody<List<EmergencyLossStat>> queryLossStatList(String storeId) {
        return ResultBody.<List<EmergencyLossStat>>OK().data(bizService.queryLossStatList(storeId));
    }

}
