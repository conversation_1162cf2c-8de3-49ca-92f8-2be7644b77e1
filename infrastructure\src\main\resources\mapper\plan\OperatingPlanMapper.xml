<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.OperatingPlanMapper">

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, PID, PLAN_CODE, PLAN_NAME, ORG_ID,
        ORG_NAME, STORE_ID, STORE_NAME, PLAN_YEAR, OBJECTIVE_DESC,
        CHARGE_PERSON, STATE, APPROVE_TIME, APPROVE_STATUS, APPROVER,
        APPROVE_OPINION, ENABLED, ATTACHMENT, CREATE_TIME, UPDATE_TIME,
        CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, DEPT_NAME,
        FATHER_PLAN_CODE,FINISH_PERCENT, SUMMARIZE, PROBLEM, MEASURE
    </sql>

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.OperatingPlan">
        <id column="ID" property="id"/>
        <result column="PID" property="pid"/>
        <result column="PLAN_CODE" property="planCode"/>
        <result column="PLAN_NAME" property="planName"/>
        <result column="ORG_ID" property="orgId"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="STORE_ID" property="storeId"/>
        <result column="STORE_NAME" property="storeName"/>
        <result column="PLAN_YEAR" property="planYear"/>
        <result column="OBJECTIVE_DESC" property="objectiveDesc"/>
        <result column="CHARGE_PERSON" property="chargePerson"/>
        <result column="STATE" property="state"/>
        <result column="APPROVE_TIME" property="approveTime"/>
        <result column="APPROVE_STATUS" property="approveStatus"/>
        <result column="APPROVER" property="approver"/>
        <result column="APPROVE_OPINION" property="approveOpinion"/>
        <result column="ENABLED" property="enabled"/>
        <result column="ATTACHMENT" property="attachment"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
        <result column="DEPT_NAME" property="deptName"/>
        <result column="FATHER_PLAN_CODE" property="fatherPlanCode"/>
        <result column="FINISH_PERCENT" property="finishPercent"/>
        <result column="SUMMARIZE" property="summarize"/>
        <result column="PROBLEM" property="problem"/>
        <result column="MEASURE" property="measure"/>
    </resultMap>

    <!-- 列表查询 -->
    <select id="getList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_OPERATING_PLAN
        <where>
            ENABLED = 1
            <!-- 计划状态 -->
            <if test="condition.state != null">
                AND STATE = #{condition.state}
            </if>
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <!-- 计划名称 -->
            <if test="condition.planName != null and condition.planName != ''">
                AND PLAN_NAME LIKE CONCAT('%', #{condition.planName}, '%')
            </if>
            <if test="condition.planCode !=null and condition.planCode !=''">
                AND PLAN_CODE = #{condition.planCode}
            </if>
            <if test="condition.pid == 0">
                AND PID = 0
            </if>
            <if test="condition.pid == 1">
                AND PID != 0
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 分页查询 -->
    <select id="getPages" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_OPERATING_PLAN
        <where>
            ENABLED = 1
            <!-- 计划状态 -->
            <if test="condition.state != null">
                AND STATE = #{condition.state}
            </if>
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <!-- 计划名称 -->
            <if test="condition.planName != null and condition.planName != ''">
                AND PLAN_NAME LIKE CONCAT('%', #{condition.planName}, '%')
            </if>
            <if test="condition.planCode !=null and condition.planCode !=''">
                AND PLAN_CODE = #{condition.planCode}
            </if>
            <if test="condition.pid == 0">
                AND PID = 0
            </if>
            <if test="condition.pid == 1">
                AND PID != 0
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
