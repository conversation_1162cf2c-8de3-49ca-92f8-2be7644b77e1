package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.plan.BrandActivity;
import com.hxdi.nmjl.service.plan.BrandActivityService;
import com.hxdi.nmjl.condition.plan.BrandActivityCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 品牌传播活动管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/7
 */
@Api(tags = "品牌传播活动管理")
@RestController
@RequestMapping("/brand/activity")
public class BrandActivityController extends BaseController<BrandActivityService, BrandActivity> {

    @ApiOperation("分页查询活动")
    @GetMapping("/page")
    public ResultBody<Page<BrandActivity>> page(BrandActivityCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }


    @ApiOperation("列表查询活动")
    @GetMapping("/list")
    public ResultBody<List<BrandActivity>> list(BrandActivityCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }


    @ApiOperation("保存/修改活动")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody BrandActivity activity) {
        if(CommonUtils.isEmpty(activity.getId())) {
            bizService.create(activity);
        } else {
            bizService.update(activity);
        }
        return ResultBody.ok();
    }


    @ApiOperation("查看活动详情")
    @GetMapping("/getDetail")
    public ResultBody<BrandActivity> getDetail(@RequestParam String activityId) {
        return ResultBody.ok().data(bizService.getDetail(activityId));
    }


    @ApiOperation("审批活动")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String activityId,
                              @RequestParam Integer approveStatus,
                              @RequestParam(required = false) String opinion) {
        bizService.approve(activityId, approveStatus, opinion);
        return ResultBody.ok();
    }


    @ApiOperation("删除活动")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation("提交审核")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String activityId) {
        bizService.submit(activityId);
        return ResultBody.ok();
    }


    @ApiOperation("更新活动目标达成率")
    @PutMapping("/updateCompletedRate")
    public ResultBody updateCompletedRate(@RequestParam String activityId, @RequestParam Integer rate) {
        bizService.updateCompletedRate(activityId, rate);
        return ResultBody.ok();
    }
}
