package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.StorehouseCondition;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.service.base.StoreHouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "仓房管理")
@RestController
@RequestMapping("/storehouse")
public class StorehouseController extends BaseController<StoreHouseService, StoreHouse> {


    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody StoreHouse storehouse) {
        if (CommonUtils.isEmpty(storehouse.getId())) {
            create(storehouse);
        } else {
            update(storehouse);
        }
        return ResultBody.OK();
    }

    @ApiOperation("新增")
    @PostMapping("/add")
    public ResultBody<Void> create(@RequestBody StoreHouse storehouse) {
        bizService.create(storehouse);
        return ResultBody.OK();
    }

    @ApiOperation("更新")
    @PutMapping("/update")
    public ResultBody<Void> update(@RequestBody StoreHouse storehouse) {
        bizService.update(storehouse);
        return ResultBody.OK();
    }

    @ApiOperation("删除")
    @PostMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        this.changeState(id, 7);
        return ResultBody.OK();
    }

    @ApiOperation("启用/禁用")
    @PostMapping("/change/state")
    public ResultBody<Void> changeState(@RequestParam String id, @RequestParam Integer state) {
        bizService.changeState(id, state);
        return ResultBody.OK();
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<StoreHouse> getStorehouse(String id) {
        return ResultBody.<StoreHouse>OK().data(bizService.getByUniqueKey(id));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<StoreHouse>> pages(StorehouseCondition condition) {
        return ResultBody.<Page<StoreHouse>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<StoreHouse>> lists(StorehouseCondition condition) {
        return ResultBody.<List<StoreHouse>>OK().data(bizService.lists(condition));
    }
}
