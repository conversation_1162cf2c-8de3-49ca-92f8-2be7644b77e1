package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.plan.OperatingPlanCondition;
import com.hxdi.nmjl.domain.plan.OperatingPlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OperatingPlanMapper extends SuperMapper<OperatingPlan> {
    @DataPermission
    List<OperatingPlan> getList(@Param("condition") OperatingPlanCondition condition);
    @DataPermission
    Page<OperatingPlan> getPages(@Param("condition") OperatingPlanCondition condition, @Param("page") Page<OperatingPlan> page);

}
