<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.mobilization.MobilizeChefMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.mobilization.MobilizeChefInfo">
        <!-- 基础字段 -->
        <id property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="idCard" column="ID_CARD"/>
        <result property="birthDay" column="BIRTHDAY"/>
        <result property="gender" column="GENDER"/>
        <result property="politicsStatus" column="POLITICS_STATUS"/>
        <result property="mobile" column="MOBILE"/>

        <!-- 联系人信息 -->
        <result property="contact1" column="CONTACT1"/>
        <result property="contactRef1" column="CONTACT_REF1"/>
        <result property="contractTel1" column="CONTRACT_TEL1"/>

        <result property="contact2" column="CONTACT2"/>
        <result property="contactRef2" column="CONTACT_REF2"/>
        <result property="contractTel2" column="CONTRACT_TEL2"/>

        <result property="contact3" column="CONTACT3"/>
        <result property="contactRef3" column="CONTACT_REF3"/>
        <result property="contractTel3" column="CONTRACT_TEL3"/>

        <!-- 描述与能力 -->
        <result property="profile" column="PROFILE"/>
        <result property="tags" column="TAGS"/>

        <!-- 附件信息 -->
        <result property="idcardFile" column="IDCARD_FILE"/>
        <result property="chefCertFile" column="CHEF_CERT_FILE"/>
        <result property="attachment" column="ATTACHMENT"/>
        <result property="attachements" column="ATTACHEMENTS"/>

        <!-- 审核信息 -->
        <result property="approveTime" column="APPROVE_TIME"/>
        <result property="approveStatus" column="APPROVE_STATUS"/>
        <result property="approver" column="APPROVER"/>
        <result property="approveOpinion" column="APPROVE_OPINION"/>

        <!-- 状态 -->
        <result property="enabled" column="ENABLED"/>

        <!-- 时间戳 -->
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>

        <!-- 用户信息 -->
        <result property="createId" column="CREATE_ID"/>
        <result property="updateId" column="UPDATE_ID"/>
        <result property="tenantId" column="TENANT_ID"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, NAME, ID_CARD, BIRTHDAY, GENDER, POLITICS_STATUS, MOBILE, CONTACT1, CONTACT_REF1, CONTRACT_TEL1, CONTACT2, CONTACT_REF2, CONTRACT_TEL2, CONTACT3, CONTACT_REF3, CONTRACT_TEL3, PROFILE, TAGS, IDCARD_FILE, CHEF_CERT_FILE, ATTACHMENT, ATTACHEMENTS, APPROVE_TIME, APPROVE_STATUS, APPROVER, APPROVE_OPINION, ENABLED
            , CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <select id="getList" parameterType="com.hxdi.nmjl.condition.mobilization.MobilizeChefCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
            FROM B_MOBILIZED_CHEF_INFO
        <where>
            enabled = 1
            <if test="condition.name != null and condition.name != ''">
                AND (NAME LIKE CONCAT('%', #{condition.name}, '%')
                OR ID_CARD LIKE CONCAT('%', #{condition.name}, '%'))
            </if>
            <if test="condition.gender != null and condition.gender != ''">
                AND GENDER = #{condition.gender}
            </if>
            <if test="condition.politicsStatus != null and condition.politicsStatus != ''">
                AND POLITICS_STATUS = #{condition.politicsStatus}
            </if>
            <if test="condition.approveStatus != null and condition.approveStatus != ''">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="getPages" parameterType="com.hxdi.nmjl.condition.mobilization.MobilizeChefCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
            FROM B_MOBILIZED_CHEF_INFO
        <where>
            enabled = 1
            <if test="condition.name != null and condition.name != ''">
                AND (NAME LIKE CONCAT('%', #{condition.name}, '%')
                OR ID_CARD LIKE CONCAT('%', #{condition.name}, '%'))
            </if>
            <if test="condition.gender != null and condition.gender != ''">
                AND GENDER = #{condition.gender}
            </if>
            <if test="condition.politicsStatus != null and condition.politicsStatus != ''">
                AND POLITICS_STATUS = #{condition.politicsStatus}
            </if>
            <if test="condition.approveStatus != null and condition.approveStatus != ''">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
