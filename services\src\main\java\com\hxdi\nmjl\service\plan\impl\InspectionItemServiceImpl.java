package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.plan.InspectionItem;
import com.hxdi.nmjl.mapper.plan.InspectionItemMapper;
import com.hxdi.nmjl.service.plan.InspectionItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class InspectionItemServiceImpl extends BaseServiceImpl<InspectionItemMapper, InspectionItem> implements InspectionItemService {

    @Override
    public List<InspectionItem> getListByPid(String pid){
        return baseMapper.selectList(Wrappers.<InspectionItem>lambdaQuery().eq(InspectionItem::getMainId, pid));
    }

    @Override
    public void updateList(String pid, List<InspectionItem> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }

        this.removeByPid(pid);

        detailList.forEach(item -> item.setMainId(pid));
        this.saveOrUpdateBatch(detailList);
    }

    private void removeByPid(String pid) {
        this.remove(Wrappers.<InspectionItem>lambdaQuery().eq(InspectionItem::getMainId, pid));
    }
}
