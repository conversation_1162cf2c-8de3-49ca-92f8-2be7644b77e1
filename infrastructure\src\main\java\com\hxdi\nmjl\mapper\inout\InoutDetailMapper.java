package com.hxdi.nmjl.mapper.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.condition.inout.InoutDetailCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_INOUT_DETAIL(出入库记录)】的数据库操作Mapper
* @createDate 2025-04-08 16:12:55
* @Entity com.hxdi.nmjl.domain.inventory.BInoutDetail
*/
public interface InoutDetailMapper extends SuperMapper<InoutDetail> {

    /**
     * 分页查询
     * @param page
     * @param condition
     * @return
     */
    @DataPermission
    Page<InoutDetail> selectPageV1(Page<InoutDetail> page, @Param("condition") InoutDetailCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    @DataPermission
    List<InoutDetail> selectListV1(@Param("condition") InoutDetailCondition condition);
}




