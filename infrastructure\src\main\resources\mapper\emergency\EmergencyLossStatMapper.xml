<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyLossStatMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyLossStat">
    <!--@mbg.generated-->
    <!--@Table B_EMERGENCY_LOSS_STAT-->
    <id column="ID" property="id" />
    <result column="TASK_CODE" property="taskCode" />
    <result column="TASK_Name" property="taskName" />
    <result column="STORE_ID" property="storeId" />
    <result column="STORE_NAME" property="storeName" />
    <result column="LOSS_TOTAL" property="lossTotal" />
    <result column="CLASSIFICATION_ID" property="classificationId" />
    <result column="CLASSIFICATION_NAME" property="classificationName" />
    <result column="AMOUNT" property="amount" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    <result column="STATE" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TASK_CODE, TASK_NAME, STORE_ID, STORE_NAME, LOSS_TOTAL, CLASSIFICATION_ID, CLASSIFICATION_NAME, AMOUNT,
    TENANT_ID, DATA_HIERARCHY_ID, "STATE"
  </sql>

</mapper>
