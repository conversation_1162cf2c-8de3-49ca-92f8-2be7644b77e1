package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel(description = "作业查询条件")
public class OptCondition extends QueryCondition {

    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;

    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;

    @ApiModelProperty(value = "仓房ID")
    private String stId;

    @ApiModelProperty(value = "任务类型")
    private String optType;
}
