package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.plan.SaleCondition;
import com.hxdi.nmjl.domain.plan.SaleOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SaleOrderMapper extends SuperMapper<SaleOrder> {

    @DataPermission
    Page<SaleOrder> selectPageV1(Page<SaleOrder> page,@Param("condition") SaleCondition condition);

    List<SaleOrder> selectListV1(@Param("condition") SaleCondition condition);
}