package com.hxdi.nmjl.enums;

import lombok.Getter;

/**
 * @program: nmjl-service
 * @description: 设备状态枚举类
 * @author: 王贝强
 * @create: 2025-04-17 12:26
 */
@Getter
public enum DeviceEnum {
    /**
     * 设备状态
     */
    ONLINE(1, "正常"),
    WARNING(2, "告警"),
    ERROR(3, "损坏"),
    OFFLINE(0, "停用(正常)"),

    /**
     * 设备类型
     */
    TEM_HUM(101, "温湿度检测设备");

    private final int code;
    private final String desc;

    DeviceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
