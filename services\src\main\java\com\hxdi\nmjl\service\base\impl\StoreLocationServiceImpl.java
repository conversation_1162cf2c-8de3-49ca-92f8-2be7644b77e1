package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.base.StoreLocationCondition;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.domain.base.StoreLocation;
import com.hxdi.nmjl.enums.BusinessType;
import com.hxdi.nmjl.mapper.base.StoreLocationMapper;
import com.hxdi.nmjl.service.base.OrganizationService;
import com.hxdi.nmjl.service.base.StoreHouseService;
import com.hxdi.nmjl.service.base.StoreLocationService;
import com.hxdi.nmjl.service.common.BarCodeService;
import com.hxdi.nmjl.utils.RedisKeys;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <货位管理实现>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/12 14:01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StoreLocationServiceImpl extends BaseServiceImpl<StoreLocationMapper, StoreLocation> implements StoreLocationService {


    @Resource
    private StoreHouseService storeHouseService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private BarCodeService barCodeService;

    @Override
    @CachePut(value = RedisKeys.Prefix.STORE_LOCATION, key = "#location.id", unless = "#result == null")
    public StoreLocation create(StoreLocation location) {
        verifyStoreLocation(location, 0);
        location.setId(IdWorker.getIdStr(location));
        location.setLocCode(location.getId());
        location.setWeight(0);
        location.setVolume(location.getCapacity().intValue());
        StoreHouse storehouse = CacheProvider.getCacheObject(RedisKeys.Prefix.STORE_HOUSE, location.getStId());
        if (storehouse == null) {
            throw new BaseException("仓房信息不存在！");
        }
        location.setStoreId(storehouse.getStoreId());

        location.setDataHierarchyId(storehouse.getDataHierarchyId());
        baseMapper.insert(location);

        //生成货位二维码
        barCodeService.generateBarCode(location.getId(), BusinessType.STORE_LOCATION, "");

        return getByUniqueKey(location.getId());
    }

    @Override
    @CachePut(value = RedisKeys.Prefix.STORE_LOCATION, key = "#location.id", unless = "#result == null")
    public StoreLocation update(StoreLocation location) {
        verifyStoreLocation(location, 1);
        StoreLocation storeLocation = baseMapper.selectById(location.getId());
        if (storeLocation == null) {
            throw new BaseException("货位信息不存在！");
        }
        if (StrPool.State.DELETE == storeLocation.getEnabled().intValue()) {
            throw new BaseException("货位信息已删除，不能更新！");
        }
//        if (storeLocation.getIsChange() == 0) {
//            //重设容量及可用容量
//            storeLocation.setVolume(storeLocation.getCapacity().intValue());
//            if (location.getWeight() > 0) {
//                location.setCapacity(BigDecimal.valueOf(storeLocation.getVolume() - location.getWeight()));
//            }
//        }

        StoreHouse storehouse = CacheProvider.getCacheObject(RedisKeys.Prefix.STORE_HOUSE, location.getStId());
        if (storehouse == null) {
            throw new BaseException("仓房信息不存在！");
        }

        if (location.getCapacity().compareTo(storehouse.getCapacity()) > 0) {
            throw new BaseException("货位容量体积不能超过仓房容量！");
        }
        if (location.getAssignState() == 1 && storeLocation.getIsChange() == 1) {
            validLocation(location);

            // 分配减少货位容量
            // 货物重量大于0，说明货位已存放货物，可用容量（capacity） = 仓房容量（volume）-货物重量（weight）
            if (location.getWeight() > 0) {
                location.setCapacity(BigDecimal.valueOf(storeLocation.getVolume() - location.getWeight()));
            }

        }
        baseMapper.updateById(location);
        return getByUniqueKey(location.getId());
    }

    private void validLocation(StoreLocation location) {
        //检测仓位状态必须启用
        if (location.getState().equals(2)) {
            throw new BaseException("货位未启用！不能添加/更新货物");
        }

        //检查货物重量不能超过货位容量
        Integer weight = location.getWeight();
        BigDecimal capacity = location.getCapacity();
        if (weight > capacity.intValue()) {
            throw new BaseException("货物重量不能超过货位可用容量！");
        }
    }

    @Override
    @CacheEvict(value = RedisKeys.Prefix.STORE_HOUSE, key = "#id", condition = "#state == 7 OR #state == 0")
    @CachePut(value = RedisKeys.Prefix.STORE_HOUSE, key = "#id", condition = "#state == 1")
    public StoreLocation changeState(String id, Integer state) {
        StoreLocation location = getByUniqueKey(id);
        if (StrPool.State.DELETE == location.getEnabled().intValue()) {
            return null;
        }

        StoreLocation updatingStoreLocation = new StoreLocation();
        updatingStoreLocation.setId(id);
        updatingStoreLocation.setEnabled(state);
        baseMapper.updateById(updatingStoreLocation);
        return getByUniqueKey(id);
    }

    @Override
    @Cacheable(value = RedisKeys.Prefix.STORE_LOCATION, key = "#id", unless = "#result == null")
    public StoreLocation getByUniqueKey(String id) {
        return getById(id);
    }

    @Override
    public Page<StoreLocation> pages(StoreLocationCondition condition) {
        Page<StoreLocation> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);

        page.getRecords().forEach(item -> {
            item.setStoreName(organizationService.getByUniqueKey(item.getStoreId()).getOrgName());
            item.setStName(storeHouseService.getByUniqueKey(item.getStId()).getName());
        });

        return page;
    }

    @Override
    public List<StoreLocation> lists(StoreLocationCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    @CachePut(value = RedisKeys.Prefix.STORE_LOCATION, key = "#id", unless = "#result == null")
    public StoreLocation changeAssignState(String id, Integer assignState) {
        StoreLocation updatingStoreLocation = getByUniqueKey(id);
        if (updatingStoreLocation == null) {
            throw new BaseException("货位信息不存在！");
        }
        updatingStoreLocation.setAssignState(assignState);
        baseMapper.updateById(updatingStoreLocation);
        return updatingStoreLocation;
    }

    private void verifyStoreLocation(StoreLocation location, int expectCount) {
        long count = baseMapper.selectCount(Wrappers.<StoreLocation>lambdaQuery()
                .eq(CommonUtils.isNotEmpty(location.getId()), StoreLocation::getId, location.getId())
                .or(r -> r.eq(StoreLocation::getStoreId, location.getStoreId())
                        .eq(StoreLocation::getStId, location.getStId())
                        .eq(StoreLocation::getName, location.getName())));

        if (count > expectCount) {
            throw new BaseException("货位信息已存在！");
        }
    }
}
