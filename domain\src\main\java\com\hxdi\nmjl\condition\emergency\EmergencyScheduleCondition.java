package com.hxdi.nmjl.condition.emergency;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "应急调度表查询条件")
@Getter
@Setter
public class EmergencyScheduleCondition extends QueryCondition {

    @ApiModelProperty(value = "调度编号（模糊查询）")
    private String scheduleNo;

    @ApiModelProperty(value = "事件编号（模糊查询）")
    private String eventCode;

    @ApiModelProperty(value = "事件名称（模糊查询）")
    private String eventName;

    @ApiModelProperty(value = "调出企业类型")
    private Integer unitType;

    @ApiModelProperty(value = "调度状态：1-进行中，2-已完成")
    private Integer scheduleState;

}
