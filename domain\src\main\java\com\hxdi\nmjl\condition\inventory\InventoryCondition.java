package com.hxdi.nmjl.condition.inventory;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 库存表查询条件
 *
 * @author: 王贝强
 * @create: 2025-03-10 15:12
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ApiModel(description = "库存查询条件")
public class InventoryCondition extends QueryCondition {

    @ApiModelProperty(value = "军供站id  ','分隔")
    private String storeId;

    @ApiModelProperty(value = "仓房id  ','分隔")
    private String stId;

    @ApiModelProperty(value = "货位id")
    private String locId;

    @ApiModelProperty(value = "品种大类")
    private Integer bigType;

    @ApiModelProperty(value = "品类Id  ','分隔")
    private String classificationId;

    @ApiModelProperty(value = "品种id  ','分隔")
    private String catalogId;

    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    @ApiModelProperty(value = "储备性质")
    private String reserveLevel;

    @ApiModelProperty(value = "质量等级")
    private String grade;

    @ApiModelProperty(value = "地区编码 ','分隔")
    private String areaCode;

    @ApiModelProperty(value = "生产日期区间：开始")
    private Date productionDateStart;

    @ApiModelProperty(value = "生产日期区间：结束")
    private Date productionDateEnd;

    @ApiModelProperty(value = "管理机构id")
    private String manageUnitId;

    /**
     * 批次条件只在明细列表（带批次）查询中使用
     */
    @ApiModelProperty(value = "批次")
    private String batchNum;
}
