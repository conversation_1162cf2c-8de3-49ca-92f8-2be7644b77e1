package com.hxdi.nmjl.controller.inout;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.nmjl.domain.inout.DispatchInfo;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.service.inout.DispatchInfoService;
import com.hxdi.nmjl.condition.inout.DispatchCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
* 库存拆分记录控制层
*
* <AUTHOR>
*/
@RestController
@Api(tags = "库存拆分记录")
@RequestMapping("/dispatchInfo")
public class DispatchInfoController extends BaseController<DispatchInfoService,DispatchInfo> {

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("/get")
    @ApiOperation(value = "单条查询")
    public ResultBody<DispatchInfo> selectOne(@RequestParam("id") String id) {
    return ResultBody.ok().data(bizService.getById(id));
    }

    @GetMapping("/page")
    @ApiOperation(value = "分页查询")
    public ResultBody<Page<DispatchInfo>> page(DispatchCondition condition) {
        return ResultBody.ok().data(bizService.PageV1(condition));
    }

    @GetMapping("/list")
    @ApiOperation(value = "列表查询")
    public ResultBody<List<DispatchInfo>> list(DispatchCondition condition) {
        return ResultBody.ok().data(bizService.listV1(condition));
    }

    @PostMapping("/addBatch")
    @ApiOperation(value = "新增一条或多条拆分记录")
    public ResultBody addBatch(@RequestBody List<DispatchInfo> list) {
        bizService.create(list);
        return ResultBody.ok();
    }

    @GetMapping("/doSubmit")
    @ApiOperation(value = "提交拆分记录，开始执行库存拆分（注意：此操作会更新当前库存数量）")
    public ResultBody doSubmit(@RequestParam("idList") String idString) {
        List<String> idList = Arrays.asList(StringUtils.commaDelimitedListToStringArray(idString));
        bizService.doSubmit(idList);
        return ResultBody.ok();
    }

    @GetMapping("/remove")
    @ApiOperation(value = "删除一条或多条记录")
    public ResultBody remove(@RequestParam("idList") String idString) {
        List<String> idList = Arrays.asList(StringUtils.commaDelimitedListToStringArray(idString));
        bizService.removeByIdList(idList);
        return ResultBody.ok();
    }

}
