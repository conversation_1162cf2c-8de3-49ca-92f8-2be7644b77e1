package com.hxdi.nmjl.service.inout.duty;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.duty.DutyEmp;

import java.util.List;

public interface DutyEmpService extends IBaseService<DutyEmp> {


    /**
     * 根据计划ID删除值班人员记录
     * @param planId
     */
    void remove(String planId);

    /**
     * 根据计划ID获取值班人员记录
     * @param dutyId
     * @return
     */
    List<DutyEmp> getList(String dutyId);
}
