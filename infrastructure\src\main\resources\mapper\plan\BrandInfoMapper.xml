<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.plan.BrandInfoMapper">

    <sql id="Base_Column_List">
        ID, BRAND_NAME, ABBR_NAME, ORG_ID, ORG_NAME, BRAND_TYPE, FOUNDING_DATE, PRODUCT_CATEGORY, LOGO, BRAND_VALUES, OBJECTIVES, BRAND_DESC, SLOGAN, ENABLED, ATTACHMENTS, CREATE_TIME
    </sql>
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.BrandInfo">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
        <result column="ABBR_NAME" jdbcType="VARCHAR" property="abbrName" />
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
        <result column="BRAND_TYPE" jdbcType="INTEGER" property="brandType" />
        <result column="FOUNDING_DATE" jdbcType="DATE" property="foundingDate" />
        <result column="PRODUCT_CATEGORY" jdbcType="VARCHAR" property="productCategory" />
        <result column="LOGO" jdbcType="VARCHAR" property="logo" />
        <result column="BRAND_VALUES" jdbcType="VARCHAR" property="brandValues" />
        <result column="OBJECTIVES" jdbcType="VARCHAR" property="objectives" />
        <result column="BRAND_DESC" jdbcType="VARCHAR" property="brandDesc" />
        <result column="SLOGAN" jdbcType="VARCHAR" property="slogan" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="ATTACHMENTS" jdbcType="VARCHAR" property="attachments" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_BRAND_INFO
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.id)">
                and ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.brandName)">
                and BRAND_NAME like concat('%', #{condition.brandName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                and ORG_ID = #{condition.orgId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                and ORG_NAME like concat('%', #{condition.orgName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.brandType)">
                and BRAND_TYPE = #{condition.brandType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.productCategory)">
                and PRODUCT_CATEGORY = #{condition.productCategory}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeStart)">
                and CREATE_TIME >= #{condition.createTimeStart}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeEnd)">
                and CREATE_TIME &lt;= #{condition.createTimeEnd}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                and DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
    <select id="selectPageV1" resultType="com.hxdi.nmjl.domain.plan.BrandInfo">
        select <include refid="Base_Column_List"/>
        from B_BRAND_INFO
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.id)">
                and ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.brandName)">
                and BRAND_NAME like concat('%', #{condition.brandName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                and ORG_ID = #{condition.orgId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                and ORG_NAME like concat('%', #{condition.orgName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.brandType)">
                and BRAND_TYPE = #{condition.brandType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.productCategory)">
                and PRODUCT_CATEGORY = #{condition.productCategory}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeStart)">
                and CREATE_TIME >= #{condition.createTimeStart}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeEnd)">
                and CREATE_TIME &lt;= #{condition.createTimeEnd}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                and DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
</mapper>