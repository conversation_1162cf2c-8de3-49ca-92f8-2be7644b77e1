package com.hxdi.nmjl.domain.mobilization;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**

 动员协议明细
 */
@Getter
@Setter
@TableName (value = "B_MOBILIZED_CONTRACT_DETAIL")
public class MobilizedContractDetail implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;
    /**
     协议 id
     */
    @TableField (value = "CONTRACT_ID")
    private String contractId;
    /**
     品类 id
     */
    @TableField (value = "CLASSIFICATION_ID")
    private String classificationId;
    /**
     品类名称
     */
    @TableField (value = "CLASSIFICATION_NAME")
    private String classificationName;
    /**
     品种 id
     */
    @TableField (value = "CATALOG_ID")
    private String catalogId;
    /**
     品种名称
     */
    @TableField (value = "CATALOG_NAME")
    private String catalogName;
    /**
     质量等级：字典：YZLDJ/LZLDJ
     */
    @TableField (value = "GRADE")
    private String grade;
    /**
     规格
     */
    @TableField (value = "SPECIFICATION")
    private String specification;
    /**
     单价
     */
    @TableField (value = "PRICE")
    private BigDecimal price;
    /**
     产地
     */
    @TableField (value = "ORIGIN")
    private String origin;
    /**
     计量单位
     */
    @TableField (value = "UNIT")
    private String unit;
    /**
     数量
     */
    @TableField (value = "QTY")
    private BigDecimal qty;
    /**
     租户 id
     */
    @TableField (value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;
    /**
     组织
     */
    @TableField (value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
