package com.hxdi.nmjl.vo.inventory;

import com.hxdi.nmjl.domain.inventory.InventoryLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 货位卡详情VO
 * @author: 王贝强
 * @create: 2025-04-18 11:08
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LocationCardDetailResVO {
    /**
    * ————————————货位卡信息————————————————
     */

    @ApiModelProperty(value="主键")
    private String id;

    @ApiModelProperty(value="军供站ID")
    private String storeId;

    @ApiModelProperty(value="仓库")
    private String storeName;

    @ApiModelProperty(value="仓房ID")
    private String stId;

    @ApiModelProperty(value="仓房")
    private String stName;

    @ApiModelProperty(value="货位ID")
    private String locId;

    @ApiModelProperty(value="货位名称")
    private String locName;

    @ApiModelProperty(value="货位状态")
    private Integer locState;

    @ApiModelProperty(value="库存ID")
    private String inventoryId;

    @ApiModelProperty(value="生产批次")
    private String batchNum;

    @ApiModelProperty(value="状态:1-有效，0-删除")
    private Integer enabled;

    @ApiModelProperty(value="创建日期")
    private Date createTime;

    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
    * ————————————其他库存、品种相关信息————————————————
    */

    @ApiModelProperty(value="品种ID")
    private String catalogId;

    @ApiModelProperty(value="品种名称")
    private String catalogName;

    @ApiModelProperty(value="省")
    private String province;

    @ApiModelProperty(value="市")
    private String city;

    @ApiModelProperty(value="县区")
    private String county;

    @ApiModelProperty(value="质量等级")
    private String grade;

    @ApiModelProperty(value="储备性质")
    private Integer reserveLevel;

    @ApiModelProperty(value="生产日期")
    private Date productionDate;

    @ApiModelProperty(value="粮权单位id")
    private String manageUnitId;

    @ApiModelProperty(value="粮权单位名称")
    private String manageUnitName;

    @ApiModelProperty(value="库存数量")
    private BigDecimal inventoryQty;

    @ApiModelProperty(value="容量")
    private BigDecimal capacity;

    @ApiModelProperty(value="容量单位")
    private String unit;

    @ApiModelProperty(value="规格")
    private String specification;

    @ApiModelProperty(value="品牌")
    private String brand;

    @ApiModelProperty(value="商品名称")
    private String goodsName;

    @ApiModelProperty(value="存储时间/天")
    private Integer maxStorageTime;

    @ApiModelProperty(value="变更日志列表")
    private List<InventoryLog> inventoryLogs;


}
