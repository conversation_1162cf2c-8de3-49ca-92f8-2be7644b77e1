package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 沟通计划执行表
 */
@ApiModel(description = "沟通计划执行表")
@TableName("B_COMM_PLAN")
@Getter
@Setter
public class CommPlan extends BModel implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 关联沟通方案ID
     */
    @TableField("SCHEMA_ID")
    @ApiModelProperty(value = "关联沟通方案ID")
    private String schemaId;

    /**
     * 沟通计划名称
     */
    @TableField("NAME")
    @ApiModelProperty(value = "沟通计划名称")
    private String name;

    /**
     * 参与方
     */
    @TableField("MAIN_PARTICIPANTS")
    @ApiModelProperty(value = "参与方")
    private String mainParticipants;

    /**
     * 沟通计划内容
     */
    @TableField("CONTENT")
    @ApiModelProperty(value = "沟通计划内容")
    private String content;

    /**
     * 时间说明
     */
    @TableField("TIME_DESC")
    @ApiModelProperty(value = "时间说明")
    private String timeDesc;

    /**
     * 沟通方式
     */
    @TableField("COMM_MODE")
    @ApiModelProperty(value = "沟通方式")
    private String commMode;

    /**
     * 计划状态
     */
    @TableField("STATUS")
    @ApiModelProperty(value = "计划状态")
    private Integer status;

    /**
     * 启用状态(1-启用，0-禁用)
     */
    @TableField("ENABLED")
    @ApiModelProperty(value = "启用状态(1-启用，0-禁用)")
    private Integer enabled;

}
