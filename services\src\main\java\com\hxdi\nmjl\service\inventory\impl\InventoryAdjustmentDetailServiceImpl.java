package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inventory.InventoryAdjustmentDetail;
import com.hxdi.nmjl.mapper.inventory.InventoryAdjustmentDetailMapper;
import com.hxdi.nmjl.service.inventory.InventoryAdjustmentDetailService;
import com.hxdi.nmjl.service.inventory.InventoryBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 库存调整明细service实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 09:48
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryAdjustmentDetailServiceImpl extends BaseServiceImpl<InventoryAdjustmentDetailMapper, InventoryAdjustmentDetail> implements InventoryAdjustmentDetailService {


    @Resource
    private InventoryBaseService inventoryBaseService;

    @Override
    public void removeByMainId(String pid) {
        baseMapper.delete(Wrappers.<InventoryAdjustmentDetail>lambdaQuery().eq(InventoryAdjustmentDetail::getPid, pid));
    }

    @Override
    public List<InventoryAdjustmentDetail> getListByPid(String parentId) {
        List<InventoryAdjustmentDetail> detailList = baseMapper.selectList(Wrappers.<InventoryAdjustmentDetail>lambdaQuery().eq(InventoryAdjustmentDetail::getPid, parentId));
        detailList.forEach(item -> item.setInventoryBase(inventoryBaseService.get(item.getInventoryId())));
        return detailList;
    }
}
