package com.hxdi.nmjl.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Date;
import java.util.Map;

/**
 * @program: nmjl-service
 * @description: 数据变更事件
 * @author: 王贝强
 * @create: 2025-03-27 09:34
 */
@Getter
public class DataChangeEvent extends ApplicationEvent {

    private final String entityType;
    private final String entityId;
    private final String operation; // CREATE, UPDATE, DELETE
    private final Map<String, Object> data;
    private final Date version; // 数据版本，用于冲突解决和幂等性辅助

    public DataChangeEvent(Object source, String entityType, String entityId, String operation, Map<String, Object> data, Date version) {
        super(source); // source 通常是触发事件的服务或组件
        this.entityType = entityType;
        this.entityId = entityId;
        this.operation = operation;
        this.data = data;
        this.version = version;
    }
}
