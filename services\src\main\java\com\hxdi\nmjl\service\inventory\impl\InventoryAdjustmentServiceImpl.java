package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inventory.InventoryAdjustmentCondition;
import com.hxdi.nmjl.domain.inventory.InventoryAdjustment;
import com.hxdi.nmjl.domain.inventory.InventoryAdjustmentDetail;
import com.hxdi.nmjl.linker.InventoryLinker;
import com.hxdi.nmjl.mapper.inventory.InventoryAdjustmentMapper;
import com.hxdi.nmjl.service.inventory.InventoryAdjustmentDetailService;
import com.hxdi.nmjl.service.inventory.InventoryAdjustmentService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 库存调整服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryAdjustmentServiceImpl extends BaseServiceImpl<InventoryAdjustmentMapper, InventoryAdjustment> implements InventoryAdjustmentService {

    @Resource
    private InventoryAdjustmentDetailService inventoryAdjustmentDetailService;

    @Resource
    @Lazy
    private InventoryLinker inventoryLinker;

    @Override
    public void saveOrUpdateV1(InventoryAdjustment inventoryAdjustment) {
        if (CommonUtils.isEmpty(inventoryAdjustment.getId())) {
            // 新增
            verifyInventoryAdjustment(inventoryAdjustment);
            baseMapper.insert(inventoryAdjustment);

            List<InventoryAdjustmentDetail> inventoryAdjustmentDetailList = inventoryAdjustment.getDetailList();
            inventoryAdjustmentDetailList.forEach(item -> item.setPid(inventoryAdjustment.getId()));
            inventoryAdjustmentDetailService.saveBatch(inventoryAdjustmentDetailList);
        } else {
            // 更新
            InventoryAdjustment existing = baseMapper.selectById(inventoryAdjustment.getId());
            if (existing.getApproveStatus() == 1 || existing.getApproveStatus() == 2) {
                throw new BaseException("该单据正在审核中，无法更新");
            }

            baseMapper.updateById(inventoryAdjustment);
            inventoryAdjustmentDetailService.removeByMainId(inventoryAdjustment.getId());
            List<InventoryAdjustmentDetail> inventoryAdjustmentDetailList = inventoryAdjustment.getDetailList();
            inventoryAdjustmentDetailList.forEach(item -> item.setPid(inventoryAdjustment.getId()));
            inventoryAdjustmentDetailService.saveBatch(inventoryAdjustmentDetailList);
        }
    }

    @Override
    public InventoryAdjustment getDetail(String id) {
        InventoryAdjustment inventoryAdjustment = baseMapper.selectById(id);
        inventoryAdjustment.setDetailList(inventoryAdjustmentDetailService.getListByPid(id));
        return inventoryAdjustment;
    }

    @Override
    public void remove(String id) {
        InventoryAdjustment adjustment = super.getById(id);
        // 检查是否可以删除（只能删除未审核的）
        if (adjustment.getApproveStatus() == 1 || adjustment.getApproveStatus() == 2) {
            throw new BaseException("该单据正在审批中，无法删除");
        }
        InventoryAdjustment updatingAdjustment = new InventoryAdjustment();
        updatingAdjustment.setId(id);
        updatingAdjustment.setEnabled(0);
        this.updateById(updatingAdjustment);
    }

    @Override
    public void approve(String id, String approveOpinion) {
        changeApproveStatus(id, 2, approveOpinion);
        // 审核通过，进行库存数量调整
        updateInventory(id);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 3, approveOpinion);
    }

    @Override
    public void submit(String id) {
        // 审核状态：0-未审核，1-待审核，2-已审核，3-驳回
        changeApproveStatus(id, 1, null);
    }

    @Override
    public Page<InventoryAdjustment> pages(InventoryAdjustmentCondition condition) {
        Page<InventoryAdjustment> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<InventoryAdjustment> lists(InventoryAdjustmentCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    /**
     * 修改审批状态
     */
    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        InventoryAdjustment adjustment = new InventoryAdjustment();
        adjustment.setId(id);
        adjustment.setApproveStatus(approveStatus);
        if (approveStatus == 2 || approveStatus == 3) {
            adjustment.setApprover(SecurityHelper.obtainUser().getNickName());
            adjustment.setApproveTime(new Date());
            adjustment.setApproveOpinion(approveOpinion);
        }
        baseMapper.updateById(adjustment);
    }

    /**
     * 库存数量调整
     */
    private void updateInventory(String id) {
        InventoryAdjustment adjustment = getById(id);
        adjustment.setDetailList(inventoryAdjustmentDetailService.getListByPid(id));
        inventoryLinker.onChange(adjustment);
    }

    /**
     * 验证数据有效性
     */
    private void verifyInventoryAdjustment(InventoryAdjustment adjustment) {
        if (CommonUtils.isNotEmpty(adjustment.getBizId())) {
            long count = baseMapper.selectCount(Wrappers.<InventoryAdjustment>lambdaQuery()
                    .eq(InventoryAdjustment::getBizId, adjustment.getBizId())
                    .eq(InventoryAdjustment::getEnabled, StrPool.State.ENABLE));

            if (count > 0) {
                BizExp.pop(String.format("该业务单%s不能重复使用", adjustment.getBizId()));
            }
        }
    }
}
