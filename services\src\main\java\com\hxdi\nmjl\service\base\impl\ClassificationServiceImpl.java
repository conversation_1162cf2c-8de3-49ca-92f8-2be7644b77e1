package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.TreeUtils;
import com.hxdi.common.core.utils.support.LambdaUtil;
import com.hxdi.nmjl.condition.base.ClassificationCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.Classification;
import com.hxdi.nmjl.dto.base.ClassificationTree;
import com.hxdi.nmjl.mapper.base.ClassificationMapper;
import com.hxdi.nmjl.service.base.CatalogService;
import com.hxdi.nmjl.service.base.ClassificationService;
import com.hxdi.nmjl.utils.RedisKeys;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <品类管理实现>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/11 14:01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ClassificationServiceImpl extends BaseServiceImpl<ClassificationMapper, Classification> implements ClassificationService {

    public static final String ROOT_PARENT_ID = "0";

    @Resource
    @Lazy
    public CatalogService catalogService;

    @Override
    @CachePut(value = RedisKeys.Prefix.CLASSIFICATION, key = "#classification.id", unless = "#result == null")
    public Classification create(Classification classification) {
        verifyClassification(classification, 0);

//        long count2 = catalogService.count(Wrappers.<Catalog>lambdaQuery().eq(Catalog::getClassificationId, classification.getParentId()).eq(Catalog::getEnabled, 1));
//
//        if (count2 > 0) {
//            throw new BaseException("该分类下已存在品种信息，无法创建下级分类");
//        }

        if (CommonUtils.isEmpty(classification.getParentId())) {
            classification.setParentId(ROOT_PARENT_ID);
        }
        int level = 0;
        String classificationPath = "";
        if (CommonUtils.notEquals(classification.getParentId(), ROOT_PARENT_ID)) {
            Classification parent = getByUniqueKey(classification.getParentId());
            parent.setLeafIs(0);
            baseMapper.updateById(parent);

            level = parent.getLevel() + 1;
            classificationPath = parent.getClassificationPath();
        }

        classification.setLeafIs(1);
        classification.setLevel(level);
        classification.setClassificationPath(CommonUtils.isEmpty(classificationPath) ? classification.getClassificationCode() : classificationPath + "." + classification.getClassificationCode());
        Integer seq = baseMapper.selectMaxSeqNumber(classification.getParentId());
        classification.setSeq(seq == null ? 0 : seq + 1);
        baseMapper.insert(classification);

        return getByUniqueKey(classification.getId());
    }

    @Override
    @CachePut(value = RedisKeys.Prefix.CLASSIFICATION, key = "#classification.id", unless = "#result == null")
    public Classification update(Classification classification) {
        verifyClassification(classification, 1);

        if (isSubclassExist(classification.getId())) {
            classification.setLeafIs(0);
        } else {
            classification.setLeafIs(1);
        }

        baseMapper.updateById(classification);

        return getByUniqueKey(classification.getId());
    }

    @Override
    @CacheEvict(value = RedisKeys.Prefix.CLASSIFICATION, key = "#id")
    public void remove(String id) {
        if (isSubclassExist(id)) {
            throw new BaseException("存在下级分类，无法删除");
        }

        long count = catalogService.count(Wrappers.<Catalog>lambdaQuery().eq(Catalog::getClassificationId, id).eq(Catalog::getEnabled, 1));
        if (count > 0) {
            throw new BaseException("商品目录存在关联当前品类，无法删除");
        }

        Classification classification = baseMapper.selectById(id);
        baseMapper.softDeleteById(id);

        Classification parent = baseMapper.selectById(classification.getParentId());
        if (parent != null) {
            update(parent);
        }
    }

    @Override
    @Cacheable(value = RedisKeys.Prefix.CLASSIFICATION, key = "#uniqueKey", unless = "#result == null")
    public Classification getByUniqueKey(String uniqueKey) {
        Classification classification = baseMapper.selectById(uniqueKey);
        if (classification == null) {
            classification = baseMapper.selectOne(Wrappers.<Classification>lambdaQuery().eq(Classification::getClassificationCode, uniqueKey));
        }

        return classification;
    }

    @Override
    public List<ClassificationTree> classTree() {
        List<Classification> classifications = baseMapper.selectList(Wrappers.<Classification>lambdaQuery().eq(Classification::getEnabled, 1));

        LinkedList<ClassificationTree> sourceTree = convertTree(classifications);
        return TreeUtils.getTreeAfterOrder(ROOT_PARENT_ID, sourceTree, ClassificationTree::getSeq);
    }

    @Override
    public List<ClassificationTree> catalogTree() {
        List<Classification> classifications = baseMapper.selectList(Wrappers.<Classification>lambdaQuery().eq(Classification::getEnabled, 1));
        List<String> leafIds = classifications.stream().filter(classification -> {
            if (classification.getLeafIs() == 1) {
                classification.setLeafIs(0);
                return true;
            }
            return false;
        }).map(Classification::getId).collect(Collectors.toList());

        List<Catalog> catalogs = catalogService.getListByClassIds(leafIds);
        catalogs.forEach(catalog -> {
            Classification catalogNode = new Classification();
            catalogNode.setId(catalog.getId());
            catalogNode.setClassificationName(catalog.getFullName());
            catalogNode.setParentId(catalog.getClassificationId());
            catalogNode.setLeafIs(1);
            catalogNode.setSeq(1);
            classifications.add(catalogNode);
        });

        LinkedList<ClassificationTree> sourceTree = convertTree(classifications);
        return TreeUtils.getTreeAfterOrder(ROOT_PARENT_ID, sourceTree, ClassificationTree::getSeq);
    }

    @Override
    public Page<Classification> pages(ClassificationCondition condition) {
        Page<Classification> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);

        page.getRecords().forEach(item -> item.setParentClassificationName(LambdaUtil.get(this.getByUniqueKey(item.getParentId()), Classification::getClassificationName)));

        return page;
    }

    @Override
    public List<Classification> lists(ClassificationCondition condition) {
        return baseMapper.selectListV1(condition);
    }


    /**
     * 是否存在子类
     *
     * @param classificationId
     * @return
     */
    public boolean isSubclassExist(String classificationId) {
        long count = baseMapper.selectCount(Wrappers.<Classification>lambdaQuery().eq(Classification::getParentId, classificationId).eq(Classification::getEnabled, 1));
        return count > 0;
    }

    /**
     * 验证数据有效性
     * 名称可以重复
     *
     * @param classification
     * @param expectCount    预期存在的条目
     */
    public void verifyClassification(Classification classification, int expectCount) {
        long count = baseMapper.selectCount(Wrappers.<Classification>lambdaQuery()
                .eq(CommonUtils.isNotEmpty(classification.getId()), Classification::getId, classification.getId())
                .or()
                .eq(Classification::getClassificationCode, classification.getClassificationCode()));

        if (count > expectCount) {
            throw new BaseException("已存在相似的分类，请检查录入数据");
        }
    }

    private LinkedList<ClassificationTree> convertTree(List<Classification> source) {
        return source.stream().map(e -> {
            ClassificationTree node = new ClassificationTree();
            node.setId(e.getId());
            node.setParentId(e.getParentId());
            node.setClassificationName(e.getClassificationName());
            node.setLevel(e.getLevel());
            node.setSeq(e.getSeq());
            node.setLeafIs(e.getLeafIs());
            node.setPreImg(e.getPreImg());
            node.setDataHierarchyId(e.getDataHierarchyId());
            return node;
        }).collect(Collectors.toCollection(LinkedList::new));
    }
}
