package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.OperatingPlanCondition;
import com.hxdi.nmjl.domain.plan.OperatingPlan;
import com.hxdi.nmjl.service.plan.OperatingPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <整体经营计划管理接口>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "整体经营计划管理")
@RestController
@RequestMapping("/operatingPlan")
public class OperatingPlanController extends BaseController<OperatingPlanService, OperatingPlan> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<OperatingPlan>> getPages(OperatingPlanCondition condition) {
        return ResultBody.ok().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<OperatingPlan>> getList(OperatingPlanCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<OperatingPlan> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除整体经营计划信息")
    @PostMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.ok().data(bizService.delete(id));
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody OperatingPlan operatingPlan) {
        if (CommonUtils.isEmpty(operatingPlan.getId())) {
            bizService.add(operatingPlan);
        } else {
            bizService.update(operatingPlan);
        }
        return ResultBody.ok();
    }

    @ApiOperation("拆分")
    @PostMapping("/split")
    public ResultBody split(@RequestBody OperatingPlan operatingPlan) {
        bizService.split(operatingPlan);
        return ResultBody.ok();
    }

    @ApiOperation(value = "审核")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @GetMapping("/reject")
    public ResultBody<Void> reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "提交")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "已完结")
    @GetMapping("/finished")
    public ResultBody<Void> finished(@RequestParam String id) {
        bizService.finished(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "下达")
    @GetMapping("/issue")
    public ResultBody<Void> issue(@RequestParam String id) {
        bizService.issue(id);
        return ResultBody.ok();
    }


}
