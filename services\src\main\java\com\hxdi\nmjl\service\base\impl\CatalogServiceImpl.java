package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.base.CatalogCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.Classification;
import com.hxdi.nmjl.linker.rule.BusinessRules;
import com.hxdi.nmjl.mapper.base.CatalogMapper;
import com.hxdi.nmjl.service.base.CatalogService;
import com.hxdi.nmjl.service.base.ClassificationService;
import com.hxdi.nmjl.utils.RedisKeys;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <品种目录管理实现>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/11 14:01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CatalogServiceImpl extends BaseServiceImpl<CatalogMapper, Catalog> implements CatalogService {


    @Resource
    private ClassificationService classificationService;

    @Resource
    @Lazy
    private BusinessRules businessRules;

    @Override
    @CachePut(value = RedisKeys.Prefix.CATALOG, key = "#catalog.id", unless = "#result == null")
    public Catalog create(Catalog catalog) {
        verifyCatalog(catalog, 0);
        Classification classification = classificationService.getByUniqueKey(catalog.getClassificationId());
        if (classification == null) {
            throw new BaseException("未找到该品类，请检查录入数据");
        }
        catalog.setClassificationName(classification.getClassificationName());
        catalog.setClassificationPath(classification.getClassificationPath());
        baseMapper.insert(catalog);
        return getByUniqueKey(catalog.getId());
    }

    @Override
    @CachePut(value = RedisKeys.Prefix.CATALOG, key = "#catalog.id", unless = "#result == null")
    public Catalog update(Catalog catalog) {
        verifyCatalog(catalog, 1);
        baseMapper.updateById(catalog);
        return getByUniqueKey(catalog.getId());
    }

    @Override
    @Cacheable(value = RedisKeys.Prefix.CATALOG, key = "#uniqueKey", unless = "#result == null")
    public Catalog getByUniqueKey(String uniqueKey) {
        Catalog catalog = baseMapper.selectById(uniqueKey);
        if (catalog == null) {
            catalog = baseMapper.selectOne(Wrappers.<Catalog>lambdaQuery().eq(Catalog::getCatalogCode, uniqueKey));
        }

        return catalog;
    }

    @Override
    public Page<Catalog> pages(CatalogCondition condition) {
        Page<Catalog> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<Catalog> lists(CatalogCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    @CacheEvict(value = RedisKeys.Prefix.CATALOG, key = "#id")
    public void remove(String id) {
        Catalog catalog = getByUniqueKey(id);
        if (businessRules.removeVerify(catalog)) {
            baseMapper.softDeleteById(id);
        } else {
            BizExp.pop("该品种已关联库存，无法删除");
        }
    }

    @Override
    public List<Catalog> getListByClassIds(List<String> leafIds) {
        return baseMapper.selectListByClassIds(leafIds);
    }

    /**
     * 验证数据有效性
     * 名称可以重复
     *
     * @param catalog
     * @param expectCount 预期存在的条目
     */
    private void verifyCatalog(Catalog catalog, int expectCount) {
        long count = baseMapper.selectCountV1(catalog);

        if (count > expectCount) {
            throw new BaseException("已存在相同的商品，请不要重复添加！");
        }
    }
}
