package com.hxdi.nmjl.controller.common;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.properties.CommonProperties;
import com.hxdi.common.core.utils.date.DateFormatter;
import com.hxdi.nmjl.dto.common.RemoteSupervisionLogin;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@Api(tags = "系统扩展接口")
@RequestMapping("/ext")
public class ExtendController {

    @Autowired
    private CommonProperties commonProperties;

    @ApiOperation(value = "安全监管：单点登录参数", response = RemoteSupervisionLogin.class)
    @GetMapping(value = "/remote/sso")
    public ResultBody<String> autologin() {
        String userName = "12345678121";
        RSA rsa = new RSA(null, commonProperties.getSsoPublicKey());
        String userNameEnc = Base64.encode(rsa.encrypt(userName, KeyType.PublicKey));
        String dateEnc = Base64.encode(rsa.encrypt(DateFormatter.format(new Date()),KeyType.PublicKey));

        RemoteSupervisionLogin sso = new RemoteSupervisionLogin();
        sso.setLoginUrl(commonProperties.getLoginUrl());
        sso.setSsoUrl(commonProperties.getSsoUrl());
        sso.setSsoUserName(userNameEnc);
        sso.setSsoParam(dateEnc);
        return ResultBody.<String>OK().data(sso.toUrl());
    }
}
