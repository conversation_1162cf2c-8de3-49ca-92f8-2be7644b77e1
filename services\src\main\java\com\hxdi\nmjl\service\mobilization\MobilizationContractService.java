package com.hxdi.nmjl.service.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.mobilization.MobilizedContract;
import com.hxdi.nmjl.condition.mobilization.MobilizedContractCondition;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MobilizationContractService extends IBaseService<MobilizedContract> {

    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    Page<MobilizedContract> pages(MobilizedContractCondition condition);

    /**
     * 列表查询
     *
     * @param condition
     * @return
     */
    List<MobilizedContract> lists(MobilizedContractCondition condition);

    /**
     * 保存
     *
     * @param contract
     */
    void create(MobilizedContract contract);

    /**
     * 修改
     *
     * @param contract
     */
    void update(MobilizedContract contract);

    /**
     * 详情查询
     *
     * @param id
     * @return
     */
    MobilizedContract getDetail(String id);

    /**
     * 删除
     *
     * @param id
     */
    void remove(String id);

    /**
     * 提交
     *
     * @param contractId
     */
    void submit(String contractId);

    /**
     * 审批
     *
     * @param contractId
     * @param approveStatus
     * @param approveOpinion
     */
    void approve(String contractId, Integer approveStatus, String approveOpinion);

    /**
     * 结束
     *
     * @param contractId
     */
    void finish(String contractId);

    /**
     * 终止
     *
     * @param contractId
     */
    void terminate(String contractId);
}
