package com.hxdi.nmjl.event;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

/**
 * @program: nmjl-service
 * @description: 消息载体
 * @author: 王贝强
 * @create: 2025-07-09 09:07
 */
@Getter
@Setter
public class MsgPayload implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("模版标识码")
    private String msgCode;
    @ApiModelProperty("接收人ID ','分割 ")
    private String receiver;
    @ApiModelProperty("消息关联数据ID")
    private String refId;
    @ApiModelProperty("自定义模版内容参数(JSON格式)")
    private Map<String, String> jsonParams;
}
