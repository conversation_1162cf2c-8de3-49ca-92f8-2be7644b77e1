package com.hxdi.nmjl.vo.bigscreen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: nmjl-service
 * @description: 筹措计划统计VO
 * @author: 王贝强
 * @create: 2025-07-31 14:07
 */
@Setter
@Getter
@ApiModel(description = "筹措计划统计VO")
public class ProcurementPlanSummaryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "地区编码")
    private String areaCode;

    @ApiModelProperty(value = "地区名称")
    private String areaName;

    @ApiModelProperty(value = "军供站Id")
    private String storeId;

    @ApiModelProperty(value = "军供站名称")
    private String storeName;

    @ApiModelProperty(value = "品类Id")
    private String classificationId;

    @ApiModelProperty(value = "品类名称")
    private String classificationName;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQty;

    @ApiModelProperty(value = "完成数量")
    private BigDecimal completedQty;

    @ApiModelProperty(value = "完成率")
    private float completedRate;
}
