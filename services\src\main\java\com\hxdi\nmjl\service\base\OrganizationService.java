package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.DataSyncMessage;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.base.OrganizationCondition;
import com.hxdi.nmjl.domain.base.Organization;

import java.util.List;
import java.util.Map;

public interface OrganizationService extends IBaseService<Organization> {

    /**
     * 查询详情
     *
     * @param uniqueKey
     * @return
     */
    Organization getByUniqueKey(String uniqueKey);

    /**
     * 分页
     *
     * @param condition
     * @return
     */
    Page<Organization> pages(OrganizationCondition condition);

    /**
     * 列表查询
     *
     * @param condition
     * @return
     */
    List<Organization> lists(OrganizationCondition condition);

    /**
     * 根据区域编码查询机构数量
     *
     * @param condition
     * @return
     */
    Map<Integer, Integer> getOrgNum(OrganizationCondition condition);

    /**
     * 新增
     *
     * @param org
     */
    Organization create(Organization org);

    /**
     * 修改
     *
     * @param org
     */
    Organization update(Organization org);

    /**
     * 修改状态：启用，禁用，删除
     *
     * @param orgId
     * @param state
     */
    Organization changeState(String orgId, Integer state);

    /**
     * 消息处理接口
     *
     * @param message
     */
    Organization handleMessage(DataSyncMessage message);

    /**
     * 获取组织树
     *
     * @param orgId 组织ID，如果为null则构建全体组织树，否则构建该节点及其下级节点的组织树
     * @return 组织树列表
     */
    List<Organization> tree(String orgId);
}
