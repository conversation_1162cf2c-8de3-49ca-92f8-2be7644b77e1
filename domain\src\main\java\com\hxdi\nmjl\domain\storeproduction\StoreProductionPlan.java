package com.hxdi.nmjl.domain.storeproduction;


import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 生产计划主表
 */
@Getter
@Setter
@TableName(value = "B_STORE_PRODUCTION_PLAN")
public class StoreProductionPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 计划编号
     */
    @TableField(value = "PLAN_NO")
    private String planNo;

    /**
     * 计划名称
     */
    @TableField(value = "PLAN_NAME")
    private String planName;

    /**
     * 计划类型：字典 SCJHLX（1 - 日常计划，2 - 应急计划，3 - 专项计划）
     */
    @TableField(value = "PLAN_TYPE")
    private Integer planType;

    /**
     * 计划开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "START_DATE")
    private Date startDate;

    /**
     * 计划结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "END_DATE")
    private Date endDate;

    /**
     * 库点id
     */
    @TableField(value = "STORE_ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    private String storeName;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @TableField(value = "APPROVE_STATUS", fill = FieldFill.INSERT_UPDATE)
    private Integer approveStatus;

    /**
     * 审核人
     */
    @TableField(value = "APPROVER")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    private String approveOpinion;

    /**
     * 计划备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 状态（1-有效 0-删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;




    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 计划详情列表
     */
    @TableField(exist = false)
    private List<StoreProductionPlanDetail> detailList;
}

