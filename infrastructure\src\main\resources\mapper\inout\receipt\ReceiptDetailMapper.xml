<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.receipt.ReceiptDetailMapper">

    <resultMap id="ReceiptDetailResultMap" type="com.hxdi.nmjl.domain.inout.opt.ReceiptDetail">
        <result column="ID" property="id" />
        <result column="RECEIPT_ID" property="receiptId" />
        <result column="INVENTORY_ID" property="inventoryId" />
        <result column="RECEIPT_QTY" property="receiptQty" />
        <result column="REMARKS" property="remarks" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
        <result column="BRAND" property="brand" />
        <result column="CATALOG_ID" property="catalogId" />
        <result column="UNIT" property="unit" />
        <result column="CATALOG_NAME" property="catalogName" />
        <result column="SPECIFICATIONS" property="specifications" />
        <result column="GRADE" property="grade" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,RECEIPT_ID,INVENTORY_ID,RECEIPT_QTY,REMARKS,TENANT_ID,
          DATA_HIERARCHY_ID,BRAND,CATALOG_ID,UNIT,
          CATALOG_NAME,SPECIFICATIONS,GRADE
    </sql>


</mapper>
