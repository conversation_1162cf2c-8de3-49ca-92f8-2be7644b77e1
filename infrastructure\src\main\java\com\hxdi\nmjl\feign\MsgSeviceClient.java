package com.hxdi.nmjl.feign;

import com.hxdi.msg.client.service.IMsgServiceClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * @program: nmjl-service
 * @description: 消息服务远程调用接口
 * @author: 王贝强
 * @create: 2025-07-08 19:42
 */
@Component
@FeignClient(value = "msg-service")
public interface MsgSeviceClient extends IMsgServiceClient {
}
