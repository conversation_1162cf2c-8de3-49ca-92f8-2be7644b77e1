package com.hxdi.nmjl.service.store;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.store.StoreApply;
import com.hxdi.nmjl.condition.store.StoreApplyCondition;

import java.util.List;

/**
 * 申请服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/8
 */
public interface StoreApplyService extends IBaseService<StoreApply> {

    /**
     * 新增申请
     * @param storeApply 申请实体
     */
    void create(StoreApply storeApply);

    /**
     * 修改申请
     * @param storeApply 申请实体
     */
    void update(StoreApply storeApply);

    /**
     * 获取申请详情
     * @param id 申请ID
     * @return 申请实体
     */
    StoreApply getDetail(String id);

    /**
     * 分页查询申请
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<StoreApply> pages(StoreApplyCondition condition);

    /**
     * 列表查询申请
     * @param condition 查询条件
     * @return 申请列表
     */
    List<StoreApply> lists(StoreApplyCondition condition);

    /**
     * 审批申请（状态变更）
     * @param id 申请ID
     * @param approveStatus 审批状态
     * @param opinion 审批意见
     */
    void approve(String id, Integer approveStatus, String opinion);

    /**
     * 删除申请
     * @param id 申请ID
     */
    void remove(String id);

    /**
     * 提交申请
     * @param id 申请ID
     */
    void submit(String id);
}
