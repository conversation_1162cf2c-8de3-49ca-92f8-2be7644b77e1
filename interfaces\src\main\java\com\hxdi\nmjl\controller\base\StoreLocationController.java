package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.StoreLocationCondition;
import com.hxdi.nmjl.domain.base.StoreLocation;
import com.hxdi.nmjl.service.base.StoreLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "货位管理")
@RestController
@RequestMapping("/location")
public class StoreLocationController extends BaseController<StoreLocationService, StoreLocation> {


    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody StoreLocation location) {
        if (CommonUtils.isEmpty(location.getId())) {
            create(location);
        } else {
            update(location);
        }
        return ResultBody.OK();
    }

    @ApiOperation("新增")
    @PostMapping("/add")
    public ResultBody<Void> create(@RequestBody StoreLocation location) {
        bizService.create(location);
        return ResultBody.OK();
    }

    @ApiOperation("更新")
    @PutMapping("/update")
    public ResultBody<Void> update(@RequestBody StoreLocation location) {
        bizService.update(location);
        return ResultBody.OK();
    }

    @ApiOperation("删除")
    @PostMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        this.changeState(id, 7);
        return ResultBody.OK();
    }

    @ApiOperation("启用/禁用")
    @PostMapping("/change/state")
    public ResultBody<Void> changeState(@RequestParam String id, @RequestParam Integer state) {
        bizService.changeState(id, state);
        return ResultBody.OK();
    }

    @ApiOperation("未启用/启用中(货位状态)")
    @PostMapping("/change/assignState")
    public ResultBody<Void> changeAssignState(@RequestParam String id, @RequestParam Integer assignState) {
        bizService.changeAssignState(id, assignState);
        return ResultBody.OK();
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<StoreLocation> getStorehouse(String id) {
        return ResultBody.<StoreLocation>OK().data(bizService.getByUniqueKey(id));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<StoreLocation>> pages(StoreLocationCondition condition) {
        return ResultBody.<Page<StoreLocation>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<StoreLocation>> lists(StoreLocationCondition condition) {
        return ResultBody.<List<StoreLocation>>OK().data(bizService.lists(condition));
    }
}
