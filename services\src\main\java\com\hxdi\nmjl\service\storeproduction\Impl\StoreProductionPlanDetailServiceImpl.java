package com.hxdi.nmjl.service.storeproduction.Impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlan;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlanDetail;
import com.hxdi.nmjl.mapper.storeproduction.StoreProductionPlanDetailMapper;
import com.hxdi.nmjl.service.storeproduction.StoreProductionPlanDetailService;
import com.hxdi.nmjl.service.storeproduction.StoreProductionPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class StoreProductionPlanDetailServiceImpl extends BaseServiceImpl<StoreProductionPlanDetailMapper, StoreProductionPlanDetail> implements StoreProductionPlanDetailService {


    @Resource
    private StoreProductionPlanService storeProductionPlanService;

    @Override
    public void update(StoreProductionPlanDetail detail) {
        StoreProductionPlan savedDetail = storeProductionPlanService.getById(detail.getPlanId());
        if (savedDetail.getApproveStatus() == 1) {
            throw new BaseException("计划已提交，不能修改");
        }
        this.updateById(detail);
    }


    @Override
    public List<StoreProductionPlanDetail> getList(String planId) {
        return baseMapper.selectList(Wrappers.<StoreProductionPlanDetail>lambdaQuery().eq(StoreProductionPlanDetail::getPlanId, planId));
    }

    @Override
    public void remove(String planId) {
        baseMapper.delete(Wrappers.<StoreProductionPlanDetail>lambdaQuery().eq(StoreProductionPlanDetail::getPlanId, planId));
    }

}
