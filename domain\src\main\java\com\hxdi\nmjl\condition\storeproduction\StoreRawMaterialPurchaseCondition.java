package com.hxdi.nmjl.condition.storeproduction;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "原料采购查询条件")
@Getter
@Setter
public class StoreRawMaterialPurchaseCondition extends QueryCondition {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "计划编号（模糊查询）")
    private String planNo;

    @ApiModelProperty(value = "原料名称（模糊查询）")
    private String materialName;

    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @ApiModelProperty(value = "库点名称（模糊查询）")
    private String storeName;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}

