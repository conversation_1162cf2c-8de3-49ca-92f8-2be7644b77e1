package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inventory.InventoryCheck;
import com.hxdi.nmjl.service.inventory.InventoryCheckService;
import com.hxdi.nmjl.condition.inventory.InventoryCheckCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 盘点记录管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Api(tags = "盘点记录管理")
@RestController
@RequestMapping("/inventoryCheck")
public class InventoryCheckController extends BaseController<InventoryCheckService, InventoryCheck> {


    @ApiOperation(value = "生成盘点记录")
    @PostMapping("/timedGenerate")
    public ResultBody timedGenerate() {
        bizService.timedGenerate();
        return ResultBody.ok();
    }

    @ApiOperation(value = "立即生成盘点记录")
    @PostMapping("/generateNow")
    public ResultBody generateNow(@RequestParam String checkConfigId) {
        bizService.generateNow(checkConfigId);
        return ResultBody.ok();
    }

    @ApiOperation(value = "更新")
    @PostMapping("/update")
    public ResultBody update(@RequestBody InventoryCheck inventoryCheck) {
        bizService.update(inventoryCheck);
        return ResultBody.ok();
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<InventoryCheck> getDetail(@RequestParam String id) {
        InventoryCheck inventoryCheck = bizService.getDetail(id);
        return ResultBody.ok().data(inventoryCheck);
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResultBody delete(@RequestParam String id) {
        bizService.delete(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<InventoryCheck>> page(InventoryCheckCondition condition) {
        Page<InventoryCheck> page = bizService.selectPage(condition);
        return ResultBody.ok().data(page);
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<InventoryCheck>> list(InventoryCheckCondition condition) {
        List<InventoryCheck> list = bizService.selectList(condition);
        return ResultBody.ok().data(list);
    }

    @ApiOperation(value = "提交")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }
    @ApiOperation(value = "审核")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @PostMapping("/reject")
    public ResultBody reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "查询已审核的盘点记录分页列表")
    @GetMapping("/approved/page")
    public ResultBody<Page<InventoryCheck>> getApprovedPageList(InventoryCheckCondition condition) {
        return ResultBody.ok().data(bizService.getApprovedPage(condition));
    }
}
