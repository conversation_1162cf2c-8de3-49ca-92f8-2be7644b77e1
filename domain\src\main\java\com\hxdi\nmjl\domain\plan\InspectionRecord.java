package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 监督检查记录
 */
@ApiModel(description = "监督检查记录")
@Getter
@Setter
@TableName("B_INSPECTION_RECORD")
public class InspectionRecord extends BModel {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @TableField("INSPECT_CODE")
    @ApiModelProperty(value = "记录编号")
    private String inspectCode;

    @TableField("PLAN_CODE")
    @ApiModelProperty(value = "计划编号")
    private String planCode;

    @TableField("PLAN_NAME")
    @ApiModelProperty(value = "计划名称")
    private String planName;

    @TableField("INSPECT_UNIT_ID")
    @ApiModelProperty(value = "被检查单位ID")
    private String inspectUnitId;

    @TableField("INSPECT_UNIT_NAME")
    @ApiModelProperty(value = "被检查单位")
    private String inspectUnitName;

    @TableField("INSPECT_TYPE")
    @ApiModelProperty(value = "检查类型")
    private String inspectType;

    @TableField("INSPECT_TIME")
    @ApiModelProperty(value = "检查日期")
    private Date inspectTime;

    @TableField("PURPOSE")
    @ApiModelProperty(value = "检查目的")
    private String purpose;

    @TableField("INSPECTORS")
    @ApiModelProperty(value = "检查人员")
    private String inspectors;

    @TableField("RESULT_STATE")
    @ApiModelProperty(value = "检查结果：0-正常，1-有问题，2-需整改")
    private Integer resultState;

    @TableField("DEAL_STATE")
    @ApiModelProperty(value = "整改状态：0-无需整改，1-整改中，2-已整改")
    private Integer dealState;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    @TableField("ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;



    /*****************************以下非实体字段*****************************/


    @TableField(exist = false)
    private List<InspectionItem> detailList;
}
