package com.hxdi.nmjl.mapper.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.mobilization.MobilizedProduct;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;

@Mapper
public interface MobilizedProductMapper extends SuperMapper<MobilizedProduct> {

    /**
     * 分页查询
     * @param page
     * @param condition
     * @return
     */
    Page<MobilizedProduct> selectPageV1(Page<MobilizedProduct> page, @Param("condition")MobilizedProductCondition condition);

    /**
     * 列表
     * @param condition
     * @return
     */
    List<MobilizedProduct> selectListV1(@Param("condition")MobilizedProductCondition condition);
}
