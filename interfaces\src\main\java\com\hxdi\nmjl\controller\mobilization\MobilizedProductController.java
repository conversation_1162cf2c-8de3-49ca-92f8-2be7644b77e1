package com.hxdi.nmjl.controller.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.mobilization.MobilizedProduct;
import com.hxdi.nmjl.service.mobilization.MobilizedProductService;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**

 动员商品管理
 <AUTHOR>
 @version 1.0
 @since 2025/7/2
 */
@Api (tags = "动员商品管理")
@RestController
@RequestMapping ("/goods")
public class MobilizedProductController extends BaseController<MobilizedProductService, MobilizedProduct> {
    @ApiOperation ("分页查询动员商品")
    @GetMapping ("/page")
    public ResultBody<Page<MobilizedProduct>> page(MobilizedProductCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }
    @ApiOperation ("列表查询动员商品")
    @GetMapping ("/list")
    public ResultBody<List<MobilizedProduct>> list(MobilizedProductCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }
    @ApiOperation ("保存/修改动员商品")
    @PostMapping ("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody MobilizedProduct mobilizedProduct) {
        if(CommonUtils.isEmpty(mobilizedProduct.getId())) {
            bizService.create(mobilizedProduct);
        } else {
            bizService.update(mobilizedProduct);
        }
        return ResultBody.ok();
    }

    @ApiOperation ("删除动员商品")
    @DeleteMapping ("/remove")
    public ResultBody remove (@RequestParam String goodsId) {
        bizService.remove(goodsId);
        return ResultBody.ok ();
    }
    @ApiOperation ("查看动员商品详情")
    @GetMapping ("/detail")
    public ResultBody<MobilizedProduct> detail(@RequestParam String goodsId) {
        return ResultBody.ok().data(bizService.getDetail(goodsId));
    }

    @ApiOperation ("确认")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id) {
        bizService.approve(id);
        return ResultBody.ok();
    }
}
