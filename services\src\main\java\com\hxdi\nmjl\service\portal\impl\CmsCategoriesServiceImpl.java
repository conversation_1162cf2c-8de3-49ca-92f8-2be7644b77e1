package com.hxdi.nmjl.service.portal.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.portal.CategoriesCondition;
import com.hxdi.nmjl.domain.portal.CmsCategories;
import com.hxdi.nmjl.mapper.portal.CmsCategoriesMapper;
import com.hxdi.nmjl.service.portal.CmsCategoriesService;
import com.hxdi.nmjl.service.portal.CmsResourcesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 门户栏目服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/18 16:53
 */
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class CmsCategoriesServiceImpl extends BaseServiceImpl<CmsCategoriesMapper, CmsCategories> implements CmsCategoriesService {

    @Resource
    @Lazy
    private CmsResourcesService cmsResourcesService;


    @Override
    public void create(CmsCategories categories) {
        categories.setId(IdWorker.getIdStr());
        if (categories.getPid() == null || "0".equals(categories.getPid())) {
            categories.setPid("0");
            categories.setCategoryPath(categories.getId());
            categories.setCategoryPathName(categories.getName());
        } else {
            CmsCategories parent = baseMapper.selectById(categories.getPid());
            categories.setModule(parent.getModule());
            categories.setModuleName(parent.getModuleName());
            categories.setCategoryPath(parent.getCategoryPath() + "." + categories.getId());
            categories.setCategoryPathName(parent.getCategoryPathName() + ">>" + categories.getName());
        }

        verifyCategories(categories, 0);
        baseMapper.insert(categories);
    }

    @Override
    public void update(CmsCategories categories) {
        verifyCategories(categories, 1);
        baseMapper.updateById(categories);
    }

    @Override
    public void remove(String id) {
        CmsCategories categories = baseMapper.selectById(id);
        categories.setEnabled(0);
        baseMapper.updateById(categories);

        // 标记删除该栏目下的所有关联资源
        cmsResourcesService.removeByCategoryId(id);
    }

    @Override
    public Page<CmsCategories> pages(CategoriesCondition condition) {
        Page<CmsCategories> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<CmsCategories> lists(CategoriesCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public CmsCategories getDetail(String categoryId) {
        return baseMapper.selectById(categoryId);
    }

    @Override
    public CmsCategories getBaseDetail(String categoryId) {
        return baseMapper.selectOne(Wrappers.<CmsCategories>lambdaQuery()
                .select(CmsCategories::getId, CmsCategories::getPid, CmsCategories::getName, CmsCategories::getModule, CmsCategories::getModuleName, CmsCategories::getShowHeadIs,
                        CmsCategories::getCategoryPath, CmsCategories::getCategoryPathName, CmsCategories::getLeafIs, CmsCategories::getSorts, CmsCategories::getUri)
                .eq(CmsCategories::getId, categoryId));
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<CmsCategories> tree(Integer module) {
        // 查询所有栏目数据
        List<CmsCategories> allCategories = baseMapper.selectList(Wrappers.<CmsCategories>lambdaQuery()
                .select(CmsCategories::getId, CmsCategories::getPid, CmsCategories::getName, CmsCategories::getModule, CmsCategories::getModuleName, CmsCategories::getLeafIs, CmsCategories::getSorts, CmsCategories::getUri, CmsCategories::getShowHeadIs)
                .eq(Objects.nonNull(module), CmsCategories::getModule, module)
                .eq(CmsCategories::getEnabled, 1)
                .orderByAsc(CmsCategories::getSorts, CmsCategories::getId));

        // 构建树结构
        Map<String, List<CmsCategories>> categoriesMap = allCategories.stream().collect(Collectors.groupingBy(CmsCategories::getPid));

        allCategories.forEach(item -> {
            List<CmsCategories> children = categoriesMap.get(item.getId());
            if (children != null) {
                item.setChildren(children);
            }
        });

        return allCategories.stream().filter(item -> "0".equals(item.getPid())).collect(Collectors.toList());
    }

    private void verifyCategories(CmsCategories categories, int expectCount) {
        long count = baseMapper.selectCount(Wrappers.<CmsCategories>lambdaQuery()
                .eq(CommonUtils.isNotEmpty(categories.getId()), CmsCategories::getId, categories.getId())
                .or(r ->
                        r.eq(CmsCategories::getModule, categories.getModule())
                                .eq(CmsCategories::getName, categories.getName())
                ));

        if (count > expectCount) {
            BizExp.pop("栏目名称不能重复");
        }
    }
}
