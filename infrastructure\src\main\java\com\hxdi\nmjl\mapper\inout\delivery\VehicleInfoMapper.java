package com.hxdi.nmjl.mapper.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.delivery.VehicleInfo;
import com.hxdi.nmjl.condition.inout.VehicleInfoCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车辆管理数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-22 14:52:10
 */
@Mapper
public interface VehicleInfoMapper extends SuperMapper<VehicleInfo> {
    /**
     * 分页查询车辆管理
     *
     * @param page      分页对象
     * @param condition 查询条件
     * @return 分页结果
     */
    @DataPermission
    Page<VehicleInfo> selectPageV1(Page<VehicleInfo> page, @Param("condition") VehicleInfoCondition condition);

    /**
     * 列表查询车辆管理
     *
     * @param condition 查询条件
     * @return 列表结果
     */
    @DataPermission
    List<VehicleInfo> selectListV1(@Param("condition") VehicleInfoCondition condition);
}
