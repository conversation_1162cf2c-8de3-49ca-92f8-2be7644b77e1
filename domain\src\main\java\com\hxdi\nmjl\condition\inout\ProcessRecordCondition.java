package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 加工工艺备案查询条件
 */
@Getter
@Setter
@ApiModel(description = "加工工艺备案查询条件")
public class ProcessRecordCondition extends QueryCondition {

    /**
     * 库点ID
     */
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    /**
     * 品种ID
     */
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    /**
     * 备案开始时间
     */
    @ApiModelProperty(value = "备案开始时间")
    private String startTime;

    /**
     * 备案结束时间
     */
    @ApiModelProperty(value = "备案结束时间")
    private String endTime;
}
