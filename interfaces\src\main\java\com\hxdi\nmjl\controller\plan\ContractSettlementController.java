package com.hxdi.nmjl.controller.plan;

import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.ContractSettlementCondition;
import com.hxdi.nmjl.domain.plan.ContractSettlement;
import com.hxdi.nmjl.service.plan.ContractSettlementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * 合同结算控制层
 */
@RestController
@Api(tags = "合同结算管理")
@RequestMapping("/contractSettlement")
public class ContractSettlementController extends CommonApiController<ContractSettlementService, ContractSettlement, ContractSettlementCondition> {
    @ApiOperation("提交合同结算")
    @GetMapping("/submit")
    @Log(value = "提交合同结算", saveReqParam = true)
    public ResultBody<Void> submit(@RequestParam String id, @RequestParam String orderIds) {
        bizService.submit(id, orderIds);
        return ResultBody.OK();
    }

    @Override
    public ResultBody<Void> saveOrUpdate(@RequestBody ContractSettlement entity) {
        if (CommonUtils.isEmpty(entity.getId())) {
            bizService.create(entity);
        }
        return ResultBody.OK();
    }
}
