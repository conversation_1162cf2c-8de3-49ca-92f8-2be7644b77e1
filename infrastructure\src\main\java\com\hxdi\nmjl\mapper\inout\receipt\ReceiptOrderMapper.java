package com.hxdi.nmjl.mapper.inout.receipt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.opt.ReceiptOrder;
import com.hxdi.nmjl.condition.inout.ReceiptCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_RECEIPT_ORDER】的数据库操作Mapper
* @createDate 2025-04-20 22:42:41
* @Entity com.hxdi.nmjl.domain.receipt.BReceiptOrder
*/
@Mapper
public interface ReceiptOrderMapper extends SuperMapper<ReceiptOrder> {


    /**
     * 分页查询
      * @param pages
     * @param receiptCondition
     */
    @DataPermission
    Page<ReceiptOrder> selectPageV1(Page<ReceiptOrder> pages,@Param("condition") ReceiptCondition receiptCondition);

    /**
     * 列表查询
      * @param receiptCondition
     * @return
     */
    List<ReceiptOrder> selectListV1(@Param("condition") ReceiptCondition receiptCondition);
}
