package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 品种目录
 */
@Getter
@Setter
@TableName(value = "C_CATALOG")
public class Catalog extends Entity<Catalog> {

    private static final long serialVersionUID = 3530906180536701629L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 分类代码
     */
    @TableField(value = "CLASSIFICATION_ID")
    private String classificationId;

    /**
     * 分类名称
     */
    @TableField(value = "CLASSIFICATION_NAME")
    private String classificationName;

    /**
     * 品种代码
     */
    @TableField(value = "CATALOG_CODE")
    private String catalogCode;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    private String catalogName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    private String specification;

    /**
     * 品牌
     */
    @TableField(value = "BRAND")
    private String brand;

    /**
     * 计量单位
     */
    @TableField(value = "UNIT")
    private String unit;

    /**
     * 包装单位
     */
    @TableField(value = "PACK_UNIT")
    private String packUnit;

    /**
     * 状态：0-禁用，1-启用，7-删除
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    @TableField(value = "PRE_IMG")
    private String preImg;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENT")
    private String attachment;

    /**
     * 存储时间/天
     */
    @TableField(value = "MAX_STORAGE_TIME")
    private Integer maxStorageTime;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 类型：
     */
    @TableField(value = "CATEGORY")
    private Integer category;

    /**
     * 分类路径
     */
    @TableField(value = "CLASSIFICATION_PATH")
    private String classificationPath;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 获取完整规格名称
     *
     * @return
     */
    public String getFullSpecification() {
        return specification + unit;
    }

    /**
     * 获取完整商品名称
     *
     * @return
     */
    public String getFullName() {
        String fullname = catalogName;
        if (!catalogName.contains(specification)) {
            fullname = fullname + specification + unit;
        }

        if (CommonUtils.isNotEmpty(brand) && !fullname.contains(brand)) {
            fullname = fullname + " " + brand;
        }
        return fullname;
    }
}
