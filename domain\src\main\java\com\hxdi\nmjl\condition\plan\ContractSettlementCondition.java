package com.hxdi.nmjl.condition.plan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.model.QueryCondition;
import com.hxdi.common.core.mybatis.annotation.Between;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 合同结算查询条件
 */
@Getter
@Setter
@ApiModel(description = "合同结算查询条件")
public class ContractSettlementCondition extends QueryCondition {

    @EQ
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @EQ
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @EQ
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    @EQ
    @ApiModelProperty(value = "业务类型：1-采购合同，2-销售合同")
    private String bizType;

    @EQ
    @ApiModelProperty(value = "结算状态：1-未结算，2-部分结算，3-已结算")
    private Integer settlementStatus;

    @ApiModelProperty(value = "创建时间")
    @Between("CREATE_TIME")
    private RangeBean<Date> createTime;

    @ApiModelProperty(value = "模糊查询：结算单号、合同编号、合同名称、机构名称、客户名称")
    @LIKE(value = "SETTLEMENT_CODE,CONTRACT_CODE,CONTRACT_NAME,CLIENT_NAME", logic = "OR", join = true)
    private String keywords;

    @EQ
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer enabled = 1;
}
