package com.hxdi.nmjl.service.storeproduction.Impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionInfoCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionInfo;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlan;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlanDetail;
import com.hxdi.nmjl.mapper.storeproduction.StoreProductionInfoMapper;
import com.hxdi.nmjl.service.base.CatalogService;
import com.hxdi.nmjl.service.storeproduction.StoreProductionInfoService;
import com.hxdi.nmjl.service.storeproduction.StoreProductionPlanDetailService;
import com.hxdi.nmjl.service.storeproduction.StoreProductionPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 库存生产信息服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class StoreProductionInfoServiceImpl extends BaseServiceImpl<StoreProductionInfoMapper, StoreProductionInfo> implements StoreProductionInfoService {

    @Resource
    private CatalogService catalogService;

    @Resource
    private StoreProductionPlanService storeProductionPlanService;

    @Resource
    private StoreProductionPlanDetailService storeProductionPlanDetailService;

    @Override
    public void create(StoreProductionInfo info) {
        BaseUserDetails userDetails = SecurityHelper.getUser();
        info.setStoreId(userDetails.getOrganId());
        info.setStoreName(userDetails.getOrganName());
        Catalog catalog = catalogService.getById(info.getCatalogId());
        info.setCatalogName(catalog.getCatalogName());

        QueryWrapper<StoreProductionPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plan_no", info.getPlanNo());
        StoreProductionPlan plan = storeProductionPlanService.getOne(queryWrapper);

        List<StoreProductionPlanDetail> detailList = storeProductionPlanDetailService.list(
                new QueryWrapper<StoreProductionPlanDetail>().eq("plan_id", plan.getId())
        );

        boolean isMatched = false;

        for (StoreProductionPlanDetail detail : detailList) {
            if (detail.getCatalogId().equals(info.getCatalogId())) {
                detail.setCompleteQuantity(detail.getCompleteQuantity().add(info.getProductionQuantity()));
                storeProductionPlanDetailService.updateById(detail);
                isMatched = true;
                this.save(info);
                break;
            }
        }

        if (!isMatched) {
            throw new BaseException("当前品类在计划中不存在");
        }
    }

    @Override
    public void update(StoreProductionInfo info) {
        StoreProductionInfo existingInfo = this.getById(info.getId());
        if (existingInfo == null) {
            throw new BaseException("库存生产信息不存在");
        }
        Catalog catalog = catalogService.getById(info.getCatalogId());
        info.setCatalogName(catalog.getCatalogName());
        QueryWrapper<StoreProductionPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plan_no", info.getPlanNo());
        StoreProductionPlan plan = storeProductionPlanService.getOne(queryWrapper);

        List<StoreProductionPlanDetail> detailList = storeProductionPlanDetailService.list(
                new QueryWrapper<StoreProductionPlanDetail>().eq("plan_id", plan.getId())
        );

        boolean isMatched = false;
        BigDecimal quantityDiff = info.getProductionQuantity().subtract(existingInfo.getProductionQuantity());

        for (StoreProductionPlanDetail detail : detailList) {
            if (detail.getCatalogId().equals(info.getCatalogId())) {
                detail.setCompleteQuantity(detail.getCompleteQuantity().add(quantityDiff));
                storeProductionPlanDetailService.updateById(detail);
                isMatched = true;
                this.updateById(info);
                break;
            }
        }
        if (!isMatched) {
            throw new BaseException("当前品类在计划中不存在");
        }
    }

    @Override
    public StoreProductionInfo getDetail(String id) {
        return this.getById(id);
    }

    @Override
    public Page<StoreProductionInfo> pages(StoreProductionInfoCondition condition) {
        Page<StoreProductionInfo> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<StoreProductionInfo> lists(StoreProductionInfoCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void remove(String id) {
        StoreProductionInfo info = this.getById(id);
        if (info == null) {
            throw new BaseException("库存生产信息不存在");
        }
        this.removeById(id);
    }
}