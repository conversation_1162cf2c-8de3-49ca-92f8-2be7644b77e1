package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@TableName(value = "B_PRODUCTION_ORDER_ITEM")
@ApiModel(description="生产订单明细")
public class ProductionOrderItem extends Entity<ProductionOrderItem> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 订单ID
     */
    @TableField(value = "ORDER_ID")
    @ApiModelProperty(value="订单ID")
    private String orderId;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value="品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value="品种名称")
    private String catalogName;

    /**
     * 品牌
     */
    @TableField(value = "BRAND")
    private String brand;

    /**
     * 质量等级：字典：YZLDJ/LZLDJ
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value="质量等级：字典：YZLDJ/LZLDJ")
    private String grade;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    @ApiModelProperty(value="规格")
    private String specification;

    /**
     * 订单件数
     */
    @TableField(value = "ORDER_PACK_QTY")
    @ApiModelProperty(value="订单件数")
    private Integer orderPackQty;

    /**
     * 订单数量
     */
    @TableField(value = "ORDER_QTY")
    @ApiModelProperty(value="订单数量")
    private BigDecimal orderQty;

    /**
     * 完成数量
     */
    @TableField(value = "COMPLETED_QTY")
    @ApiModelProperty(value="完成数量")
    private BigDecimal completedQty;

    /**
     * 累计生产件数
     */
    @TableField(value = "COMPLETED_PACK_QTY")
    @ApiModelProperty(value="累计生产件数")
    private Integer completedPackQty;

    /**
     * 退货数量
     */
    @TableField(value = "RETURN_QTY")
    @ApiModelProperty(value="退货数量")
    private BigDecimal returnQty;

    /**
     * 储备性质:字典CBXZ
     */
    @TableField(value = "RESERVE_LEVEL")
    @ApiModelProperty(value="储备性质:字典CBXZ")
    private String reserveLevel;

    /**
     * 单价
     */
    @TableField(value = "PRICE")
    @ApiModelProperty(value="单价")
    private BigDecimal price;

    /**
     * 生产日期
     */
    @TableField(value = "PRODUCT_DATE")
    @ApiModelProperty(value="生产日期")
    private String productDate;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;

    /**
     * 生产订单填报数据
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "生产订单填报数据")
    private List<ProductionOrderTrace> traceList;
}