package com.hxdi.nmjl.controller.mobilization;



import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.mobilization.MobilizedEnterprise;
import com.hxdi.nmjl.service.mobilization.MobilizedEnterpriseService;
import com.hxdi.nmjl.condition.mobilization.MobilizedEnterpriseCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动员企业管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/1
 */
@Api(tags = "动员企业管理")
@RestController
@RequestMapping("/enterprise")
public class MobilizedEnterpriseController extends BaseController<MobilizedEnterpriseService, MobilizedEnterprise> {

    @ApiOperation("分页查询动员企业")
    @GetMapping("/page")
    public ResultBody<Page<MobilizedEnterprise>> page(MobilizedEnterpriseCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询动员企业")
    @GetMapping("/list")
    public ResultBody<List<MobilizedEnterprise>> list(MobilizedEnterpriseCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("保存/修改计划")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody MobilizedEnterprise mobilizedEnterprise) {
        if(CommonUtils.isEmpty(mobilizedEnterprise.getId())) {
            bizService.create(mobilizedEnterprise);
        } else {
            bizService.update(mobilizedEnterprise);
        }
        return ResultBody.ok();
    }

    @ApiOperation("删除动员企业")
    @DeleteMapping("/remove")
    public ResultBody remove(@RequestParam String enterpriseId) {
        bizService.remove(enterpriseId);
        return ResultBody.ok();
    }

    @ApiOperation("查看动员企业详情")
    @GetMapping("/detail")
    public ResultBody<MobilizedEnterprise> getDetail(@RequestParam String enterpriseId) {
        return ResultBody.ok().data(bizService.getById(enterpriseId));
    }

    @ApiOperation("变更企业状态")
    @PutMapping("/changeState")
    public ResultBody changeState(@RequestParam String enterpriseId,
                                  @RequestParam Integer state) {
        bizService.changeState(enterpriseId, state);
        return ResultBody.ok();
    }

    @ApiOperation(value = "审核")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @GetMapping("/reject")
    public ResultBody<Void> reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "提交")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }
}
