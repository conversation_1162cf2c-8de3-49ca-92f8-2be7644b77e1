package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 盘点记录
 */
@ApiModel(description = "盘点记录")
@Getter
@Setter
@TableName(value = "B_INVENTORY_CHECK")
public class InventoryCheck implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;
    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;
    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value = "仓房ID")
    private String stId;
    /**
     * 仓房名称
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value = "仓房名称")
    private String stName;
    /**
     * 计划开始时间
     */
    @TableField(value = "PLAN_DATE")
    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDate;
    /**
     * 盘点时间
     */
    @TableField(value = "START_TIME")
    @ApiModelProperty(value = "盘点时间")
    private Date startTime;
    /**
     * 盘点人员
     */
    @TableField(value = "PERSONS")
    @ApiModelProperty(value = "盘点人员")
    private String persons;
    /**
     * 盘点负责人
     */
    @TableField(value = "FZR")
    @ApiModelProperty(value = "盘点负责人")
    private String fzr;
    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;
    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @TableField(value = "APPROVE_STATUS")
    @ApiModelProperty(value = "审核状态：-1-默认值（未提交） 0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;
    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;
    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;
    /**
     * 盘点状态：0-未盘点，1-已盘点
     */
    @TableField(value = "CHECK_STATUS")
    @ApiModelProperty(value = "盘点状态：0-未盘点，1-已盘点")
    private Integer checkStatus;
    /**
     * 状态：1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态：1-有效，0-删除")
    private Integer enabled;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;
    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    /********************拓展字段****************************/

    /**
     * 计划ID
     */
    @TableField(exist = false)
    private String planId;

    /**
     * 计划名称
     */
    @TableField(exist = false)
    private String planName;

    /**
     * 盘点记录明细
     */
    @ApiModelProperty(value = "盘点记录明细")
    @TableField(exist = false)
    private List<InventoryCheckDetail> inventoryCheckDetailList;

}
