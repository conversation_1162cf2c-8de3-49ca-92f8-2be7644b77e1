package com.hxdi.nmjl.mapper.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryEvent;
import com.hxdi.nmjl.condition.inout.DeliveryEventCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 配送事件记录表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-23 10:23:26
 */
@Mapper
public interface DeliveryEventMapper extends SuperMapper<DeliveryEvent> {

    @DataPermission
    Page<DeliveryEvent> selectPageV1(Page<DeliveryEvent> page, @Param("condition") DeliveryEventCondition condition);

    @DataPermission
    List<DeliveryEvent> selectListV1(@Param("condition") DeliveryEventCondition condition);
}
