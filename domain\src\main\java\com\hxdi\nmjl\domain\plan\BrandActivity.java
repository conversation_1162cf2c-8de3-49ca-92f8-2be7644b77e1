package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 品牌传播活动
 */
@Getter
@Setter
@TableName(value = "B_BRAND_ACTIVITY")
public class BrandActivity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 品牌id
     */
    @TableField(value = "BRAND_ID")
    private String brandId;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND_NAME")
    private String brandName;

    /**
     * 计划名称
     */
    @TableField(value = "ACTIVITY_NAME")
    private String activityName;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "START_DATE")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "END_DATE")
    private Date endDate;

    /**
     * 活动类型: 字典：PPHDLX
     */
    @TableField(value = "ACTIVITY_TYPE")
    private String activityType;

    /**
     * 渠道方式：字典：PPQDFS
     */
    @TableField(value = "CHANNEL")
    private String channel;

    /**
     * 目标受众：字典：PPMBSZ
     */
    @TableField(value = "TARGET_USER")
    private String targetUser;

    /**
     * 预算金额
     */
    @TableField(value = "BUDGET")
    private BigDecimal budget;

    /**
     * 负责人
     */
    @TableField(value = "CHARGE_PERSON")
    private String chargePerson;

    /**
     * 联系电话
     */
    @TableField(value = "MOBILE")
    private String mobile;

    /**
     * 办理单位
     */
    @TableField(value = "ORG_ID")
    private String orgId;

    /**
     * 办理单位名称
     */
    @TableField(value = "ORG_NAME")
    private String orgName;

    /**
     * 活动目标
     */
    @TableField(value = "GOALS")
    private String goals;

    /**
     * 目标达成率%
     */
    @TableField(value = "COMPLETED_RATE")
    private Integer completedRate;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @TableField(value = "APPROVE_STATUS")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    private String approveOpinion;

    /**
     * 状态（1-有效 0删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    private String attachments;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 品牌传播活动执行记录
     */
    @TableField(exist = false)
    private List<BrandActivityPerform> performList;
}