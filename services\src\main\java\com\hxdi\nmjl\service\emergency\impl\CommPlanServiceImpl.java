package com.hxdi.nmjl.service.emergency.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.emergency.CommPlan;
import com.hxdi.nmjl.mapper.emergency.CommPlanMapper;
import com.hxdi.nmjl.service.emergency.CommPlanService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* <AUTHOR>
* @description 针对表【B_COMM_PLAN(沟通计划执行表)】的数据库操作Service实现
* @createDate 2025-08-15 09:22:05
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class CommPlanServiceImpl extends BaseServiceImpl<CommPlanMapper, CommPlan> implements CommPlanService{

}




