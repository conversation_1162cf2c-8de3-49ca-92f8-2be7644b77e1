package com.hxdi.nmjl.linker.rule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.ClientInfo;
import com.hxdi.nmjl.domain.clientrelated.ClientRelation;
import com.hxdi.nmjl.domain.base.ProductionSupplier;
import com.hxdi.nmjl.domain.inventory.InventoryBase;
import com.hxdi.nmjl.mapper.base.ClientRelationMapper;
import com.hxdi.nmjl.service.base.ClientInfoService;
import com.hxdi.nmjl.service.inventory.InventoryBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/9 9:34 上午
 * @description 业务规则定义实现
 */
@Component
@Slf4j
public class BusinessRuleImpl implements BusinessRules {

    @Resource
    private InventoryBaseService inventoryBaseService;

    @Resource
    private ClientInfoService clientInfoService;

    @Resource
    private ClientRelationMapper clientRelationMapper;

    @Override
    public boolean removeVerify(Catalog catalog) {
        long count = inventoryBaseService.count(Wrappers.<InventoryBase>lambdaQuery().eq(InventoryBase::getCatalogId, catalog.getId()).eq(InventoryBase::getEnabled, 1));
        if (count > 0) {
            return false;
        }
        return true;
    }

    @Override
    public boolean removeVerify(ProductionSupplier productionSupplier) {
        ClientInfo clientInfo = clientInfoService.getOne(Wrappers.<ClientInfo>lambdaQuery().eq(ClientInfo::getRefId, productionSupplier.getId()).eq(ClientInfo::getEnabled, 1));
        if (clientInfo == null) {
            return true;
        }

        long count = clientRelationMapper.selectCount(Wrappers.<ClientRelation>lambdaQuery().eq(ClientRelation::getClientId, clientInfo.getId()));
        return count <= 0;
    }
}
