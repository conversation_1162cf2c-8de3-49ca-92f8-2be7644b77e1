<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.delivery.FuelAnalysisMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.delivery.FuelAnalysis">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="storeId" column="STORE_ID" jdbcType="VARCHAR"/>
        <result property="storeName" column="STORE_NAME" jdbcType="VARCHAR"/>
        <result property="vehicleKind" column="VEHICLE_KIND" jdbcType="VARCHAR"/>
        <result property="vehicleNo" column="VEHICLE_NO" jdbcType="VARCHAR"/>
        <result property="refuelDate" column="REFUEL_DATE" jdbcType="TIMESTAMP"/>
        <result property="refuelQuantity" column="REFUEL_QUANTITY" jdbcType="DECIMAL"/>
        <result property="curFuelPrice" column="CUR_FUEL_PRICE" jdbcType="DECIMAL"/>
        <result property="fuelType" column="FUEL_TYPE" jdbcType="VARCHAR"/>
        <result property="mileageAfter" column="MILEAGE_AFTER" jdbcType="INTEGER"/>
        <result property="totalDistance" column="TOTAL_DISTANCE" jdbcType="DECIMAL"/>
        <result property="operator" column="OPERATOR" jdbcType="VARCHAR"/>
        <result property="fuelPlace" column="FUEL_PLACE" jdbcType="VARCHAR"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
        <result property="attachment" column="ATTACHMENT" jdbcType="VARCHAR"/>
        <result property="fuelConsumption" column="FUEL_CONSUMPTION" jdbcType="DECIMAL"/>
        <result property="fuelConsumptionActual" column="FUEL_CONSUMPTION_ACTUAL" jdbcType="DECIMAL"/>
        <result property="mark" column="MARK" jdbcType="VARCHAR"/>
        <result property="confirmResult" column="CONFIRM_RESULT" jdbcType="VARCHAR"/>
        <result property="confirmRemark" column="CONFIRM_REMARK" jdbcType="VARCHAR"/>
        <result property="confirmUser" column="CONFIRM_USER" jdbcType="VARCHAR"/>
        <result property="confirmTime" column="CONFIRM_TIME" jdbcType="TIMESTAMP"/>
        <result property="statTime" column="STAT_TIME" jdbcType="TIMESTAMP"/>
        <result property="refuelCount" column="REFUEL_COUNT" jdbcType="INTEGER"/>
        <result property="totalFuelAmount" column="TOTAL_FUEL_AMOUNT" jdbcType="DECIMAL"/>
        <result property="totalFuelCost" column="TOTAL_FUEL_COST" jdbcType="DECIMAL"/>
        <result property="totalDriveDistance" column="TOTAL_DRIVE_DISTANCE" jdbcType="DECIMAL"/>
        <result property="alertCount" column="ALERT_COUNT" jdbcType="INTEGER"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="UPDATED_TIME" jdbcType="TIMESTAMP"/>
        <result property="createId" column="CREATE_ID" jdbcType="VARCHAR"/>
        <result property="updateId" column="UPDATE_ID" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,STORE_ID,STORE_NAME,
        VEHICLE_KIND,VEHICLE_NO,REFUEL_DATE,
        REFUEL_QUANTITY,CUR_FUEL_PRICE,FUEL_TYPE,
        MILEAGE_AFTER,TOTAL_DISTANCE,OPERATOR,
        FUEL_PLACE,REMARK,ATTACHMENT,
        FUEL_CONSUMPTION,FUEL_CONSUMPTION_ACTUAL,MARK,
        CONFIRM_RESULT,CONFIRM_REMARK,CONFIRM_USER,
        CONFIRM_TIME,STAT_TIME,REFUEL_COUNT,
        TOTAL_FUEL_AMOUNT,TOTAL_FUEL_COST,TOTAL_DRIVE_DISTANCE,
        ALERT_COUNT,CREATED_TIME,
        UPDATED_TIME,CREATE_ID,UPDATE_ID,
        TENANT_ID,DATA_HIERARCHY_ID
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_FUEL_ANALYSIS
        <where>
            <if test="condition.startTime!=null">
                AND REFUEL_DATE &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime!=null">
                AND REFUEL_DATE &lt;= #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.mark)">
                AND MARK = #{condition.mark}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.confirmResult)">
                AND CONFIRM_RESULT = #{condition.confirmResult}
            </if>
        </where>
        ORDER BY CREATED_TIME DESC
    </select>

    <!-- 分页查询 -->
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_FUEL_ANALYSIS
        <where>
            <if test="condition.startTime!=null">
                AND REFUEL_DATE &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime!=null">
                AND REFUEL_DATE &lt;= #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.mark)">
                AND MARK = #{condition.mark}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.confirmResult)">
                AND CONFIRM_RESULT = #{condition.confirmResult}
            </if>
        </where>
        ORDER BY CREATED_TIME DESC
    </select>
    <!--分页分组查询-->
    <select id="selectPageV2" resultMap="BaseResultMap">
        SELECT
        STORE_NAME,
        VEHICLE_KIND,
        VEHICLE_NO,
        MAX(STAT_TIME) AS STAT_TIME,
        COUNT(VEHICLE_NO) AS REFUEL_COUNT,
        SUM(REFUEL_QUANTITY) AS TOTAL_FUEL_AMOUNT,
        SUM(REFUEL_QUANTITY * CUR_FUEL_PRICE) AS TOTAL_FUEL_COST,
        SUM(TOTAL_DISTANCE) AS TOTAL_DRIVE_DISTANCE,
        COUNT(CASE WHEN MARK = '超标预警' THEN 1 ELSE NULL END) AS ALERT_COUNT
        FROM B_FUEL_ANALYSIS
        <where>
            <if test="condition.startTime!=null">
                AND REFUEL_DATE &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime!=null">
                AND REFUEL_DATE &lt;= #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.mark)">
                AND MARK = #{condition.mark}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.confirmResult)">
                AND CONFIRM_RESULT = #{condition.confirmResult}
            </if>
        </where>
        GROUP BY VEHICLE_NO
        ORDER BY MAX(CREATED_TIME) DESC
    </select>

</mapper>
