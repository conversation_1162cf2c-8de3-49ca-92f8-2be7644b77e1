package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.BidInfoCondition;
import com.hxdi.nmjl.domain.plan.BidInfo;
import com.hxdi.nmjl.domain.plan.BidObject;
import com.hxdi.nmjl.mapper.plan.BidInfoMapper;
import com.hxdi.nmjl.service.plan.BidInfoService;
import com.hxdi.nmjl.service.plan.BidObjectService;
import com.hxdi.nmjl.service.plan.ProcurementPlanService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class BidInfoServiceImpl extends BaseServiceImpl<BidInfoMapper, BidInfo> implements BidInfoService {

    @Resource
    private BidObjectService bidObjectService;

    @Resource
    private ProcurementPlanService planService;

    @Override
    public Page<BidInfo> getPages(BidInfoCondition condition) {
        Page<BidInfo> page = condition.newPage();
        return baseMapper.getPages(condition, page);
    }

    @Override
    public List<BidInfo> getList(BidInfoCondition condition) {
        return baseMapper.getList(condition);
    }

    @Override
    public BidInfo getDetail(String bidId) {
        BidInfo bidInfo = baseMapper.selectById(bidId);
        bidInfo.setDetailList(bidObjectService.getList(Collections.singletonList(bidId)));
        return bidInfo;
    }

    @Override
    public boolean add(BidInfo bidInfo) {
        bidInfo.setState(0);
        if (!this.save(bidInfo)) {
            BizExp.pop("招标信息保存失败");
        }
        List<BidObject> detailList = bidInfo.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            BizExp.pop("请填写招标明细!");
        }
        // 设置 bidId 并保存明细
        for (BidObject bidObject : detailList) {
            bidObject.setBidId(bidInfo.getId());
        }
        if (!bidObjectService.saveBatch(detailList)) {
            BizExp.pop("招标明细保存失败");
        }
        return true;
    }

    @Override
    public List<String> getExistingBidObject(String planId) {
        LambdaQueryWrapper<BidInfo> wrapper = new LambdaQueryWrapper<BidInfo>()
                .eq(BidInfo::getPlanId, planId)
                .eq(BidInfo::getEnabled, StrPool.State.ENABLE)
                .in(BidInfo::getState, 1, 2);
        List<BidInfo> bidInfoList = this.list(wrapper);

        List<String> resultList = new ArrayList<>();

        if (!bidInfoList.isEmpty()) {
            List<BidObject> bidObjectList = bidObjectService.getList(bidInfoList.stream().map(BidInfo::getId).collect(Collectors.toList()));
            bidObjectList.forEach(bidObject -> resultList.add(bidObject.getClassificationId() + bidObject.getGrade()));
        }
        return resultList;
    }

    @Override
    public void updateV1(BidInfo bidInfo) {

        //更新主表信息
        if (!this.updateById(bidInfo)) {
            BizExp.pop("招标信息更新失败");
        }
        //获取新的明细数据
        List<BidObject> detailList = bidInfo.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            BizExp.pop("请填写招标明细!");
        }
        //删除原有明细
        bidObjectService.remove(Wrappers.<BidObject>lambdaQuery().eq(BidObject::getBidId, bidInfo.getId()));
        //设置 bidId 并保存新明细
        for (BidObject bidObject : detailList) {
            bidObject.setBidId(bidInfo.getId());
        }
        if (!bidObjectService.saveBatch(detailList)) {
            BizExp.pop("招标明细保存失败");
        }
    }

    @Override
    public void deleteV1(String bidId) {
        BidInfo bidInfo = this.getById(bidId);
        if (bidInfo == null) {
            BizExp.pop("招标信息不存在");
        }
        if (bidInfo.getApproveStatus() != null) {
            BizExp.pop("已提交的数据无法删除!");
        }
        //逻辑删除主表，明细保留
        this.update(Wrappers.<BidInfo>lambdaUpdate().eq(BidInfo::getId, bidId).set(BidInfo::getEnabled, 0));
    }

    @Override
    public void changeFlowState(String id, Integer flowState) {
        if (CommonUtils.isEmpty(flowState) || flowState < 1 || flowState > 4) {
            throw new BaseException("无效的流程状态");
        }
        BidInfo bidInfo = baseMapper.selectById(id);
        if (bidInfo == null) {
            throw new BaseException("招标信息不存在");
        }
        if (bidInfo.getFlowState() >= flowState) {
            throw new BaseException("招标流程已进行到该步骤，请勿重复操作！");
        }
        bidInfo.setFlowState(flowState);
        baseMapper.updateById(bidInfo);
    }

    @Override
    public void approve(String id, String approveOpinion) {
        //审批通过
        changeApproveStatus(id, 1, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 2, approveOpinion);
    }

    @Override
    public void submit(String id) {
        // 审核状态：0-未审核，1-待审核，2-已审核，3-驳回
        changeApproveStatus(id, 0, null);
    }

    @Override
    public void winBid(String id) {
        BidInfo bidInfo = this.getById(id);
        if (bidInfo.getApproveStatus() != null && bidInfo.getApproveStatus() != 1 || bidInfo.getState() != 1 || bidInfo.getFlowState() != 4) {
            throw new BaseException("只有审核通过且状态为招标中的招标信息才能中标!");
        }
        //将招标信息状态改为已完成，并更新对应的筹措计划状态
        bidInfo.setState(2);
        baseMapper.updateById(bidInfo);
        planService.updatePlanState(bidInfo.getPlanId(), 3);
    }

    @Override
    public void flow(String id) {
        BidInfo bidInfo = this.getById(id);
        if (bidInfo.getApproveStatus() != null && bidInfo.getApproveStatus() != 1 || bidInfo.getState() != 1 || bidInfo.getFlowState() != 4) {
            throw new BaseException("只有审核通过且状态为招标中的招标信息才能流标!");
        }
        //将招标信息状态改为流标
        bidInfo.setState(3);
        baseMapper.updateById(bidInfo);
    }

    @Override
    public boolean existsBidForDetail(String id) {
        Long count = baseMapper.selectCount(Wrappers.<BidInfo>lambdaQuery()
                .eq(BidInfo::getEnabled, StrPool.State.ENABLE)
                .eq(BidInfo::getPlanId, id)
                .in(BidInfo::getState, 2));
        return count > 0;
    }

    /**
     * 更新审核状态
     *
     * @param id
     * @param approveStatus
     * @param approveOpinion
     */
    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        BidInfo bidInfo = baseMapper.selectById(id);
        bidInfo.setDetailList(bidObjectService.getList(Collections.singletonList(id)));
        if (bidInfo.getEnabled() == 0) {
            throw new BaseException("招标信息不存在！");
        }

        // 校验状态：已审核的不能重复审核
        if (approveStatus != 0 && bidInfo.getApproveStatus() != null && bidInfo.getApproveStatus() != 0) {
            throw new BaseException("该招标信息已审核，无法重复操作！");
        }

        bidInfo.setApproveStatus(approveStatus);

        //提交时，先根据筹措计划id查询不能新增的筹措计划品种，避免同一个品种被重复提交
        if (approveStatus == 1) {
            List<String> existingBidObject = getExistingBidObject(bidInfo.getPlanId());
            if (existingBidObject != null && !existingBidObject.isEmpty()) {
                List<BidObject> detailList = bidInfo.getDetailList();
                for (BidObject bidObject : detailList) {
                    if (existingBidObject.contains(bidObject.getClassificationId() + bidObject.getGrade())) {

                        bidInfo.setApproveStatus(2);
                        bidInfo.setApproveOpinion("当前招标信息所选标的物已在现有招标信息中存在，请勿重复提交！");
                        baseMapper.updateById(bidInfo);

                        throw new BaseException("当前招标信息所选标的物已在现有招标信息中存在，请勿重复提交！");
                    }
                }
            }
        }


        if (approveStatus == 1 || approveStatus == 2) {
            bidInfo.setApprover(SecurityHelper.obtainUser().getNickName());
            bidInfo.setApproveTime(new Date());
            bidInfo.setApproveOpinion(approveOpinion);

            //更新对应的筹措计划状态（招标关联计划为父计划时，同时更新其子计划状态）

            if (approveStatus == 1) {
                bidInfo.setState(1);
                bidInfo.setFlowState(2);
                planService.updatePlanState(bidInfo.getPlanId(), 2);
            }
        } else {
            //清除审核信息
            bidInfo.setApprover("");
            bidInfo.setApproveOpinion("");
        }

        baseMapper.updateById(bidInfo);
    }
}
