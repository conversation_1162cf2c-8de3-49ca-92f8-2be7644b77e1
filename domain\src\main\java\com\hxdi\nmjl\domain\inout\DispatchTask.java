package com.hxdi.nmjl.domain.inout;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.inventory.Inventory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description="库存拆分计划")
@TableName(value = "B_DISPATCH_TASK")
public class DispatchTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 任务单号
     */
    @TableField(value = "TASK_CODE")
    @ApiModelProperty(value="任务单号")
    private String taskCode;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value="库点名称")
    private String storeName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value="仓房ID")
    private String stId;

    /**
     * 仓房名称
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value="仓房名称")
    private String stName;

    /**
     * 计划开始时间
     */
    @TableField(value = "PLAN_DATE")
    @ApiModelProperty(value="计划开始时间")
    private Date planDate;

    /**
     * 库存ID
     */
    @TableField(value = "INVENTORY_ID")
    @ApiModelProperty(value="库存ID")
    private String inventoryId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value="品种名称")
    private String catalogName;

    /**
     * 计划数量
     */
    @TableField(value = "PLAN_QTY")
    @ApiModelProperty(value="计划数量")
    private BigDecimal planQty;

    /**
     * 完成数量
     */
    @TableField(value = "COMPLETED_QTY")
    @ApiModelProperty(value="完成数量")
    private BigDecimal completedQty;

    /**
     * 业务状态：0-未执行，1-执行中，2-已完成
     */
    @TableField(value = "\"STATE\"")
    @ApiModelProperty(value="业务状态：0-未执行，1-执行中，2-已完成")
    private Integer state;

    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value="备注")
    private String remarks;

    /**
     * 附件
     */
    @TableField(value = "ATTACHEMENTS")
    @ApiModelProperty(value="附件")
    private String attachements;

    /**
     * 状态：1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value="状态：1-有效，0-删除")
    private Integer enabled;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;

    @ApiModelProperty(value="库存信息")
    @TableField(exist = false)
    private Inventory inventory;

    @ApiModelProperty(value="品种信息")
    @TableField(exist = false)
    private Catalog catalog;
}