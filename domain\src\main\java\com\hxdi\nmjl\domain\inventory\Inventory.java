package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 库存表
 * @author: 王贝强
 * @create: 2025-03-10 15:12
 */
@ApiModel(description="库存信息")
@Getter
@Setter
@TableName(value = "B_INVENTORY")
public class Inventory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 库存ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="库存ID")
    private String id;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="军供站ID")
    private String storeId;

    /**
     * 仓库
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value="仓库")
    private String storeName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value="仓房ID")
    private String stId;

    /**
     * 仓房
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value="仓房")
    private String stName;

    /**
     * 货位ID
     */
    @TableField(value = "LOC_ID")
    @ApiModelProperty(value="货位ID")
    private String locId;

    /**
     * 货位名称
     */
    @TableField(value = "LOC_NAME")
    @ApiModelProperty(value="货位名称")
    private String locName;

    /**
     * 品种大类
     */
    @TableField(value = "BIG_TYPE")
    @ApiModelProperty(value="品种大类")
    private Integer bigType;

    /**
     * 品类ID
     */
    @TableField(value = "CLASSIFICATION_ID")
    @ApiModelProperty(value="品类ID")
    private String classificationId;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value="品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value="品种名称")
    private String catalogName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATIONS")
    @ApiModelProperty(value="规格")
    private String specifications;

    /**
     * 质量等级
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value="质量等级")
    private String grade;

    /**
     * 储备性质
     */
    @TableField(value = "RESERVE_LEVEL")
    @ApiModelProperty(value="储备性质")
    private Integer reserveLevel;

    /**
     * 省
     */
    @TableField(value = "PROVINCE")
    @ApiModelProperty(value="省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "CITY")
    @ApiModelProperty(value="市")
    private String city;

    /**
     * 区
     */
    @TableField(value = "COUNTY")
    @ApiModelProperty(value="县区")
    private String county;

    /**
     * 生产日期
     */
    @TableField(value = "PRODUCTION_DATE")
    @ApiModelProperty(value="生产日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 管理机构id
     */
    @TableField(value = "MANAGE_UNIT_ID")
    @ApiModelProperty(value="管理机构id")
    private String manageUnitId;

    /**
     * 管理机构名称
     */
    @TableField(value = "MANAGE_UNIT_NAME")
    @ApiModelProperty(value="管理机构名称")
    private String manageUnitName;

    /**
     * 锁定库存量
     */
    @TableField(value = "LOCK_QTY")
    @ApiModelProperty(value="锁定库存量")
    private BigDecimal lockQty;

    /**
     * 库存数量
     */
    @TableField(value = "INVENTORY_QTY")
    @ApiModelProperty(value="库存数量")
    private BigDecimal inventoryQty;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value="状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
/**
 * -----------------非数据库属性--------
 */


     @ApiModelProperty(value="品牌")
     @TableField(exist = false)
     private String brand;


    @ApiModelProperty(value = "计量单位")
    @TableField(exist = false)
    private String unit;

    @ApiModelProperty(value = "批次")
    @TableField(exist = false)
    private String batchNum;

}
