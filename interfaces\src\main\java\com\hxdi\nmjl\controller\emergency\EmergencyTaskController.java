package com.hxdi.nmjl.controller.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.emergency.EmergencyTaskCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyTask;
import com.hxdi.nmjl.service.emergency.EmergencyTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急任务管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/21
 */
@Api(tags = "应急任务管理")
@RestController
@RequestMapping("/emergencyTask")
public class EmergencyTaskController extends BaseController<EmergencyTaskService, EmergencyTask> {

    @ApiOperation("分页查询应急任务")
    @GetMapping("/page")
    public ResultBody<Page<EmergencyTask>> page(EmergencyTaskCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询应急任务")
    @GetMapping("/list")
    public ResultBody<List<EmergencyTask>> list(EmergencyTaskCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("保存/修改应急任务")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody EmergencyTask task) {
        if (task.getId() == null) {
            bizService.create(task);
        } else {
            bizService.update(task);
        }
        return ResultBody.ok();
    }

    @ApiOperation("查看应急任务详情")
    @GetMapping("/getDetail")
    public ResultBody<EmergencyTask> getDetail(@RequestParam String taskId) {
        return ResultBody.ok().data(bizService.getDetail(taskId));
    }

    @ApiOperation("更新应急任务运送状态")
    @PostMapping("/updateState")
    public ResultBody updateState(@RequestParam String taskId) {
        bizService.updateState(taskId);
        return ResultBody.ok();
    }

    @ApiOperation("删除应急任务")
    @DeleteMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

}