<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.iot.DeviceInfoMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.iot.DeviceInfo">
    <!--@mbg.generated-->
    <!--@Table B_DEVICE_INFO-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
    <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
    <result column="DEVICE_NAME" jdbcType="VARCHAR" property="deviceName" />
    <result column="ST_ID" jdbcType="VARCHAR" property="stId" />
    <result column="ST_NAME" jdbcType="VARCHAR" property="stName" />
    <result column="DEVICE_STATE" jdbcType="INTEGER" property="deviceState" />
    <result column="MAKER" jdbcType="VARCHAR" property="maker" />
    <result column="IPA" jdbcType="VARCHAR" property="ipa" />
    <result column="PORTS" jdbcType="VARCHAR" property="ports" />
    <result column="SERIAL" jdbcType="VARCHAR" property="serial" />
    <result column="LON" jdbcType="VARCHAR" property="lon" />
    <result column="LAT" jdbcType="VARCHAR" property="lat" />
    <result column="DEVICE_TYPE" jdbcType="VARCHAR" property="deviceType" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    <result column="NODE_NUM" jdbcType="INTEGER" property="nodeNum" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, STORE_ID, STORE_NAME, DEVICE_NAME, ST_ID, ST_NAME, DEVICE_STATE, MAKER, IPA, 
    PORTS, SERIAL, LON, LAT, DEVICE_TYPE, AREA, ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, 
    UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, NODE_NUM
  </sql>

  <select id="getList" parameterType="com.hxdi.nmjl.condition.iot.DeviceInfoCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_DEVICE_INFO
    <where>
      <if test="condition.stId != null and condition.stId !='' ">
        and ST_ID = #{condition.stId}
      </if>
      <if test="condition.stName != null and condition.stName !='' ">
        and ST_NAME like concat('%',#{condition.stName},'%')
      </if>
      <if test="condition.deviceState != null and condition.deviceState !='' ">
        and DEVICE_STATE = #{condition.deviceState}
      </if>
      <if test="condition.ipa != null and condition.ipa !='' ">
        and IPA = #{condition.ipa}
      </if>
      and ENABLED = 1
    </where>
    ORDER BY CREATE_TIME DESC
  </select>

  <select id="getPage" parameterType="com.hxdi.nmjl.condition.iot.DeviceInfoCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_DEVICE_INFO
    <where>
      <if test="condition.stId != null and condition.stId !='' ">
        and ST_ID = #{condition.stId}
      </if>
      <if test="condition.stName != null and condition.stName !='' ">
        and ST_NAME like concat('%',#{condition.stName},'%')
      </if>
      <if test="condition.deviceState != null and condition.deviceState !='' ">
        and DEVICE_STATE = #{condition.deviceState}
      </if>
      <if test="condition.ipa != null and condition.ipa !='' ">
        and IPA = #{condition.ipa}
      </if>
      and ENABLED = 1
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>