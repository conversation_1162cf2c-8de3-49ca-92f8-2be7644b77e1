package com.hxdi.nmjl.condition.plan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.model.QueryCondition;
import com.hxdi.common.core.mybatis.annotation.Between;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "监察计划查询参数")
@Setter
@Getter
public class InspectionRecordCondition extends QueryCondition {

    @ApiModelProperty(value = "关键字")
    @LIKE(value = "PLAN_CODE,PLAN_NAME,INSPECT_UNIT_NAME", logic = "OR", join = true)
    private String keywords;

    @EQ
    @ApiModelProperty(value = "业务状态：0-未执行，1-执行中，2-已完成，3-已取消")
    private Integer state;

    @ApiModelProperty(value = "检查日期")
    @Between("INSPECT_TIME")
    private RangeBean<Date> inspectTime;


    @EQ
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer enabled = 1;
}
