package com.hxdi.nmjl.service.clientrelated.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.SupplierCommunicationCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierCommunication;
import com.hxdi.nmjl.mapper.clientrelated.SupplierCommunicationMapper;
import com.hxdi.nmjl.service.clientrelated.SupplierCommunicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 供应商沟通记录管理实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-11
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SupplierCommunicationServiceImpl extends BaseServiceImpl<SupplierCommunicationMapper, SupplierCommunication> implements SupplierCommunicationService {

    @Override
    public void create(SupplierCommunication record) {
        // 数据验证
        verifyCommunicationRecord(record);

        // 设置默认值
        if (record.getStatus() == null) {
            record.setStatus(1); // 默认状态：待处理
        }
        if (record.getPriority() == null) {
            record.setPriority(2); // 默认优先级：中
        }
        if (record.getEnabled() == null) {
            record.setEnabled(1); // 默认启用
        }

        baseMapper.insert(record);
    }

    @Override
    public void update(SupplierCommunication record) {
        if (CommonUtils.isEmpty(record.getId())) {
            throw new BaseException("沟通记录ID不能为空");
        }

        // 验证记录是否存在
        SupplierCommunication existRecord = baseMapper.selectById(record.getId());
        if (existRecord == null || existRecord.getEnabled() != 1) {
            throw new BaseException("沟通记录不存在或已删除");
        }

        baseMapper.updateById(record);
    }

    @Override
    public void changeState(String id, Integer enabled) {
        if (CommonUtils.isEmpty(id)) {
            throw new BaseException("沟通记录ID不能为空");
        }

        baseMapper.changeStatus(id, enabled);
    }

    @Override
    public SupplierCommunication getDetail(String id) {
        if (CommonUtils.isEmpty(id)) {
            throw new BaseException("沟通记录ID不能为空");
        }

        SupplierCommunication record = baseMapper.selectById(id);
        if (record == null || record.getEnabled() != 1) {
            throw new BaseException("沟通记录不存在或已删除");
        }

        return record;
    }

    @Override
    public Page<SupplierCommunication> pages(SupplierCommunicationCondition condition) {
        Page<SupplierCommunication> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<SupplierCommunication> lists(SupplierCommunicationCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void updateStatus(String id, Integer status) {
        if (CommonUtils.isEmpty(id)) {
            throw new BaseException("沟通记录ID不能为空");
        }
        if (status == null || status < 1 || status > 3) {
            throw new BaseException("沟通状态错误");
        }

        baseMapper.changeStatus(id, status);
    }

    @Override
    public void batchUpdateStatus(List<String> ids, Integer status) {
        if (CommonUtils.isEmpty(ids)) {
            throw new BaseException("沟通记录ID列表不能为空");
        }
        if (status == null || status < 1 || status > 4) {
            throw new BaseException("沟通状态错误");
        }

        baseMapper.batchUpdateStatus(ids, status);
    }

    @Override
    public void handleRecord(String id, String result, String handlerId, String handlerName) {
        if (CommonUtils.isEmpty(id)) {
            throw new BaseException("沟通记录ID不能为空");
        }

        SupplierCommunication record = getDetail(id);
        record.setResult(result);
        record.setHandlerId(handlerId);
        record.setHandlerName(handlerName);
        record.setHandleTime(new Date());
        record.setStatus(2); // 处理中

        baseMapper.updateById(record);
    }

    @Override
    public void closeRecord(String id) {
        updateStatus(id, 3); // 已处理
    }

    @Override
    public Long countBySupplierId(String supplierId) {
        if (CommonUtils.isEmpty(supplierId)) {
            return 0L;
        }

        return baseMapper.countBySupplierId(supplierId);
    }

    /**
     * 验证沟通记录数据有效性
     *
     * @param record 沟通记录
     */
    private void verifyCommunicationRecord(SupplierCommunication record) {
        if (CommonUtils.isEmpty(record.getSupplierId())) {
            throw new BaseException("供应商ID不能为空");
        }

        if (record.getCommunicationType() == null || record.getCommunicationType() < 1 || record.getCommunicationType() > 5) {
            throw new BaseException("沟通类型参数无效");
        }

        if (CommonUtils.isEmpty(record.getTitle())) {
            throw new BaseException("沟通标题不能为空");
        }

        if (record.getPriority() != null && (record.getPriority() < 1 || record.getPriority() > 4)) {
            throw new BaseException("优先级参数无效");
        }

        if (record.getStatus() != null && (record.getStatus() < 1 || record.getStatus() > 3)) {
            throw new BaseException("沟通状态参数无效");
        }
    }
}
