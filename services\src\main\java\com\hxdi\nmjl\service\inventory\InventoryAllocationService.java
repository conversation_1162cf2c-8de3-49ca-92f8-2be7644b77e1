package com.hxdi.nmjl.service.inventory;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.inventory.InventoryAllocationCondition;
import com.hxdi.nmjl.domain.inventory.InventoryAllocation;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【B_INVENTORY_ALLOCATION(库存调配管理)】的数据库操作Service
 * @createDate 2025-07-31 09:45:14
 */
public interface InventoryAllocationService extends IBaseService<InventoryAllocation> {
    /**
     * 创建或更新调度计划（注意：无法修改已经开始执行的计划）
     *
     * @param inventoryAllocation
     */
    void createOrUpdate(InventoryAllocation inventoryAllocation);

    void removeById(String id);

    /**
     * 查询调度计划及对应的库存及品种信息
     *
     * @param id
     * @return
     */
    InventoryAllocation getMore(String id);

    /**
     * 查询调度计划（带权限控制）
     *
     * @param condition
     * @return
     */
    List<InventoryAllocation> listV1(InventoryAllocationCondition condition);

    /**
     * 分页查询调度计划（带权限控制）
     *
     * @param condition
     * @return
     */
    Page<InventoryAllocation> PageV1(InventoryAllocationCondition condition);

    /**
     * 提交生产订单
     *
     * @param id 生产订单ID
     */
    void submitV1(String id);

    void removeV1(String id);

    /**
     * @param id             生产订单ID
     * @param approveStatus  审核状态
     * @param opinion        审核意见
     */
    void approveV1(String id, Integer approveStatus, String opinion);
}
