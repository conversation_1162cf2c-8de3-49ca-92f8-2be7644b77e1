package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.nmjl.domain.plan.SaleOrderItem;
import com.hxdi.nmjl.mapper.plan.SaleOrderItemMapper;
import com.hxdi.nmjl.service.plan.SaleOrderItemService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SaleOrderItemServiceImpl extends ServiceImpl<SaleOrderItemMapper, SaleOrderItem> implements SaleOrderItemService {

    @Override
    public List<SaleOrderItem> getList(String orderId) {
        return baseMapper.selectList(Wrappers.<SaleOrderItem>lambdaQuery().eq(SaleOrderItem::getOrderId, orderId));
    }

    @Override
    public List<SaleOrderItem> getList(List<String> IdList) {
        return baseMapper.selectList(Wrappers.<SaleOrderItem>lambdaQuery().in(SaleOrderItem::getOrderId, IdList));
    }

    @Override
    public void removeV1(String id) {
        baseMapper.delete(Wrappers.<SaleOrderItem>lambdaQuery().eq(SaleOrderItem::getOrderId, id));
    }

    @Override
    public SaleOrderItem listByCatalogIdAndGrade(String orderId, String catalogId, String grade) {
        return baseMapper.selectOne(Wrappers.<SaleOrderItem>lambdaQuery()
                .eq(SaleOrderItem::getOrderId, orderId)
                .eq(SaleOrderItem::getCatalogId, catalogId)
                .eq(SaleOrderItem::getGrade, grade));
    }
}
