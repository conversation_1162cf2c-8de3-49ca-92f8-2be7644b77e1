package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 品牌型号
 */
@TableName(value = "C_TRADEMARKS")
@Getter
@Setter
public class Trademarks extends Entity<Trademarks> {

    private static final long serialVersionUID = 6684379199827737516L;

    /**
     * 品牌ID
     */
    @TableId(value = "BRAND_ID", type = IdType.ASSIGN_ID)
    private String brandId;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND_NAME")
    private String brandName;

    /**
     * 分类编码
     */
    @TableField(value = "CLASSIFICATION_CODE")
    private String classificationId;

    /**
     * 排序
     */
    @TableField(value = "SEQ")
    private Integer seq;

    /**
     * 修改时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * --------------------
     */

    public Trademarks() {
    }

    public Trademarks(String brandName, String classificationId) {
        this.brandName = brandName;
        this.classificationId = classificationId;
    }
}
