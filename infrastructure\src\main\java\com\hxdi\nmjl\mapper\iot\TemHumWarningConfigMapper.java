package com.hxdi.nmjl.mapper.iot;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.iot.TemHumWarningConfigCondition;
import com.hxdi.nmjl.domain.iot.TemHumWarningConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TemHumWarningConfigMapper extends SuperMapper<TemHumWarningConfig> {
    @DataPermission
    List<TemHumWarningConfig> selectListV1(@Param("condition") TemHumWarningConfigCondition condition);

    @DataPermission
    IPage<TemHumWarningConfig> selectPageV1(Page<TemHumWarningConfig> page, @Param("condition") TemHumWarningConfigCondition condition);
}