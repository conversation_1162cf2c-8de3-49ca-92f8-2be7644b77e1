package com.hxdi.nmjl.condition.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 */
@ApiModel(description = "粮食筹措计划明细查询条件")
@Setter
@Getter
public class ProcurementPlanDetailCondition extends QueryCondition {


    @ApiModelProperty(value = "明细ID")
    private String id;

    @ApiModelProperty(value = "计划ID")
    private String planId;

    @ApiModelProperty(value = "品类名称")
    private String classificationName;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "质量等级")
    private String grade;

    @ApiModelProperty(value = "需求下限（大于等于）")
    private BigDecimal minLimit;

    @ApiModelProperty(value = "需求上限（小于等于）")
    private BigDecimal maxLimit;

    @ApiModelProperty(value = "产地及年份要求")
    private String requirement;

    @ApiModelProperty(value = "首次交货开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date firstDeliveryTimeStart;

    @ApiModelProperty(value = "首次交货结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date firstDeliveryTimeEnd;

}
