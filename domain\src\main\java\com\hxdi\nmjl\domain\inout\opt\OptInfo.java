package com.hxdi.nmjl.domain.inout.opt;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 作业登记表
 * @TableName
 */
@ApiModel(description = "作业登记")
@TableName("B_OPT_INFO")
@Setter
@Getter
public class OptInfo implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("OPT_CODE")
    @ApiModelProperty(value = "作业编号")
    private String optCode;

    @TableField("STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @TableField("STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @TableField("ST_ID")
    @ApiModelProperty(value = "仓房ID")
    private String stId;

    @TableField("ST_NAME")
    @ApiModelProperty(value = "仓房")
    private String stName;

    @TableField("OPT_TYPE")
    @ApiModelProperty(value = "任务类型")
    private String optType;

    @TableField("OPT_TIME")
    @ApiModelProperty(value = "作业时间")
    private Date optTime;

    @TableField("FZR")
    @ApiModelProperty(value = "负责人")
    private String fzr;

    @TableField("EMPS")
    @ApiModelProperty(value = "作业人员")
    private String emps;

    @TableField("BIZ_TYPE")
    @ApiModelProperty(value = "作业类型")
    private String bizType;

    @TableField("REMARKS")
    @ApiModelProperty(value = "记录")
    private String remarks;

    @TableField("REQUIREMENT")
    @ApiModelProperty(value = "处理要求")
    private String requirement;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态：1-有效，0-删除")
    private Integer enabled;

    @TableField("ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID",fill= FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    @TableField("LOCATION")
    @ApiModelProperty(value = "具体位置")
    private String location;


    /**
     * ---------------------------------非数据库对象---------------------------------
     */

    @TableField(exist = false)
    @ApiModelProperty(value = "环境监控数据")
    private List<OptData> optDataList;

    @TableField(exist = false)
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @TableField(exist = false)
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @TableField(exist = false)
    @ApiModelProperty(value = "数据来源")
    private String dataOrigin;
}
