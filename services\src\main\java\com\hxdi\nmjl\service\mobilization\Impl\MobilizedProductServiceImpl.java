package com.hxdi.nmjl.service.mobilization.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.Classification;
import com.hxdi.nmjl.domain.mobilization.MobilizedProduct;
import com.hxdi.nmjl.mapper.mobilization.MobilizedProductMapper;
import com.hxdi.nmjl.service.mobilization.MobilizedProductCatalogService;
import com.hxdi.nmjl.service.mobilization.MobilizedProductService;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MobilizedProductServiceImpl extends BaseServiceImpl<MobilizedProductMapper, MobilizedProduct> implements MobilizedProductService {

    @Autowired
    private MobilizedProductCatalogService mobilizedProductCatalogService;

    @Override
    public Page<MobilizedProduct> pages(MobilizedProductCondition condition) {
        Page<MobilizedProduct> page = condition.newPage();
        baseMapper.selectPageV1(page,condition);
        return page;
    }

    @Override
    public List<MobilizedProduct> lists(MobilizedProductCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void create(MobilizedProduct mobilizedProduct) {
        LambdaQueryWrapper<MobilizedProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MobilizedProduct::getEnterpriseId, mobilizedProduct.getEnterpriseId())
                .eq(MobilizedProduct::getCatalogId, mobilizedProduct.getCatalogId())
                .eq(MobilizedProduct::getEnabled, 1);
        if (baseMapper.selectCount(queryWrapper) > 0) {
            BizExp.pop("商品信息已存在");
        }

        mobilizedProduct.setState(0);
        this.save(mobilizedProduct);
    }

    @Override
    public void update(MobilizedProduct mobilizedProduct) {
        LambdaQueryWrapper<MobilizedProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MobilizedProduct::getEnterpriseId, mobilizedProduct.getEnterpriseId())
                .eq(MobilizedProduct::getCatalogId, mobilizedProduct.getCatalogId())
                .ne(MobilizedProduct::getId, mobilizedProduct.getId())
                .eq(MobilizedProduct::getEnabled, 1);

        if (baseMapper.selectCount(queryWrapper) > 0) {
            BizExp.pop("商品信息已存在");
        }

        this.updateById(mobilizedProduct);
    }

    @Override
    public void remove(String id) {
        MobilizedProduct product = baseMapper.selectById(id);
        product.setEnabled(0);
        this.updateById(product);
    }

    @Override
    public MobilizedProduct getDetail(String goodsId) {
        return baseMapper.selectById(goodsId);
    }

    @Override
    public void approve(String id) {
        MobilizedProduct mobilizedProduct = baseMapper.selectById(id);
        mobilizedProduct.setState(1);
        this.updateById(mobilizedProduct);
    }
}
