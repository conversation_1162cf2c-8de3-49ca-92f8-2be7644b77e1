package com.hxdi.nmjl.controller.emergency;

import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.emergency.CommPlanCondition;
import com.hxdi.nmjl.domain.emergency.CommPlan;
import com.hxdi.nmjl.service.emergency.CommPlanService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "规划沟通管理计划接口")
@RequestMapping("/commPlan")
@RestController
public class CommPlanController extends CommonApiController<CommPlanService, CommPlan, CommPlanCondition> {


}
