package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "品牌信息查询条件")
@Getter
@Setter
public class BrandInfoCondition extends QueryCondition {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "品牌名称（模糊查询）")
    private String brandName;

    @ApiModelProperty(value = "归属单位ID")
    private String orgId;

    @ApiModelProperty(value = "归属单位名称（模糊查询）")
    private String orgName;

    @ApiModelProperty(value = "品牌类型：1-自有，2-外部")
    private Integer brandType;

    @ApiModelProperty(value = "产品类别：字典CPLB")
    private String productCategory;

    @ApiModelProperty(value = "创建日期（开始）")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建日期（结束）")
    private Date createTimeEnd;

    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}