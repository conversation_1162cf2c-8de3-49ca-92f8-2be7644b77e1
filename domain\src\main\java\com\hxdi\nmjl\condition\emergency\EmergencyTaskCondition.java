package com.hxdi.nmjl.condition.emergency;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "应急任务查询条件")
@Getter
@Setter
public class EmergencyTaskCondition extends QueryCondition {

    @ApiModelProperty(value = "调度编号")
    private String scheduleNo;

    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "司机姓名")
    private String driver;

    @ApiModelProperty(value = "司机手机号")
    private String mobile;

    @ApiModelProperty(value = "运送状态：1-进行中，2-已送达")
    private Integer states;

}
