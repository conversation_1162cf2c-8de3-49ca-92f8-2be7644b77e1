package com.hxdi.nmjl.controller.emergency;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.emergency.CommSchemaCondition;
import com.hxdi.nmjl.domain.emergency.CommSchema;
import com.hxdi.nmjl.service.emergency.CommSchemaService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "规划沟通管理方案接口")
@RequestMapping("/commSchema")
@RestController
public class CommSchemaController extends CommonApiController<CommSchemaService, CommSchema, CommSchemaCondition> {


    @GetMapping("/submit")
    public ResultBody commit(String id) {
        bizService.commit(id);
        return ResultBody.ok();
    }
}
