package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.plan.PackageApplyCondition;
import com.hxdi.nmjl.domain.plan.ProductionPackageApply;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.ProductionPackageApplyMapper;
import com.hxdi.nmjl.service.plan.ProductionPackageApplyService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ProductionPackageApplyServiceImpl extends BaseServiceImpl<ProductionPackageApplyMapper, ProductionPackageApply> implements ProductionPackageApplyService {

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public void saveV1(ProductionPackageApply apply) {

        //生成包装物编码
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("PROD_PKG_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        apply.setApplyCode((String) businessCode.getValue());
        //保存包装物信息
        this.save(apply);
    }

    @Override
    public void updateV1(ProductionPackageApply apply) {
        this.updateById(apply);
    }

    @Override
    public void removeV1(String id) {
        //逻辑删除
        this.update(Wrappers.<ProductionPackageApply>lambdaUpdate()
                .eq(ProductionPackageApply::getId, id)
                .set(ProductionPackageApply::getEnabled, 0));
    }

    @Override
    public void approveV1(String id, Integer approveStatus, String opinion) {
        ProductionPackageApply apply = this.getById(id);
        if (apply == null) {
            throw new BaseException("包装申请不存在");
        }
        // 校验状态：已审核的不能重复审核
        if (apply.getApproveStatus() != 0) {
            throw new BaseException("该申请已审核，无法重复操作");
        }
        apply.setApproveStatus(approveStatus);
        apply.setApproveOpinion(opinion);
        apply.setApprover(SecurityHelper.obtainUser().getNickName());
        apply.setApproveTime(new Date());
        this.updateById(apply);
    }

    @Override
    public void submitV1(String id) {
        ProductionPackageApply apply = this.getById(id);
        if (apply == null) {
            throw new BaseException("包装申请不存在");
        }
        if (apply.getApproveStatus() != null && apply.getApproveStatus() != 2) {
            throw new BaseException("该申请已提交，无法重复操作");
        }
        apply.setApproveStatus(0);
        apply.setApproveOpinion("");
        apply.setApprover("");
        this.updateById(apply);
    }

    @Override
    public List<ProductionPackageApply> listV1(PackageApplyCondition condition) {
        return baseMapper.listV1(condition);
    }

    @Override
    public Page<ProductionPackageApply> pageV1(PackageApplyCondition condition) {
        return baseMapper.pageV1(condition.newPage(), condition);
    }

}
