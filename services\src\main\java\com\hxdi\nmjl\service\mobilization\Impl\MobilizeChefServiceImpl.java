package com.hxdi.nmjl.service.mobilization.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.mobilization.MobilizeChefCondition;
import com.hxdi.nmjl.domain.mobilization.MobilizeChefExp;
import com.hxdi.nmjl.domain.mobilization.MobilizeChefInfo;
import com.hxdi.nmjl.mapper.mobilization.MobilizeChefMapper;
import com.hxdi.nmjl.service.mobilization.MobilizeChefExpService;
import com.hxdi.nmjl.service.mobilization.MobilizeChefService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 厨师动员信息业务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MobilizeChefServiceImpl extends BaseServiceImpl<MobilizeChefMapper, MobilizeChefInfo> implements MobilizeChefService {

    @Resource
    private MobilizeChefExpService mobilizeChefExpService;

    @Override
    public Page<MobilizeChefInfo> getPages(MobilizeChefCondition condition) {
        Page<MobilizeChefInfo> page = condition.newPage();
        return baseMapper.getPages(condition, page);
    }

    @Override
    public List<MobilizeChefInfo> getList(MobilizeChefCondition condition) {
        return baseMapper.getList(condition);
    }

    @Override
    public MobilizeChefInfo getDetail(String Id) {
        MobilizeChefInfo item = baseMapper.selectById(Id);
        item.setMobilizeChefExp(mobilizeChefExpService.getList(Id));
        return item;
    }

    @Override
    public void add(MobilizeChefInfo mobilizeChefInfo) {
        mobilizeChefInfo.setApproveStatus(0);
        mobilizeChefInfo.setEnabled(1);
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        String tenantId = baseUserDetails.getTenantId(); // 租户id
        // 组织层级
        String dataHierarchyId = baseUserDetails.getDataHierarchyId();
        mobilizeChefInfo.setCreateId(baseUserDetails.getUserId());
        mobilizeChefInfo.setUpdateId(baseUserDetails.getUserId());
        mobilizeChefInfo.setTenantId(tenantId);
        mobilizeChefInfo.setDataHierarchyId(dataHierarchyId);
        if (!this.save(mobilizeChefInfo)) {
            BizExp.pop("厨师信息保存失败");
        }
        MobilizeChefExp detailList = mobilizeChefInfo.getMobilizeChefExp();
        if (detailList == null) {
            BizExp.pop("请填写厨师信息详情!");
        }
        detailList.setChefId(mobilizeChefInfo.getId());
        if (!mobilizeChefExpService.save(detailList)) {
            BizExp.pop("厨师信息详情保存失败");
        }
    }

    @Override
    public void update(MobilizeChefInfo mobilizeChefInfo) {
        if (mobilizeChefInfo.getApproveStatus() == 1 || mobilizeChefInfo.getApproveStatus() == 2) {
            BizExp.pop("审批状态为已提交或审批通过的招标信息无法编辑!");
        }
        // 1. 更新主表信息
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        mobilizeChefInfo.setUpdateId(baseUserDetails.getUserId());
        if (!this.updateById(mobilizeChefInfo)) {
            BizExp.pop("厨师信息更新失败");
        }
        // 2. 获取新的明细数据
        MobilizeChefExp detailList = mobilizeChefInfo.getMobilizeChefExp();
        if (detailList == null) {
            BizExp.pop("请填写厨师信息详情!");
        }
        // 3. 删除原有详情
        mobilizeChefExpService.remove(Wrappers.<MobilizeChefExp>lambdaQuery().eq(MobilizeChefExp::getChefId, mobilizeChefInfo.getId()));
        detailList.setChefId(mobilizeChefInfo.getId());
        if (!mobilizeChefExpService.save(detailList)) {
            BizExp.pop("厨师信息详情保存失败");
        }
    }

    @Override
    public boolean delete(String Id) {
        MobilizeChefInfo mobilizeChefInfo = this.getById(Id);
        if (mobilizeChefInfo == null) {
            BizExp.pop("厨师信息不存在");
        }
        if (mobilizeChefInfo.getApproveStatus() == 1 || mobilizeChefInfo.getApproveStatus() == 2) {
            BizExp.pop("审批状态为已提交或审批通过的信息无法删除!");
        }
        mobilizeChefExpService.remove(Wrappers.<MobilizeChefExp>lambdaQuery().eq(MobilizeChefExp::getChefId, Id));
        return this.update(Wrappers.<MobilizeChefInfo>lambdaUpdate().eq(MobilizeChefInfo::getId, Id).set(MobilizeChefInfo::getEnabled, 0));
    }

    @Override
    public void approve(String id, String approveOpinion) {
        //审批通过
        changeApproveStatus(id, 2, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 3, approveOpinion);
    }

    @Override
    public void submit(String id) {
        // 审核状态：0-待提交，1-待审核，2-已审核，3-驳回
        changeApproveStatus(id, 1, null);
    }

    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        MobilizeChefInfo mobilizeChefInfo = baseMapper.selectById(id);
        mobilizeChefInfo.setApproveStatus(approveStatus);
        if (approveStatus == 2 || approveStatus == 3) {
            mobilizeChefInfo.setApprover(SecurityHelper.obtainUser().getNickName());
            mobilizeChefInfo.setApproveTime(new Date());
            mobilizeChefInfo.setApproveOpinion(approveOpinion);
        }
        baseMapper.updateById(mobilizeChefInfo);
    }
}
