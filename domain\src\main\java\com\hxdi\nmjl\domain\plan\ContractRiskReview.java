package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 合同风险审查表
 */
@ApiModel(description = "合同风险审查表")
@Getter
@Setter
@TableName(value = "B_CONTRACT_RISK_REVIEW")
public class ContractRiskReview extends Entity<ContractRiskReview> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 合同ID
     */
    @TableField(value = "CONTRACT_ID")
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 合同号
     */
    @TableField(value = "CONTRACT_CODE")
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 合同名称
     */
    @TableField(value = "CONTRACT_NAME")
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 客户ID
     */
    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    /**
     * 客户名称
     */
    @TableField(value = "CLIENT_NAME")
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    /**
     * 客户信用代码
     */
    @TableField(value = "CREDIT_CODE")
    @ApiModelProperty(value = "客户信用代码")
    private String creditCode;

    /**
     * 风险类型：1-供应商风险，2-合同条款风险，3-执行风险，4-财务风险，5-法律风险，9-其他风险
     */
    @TableField(value = "RISK_TYPE")
    @ApiModelProperty(value = "风险类型：1-供应商风险，2-合同条款风险，3-执行风险，4-财务风险，5-法律风险，9-其他风险")
    private String riskType;

    /**
     * 风险描述
     */
    @TableField(value = "RISK_DESCRIPTION")
    @ApiModelProperty(value = "风险描述")
    private String riskDescription;

    /**
     * 风险等级：1-低风险，2-中风险，3-高风险，4-极高风险
     */
    @TableField(value = "RISK_LEVEL")
    @ApiModelProperty(value = "风险等级：1-低风险，2-中风险，3-高风险，4-极高风险")
    private String riskLevel;

    /**
     * 影响程度：1-轻微，2-一般，3-严重，4-非常严重
     */
    @TableField(value = "IMPACT_LEVEL")
    @ApiModelProperty(value = "影响程度：1-轻微，2-一般，3-严重，4-非常严重")
    private String impactLevel;

    /**
     * 发生概率：1-很低，2-较低，3-中等，4-较高，5-很高
     */
    @TableField(value = "PROBABILITY")
    @ApiModelProperty(value = "发生概率：1-很低，2-较低，3-中等，4-较高，5-很高")
    private String probability;

    /**
     * 处理用户ID
     */
    @TableField(value = "USER_ID")
    @ApiModelProperty(value = "处理用户ID")
    private String userId;

    /**
     * 处理人
     */
    @TableField(value = "USER_NAME")
    @ApiModelProperty(value = "处理人")
    private String userName;

    /**
     * 处理时间
     */
    @TableField(value = "DISPOSE_TIME")
    @ApiModelProperty(value = "处理时间")
    private Date disposeTime;

    /**
     * 处理状态:0-未处理，1-已处理
     */
    @TableField(value = "DISPOSE_STATE")
    @ApiModelProperty(value = "处理状态:0-未处理，1-已处理")
    private Integer disposeState;

    /**
     * 处理描述
     */
    @TableField(value = "DISPOSE_DESC")
    @ApiModelProperty(value = "处理描述")
    private String disposeDesc;

    /**
     * 机构ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    /**
     * 机构名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;

    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}