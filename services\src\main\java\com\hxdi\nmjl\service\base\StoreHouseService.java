package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.base.StorehouseCondition;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.dto.base.StoreCapacityDTO;

import java.util.List;
import java.util.Map;

public interface StoreHouseService extends IBaseService<StoreHouse> {

    /**
     * 新增
     *
     * @param storehouse
     */
    StoreHouse create(StoreHouse storehouse);

    /**
     * 更新
     *
     * @param storehouse
     */
    StoreHouse update(StoreHouse storehouse);

    /**
     * 修改状态：1-启用、0-禁用、7-删除
     *
     * @param id
     * @param state
     */
    StoreHouse changeState(String id, Integer state);

    /**
     * 根据军供站ID查询仓房容量
     *
     * @param storeId
     * @return
     */
    List<StoreCapacityDTO> getCapacityByStoreId(List<String> storeId);

    /**
     * 根据地区编码，查询对应地区下属各个二级地区的仓房汇总容量
     *
     * @param areaCode
     * @return key:地区编码 value:仓房容量
     */
    Map<String, String> getStorehouseListByAreaCodeList(String areaCode);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    StoreHouse getByUniqueKey(String id);

    /**
     * 分页
     *
     * @param condition
     * @return
     */
    Page<StoreHouse> pages(StorehouseCondition condition);

    /**
     * 列表
     *
     * @param condition
     * @return
     */
    List<StoreHouse> lists(StorehouseCondition condition);
}
