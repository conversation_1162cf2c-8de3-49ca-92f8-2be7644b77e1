package com.hxdi.nmjl.service.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.common.WarningInfoCondition;
import com.hxdi.nmjl.domain.common.WarningInfo;

import java.util.List;

public interface WarningInfoService extends IBaseService<WarningInfo> {

    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    Page<WarningInfo> getPageByCondition(WarningInfoCondition condition);

    /**
     * 列表查询
     *
     * @param condition
     * @return
     */
    List<WarningInfo> getListByCondition(WarningInfoCondition condition);

    /**
     * 大屏查询接口
     *
     * @param condition
     * @return
     */
    Page<WarningInfo> getBigScreenPageByCondition(WarningInfoCondition condition);

    void handle(String id, String disposeDesc);
}
