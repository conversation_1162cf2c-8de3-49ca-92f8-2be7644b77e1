package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 合同信息
 */
@ApiModel(description = "合同信息")
@Getter
@Setter
@TableName("B_CONTRACT_INFO")
public class ContractInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String planId;

    /**
     * 招标ID
     */
    @ApiModelProperty(value = "招标ID")
    private String bidId;

    @ApiModelProperty(value = "招标项目名称")
    private String projectName;

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractCode;

    /**
     * 原合同编号
     */
    @ApiModelProperty(value = "原合同编号")
    private String originCode;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String name;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型：1-采购合同，2-销售合同")
    private String bizType;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    /**
     * 客户信用代码
     */
    @ApiModelProperty(value = "客户信用代码")
    private String creditCode;

    /**
     * 签订人
     */
    @ApiModelProperty(value = "签订人")
    private String signer;

    /**
     * 签订人身份证号
     */
    @ApiModelProperty(value = "签订人身份证号")
    private String sidCard;

    /**
     * 签订时间
     */
    @ApiModelProperty(value = "签订时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signedDate;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 截止时间
     */
    @ApiModelProperty(value = "截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * 合同数量
     */
    @ApiModelProperty(value = "合同数量")
    private BigDecimal contractQty;

    /**
     * 履约数量
     */
    @ApiModelProperty(value = "履约数量")
    private BigDecimal completedQty;

    /**
     * 合同总金额
     */
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal budgetAmount;

    /**
     * 履约保证金
     */
    @ApiModelProperty(value = "履约保证金")
    private BigDecimal performanceBondAmount;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式:1-批量")
    private Integer settlementMethod;

    /**
     * 交货地址
     */
    @ApiModelProperty(value = "交货地址")
    private String rcvAddr;

    /**
     * 交货详细地址
     */
    @ApiModelProperty(value = "交货详细地址")
    private String rcvDetailAddr;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
    private String orgBankNo;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    private String orgBank;

    /**
     * 客户银行卡号
     */
    @ApiModelProperty(value = "客户银行卡号")
    private String clientBankNo;

    /**
     * 客户开户行
     */
    @ApiModelProperty(value = "客户开户行")
    private String clientBank;

    /**
     * 储备性质:字典CBXZ
     */
    @ApiModelProperty(value = "储备性质:字典CBXZ")
    private String reserveLevel;

    /**
     * 业务状态：0-待提交 1-进行中，2-已完成
     */
    @ApiModelProperty(value = "业务状态：0-待提交 1-进行中，2-已完成")
    private Integer state;

    /**
     * 状态:1-有效，0-删除
     */
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String attachements;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @ApiModelProperty(value = "审核状态")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @ApiModelProperty(value = "创建id")
    @TableField(fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @ApiModelProperty(value = "更新id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    @TableField(fill = FieldFill.INSERT)
    private String dataHierarchyId;


    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 结算状态
     */
    @TableField(value = "SETTLEMENT_STATUS", exist = false)
    @ApiModelProperty(value = "结算状态：1-未结算，2-部分结算，3-已结算")
    private Integer settlementStatus;


    /**
     * 合同详情
     */
    @TableField(exist = false)
    private List<ContractDetail> detailList;
}
