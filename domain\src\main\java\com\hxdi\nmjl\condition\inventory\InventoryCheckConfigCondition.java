package com.hxdi.nmjl.condition.inventory;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 库存盘点计划查询条件
 */
@Getter
@Setter
@ApiModel(description = "库存盘点计划查询条件")
public class InventoryCheckConfigCondition extends QueryCondition {

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;
} 