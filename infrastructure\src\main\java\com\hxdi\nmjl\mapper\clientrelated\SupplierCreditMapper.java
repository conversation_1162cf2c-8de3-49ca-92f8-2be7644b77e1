package com.hxdi.nmjl.mapper.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.clientrelated.SupplierCredit;
import com.hxdi.nmjl.condition.clientrelated.SupplierCreditCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商信用管理数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@Mapper
public interface SupplierCreditMapper extends SuperMapper<SupplierCredit> {


    void changeState(@Param("id") String id, @Param("enabled") Integer enabled);

    Page<SupplierCredit> selectPageV1(Page<SupplierCredit> page, @Param("condition") SupplierCreditCondition condition);

    List<SupplierCredit> selectListV1(@Param("condition") SupplierCreditCondition condition);
}
