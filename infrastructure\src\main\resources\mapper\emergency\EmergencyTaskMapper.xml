<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyTaskMapper">

    <sql id="Base_Column_List">
        ID, SCHEDULE_NO, VEHICLE_TYPE, VEHICLE_NO, DRIVER, MOBILE,
    STATE, START_TIME, ARRIVED_TIME, ENABLED, CREATE_TIME,
    UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyTask">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="SCHEDULE_NO" jdbcType="VARCHAR" property="scheduleNo"/>
        <result column="VEHICLE_TYPE" jdbcType="VARCHAR" property="vehicleType"/>
        <result column="VEHICLE_NO" jdbcType="VARCHAR" property="vehicleNo"/>
        <result column="DRIVER" jdbcType="VARCHAR" property="driver"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="STATE" jdbcType="INTEGER" property="state"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="ARRIVED_TIME" jdbcType="TIMESTAMP" property="arrivedTime"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_EMERGENCY_TASK
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.scheduleNo)">
                and SCHEDULE_NO like concat('%', #{condition.scheduleNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleType)">
                and VEHICLE_TYPE = #{condition.vehicleType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                and VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.driver)">
                and DRIVER like concat('%', #{condition.driver}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.mobile)">
                and MOBILE = #{condition.mobile}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.states)">
                and STATE = #{condition.states}
            </if>
            order by CREATE_TIME desc
        </where>
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_EMERGENCY_TASK
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.scheduleNo)">
                and SCHEDULE_NO = #{condition.scheduleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleType)">
                and VEHICLE_TYPE = #{condition.vehicleType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                and VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.driver)">
                and DRIVER like concat('%', #{condition.driver}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.mobile)">
                and MOBILE = #{condition.mobile}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.states)">
                and STATE = #{condition.states}
            </if>
            order by CREATE_TIME desc
        </where>
    </select>

</mapper>
