package com.hxdi.nmjl.condition.iot;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: nmjl-service
 * @description: 温湿度检测统计表查询条件
 * @author: 王贝强
 * @create: 2025-07-25 11:17
 */
@Setter
@Getter
public class TemHumDayRecordCondition extends QueryCondition {

    @ApiModelProperty(value = "仓房ID ','分割")
    String stId;

    @ApiModelProperty(value = "库点ID ','分割")
    String storeId;

    @ApiModelProperty(value = "统计开始时间")
    String startTime;

    @ApiModelProperty(value = "统计结束时间")
    String endTime;
}
