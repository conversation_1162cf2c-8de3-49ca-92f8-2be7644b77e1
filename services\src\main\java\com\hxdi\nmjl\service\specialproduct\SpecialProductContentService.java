package com.hxdi.nmjl.service.specialproduct;


import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductContent;

/**
 * 地方特色产品描述服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/16
 */
public interface SpecialProductContentService extends IBaseService<SpecialProductContent> {

    /**
     * 创建产品描述
     * @param productId 产品ID
     * @param specialProductContent 产品描述内容
     */
    void create(String productId, String specialProductContent);

    /**
     * 更新产品描述
     * @param productId 产品ID
     * @param specialProductContent 产品描述内容
     */
    void update(String productId, String specialProductContent);

    /**
     * 获取产品描述
     * @param productId 产品ID
     * @return 产品描述内容
     */
    String getDetail(String productId);

    /**
     * 删除产品描述
     * @param productId 产品ID
     */
    void remove(String productId);


}
