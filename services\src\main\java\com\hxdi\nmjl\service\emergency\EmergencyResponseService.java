package com.hxdi.nmjl.service.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.emergency.EmergencyResponse;
import com.hxdi.nmjl.condition.emergency.EmergencyResponseCondition;

import java.util.List;

/**
 * 应急预案管理服务接口
 */
public interface EmergencyResponseService extends IBaseService<EmergencyResponse> {

    /**
     * 查询应急预案详情
     *
     * @param id 应急预案ID
     * @return EmergencyResponse
     */
    EmergencyResponse getDetail(String id);

    /**
     * 分页查询应急预案信息
     *
     * @param condition 查询条件
     * @return Page<EmergencyResponse>
     */
    Page<EmergencyResponse> getPages(EmergencyResponseCondition condition);

    /**
     * 列表查询应急预案信息
     *
     * @param condition 查询条件
     * @return List<EmergencyResponse>
     */
    List<EmergencyResponse> getList(EmergencyResponseCondition condition);

    /**
     * 新增应急预案信息
     *
     * @param emergencyResponse 应急预案信息
     */
    void add(EmergencyResponse emergencyResponse);

    /**
     * 更新应急预案信息
     *
     * @param emergencyResponse 应急预案信息
     */
    void update(EmergencyResponse emergencyResponse);

    /**
     * 删除应急预案信息
     *
     * @param id 应急预案ID
     * @return boolean
     */
    boolean delete(String id);

    /**
     * 审核
     *
     * @param id             调整ID
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id             调整ID
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     *
     * @param id 调整ID
     */
    void submit(String id);
}
