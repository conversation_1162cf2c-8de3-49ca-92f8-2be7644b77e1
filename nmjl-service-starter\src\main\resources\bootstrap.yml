server:
  port: ${app.port}
  undertow:
    buffer-size: 1024
    direct-buffers: true
  servlet:
    session:
      timeout: PT30S
spring:
  application:
    name: ${artifactId}
  cloud:
    #手动配置Bus id,
    bus:
      id: ${artifactId}:${app.port}
    nacos:
      username: ${auth.username}
      password: ${auth.password}
      config:
        namespace: ${config.namespace}
        server-addr: ${config.server-addr}
        group: ${config.group}
        shared-configs:
          - dataId: common.properties
            refresh: true
          - dataId: manage_db.properties
            refresh: false
          - dataId: redis.properties
            refresh: false
          - dataId: rabbitmq.properties
            refresh: false
      discovery:
        namespace: ${config.namespace}
        server-addr: ${discovery.server-addr}
        register-enabled: false  #${non.debug}

  main:
    allow-bean-definition-overriding: true
  #解决restful 404错误 spring.mvc.throw-exception-if-no-handler-found=true spring.resources.add-mappings=false
  mvc:
    throw-exception-if-no-handler-found: true
  resources:
    add-mappings: false
  profiles:
    active: ${profile.name}


management:
  endpoints:
    web:
      exposure:
        include: '*'

knife4j:
  enable: ${api.debug}
  setting:
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: "Apache License 2.0 | Copyright  2025-[军粮]"

cloud:
  swagger2:
    enabled: ${api.debug}
    description: 业务平台
    title: 业务平台

#mybatis plus 设置
mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.hxdi.nmjl.entity
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto
  configuration:
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
    aggressive-lazy-loading: false
    lazy-load-trigger-methods:


amap:
  key: b083a54be9da6e1449f28258c71e9cff
