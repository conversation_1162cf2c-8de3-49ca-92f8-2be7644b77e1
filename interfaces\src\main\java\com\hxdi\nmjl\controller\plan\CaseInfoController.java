package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.CaseInfoCondition;
import com.hxdi.nmjl.domain.plan.CaseInfo;
import com.hxdi.nmjl.service.plan.CaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <案件管理接口>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "案件管理")
@RestController
@RequestMapping("/caseInfo")
public class CaseInfoController extends BaseController<CaseInfoService, CaseInfo> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<CaseInfo>> getPages(CaseInfoCondition condition) {
        return ResultBody.ok().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<CaseInfo>> getList(CaseInfoCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<CaseInfo> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除案件信息")
    @PostMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.ok().data(bizService.delete(id));
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody CaseInfo caseInfo) {
        if (CommonUtils.isEmpty(caseInfo.getId())) {
            bizService.add(caseInfo);
        } else {
            bizService.update(caseInfo);
        }
        return ResultBody.ok();
    }
}
