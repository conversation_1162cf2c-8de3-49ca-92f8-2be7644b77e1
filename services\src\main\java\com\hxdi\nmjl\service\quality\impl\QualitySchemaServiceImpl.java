package com.hxdi.nmjl.service.quality.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.quality.QuatitySchemaCondition;
import com.hxdi.nmjl.domain.quality.QualityItem;
import com.hxdi.nmjl.domain.quality.QualitySchema;
import com.hxdi.nmjl.dto.quality.QualitySchemaAndItems;
import com.hxdi.nmjl.mapper.quality.QualitySchemaMapper;
import com.hxdi.nmjl.service.quality.QualityItemService;
import com.hxdi.nmjl.service.quality.QualitySchemaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class QualitySchemaServiceImpl extends BaseServiceImpl<QualitySchemaMapper, QualitySchema> implements QualitySchemaService {

    @Resource
    private QualityItemService itemService;

    @Override
    public void insertOrUpdate(QualitySchemaAndItems schemaAndItems) {
        if (CommonUtils.isEmpty(schemaAndItems)) {
            return;
        }
        QualitySchema schema = schemaAndItems.convertToQualitySchema();

        if (schema.getIsdefault() == 1) {
            LambdaQueryWrapper<QualitySchema> queryWrapper = new LambdaQueryWrapper<QualitySchema>()
                    .eq(QualitySchema::getClassificationId, schema.getClassificationId())
                    .eq(QualitySchema::getIsdefault, 1)
                    .eq(QualitySchema::getEnabled, StrPool.State.ENABLE);
            Long ret = baseMapper.selectCount(queryWrapper);
            if (ret != null && ret > 0) {
                throw new BaseException("该品类已存在默认质检方案！");
            }
        }

        // 新增
        if (CommonUtils.isEmpty(schema.getId())) {
            baseMapper.insert(schema);
            if (!schemaAndItems.getItems().isEmpty()) {
                //将返回的方案Id写入到对应的标准项中
                List<QualityItem> items = schemaAndItems.getItems();
                items.forEach(item -> item.setSchemaId(schema.getId()));
                itemService.insertOrUpdate(schemaAndItems.getItems());
            }
        } else {
            //存在则更新
            QualitySchema qualitySchema = getById(schemaAndItems.getId());
            if (CommonUtils.isNotEmpty(qualitySchema)) {
                baseMapper.updateById(schema);
                //更新后的标准项列表
                List<QualityItem> items = schemaAndItems.getItems();
                //更新前的标准项Id列表
                List<String> idList = itemService.getList(schema.getId()).stream().map(QualityItem::getId).collect(Collectors.toList());
                items.forEach(item -> {
                    item.setSchemaId(schema.getId());
                    idList.remove(item.getId());
                });
                //删除多余的标准项
                idList.forEach(id -> itemService.removeById(id));
                //更新原有的标准项
                itemService.insertOrUpdate(schemaAndItems.getItems());
            }
        }
    }

    @Override
    public void changeStatus(Serializable schemaId, Integer status) {
        if (Objects.equals(status, StrPool.State.DISABLE)) {
            QualitySchema schema = baseMapper.selectById(schemaId);
            if (schema.getIsdefault() == 1) {
                throw new BaseException("默认质检方案不允许禁用！");
            }
        }
        baseMapper.changeStatus(schemaId, status);
    }

    @Override
    public QualitySchemaAndItems getSchemaAndItems(Serializable schemaId) {
        QualitySchema schema = baseMapper.selectById(schemaId);
        if (CommonUtils.isNotEmpty(schema)) {
            List<QualityItem> items = itemService.getList(schemaId);
            return new QualitySchemaAndItems(schema, items);
        }
        return null;
    }

    @Override
    public QualitySchemaAndItems selectDefaultByClassificationId(Serializable classificationId) {

        QualitySchema schema = baseMapper.selectDefaultByClassificationId(classificationId);
        //查询对应的标准项
        if (CommonUtils.isNotEmpty(schema)) {
            List<QualityItem> items = itemService.getList(schema.getId());
            return new QualitySchemaAndItems(schema, items);
        }
        return null;
    }

    @Override
    public List<QualitySchema> getList(QuatitySchemaCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public Page<QualitySchema> getPage(QuatitySchemaCondition condition) {
        Page<QualitySchema> page = condition.newPage();
        return baseMapper.selectPageV1(condition, page);
    }
}
