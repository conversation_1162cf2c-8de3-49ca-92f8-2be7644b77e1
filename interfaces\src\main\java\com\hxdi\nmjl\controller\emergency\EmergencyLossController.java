package com.hxdi.nmjl.controller.emergency;

import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.emergency.EmergencyLossCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyLoss;
import com.hxdi.nmjl.service.emergency.EmergencyLossService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * <应急损耗管理>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:12
 */
@RestController
@RequestMapping("/emergency/loss")
@Api(tags = "应急损耗管理")
public class EmergencyLossController extends CommonApiController<EmergencyLossService, EmergencyLoss, EmergencyLossCondition> {


    @ApiOperation("确认")
    @GetMapping("/confirm")
    @Log("应急损耗确认")
    public ResultBody<Void> confirm(@RequestParam String lossId) {
        bizService.confirm(lossId);
        return ResultBody.OK();
    }
}
