package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 经营计划明细
 */
@ApiModel(description = "经营计划明细信息")
@Getter
@Setter
@TableName("B_OPERATING_PLAN_DETAIL")
public class OperatingPlanDetail implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("PLAN_CODE")
    @ApiModelProperty(value = "计划编号")
    private String planCode;

    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    @TableField("PROJECT_NAME")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @TableField("OBJECTIVED_DESC")
    @ApiModelProperty(value = "关键目标描述")
    private String objectivedDesc;

    @TableField("RESULT")
    @ApiModelProperty(value = "结果")
    private String result;

    @TableField("FINISH_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "完成日期")
    private Date finishDate;
}
