package com.hxdi.nmjl.domain.mobilization;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 厨师工作经历
 */
@ApiModel(description = "厨师工作经历")
@Getter
@Setter
@TableName("B_MOBILIZED_CHEF_EXPERIENCE")
public class MobilizeChefExp implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("CHEF_ID")
    @ApiModelProperty(value = "厨师ID")
    private String chefId;

    // 工作经历1
    @TableField("WORK_UNIT1")
    @ApiModelProperty(value = "工作单位1")
    private String workUnit1;

    @TableField("JOB_TITLE1")
    @ApiModelProperty(value = "岗位1")
    private String jobTitle1;

    @TableField("JOB_DESC1")
    @ApiModelProperty(value = "工作职责1")
    private String jobDesc1;

    @TableField("START_TIME1")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间1")
    private Date startTime1;

    @TableField("END_TIME1")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间1")
    private Date endTime1;

    // 工作经历2
    @TableField("WORK_UNIT2")
    @ApiModelProperty(value = "工作单位2")
    private String workUnit2;

    @TableField("JOB_TITLE2")
    @ApiModelProperty(value = "岗位2")
    private String jobTitle2;

    @TableField("JOB_DESC2")
    @ApiModelProperty(value = "工作职责2")
    private String jobDesc2;

    @TableField("START_TIME2")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间2")
    private Date startTime2;

    @TableField("END_TIME2")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间2")
    private Date endTime2;

    // 工作经历3
    @TableField("WORK_UNIT3")
    @ApiModelProperty(value = "工作单位3")
    private String workUnit3;

    @TableField("JOB_TITLE3")
    @ApiModelProperty(value = "岗位3")
    private String jobTitle3;

    @TableField("JOB_DESC3")
    @ApiModelProperty(value = "工作职责3")
    private String jobDesc3;

    @TableField("START_TIME3")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间3")
    private Date startTime3;

    @TableField("END_TIME3")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间3")
    private Date endTime3;
}
