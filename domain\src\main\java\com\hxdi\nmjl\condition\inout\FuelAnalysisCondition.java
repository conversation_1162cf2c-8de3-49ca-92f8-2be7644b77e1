package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 描述：能源分析查询条件
 */

@Data
@ApiModel(description = "库存记录查询条件")
public class FuelAnalysisCondition extends QueryCondition {
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "异常类型")
    private String mark;

    @ApiModelProperty(value = "确认结果")
    private String confirmResult;

}