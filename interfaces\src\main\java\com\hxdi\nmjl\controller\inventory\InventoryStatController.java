package com.hxdi.nmjl.controller.inventory;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inventory.InventoryStat;
import com.hxdi.nmjl.service.inventory.InventoryStatService;
import com.hxdi.nmjl.condition.inventory.InventoryStatCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: nmjl-service
 * @description: 库存日志控制器
 * @author: 王贝强
 * @create: 2025-04-21 15:37
 */
@RestController
@Api(tags = "库存统计")
@RequestMapping("/inventoryStat")
public class InventoryStatController extends BaseController<InventoryStatService, InventoryStat> {
    @PostMapping("/saveAndUpdate")
    @ApiOperation("初始化当日统计数据并完善前一天的统计数据（定时任务接口，请勿直接调用）")
    public ResultBody createOrUpdate(){
        bizService.createBatch();
        bizService.updateBatch();
        return ResultBody.ok();
    }

    @GetMapping("/listAll")
    @ApiOperation("查询库存保管总账列表（带用户数据权限）")
    public ResultBody listAll(InventoryStatCondition condition){
        return ResultBody.ok().data(bizService.listV1(condition));
    }

    @GetMapping("/pageAll")
    @ApiOperation("查询库存保管总账分页列表（带用户数据权限）")
    public ResultBody pageAll(InventoryStatCondition condition){
        return ResultBody.ok().data(bizService.pageV1(condition));
    }

    @GetMapping("/listDetail")
    @ApiOperation("查询库存保管明细账列表（带用户数据权限）")
    public ResultBody listDetail(InventoryStatCondition condition){
        return ResultBody.ok().data(bizService.detailListV1(condition));
    }

    @GetMapping("/pageDetail")
    @ApiOperation("查询库存保管明细账分页列表（带用户数据权限）")
    public ResultBody pageDetail(InventoryStatCondition condition){
        return ResultBody.ok().data(bizService.detailPageV1(condition));
    }
}
