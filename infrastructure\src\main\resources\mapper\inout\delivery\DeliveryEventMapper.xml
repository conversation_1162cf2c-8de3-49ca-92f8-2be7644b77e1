<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.delivery.DeliveryEventMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.delivery.DeliveryEvent">
        <!--@Table B_DELIVERY_EVENT-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="deliveryId" column="DELIVERY_ID" jdbcType="VARCHAR"/>
        <result property="deliveryCode" column="DELIVERY_CODE" jdbcType="VARCHAR"/>
        <result property="vehicleNo" column="VEHICLE_NO" jdbcType="VARCHAR"/>
        <result property="driver" column="DRIVER" jdbcType="VARCHAR"/>
        <result property="mobile" column="MOBILE" jdbcType="VARCHAR"/>
        <result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="END_TIME" jdbcType="TIMESTAMP"/>
        <result property="eventType" column="EVENT_TYPE" jdbcType="VARCHAR"/>
        <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>
        <result property="enabled" column="ENABLED" jdbcType="INTEGER"/>
        <result property="attachments" column="ATTACHMENTS" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createId" column="CREATE_ID" jdbcType="VARCHAR"/>
        <result property="updateId" column="UPDATE_ID" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
        <result property="deliverer" column="DELIVERER" jdbcType="VARCHAR"/>
        <result property="storeId" column="STORE_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        DELIVERY_ID,
        DELIVERY_CODE,
        VEHICLE_NO,
        DRIVER,
        MOBILE,
        START_TIME,
        END_TIME,
        EVENT_TYPE,
        REMARKS,
        ENABLED,
        ATTACHMENTS,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID,
        DELIVERER,
        STORE_ID
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_DELIVERY_EVENT
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.mobile)">
                AND MOBILE = #{condition.mobile}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
                AND START_TIME &gt;= #{condition.startTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
                AND END_TIME &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_DELIVERY_EVENT
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.mobile)">
                AND MOBILE = #{condition.mobile}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
                AND START_TIME &gt;= #{condition.startTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
                AND END_TIME &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>

