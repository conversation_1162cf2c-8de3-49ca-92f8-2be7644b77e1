package com.hxdi.nmjl.mapper.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.portal.CategoriesCondition;
import com.hxdi.nmjl.domain.portal.CmsCategories;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CmsCategoriesMapper extends SuperMapper<CmsCategories> {

    /**
     * 分页查询
     * @param page
     * @param condition
     * @return
     */
    Page<CmsCategories> selectPageV1(Page<CmsCategories> page, @Param("condition") CategoriesCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    List<CmsCategories> selectListV1(@Param("condition") CategoriesCondition condition);
}
