package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inventory.InventoryAlarmConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InventoryAlarmConfigMapper extends SuperMapper<InventoryAlarmConfig> {

    @DataPermission
    List<InventoryAlarmConfig> listV1(@Param("condition") InventoryAlarmConfig condition);

    @DataPermission
    Page<InventoryAlarmConfig> pageV1(@Param("condition") InventoryAlarmConfig condition, @Param("page") Page<InventoryAlarmConfig> page);
}
