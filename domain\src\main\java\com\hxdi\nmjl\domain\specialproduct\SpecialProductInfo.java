package com.hxdi.nmjl.domain.specialproduct;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 地方特色产品信息
 */
@Getter
@Setter
@TableName (value = "B_SPECIAL_PRODUCT_INFO")
public class SpecialProductInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**

     产品名称
     */
    @TableField (value = "PRODUCT_NAME")
    private String productName;

    /**

     产品编号
     */
    @TableField (value = "PRODUCT_CODE")
    private String productCode;

    /**

     规格
     */
    @TableField (value = "SPEC")
    private String spec;

    /**

     计量单位：字典
     */
    @TableField (value = "UNIT")
    private String unit;

    /**

     质量等级：字典
     */
    @TableField (value = "GRADE")
    private String grade;

    /**

     保质期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "EXPIRS")
    private Integer expirs;

    /**

     产地
     */
    @TableField (value = "ORIGIN")
    private String origin;

    /**

     生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "PRODUCT_DATE")
    private Date productDate;

    /**

     联系人
     */
    @TableField (value = "LINKER")
    private String linker;

    /**

     联系电话
     */
    @TableField (value = "MOBILE")
    private String mobile;

    /**

     客户名称
     */
    @TableField (value = "CLIENT_NAME")
    private String clientName;

    /**

     营业执照附件
     */
    @TableField (value = "BIZ_LICENSE")
    private String bizLicense;

    /**

     食品流通许可证附件
     */
    @TableField (value = "FOOD_LICENSE")
    private String foodLicense;

    /**

     邮件地址
     */
    @TableField (value = "EMAIL")
    private String email;

    /**

     客户 ID
     */
    @TableField (value = "CLIENT_ID")
    private String clientId;

    /**

     审核状态：0 - 审核中，1 - 已审核，2 - 驳回
     */
    @TableField (value = "APPROVE_STATUS")
    private Integer approveStatus;

    /**

     上线状态：0 - 待发布，1 - 已发布
     */
    @TableField (value = "PUBLISH_STATE")
    private Integer publishState;

    /**

     状态（1 - 有效 0 删除）
     */
    @TableField (value = "ENABLED")
    private Integer enabled;

    /**

     附件
     */
    @TableField (value = "ATTACHMENTS")
    private String attachments;

    /**

     创建日期
     */
    @TableField (value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**

     更新时间
     */
    @TableField (value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**

     创建 id
     */
    @TableField (value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**

     更新 id
     */
    @TableField (value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**

     租户 id
     */
    @TableField (value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**

     组织
     */
    @TableField (value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 地方特色产品描述
     */
    @TableField(exist = false)
    private String specialProductContent;


    /**
     * 优特产品分析报告
     */
    @TableField(exist = false)
    private Page<SpecialProductMonitoring> specialProductMonitorings;
}
