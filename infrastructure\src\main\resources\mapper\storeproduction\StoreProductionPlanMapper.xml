<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.storeproduction.StoreProductionPlanMapper">

    <sql id="Base_Column_List">
        ID, PLAN_NO, PLAN_NAME, PLAN_TYPE, START_DATE, END_DATE,
        STORE_ID, STORE_NAME, APPROVE_STATUS, APPROVER, APPROVE_TIME,
        APPROVE_OPINION, REMARK, ENABLED, CREATE_TIME, UPDATE_TIME,
        CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.storeproduction.StoreProductionPlan">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
        <result column="PLAN_NAME" jdbcType="VARCHAR" property="planName" />
        <result column="PLAN_TYPE" jdbcType="INTEGER" property="planType" />
        <result column="START_DATE" jdbcType="DATE" property="startDate" />
        <result column="END_DATE" jdbcType="DATE" property="endDate" />
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus" />
        <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_PRODUCTION_PLAN
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.planNo)">
                AND PLAN_NO LIKE CONCAT('%', #{condition.planNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planName)">
                AND PLAN_NAME LIKE CONCAT('%', #{condition.planName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planType)">
                AND PLAN_TYPE = #{condition.planType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startDate)">
                AND START_DATE >= #{condition.startDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endDate)">
                AND END_DATE &lt;= #{condition.endDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_PRODUCTION_PLAN
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.planNo)">
                AND PLAN_NO LIKE CONCAT('%', #{condition.planNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planName)">
                AND PLAN_NAME LIKE CONCAT('%', #{condition.planName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planType)">
                AND PLAN_TYPE = #{condition.planType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startDate)">
                AND START_DATE >= #{condition.startDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endDate)">
                AND END_DATE &lt;= #{condition.endDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>