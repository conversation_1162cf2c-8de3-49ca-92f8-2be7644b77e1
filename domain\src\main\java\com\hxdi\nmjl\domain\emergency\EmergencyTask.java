package com.hxdi.nmjl.domain.emergency;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 应急任务表
 */
@Getter
@Setter
@TableName(value = "B_EMERGENCY_TASK")
public class EmergencyTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调度编号
     */
    @TableField(value = "SCHEDULE_NO")
    private String scheduleNo;

    /**
     * 车辆类型
     */
    @TableField(value = "VEHICLE_TYPE")
    private String vehicleType;

    /**
     * 车牌号
     */
    @TableField(value = "VEHICLE_NO")
    private String vehicleNo;

    /**
     * 司机姓名
     */
    @TableField(value = "DRIVER")
    private String driver;

    /**
     * 司机手机号
     */
    @TableField(value = "MOBILE")
    private String mobile;

    /**
     * 运送状态：1-进行中，2-已送达
     */
    @TableField(value = "STATE")
    private Integer state;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "START_TIME")
    private Date startTime;

    /**
     * 预计送达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "ARRIVED_TIME")
    private Date arrivedTime;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 应急调度任务单明细
     */
    @TableField(exist = false)
    private List<EmergencyTaskItem> emergencyTaskItem;
}
