package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inventory.InventoryBase;

public interface InventoryBaseService extends IService<InventoryBase>{


    /**
     * 保存库存基础信息
     * @param inventoryBase
     */
    void saveBaseInfo(InventoryBase inventoryBase);

    /**
     * 查询库存静态数据信息详情
     * @param inventoryId
     * @return
     */
    InventoryBase get(String inventoryId);
}
