package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


/**
 * <应急损耗记录-统计关系实体>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:00
 */
@Getter
@Setter
@TableName("B_EMERGENCY_LOSS_STAT_REF")
public class EmergencyLossStatRef implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String lossId;

    private String statId;
}
