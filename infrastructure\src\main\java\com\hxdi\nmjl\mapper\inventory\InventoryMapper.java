package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.vo.bigscreen.InventorySumAndCapacityVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface InventoryMapper extends SuperMapper<Inventory> {
    @DataPermission
    Page<Inventory> selectCatalogInventoryByPage(@Param("page") Page<Inventory> page, @Param("condition") InventoryCondition condition);

    @DataPermission
    Page<Inventory> selectInventoryDetailByPage(@Param("page") Page<Inventory> page, @Param("condition") InventoryCondition condition);

    @DataPermission
    List<Inventory> selectCatalogInventoryList(@Param("condition") InventoryCondition condition);

    @DataPermission
    List<Inventory> selectClassificationInventoryList(@Param("condition") InventoryCondition condition);

    @DataPermission
    List<Inventory> selectInventoryDetailList(@Param("condition") InventoryCondition condition);

    List<Inventory> getInventoryStorehouseList(String storeId);

    String getQuantity(String locId);

    @DataPermission
    Page<Inventory> selectInventorySummeryByPage(Page<Inventory> page, @Param("condition") InventoryCondition condition);

    @DataPermission
    BigDecimal selectInventorySummery(@Param("areaCodeList") List<String> areaCodeList, @Param("catalogIdList") List<String> catalogIdList);

    @DataPermission
    List<InventorySumAndCapacityVO> getInventorySumAndCapacity(@Param("areaCode") List<String> areaCode);

    @DataPermission(alias = "s")
    Page<InventorySumAndCapacityVO> getInventorySumAndCapacityPage(Page<InventorySumAndCapacityVO> page, @Param("areaCodeList") List<String> areaCodeList, @Param("storeId") String storeId);

    @DataPermission
    BigDecimal getInventorySum(String areaCode);
}
