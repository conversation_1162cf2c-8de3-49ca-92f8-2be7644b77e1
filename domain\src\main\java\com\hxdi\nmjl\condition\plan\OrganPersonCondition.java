package com.hxdi.nmjl.condition.plan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.model.QueryCondition;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.base.support.Logic;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class OrganPersonCondition extends QueryCondition {

    @LIKE(value = "name,mobile,", logic = Logic.OR, join = true)
    private String keywords;

    @EQ
    private String orgId;

    @EQ
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer enabled = 1;
}
