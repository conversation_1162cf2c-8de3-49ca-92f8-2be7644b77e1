package com.hxdi.nmjl.service.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCatalogCondition;
import com.hxdi.nmjl.domain.mobilization.MobilizedProductCatalog;

import java.util.List;

/**
 * 动员商品目录服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/02 10:30
 */
public interface MobilizedProductCatalogService extends IBaseService<MobilizedProductCatalog> {

    /**
     * 分页查询
     * @param condition 查询条件
     * @return
     */
    Page<MobilizedProductCatalog> pages(MobilizedProductCatalogCondition condition);

    /**
     * 列表查询
     * @param condition 列表查询条件
     * @return
     */
    List<MobilizedProductCatalog> lists(MobilizedProductCatalogCondition condition);



    /**
     * 新增
     * @param catalog
     */
    void create(MobilizedProductCatalog catalog);

    /**
     * 修改
     * @param catalog
     */
    void update(MobilizedProductCatalog catalog);

    /**
     * 删除
     * @param id
     */
    void remove(String id);

    /**
     * 查看详情
     * @param id
     * @return
     */
    MobilizedProductCatalog getDetail(String id);

    /**
     * 确认
     * @param id
     * @return
     */
    void approve(String id);
}
