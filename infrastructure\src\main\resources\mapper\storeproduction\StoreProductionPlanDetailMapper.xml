<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.storeproduction.StoreProductionPlanDetailMapper">

    <sql id="Base_Column_List">
        ID, PLAN_ID, CATALOG_ID, CATALOG_CODE, CATALOG_NAME,
        CLASSIFICATION_ID, CLASSIFICATION_NAME, SPECIFICATION,
        UNIT, PLAN_QUANTITY, COMPLETE_QUANTITY
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.storeproduction.StoreProductionPlanDetail">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="PLAN_ID" jdbcType="VARCHAR" property="planId" />
        <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId" />
        <result column="CATALOG_CODE" jdbcType="VARCHAR" property="catalogCode" />
        <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
        <result column="CLASSIFICATION_ID" jdbcType="VARCHAR" property="classificationId" />
        <result column="CLASSIFICATION_NAME" jdbcType="VARCHAR" property="classificationName" />
        <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification" />
        <result column="UNIT" jdbcType="VARCHAR" property="unit" />
        <result column="PLAN_QUANTITY" jdbcType="DECIMAL" property="planQuantity" />
        <result column="COMPLETE_QUANTITY" jdbcType="DECIMAL" property="completeQuantity" />
    </resultMap>

    <select id="selectListV1" parameterType="com.hxdi.nmjl.condition.storeproduction.StoreProductionPlanDetailCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_PRODUCTION_PLAN_DETAIL
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.id)">
                AND ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planId)">
                AND PLAN_ID = #{condition.planId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.classificationId)">
                AND CLASSIFICATION_ID = #{condition.classificationId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
    </select>
</mapper>
