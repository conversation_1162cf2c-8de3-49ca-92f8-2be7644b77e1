<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.opt.OptInfoMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.opt.OptInfo">
        <result column="ID" property="id" />
        <result column="OPT_CODE" property="optCode" />
        <result column="STORE_ID" property="storeId" />
        <result column="STORE_NAME" property="storeName" />
        <result column="ST_ID" property="stId" />
        <result column="ST_NAME" property="stName" />
        <result column="OPT_TYPE" property="optType" />
        <result column="OPT_TIME" property="optTime" />
        <result column="FZR" property="fzr" />
        <result column="EMPS" property="emps" />
        <result column="BIZ_TYPE" property="bizType" />
        <result column="REMARKS" property="remarks" />
        <result column="REQUIREMENT" property="requirement" />
        <result column="ENABLED" property="enabled" />
        <result column="ATTACHMENTS" property="attachments" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,OPT_CODE,STORE_ID,STORE_NAME,ST_ID,ST_NAME,
        OPT_TYPE,OPT_TIME,FZR,EMPS,BIZ_TYPE,
        REMARKS,REQUIREMENT,ENABLED,ATTACHMENTS,CREATE_TIME,
        UPDATE_TIME,CREATE_ID,UPDATE_ID,TENANT_ID,DATA_HIERARCHY_ID,LOCATION
    </sql>

    <select id="selectListV1" resultMap="BaseResultMap" >
        SELECT <include refid="Base_Column_List"/> from B_OPT_INFO
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.optType)">
                and OPT_TYPE = #{condition.optType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
                and ST_ID = #{condition.stId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
    </select>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from B_OPT_INFO
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.optType)">
                and OPT_TYPE = #{condition.optType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
                and ST_ID = #{condition.stId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
    </select>


</mapper>
