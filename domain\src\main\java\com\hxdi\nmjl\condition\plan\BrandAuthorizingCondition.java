package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "品牌授权查询条件")
@Getter
@Setter
public class BrandAuthorizingCondition extends QueryCondition {

    @ApiModelProperty(value = "品牌ID")
    private String brandId;

    @ApiModelProperty(value = "授权协议编号")
    private String licenseNo;

    @ApiModelProperty(value = "被授权企业ID")
    private String clientId;

    @ApiModelProperty(value = "授权类型：字典PPSQLX")
    private String authType;

    @ApiModelProperty(value = "授权状态：0-无效，1-有效，2-已过期")
    private Integer authState;

    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "创建时间（开始）")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间（结束）")
    private Date createTimeEnd;

    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}