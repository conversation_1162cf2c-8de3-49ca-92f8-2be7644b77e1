package com.hxdi.nmjl.service.inout.duty;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.duty.DutyPlan;
import com.hxdi.nmjl.condition.inout.DutyCondition;

import java.util.List;

public interface DutyPlanService extends IBaseService<DutyPlan> {

    /**
     * 查询
     * @param dutyId
     * @return
     */
    DutyPlan detail(String dutyId);

    /**
     * 新增
     * @param dutyPlan
     */
    void create(DutyPlan dutyPlan);

    /**
     * 修改
     * @param dutyPlan
     */
    void updating(DutyPlan dutyPlan);

    /**
     * 分页查询
     * @param dutyCondition
     * @return
     */
    Page<DutyPlan> getPage(DutyCondition dutyCondition);

    /**
     * 查询列表
     * @param dutyCondition
     * @return
     */
    List<DutyPlan> getList(DutyCondition dutyCondition);

    /**
     * 审核
     * @param dutyId
     */
    void approve(String dutyId ,String approveOpinion);

    /**
     * 驳回
     * @param dutyId
     */
    void reject(String dutyId ,String approveOpinion);
    /**
     * 删除
     * @param dutyId
     */
    void delete(String dutyId);


    /**
     * 下发
      * @param dutyId
     */
    void approved(String dutyId);
}
