package com.hxdi.nmjl.service.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryOrder;
import com.hxdi.nmjl.domain.inout.delivery.TransportRoute;
import com.hxdi.nmjl.condition.inout.DeliveryOrderCondition;

import java.util.List;

/**
 * 调度配送单Service接口
 *
 * <AUTHOR>
 * @since 2025/4/23 11:09
 */
public interface DeliveryOrderService extends IBaseService<DeliveryOrder> {

    /**
     * 保存或更新
     *
     * @param deliveryOrder 实体
     */
    void saveOrUpdateV1(DeliveryOrder deliveryOrder);

    /**
     * 删除
     *
     * @param id 主键
     * @return 是否成功
     */
    void remove(String id);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<DeliveryOrder> pages(DeliveryOrderCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<DeliveryOrder> lists(DeliveryOrderCondition condition);

    /**
     * 查询详情
     *
     * @param condition 查询条件
     * @return 实体
     */
    DeliveryOrder getDetail(DeliveryOrderCondition condition);

    /**
     * 更改运输状态
     *
     * @param id    主键
     * @param state 状态
     */
    void updateState(String id, Integer state);

    /**
     * 新增运输轨迹
     *
     * @param transportRoute 运输轨迹
     */
    void saveTrace(TransportRoute transportRoute);

}
