package com.hxdi.nmjl.domain.inout.duty;

import java.io.Serializable;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
*
* 值班记录信息
*/
@ApiModel(description = "值班记录信息")
@Getter
@Setter
@TableName("B_DUTY_RECORD")
public class DutyRecord implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("DUTY_PLAN_ID")
    @ApiModelProperty(value = "计划ID")
    private String dutyPlanId;

    @TableField("DUTY_NAME")
    @ApiModelProperty(value = "计划名称")
    private String dutyName;

    @TableField("STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @TableField("STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @TableField("START_TIME")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @TableField("END_TIME")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @TableField("DUTY_WAY")
    @ApiModelProperty(value = "值班方式：字典ZBFS")
    private String dutyWay;

    @TableField("AREA")
    @ApiModelProperty(value = "地点")
    private String area;

    @TableField("EMP_ID")
    @ApiModelProperty(value = "值班人员ID")
    private String empId;

    @TableField("EMP_NAME")
    @ApiModelProperty(value = "人员姓名")
    private String empName;

    @TableField("MOBILE")
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    @TableField("FZR")
    @ApiModelProperty(value = "负责人")
    private String fzr;

    @TableField("REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    /**
     * --------------非数据库字段-------------
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "值班计划编号")
    private String dutyPlanCode;
}
