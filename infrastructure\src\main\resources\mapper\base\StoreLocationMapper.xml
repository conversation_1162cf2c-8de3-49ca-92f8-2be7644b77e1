<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.StoreLocationMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.StoreLocation">
    <!--@mbg.generated-->
    <!--@Table C_STORE_LOCATION-->
    <id column="ID" property="id" />
    <result column="ST_ID" property="stId" />
    <result column="NAME" property="name" />
    <result column="LOC_CODE" property="locCode" />
    <result column="CAPACITY" property="capacity" />
    <result column="USED_DATE" property="usedDate" />
    <result column="STATE" property="state" />
    <result column="REMARKS" property="remarks" />
    <result column="ENABLED" property="enabled" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    <result column="IMAGE_PATH" property="imagePath"/>
    <result column="FREQUENCE" property="frequence"/>
    <result column="WEIGHT" property="weight"/>
    <result column="VOLUME" property="volume"/>
    <result column="GOODS_NAME" property="goodsName"/>
    <result column="ASSIGN_STATE" property="assignState"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ST_ID, "NAME", LOC_CODE, CAPACITY, USED_DATE, "STATE", REMARKS, ENABLED, CREATE_TIME,
    UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,STORE_ID,IMAGE_PATH,
    FREQUENCE,WEIGHT,VOLUME,GOODS_NAME,ASSIGN_STATE
  </sql>

  <select id="selectPageV1" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM C_STORE_LOCATION
    <where>
      ENABLED !=7
      <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
        AND STORE_ID = #{condition.storeId}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
        AND ST_ID = #{condition.stId}
      </if>
    </where>
    order by CREATE_TIME DESC
  </select>

  <select id="selectListV1" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM C_STORE_LOCATION
    <where>
      ENABLED !=7
      <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
        AND STORE_ID = #{condition.storeId}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
        AND ST_ID = #{condition.stId}
      </if>
    </where>
    order by CREATE_TIME DESC
  </select>
</mapper>
