package com.hxdi.nmjl.domain.iot;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * 温湿度数据统计表
 */

@Getter
@Setter
@ApiModel(description="温湿度数据统计表")
@TableName(value = "B_TEM_HUM_DAY_RECORD")
public class TemHumDayRecord extends Entity<TemHumDayRecord> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value="库点名称")
    private String storeName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value="仓房ID")
    private String stId;

    /**
     * 仓房名称
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value="仓房名称")
    private String stName;

    /**
     * 平均温度℃
     */
    @TableField(value = "WD")
    @ApiModelProperty(value="平均温度℃")
    private String wd;

    /**
     * 平均湿度%
     */
    @TableField(value = "SD")
    @ApiModelProperty(value="平均湿度%")
    private String sd;

    /**
     * 统计日期（YYYY-MM-DD）
     */
    @TableField(value = "STATISTICAL_DATE")
    @ApiModelProperty(value="统计日期")
    private String statisticalDate;

    /**
     * 组织 不添加字段填充，由定时任务赋值
     */
    @TableField(value = "DATA_HIERARCHY_ID")
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}