package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产订单交付验收信息表
 */
@ApiModel(description = "生产订单交付验收信息表")
@Getter
@Setter
@TableName(value = "B_PRODUCTION_ORDER_ACCEPTANCE")
public class ProductionOrderAcceptance extends BModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 验收编号
     */
    @TableField(value = "ACCEPTANCE_NO")
    @ApiModelProperty(value = "验收编号")
    private String acceptanceNo;

    /**
     * 生产订单ID
     */
    @TableField(value = "ORDER_ID")
    @ApiModelProperty(value = "生产订单ID")
    private String orderId;

    /**
     * 生产订单编号
     */
    @TableField(value = "ORDER_CODE")
    @ApiModelProperty(value = "生产订单编号")
    private String orderCode;

    /**
     * 合同ID
     */
    @TableField(value = "CONTRACT_ID")
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 合同编号
     */
    @TableField(value = "CONTRACT_CODE")
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    /**
     * 机构ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    /**
     * 机构名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 客户ID
     */
    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    /**
     * 客户名称
     */
    @TableField(value = "CLIENT_NAME")
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "\"CATALOG_NAME\"")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    /**
     * 生产批次号
     */
    @TableField(value = "BATCH_NO")
    @ApiModelProperty(value = "生产批次号")
    private String batchNo;

    /**
     * 交付数量
     */
    @TableField(value = "DELIVERY_QTY")
    @ApiModelProperty(value = "交付数量")
    private BigDecimal deliveryQty;

    /**
     * 验收数量
     */
    @TableField(value = "ACCEPTANCE_QTY")
    @ApiModelProperty(value = "验收数量")
    private BigDecimal acceptanceQty;

    /**
     * 合格数量
     */
    @TableField(value = "QUALIFIED_QTY")
    @ApiModelProperty(value = "合格数量")
    private BigDecimal qualifiedQty;

    /**
     * 不合格数量
     */
    @TableField(value = "UNQUALIFIED_QTY")
    @ApiModelProperty(value = "不合格数量")
    private BigDecimal unqualifiedQty;


    /**
     * 质检报告ID
     */
    @TableField(value = "QUALITY_INSPECTION_ID")
    @ApiModelProperty(value = "质检报告ID")
    private String qualityInspectionId;

    /**
     * 质检报告编号
     */
    @TableField(value = "QUALITY_INSPECTION_NO")
    @ApiModelProperty(value = "质检报告编号")
    private String qualityInspectionNo;


    /**
     * 交付日期
     */
    @TableField(value = "DELIVERY_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "交付日期")
    private Date deliveryDate;

    /**
     * 验收日期
     */
    @TableField(value = "ACCEPTANCE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "验收日期")
    private Date acceptanceDate;

    /**
     * 验收人
     */
    @TableField(value = "ACCEPTOR")
    @ApiModelProperty(value = "验收人")
    private String acceptor;

    /**
     * 验收结果:1-合格,0-不合格,2-部分合格
     */
    @TableField(value = "ACCEPTANCE_RESULT")
    @ApiModelProperty(value = "验收结果:1-合格,0-不合格,2-部分合格")
    private Integer acceptanceResult;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "验收附件")
    private String attachments;

    /**
     * 不合格原因
     */
    @TableField(value = "UNQUALIFIED_REASON")
    @ApiModelProperty(value = "不合格原因")
    private String unqualifiedReason;

    /**
     * 处理方式:1-退货,2-换货,3-降价处理,4-其他
     */
    @TableField(value = "HANDLE_METHOD")
    @ApiModelProperty(value = "处理方式:0-无需处理,1-退货,2-换货,3-降价处理,4-其他")
    private Integer handleMethod;

    /**
     * 处理状态:0-待处理,1-处理中,2-已完成
     */
    @TableField(value = "HANDLE_STATUS")
    @ApiModelProperty(value = "处理状态:0-待处理,1-处理中,2-已完成")
    private Integer handleStatus;

    /**
     * 处理日期
     */
    @TableField(value = "HANDLE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "处理日期")
    private Date handleDate;

    /**
     * 处理人
     */
    @TableField(value = "HANDLE_PERSON")
    @ApiModelProperty(value = "处理人")
    private String handlePerson;

    /**
     * 处理备注
     */
    @TableField(value = "HANDLE_REMARK")
    @ApiModelProperty(value = "处理备注")
    private String handleRemark;

    /**
     * 业务状态:0-待验收,1-已验收,2-已处理
     */
    @TableField(value = "\"STATE\"")
    @ApiModelProperty(value = "业务状态:0-待验收,1-已验收,2-已处理")
    private Integer state;

    /**
     * 状态:1-有效,0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效,0-删除")
    private Integer enabled;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人ID")
    private String createId;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人ID")
    private String updateId;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 数据权限
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "数据权限")
    private String dataHierarchyId;
}