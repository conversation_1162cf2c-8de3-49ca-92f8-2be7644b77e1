<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.delivery.DeliveryOrderMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.delivery.DeliveryOrder">
        <!--@Table B_DELIVERY_ORDER-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="deliveryCode" column="DELIVERY_CODE" jdbcType="VARCHAR"/>
        <result property="orderNo" column="ORDER_NO" jdbcType="VARCHAR"/>
        <result property="catalogId" column="CATALOG_ID" jdbcType="VARCHAR"/>
        <result property="catalogName" column="CATALOG_NAME" jdbcType="VARCHAR"/>
        <result property="brand" column="BRAND" jdbcType="VARCHAR"/>
        <result property="specifications" column="SPECIFICATIONS" jdbcType="VARCHAR"/>
        <result property="grade" column="GRADE" jdbcType="VARCHAR"/>
        <result property="qty" column="QTY" jdbcType="DECIMAL"/>
        <result property="storeId" column="STORE_ID" jdbcType="VARCHAR"/>
        <result property="storeName" column="STORE_NAME" jdbcType="VARCHAR"/>
        <result property="oriAddrCode" column="ORI_ADDR_CODE" jdbcType="VARCHAR"/>
        <result property="oriAddr" column="ORI_ADDR" jdbcType="VARCHAR"/>
        <result property="oriDetailAddr" column="ORI_DETAIL_ADDR" jdbcType="VARCHAR"/>
        <result property="pickupDate" column="PICKUP_DATE" jdbcType="TIMESTAMP"/>
        <result property="linker" column="LINKER" jdbcType="VARCHAR"/>
        <result property="mobile" column="MOBILE" jdbcType="VARCHAR"/>
        <result property="clientId" column="CLIENT_ID" jdbcType="VARCHAR"/>
        <result property="clientName" column="CLIENT_NAME" jdbcType="VARCHAR"/>
        <result property="rcvAddrCode" column="RCV_ADDR_CODE" jdbcType="VARCHAR"/>
        <result property="rcvAddr" column="RCV_ADDR" jdbcType="VARCHAR"/>
        <result property="rcvDetailAddr" column="RCV_DETAIL_ADDR" jdbcType="VARCHAR"/>
        <result property="rcvDate" column="RCV_DATE" jdbcType="TIMESTAMP"/>
        <result property="rcvMan" column="RCV_MAN" jdbcType="VARCHAR"/>
        <result property="rcvPhone" column="RCV_PHONE" jdbcType="VARCHAR"/>
        <result property="vehicleNo" column="VEHICLE_NO" jdbcType="VARCHAR"/>
        <result property="driver" column="DRIVER" jdbcType="VARCHAR"/>
        <result property="driverPhone" column="DRIVER_PHONE" jdbcType="VARCHAR"/>
        <result property="idCard" column="ID_CARD" jdbcType="VARCHAR"/>
        <result property="scry" column="SCRY" jdbcType="VARCHAR"/>
        <result property="scryPhone" column="SCRY_PHONE" jdbcType="VARCHAR"/>
        <result property="deliveryState" column="DELIVERY_STATE" jdbcType="INTEGER"/>
        <result property="enabled" column="ENABLED" jdbcType="INTEGER"/>
        <result property="attachments" column="ATTACHMENTS" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createId" column="CREATE_ID" jdbcType="VARCHAR"/>
        <result property="updateId" column="UPDATE_ID" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
        <result property="deliveryDate" column="DELIVERY_DATE" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        DELIVERY_CODE,
        ORDER_NO,
        CATALOG_ID,
        "CATALOG_NAME",
        BRAND,
        SPECIFICATIONS,
        GRADE,
        QTY,
        STORE_ID,
        STORE_NAME,
        ORI_ADDR_CODE,
        ORI_ADDR,
        ORI_DETAIL_ADDR,
        PICKUP_DATE,
        LINKER,
        MOBILE,
        CLIENT_ID,
        CLIENT_NAME,
        RCV_ADDR_CODE,
        RCV_ADDR,
        RCV_DETAIL_ADDR,
        RCV_DATE,
        RCV_MAN,
        RCV_PHONE,
        VEHICLE_NO,
        DRIVER,
        DRIVER_PHONE,
        ID_CARD,
        SCRY,
        SCRY_PHONE,
        DELIVERY_STATE,
        ENABLED,
        ATTACHMENTS,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID,
        DELIVERY_DATE
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_DELIVERY_ORDER
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.deliveryCode)">
                AND DELIVERY_CODE = #{condition.deliveryCode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orderNo)">
                AND ORDER_NO = #{condition.orderNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.deliveryStartDate) and @plugins.OGNL@isNotEmpty(condition.deliveryEndDate)">
                AND DELIVERY_DATE BETWEEN #{condition.deliveryStartDate} AND #{condition.deliveryEndDate}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_DELIVERY_ORDER
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.deliveryCode)">
                AND DELIVERY_CODE = #{condition.deliveryCode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orderNo)">
                AND ORDER_NO = #{condition.orderNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.deliveryStartDate) and @plugins.OGNL@isNotEmpty(condition.deliveryEndDate)">
                AND DELIVERY_DATE BETWEEN #{condition.deliveryStartDate} AND #{condition.deliveryEndDate}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectOneV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_DELIVERY_ORDER
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.deliveryCode)">
                AND DELIVERY_CODE = #{condition.deliveryCode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orderNo)">
                AND ORDER_NO = #{condition.orderNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.deliveryStartDate) and @plugins.OGNL@isNotEmpty(condition.deliveryEndDate)">
                AND DELIVERY_DATE BETWEEN #{condition.deliveryStartDate} AND #{condition.deliveryEndDate}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
        LIMIT 1
    </select>
</mapper>
