package com.hxdi.nmjl.vo.bigscreen;

import com.hxdi.nmjl.domain.bigscreen.InfoArea;
import com.hxdi.nmjl.domain.bigscreen.OrderMonthRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 大屏统计： 区域订单统计VO
 * @author: 王贝强
 * @create: 2025-07-28 16:00
 */
@Getter
@Setter
@ApiModel(description = "区域供销订单统计")
public class AreaOrderStatVO {

    @ApiModelProperty(value = "区域信息")
    private InfoArea area;

    @ApiModelProperty(value = "日期(只在按月份分组的结果中返回):yyyy-MM")
    private String month;

    @ApiModelProperty(value = "品类(只在按品类分组的结果中返回)")
    private String category;

    @ApiModelProperty(value = "订单记录列表")
    private List<OrderMonthRecord> orderRecords;

    @ApiModelProperty(value = "子区域统计")
    private List<AreaOrderStatVO> children;

    @ApiModelProperty(value = "采购订单总数")
    private Integer purchaseOrderCount;

    @ApiModelProperty(value = "销售订单总数")
    private Integer saleOrderCount;

    @ApiModelProperty(value = "采购订单总量")
    private BigDecimal purchaseOrderQty;

    @ApiModelProperty(value = "销售订单总量")
    private BigDecimal saleOrderQty;

    @ApiModelProperty(value = "采购完成总量")
    private BigDecimal purchaseCompletedQty;

    @ApiModelProperty(value = "销售完成总量")
    private BigDecimal saleCompletedQty;
}