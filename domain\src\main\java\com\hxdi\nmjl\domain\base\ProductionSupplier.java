package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import com.hxdi.nmjl.domain.clientrelated.SupplierCredit;
import com.hxdi.nmjl.domain.clientrelated.SupplierProduct;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 供应商
 */

@Getter
@Setter
@TableName("B_PRODUCTION_SUPPLIER")
public class ProductionSupplier extends Entity<ProductionSupplier> implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 供应商编号
     */
    @TableField("SUPPLIER_CODE")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 供应商类型
     */
    @TableField("SUPPLIER_TYPE")
    private String supplierType;

    /**
     * 企业简称
     */
    @TableField("ABBR_NAME")
    private String abbrName;

    /**
     * 企业性质
     */
    @TableField("COMPANY_TYPE")
    private String companyType;

    /**
     * 所在省id
     */
    @TableField("PROVINCE_ID")
    private String provinceId;

    /**
     * 所在省
     */
    @TableField("PROVINCE")
    private String province;

    /**
     * 所在市id
     */
    @TableField("CITY_ID")
    private String cityId;

    /**
     * 所在市
     */
    @TableField("CITY")
    private String city;

    /**
     * 所在县区id
     */
    @TableField("COUNTY_ID")
    private String countyId;

    /**
     * 所在县区
     */
    @TableField("COUNTY")
    private String county;

    /**
     * 详细地址
     */
    @TableField("DETAIL_ADDR")
    private String detailAddr;

    /**
     * 统一社会信用代码
     */
    @TableField("CREDIT_CODE")
    private String creditCode;

    /**
     * 企业经济类型
     */
    @TableField("ECONOMIC_TYPE")
    private String economicType;

    /**
     * 登记注册类型
     */
    @TableField("REGISTER_TYPE")
    private String registerType;

    /**
     * 工商登记注册号
     */
    @TableField("BUSINESS_NO")
    private String businessNo;

    /**
     * 法定代表人
     */
    @TableField("LEGAL")
    private String legal;

    /**
     * 联系电话
     */
    @TableField("PHONE")
    private String phone;

    /**
     * 传真
     */
    @TableField("FAX")
    private String fax;

    /**
     * 电子邮箱
     */
    @TableField("MAIL")
    private String mail;

    /**
     * 企业网址
     */
    @TableField("WEBSITE")
    private String website;

    /**
     * 邮政编码
     */
    @TableField("POST_CODE")
    private String postCode;

    /**
     * 经度
     */
    @TableField("LON")
    private String lon;

    /**
     * 纬度
     */
    @TableField("LAT")
    private String lat;

    /**
     * 开户银行
     */
    @TableField("BANK")
    private String bank;

    /**
     * 银行账号
     */
    @TableField("ACCOUNT")
    private String account;

    /**
     * 银行信用等级
     */
    @TableField("BANK_CREDIT_LEVEL")
    private String bankCreditLevel;

    /**
     * 固定资产
     */
    @TableField("FIXED_ASSETS")
    private String fixedAssets;

    /**
     * 注册资本(万元)
     */
    @TableField("REGISTERED_CAPITAL")
    private BigDecimal registeredCapital;

    /**
     * 资产
     */
    @TableField("ASSETS")
    private String assets;

    /**
     * 企业从业人数
     */
    @TableField("EMPLOYMENTS")
    private Integer employments;

    /**
     * 食品生产许可
     */
    @TableField("FOOD_PRODUCTION_LICENSE")
    private String foodProductionLicense;

    /**
     * 描述
     */
    @TableField("REMARKS")
    private String remarks;

    /**
     * 评级
     */
    @TableField("GRADE")
    private Integer grade;

    /**
     * 状态（0禁用，1有效，7删除）
     */
    @TableField("ENABLED")
    private Integer enabled;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @ApiModelProperty(value = "审核状态")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 数据权限字段
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    @TableField("AVG_SCORE")
    private BigDecimal avgScore;

    /*********************** 扩展字段 **********/

    @ApiModelProperty(value = "企业信用")
    @TableField(exist = false)
    private List<SupplierCredit> supplierCreditList;

    @ApiModelProperty(value = "企业产品")
    @TableField(exist = false)
    private List<SupplierProduct> supplierProductList;

}
