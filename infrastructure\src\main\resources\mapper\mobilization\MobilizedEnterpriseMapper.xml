<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.mobilization.MobilizedEnterpriseMapper">

    <sql id="Base_Column_List">
        ID, ENTERPRISE_CODE, UNIT_ID, UNIT_NAME, ENTERPRISE_NAME,
        CREDIT_CODE, TYPE, ADDR, DETAIL_ADDR, PRINCIPAL,
        PRINCIPAL_TEL, ALTERNATE_CONTACT_PERSON, ALTERNATE_CONTACT_TEL,
        LON, LAT, STATE, ENABLED, ATTACHEMENTS,
        CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID,
        TENANT_ID, DATA_HIERARCHY_ID,AREA_CODE,APPROVE_STATUS
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.mobilization.MobilizedEnterprise">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="ENTERPRISE_CODE" jdbcType="VARCHAR" property="enterpriseCode" />
        <result column="UNIT_ID" jdbcType="VARCHAR" property="unitId" />
        <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
        <result column="ENTERPRISE_NAME" jdbcType="VARCHAR" property="enterpriseName" />
        <result column="CREDIT_CODE" jdbcType="VARCHAR" property="creditCode" />
        <result column="TYPE" jdbcType="VARCHAR" property="type" />
        <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
        <result column="ADDR" jdbcType="VARCHAR" property="addr" />
        <result column="DETAIL_ADDR" jdbcType="VARCHAR" property="detailAddr" />
        <result column="PRINCIPAL" jdbcType="VARCHAR" property="principal" />
        <result column="PRINCIPAL_TEL" jdbcType="VARCHAR" property="principalTel" />
        <result column="ALTERNATE_CONTACT_PERSON" jdbcType="VARCHAR" property="alternateContactPerson" />
        <result column="ALTERNATE_CONTACT_TEL" jdbcType="VARCHAR" property="alternateContactTel" />
        <result column="LON" jdbcType="VARCHAR" property="lon" />
        <result column="LAT" jdbcType="VARCHAR" property="lat" />
        <result column="STATE" jdbcType="INTEGER" property="state" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="ATTACHEMENTS" jdbcType="VARCHAR" property="attachements" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus" />
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_MOBILIZED_ENTERPRISE
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.unitId)">
                AND UNIT_ID = #{condition.unitId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseCode)">
                AND ENTERPRISE_CODE = #{condition.enterpriseCode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseName)">
                AND ENTERPRISE_NAME LIKE CONCAT('%',#{condition.enterpriseName},'%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.type)">
                AND TYPE = #{condition.type}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                AND STATE = #{condition.state}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.areaCode)">
                AND AREA_CODE IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.areaCode)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_MOBILIZED_ENTERPRISE
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.unitId)">
                AND UNIT_ID = #{condition.unitId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseName)">
                AND ENTERPRISE_NAME LIKE CONCAT('%',#{condition.enterpriseName},'%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.type)">
                AND TYPE = #{condition.type}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                AND STATE = #{condition.state}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.areaCode)">
                AND AREA_CODE IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.areaCode)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>