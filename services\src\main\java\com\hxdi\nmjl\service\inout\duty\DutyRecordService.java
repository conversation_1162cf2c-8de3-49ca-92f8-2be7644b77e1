package com.hxdi.nmjl.service.inout.duty;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.duty.DutyRecord;
import com.hxdi.nmjl.condition.inout.DutyCondition;

import java.util.List;

public interface DutyRecordService extends IBaseService<DutyRecord> {
    /**
     * 创建
      * @param dutyRecord
     */
    void create(DutyRecord dutyRecord);

    /**
     * 更新
      * @param dutyRecord
     */
    void updating(DutyRecord dutyRecord);

    /**
     * 查询
     * @param dutyId
     * @return
     */
    DutyRecord detail(String dutyId);

    /**
     * 删除
      * @param dutyId
     */
    void delete(String dutyId);

    /**
     * 分页查询
     * @param dutyCondition
     * @return
     */
    Page<DutyRecord> getPage(DutyCondition dutyCondition);

    /**
     * 查询列表
     * @param dutyCondition
     * @return
     */
    List<DutyRecord> getList(DutyCondition dutyCondition);
}
