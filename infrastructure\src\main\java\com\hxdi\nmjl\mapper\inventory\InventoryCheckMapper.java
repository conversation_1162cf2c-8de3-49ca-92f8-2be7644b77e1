package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inventory.InventoryCheck;
import com.hxdi.nmjl.condition.inventory.InventoryCheckCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盘点记录服务数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Mapper
public interface InventoryCheckMapper extends SuperMapper<InventoryCheck> {

    /**
     * 分页查询盘点记录
     *
     * @param page      分页参数
     * @param condition 查询条件
     * @return 分页结果
     */
    @DataPermission(alias = "a")
    Page<InventoryCheck> selectPageV1(Page<InventoryCheck> page, @Param("condition") InventoryCheckCondition condition);

    /**
     * 查询盘点记录列表
     *
     * @param condition 查询条件
     * @return 盘点记录列表
     */
    @DataPermission(alias = "a")
    List<InventoryCheck> selectListV1(@Param("condition") InventoryCheckCondition condition);

}

