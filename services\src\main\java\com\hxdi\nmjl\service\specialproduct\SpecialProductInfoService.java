package com.hxdi.nmjl.service.specialproduct;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo;
import com.hxdi.nmjl.condition.specialproduct.SpecialProductInfoCondition;

import java.util.List;

/**
 * 地方特色产品信息服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/16 10:00
 */
public interface SpecialProductInfoService extends IBaseService<SpecialProductInfo> {

    /**
     * 新增地方特色产品信息
     * @param productInfo 产品信息实体
     */
    void create(SpecialProductInfo productInfo);

    /**
     * 修改地方特色产品信息
     * @param productInfo 产品信息实体
     */
    void update(SpecialProductInfo productInfo);

    /**
     * 获取产品详情
     * @param productId 产品ID
     * @return 产品信息实体
     */
    SpecialProductInfo getDetail(String productId);

    /**
     * 分页查询产品
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<SpecialProductInfo> pages(SpecialProductInfoCondition condition);

    /**
     * 列表查询产品
     * @param condition 查询条件
     * @return 产品列表
     */
    List<SpecialProductInfo> lists(SpecialProductInfoCondition condition);

    /**
     * 审核产品（状态变更）
     * @param productId 产品ID
     * @param approveStatus 审核状态
     */
    void approve(String productId, Integer approveStatus, String opinion);

    /**
     * 删除产品（逻辑删除）
     * @param productId 产品ID
     */
    void remove(String productId);

    /**
     * 发布产品（上线状态变更）
     * @param productId 产品ID
     */
    void publish(String productId);

    /**
     * 取消发布产品（下线状态变更）
     * @param productId 产品ID
     */
    void unpublish(String productId);

    /**
     * 提交产品（状态变更）
     * @param productId 产品ID
     */
    void submit(String productId);
}
