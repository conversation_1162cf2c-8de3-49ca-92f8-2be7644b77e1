package com.hxdi.nmjl.mapper.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.mobilization.MobilizedContract;
import com.hxdi.nmjl.condition.mobilization.MobilizedContractCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MobilizedContractMapper extends SuperMapper<MobilizedContract> {
    Page<MobilizedContract> selectPageV1(Page<MobilizedContract> page, @Param("condition")MobilizedContractCondition condition);

    List<MobilizedContract> selectListV1(@Param("condition")MobilizedContractCondition condition);
}
