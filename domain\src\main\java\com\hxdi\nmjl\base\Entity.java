package com.hxdi.nmjl.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/22 4:10 PM
 * @description An entity, as explained in the DDD mode.
 * @version 1.0
 */
public abstract class Entity<T> implements Serializable {


    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    public Map<String, Object> additionalInfo = new HashMap<>();


}
