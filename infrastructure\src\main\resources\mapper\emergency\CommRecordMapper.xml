<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.CommRecordMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.CommRecord">
            <id property="ID" column="ID" jdbcType="VARCHAR"/>
            <result property="TITLE" column="TITLE" jdbcType="VARCHAR"/>
            <result property="COMM_TYPE" column="COMM_TYPE" jdbcType="VARCHAR"/>
            <result property="RELATED_PARTIES" column="RELATED_PARTIES" jdbcType="VARCHAR"/>
            <result property="RECORD_DATE" column="RECORD_DATE" jdbcType="DATE"/>
            <result property="RECORD_STATUS" column="RECORD_STATUS" jdbcType="INTEGER"/>
            <result property="CONTENT" column="CONTENT" jdbcType="VARCHAR"/>
            <result property="ENABLED" column="ENABLED" jdbcType="INTEGER"/>
            <result property="CREATE_TIME" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="UPDATE_TIME" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="CREATE_ID" column="CREATE_ID" jdbcType="VARCHAR"/>
            <result property="UPDATE_ID" column="UPDATE_ID" jdbcType="VARCHAR"/>
            <result property="TENANT_ID" column="TENANT_ID" jdbcType="VARCHAR"/>
            <result property="DATA_HIERARCHY_ID" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,TITLE,
        COMM_TYPE,RELATED_PARTIES,RECORD_DATE,
        RECORD_STATUS,CONTENT,
        ENABLED,CREATE_TIME,UPDATE_TIME,
        CREATE_ID,UPDATE_ID,TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>
</mapper>
