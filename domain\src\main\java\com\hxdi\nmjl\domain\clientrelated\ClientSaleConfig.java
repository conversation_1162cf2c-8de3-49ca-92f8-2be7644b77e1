package com.hxdi.nmjl.domain.clientrelated;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 销售策略配置
 */
@ApiModel(description = "销售策略配置")
@Getter
@Setter
@TableName("B_CLIENT_SALE_CONFIG") // 表名映射
public class ClientSaleConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    @TableField("CLIENT_NAME")
    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @TableField("PAYMENT_PERIOD")
    @ApiModelProperty(value = "账期/天")
    private Integer paymentPeriod;

    @TableField("DISCOUNT_RATE")
    @ApiModelProperty(value = "折扣率", example = "0.95")
    private BigDecimal discountRate;

    @TableField("TARGET_AMOUNT")
    @ApiModelProperty(value = "达成金额", example = "100000.00")
    private BigDecimal targetAmount;

    @TableField("REFUND_RATE")
    @ApiModelProperty(value = "返利百分比", example = "0.05")
    private BigDecimal refundRate;

    @TableField("REFUND_MAX")
    @ApiModelProperty(value = "返利上限", example = "5000.00")
    private BigDecimal refundMax;

    @TableField("START_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始日期")
    private Date startTime;

    @TableField("END_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束日期")
    private Date endTime;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态:0-无效，1-有效", example = "1")
    private Integer enabled;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
