package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.PackageApplyCondition;
import com.hxdi.nmjl.domain.plan.ProductionPackageApply;
import com.hxdi.nmjl.service.plan.ProductionPackageApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产包装控制层
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "生产包装管理")
@RequestMapping("/productionPkg")
public class ProductionPackageApplyController extends BaseController<ProductionPackageApplyService, ProductionPackageApply> {

    @ApiOperation("列表查询包装申请")
    @GetMapping("/list")
    public ResultBody<List<ProductionPackageApply>> list(PackageApplyCondition condition) {
        return ResultBody.ok().data(bizService.listV1(condition));
    }

    @ApiOperation("分页查询包装申请")
    @GetMapping("/page")
    public ResultBody<Page<ProductionPackageApply>> page(PackageApplyCondition condition) {
        return ResultBody.ok().data(bizService.pageV1(condition));
    }

    @ApiOperation("保存/修改包装申请")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody ProductionPackageApply apply) {
        if (CommonUtils.isEmpty(apply.getId())) {
            bizService.saveV1(apply);
        } else {
            bizService.updateV1(apply);
        }
        return ResultBody.ok();
    }

    @ApiOperation("删除包装申请")
    @GetMapping("/remove")
    public ResultBody<Void> remove(String id) {
        bizService.removeV1(id);
        return ResultBody.ok();
    }

    @ApiOperation("提交包装申请")
    @GetMapping("/submit")
    public ResultBody<Void> submit(String id) {
        bizService.submitV1(id);
        return ResultBody.ok();
    }

    @ApiOperation("审核包装申请")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id,
                                    @RequestParam Integer approveStatus,
                                    @RequestParam(required = false) String approveOpinion) {
        bizService.approveV1(id, approveStatus, approveOpinion);
        return ResultBody.ok();
    }

}
