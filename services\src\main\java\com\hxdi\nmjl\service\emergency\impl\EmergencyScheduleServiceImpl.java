package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.emergency.EmergencyScheduleCondition;
import com.hxdi.nmjl.condition.emergency.EmergencyScheduleItemCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.emergency.EmergencySchedule;
import com.hxdi.nmjl.domain.emergency.EmergencyScheduleItem;
import com.hxdi.nmjl.domain.mobilization.MobilizedEnterprise;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.emergency.EmergencyScheduleMapper;
import com.hxdi.nmjl.service.base.CatalogService;
import com.hxdi.nmjl.service.base.OrganizationService;
import com.hxdi.nmjl.service.emergency.EmergencyScheduleItemService;
import com.hxdi.nmjl.service.emergency.EmergencyScheduleService;
import com.hxdi.nmjl.service.mobilization.MobilizedEnterpriseService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class EmergencyScheduleServiceImpl extends BaseServiceImpl<EmergencyScheduleMapper, EmergencySchedule> implements EmergencyScheduleService {

    @Resource
    private EmergencyScheduleItemService emergencyScheduleItemService;

    @Resource
    private MobilizedEnterpriseService mobilizedEnterpriseService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private CatalogService catalogService;

    @Override
    public void create(EmergencySchedule schedule) {

        // 生成调度编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("EMERGENCY_SCHEDULE_NO");
        params.setDt(DataType.STRING);
        BusinessCode businessCode2 = systemNumberRuleClient.getNumber(params).getData();
        schedule.setScheduleNo((String) businessCode2.getValue());
        // 设置调度状态为进行中
        schedule.setScheduleState(1);
        MobilizedEnterprise mobilizedEnterprise = mobilizedEnterpriseService.getById(schedule.getUnitId());
        schedule.setUnitName(mobilizedEnterprise.getEnterpriseName());
        Organization organization = organizationService.getById(schedule.getRcvOrgId());
        schedule.setRcvOrgName(organization.getOrgName());

        this.save(schedule);
        // 设置调度项名称和调度id
        for (EmergencyScheduleItem item : schedule.getEmergencyScheduleItem()) {
            item.setScheduleId(schedule.getId());
            item.setCatalogName(catalogService.getById(item.getCatalogId()).getCatalogName());
            Catalog catalog = catalogService.getById(item.getCatalogId());
            item.setCatalogName(catalog.getCatalogName());
        }
        // 保存调度项
        if (schedule.getEmergencyScheduleItem() != null) {
            emergencyScheduleItemService.saveBatch(schedule.getEmergencyScheduleItem());
        }
    }

    @Override
    public void update(EmergencySchedule schedule) {
        // 查询原调度信息
        EmergencySchedule savedSchedule = this.getById(schedule.getId());
        if (savedSchedule == null) {
            throw new BaseException("应急调度信息不存在");
        }
        // 校验调度状态
        if (savedSchedule.getScheduleState() == 2) {
            throw new BaseException("已完成的调度无法修改");
        }
        this.updateById(schedule);
        for (EmergencyScheduleItem item : schedule.getEmergencyScheduleItem()) {
            item.setScheduleId(schedule.getId());
            item.setCatalogName(catalogService.getById(item.getCatalogId()).getCatalogName());
            Catalog catalog = catalogService.getById(item.getCatalogId());
            item.setCatalogName(catalog.getCatalogName());
        }
        if (schedule.getEmergencyScheduleItem() != null) {
            emergencyScheduleItemService.removeBatch(schedule.getId());
            emergencyScheduleItemService.saveBatch(schedule.getEmergencyScheduleItem());

        }
    }

    @Override
    public EmergencySchedule getDetail(String scheduleId) {
        EmergencyScheduleItemCondition emergencyScheduleItemCondition = new EmergencyScheduleItemCondition();
        emergencyScheduleItemCondition.setScheduleId(scheduleId);
        List<EmergencyScheduleItem> lists = emergencyScheduleItemService.lists(emergencyScheduleItemCondition);
        EmergencySchedule emergencySchedule = baseMapper.selectById(scheduleId);
        emergencySchedule.setEmergencyScheduleItem(lists);
        return emergencySchedule;
    }

    @Override
    public Page<EmergencySchedule> pages(EmergencyScheduleCondition condition) {
        Page<EmergencySchedule> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<EmergencySchedule> lists(EmergencyScheduleCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void complete(String scheduleId) {
        EmergencySchedule schedule = baseMapper.selectById(scheduleId);
        if (schedule == null) {
            throw new BaseException("应急调度信息不存在");
        }
        if (schedule.getScheduleState() == 2) {
            throw new BaseException("该调度已完成，无法重复操作");
        }
        schedule.setScheduleState(2);
        schedule.setEndTime(new Date());
        this.updateById(schedule);
    }

    @Override
    public void remove(String scheduleId) {
        // 查看调度是否存在
        EmergencySchedule schedule = baseMapper.selectById(scheduleId);
        if (schedule == null) {
            throw new BaseException("应急调度信息不存在");
        }
        // 校验状态
        if (schedule.getScheduleState() == 1) {
            // 逻辑删除
            schedule.setEnabled(0);
            this.updateById(schedule);
        } else {
            throw new BaseException("已完成的调度不能删除");
        }
    }
}