package com.hxdi.nmjl.service.storeproduction.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionPlanCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlan;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlanDetail;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.storeproduction.StoreProductionPlanMapper;
import com.hxdi.nmjl.service.base.CatalogService;
import com.hxdi.nmjl.service.storeproduction.StoreProductionPlanDetailService;
import com.hxdi.nmjl.service.storeproduction.StoreProductionPlanService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class StoreProductionPlanServiceImpl extends BaseServiceImpl<StoreProductionPlanMapper, StoreProductionPlan> implements StoreProductionPlanService {

    @Resource
    private StoreProductionPlanDetailService storeProductionPlanDetailService;

    @Resource
    private CatalogService catalogService;


    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public void create(StoreProductionPlan plan) {
        // 生成计划编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("STORE_PRODUCTION_PLAN_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        plan.setPlanNo((String) businessCode.getValue());

        BaseUserDetails user = SecurityHelper.getUser();
        plan.setStoreName(user.getOrganName());
        plan.setStoreId(user.getOrganId());

        // 保存详情和主表
        this.save(plan);
        //保存详情
        if (plan.getDetailList() != null) {
            for (StoreProductionPlanDetail detail : plan.getDetailList()) {
                detail.setPlanId(plan.getId());
                Catalog catalog = catalogService.getById(detail.getCatalogId());
                detail.setCatalogName(catalog.getCatalogName());
                detail.setCatalogCode(catalog.getCatalogCode());
                detail.setClassificationId(catalog.getClassificationId());
                detail.setClassificationName(catalog.getClassificationName());
            }
            storeProductionPlanDetailService.saveBatch(plan.getDetailList());
        }
    }

    @Override
    public void update(StoreProductionPlan plan) {

        BaseUserDetails user = SecurityHelper.getUser();
        plan.setStoreName(user.getOrganName());
        plan.setStoreId(user.getOrganId());

        // 更新详情和主表
        this.updateById(plan);
        if (plan.getDetailList() != null) {
            storeProductionPlanDetailService.remove(plan.getId());
            for (StoreProductionPlanDetail detail : plan.getDetailList()) {
                detail.setPlanId(plan.getId());
                Catalog catalog = catalogService.getById(detail.getCatalogId());
                detail.setCatalogName(catalog.getCatalogName());
                detail.setCatalogCode(catalog.getCatalogCode());
                detail.setClassificationId(catalog.getClassificationId());
                detail.setClassificationName(catalog.getClassificationName());
            }
            storeProductionPlanDetailService.saveBatch(plan.getDetailList());
        }
    }

    @Override
    public StoreProductionPlan getDetail(String planId) {
        StoreProductionPlan plan = baseMapper.selectById(planId);
        List<StoreProductionPlanDetail> list = storeProductionPlanDetailService.getList(planId);
        plan.setDetailList(list);
        return plan;
    }

    @Override
    public Page<StoreProductionPlan> pages(StoreProductionPlanCondition condition) {
        Page<StoreProductionPlan> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<StoreProductionPlan> lists(StoreProductionPlanCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void approve(String planId, Integer approveStatus, String opinion) {
        StoreProductionPlan plan = baseMapper.selectById(planId);
        if (plan == null) {
            throw new BaseException("计划不存在");
        }
        if (plan.getApproveStatus() == null || plan.getApproveStatus() == 1) {
            throw new BaseException("该计划未提交或已审核");
        }

        BaseUserDetails user = SecurityHelper.obtainUser();
        plan.setApproveStatus(approveStatus);
        plan.setApprover(user.getNickName());
        plan.setApproveTime(new Date());
        plan.setApproveOpinion(opinion);

        this.updateById(plan);
    }

    @Override
    public void remove(String planId) {
        StoreProductionPlan plan = baseMapper.selectById(planId);
        if (plan == null) {
            throw new BaseException("计划不存在");
        }

        // 校验状态
        if (plan.getApproveStatus().equals(1)) {
            BizExp.pop("只有未审核的计划可以删除");
        }

        // 删除详情和主表（逻辑删除）
        storeProductionPlanDetailService.remove(planId);
        plan.setEnabled(0);
        this.updateById(plan);
    }

    @Override
    public void submit(String id) {
        StoreProductionPlan storeProductionPlan = baseMapper.selectById(id);
        storeProductionPlan.setApproveStatus(0);
        this.updateById(storeProductionPlan);
    }

    @Override
    public List<StoreProductionPlan> approvedLists() {
        // 查询所有审核通过的计划
        return baseMapper.selectList(new QueryWrapper<StoreProductionPlan>().eq("approve_status", 1));
    }
}