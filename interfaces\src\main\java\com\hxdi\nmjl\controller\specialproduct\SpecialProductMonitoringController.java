package com.hxdi.nmjl.controller.specialproduct;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductMonitoring;
import com.hxdi.nmjl.service.specialproduct.SpecialProductMonitoringService;
import com.hxdi.nmjl.condition.specialproduct.SpecialProductMonitoringCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 优特产品分析报告管理
 <AUTHOR>
 @version 1.0
 @since 2025/7/16
 */
@Api(tags = "优特产品分析报告管理")
@RestController
@RequestMapping("/specialProductMonitoring")
public class SpecialProductMonitoringController extends BaseController<SpecialProductMonitoringService, SpecialProductMonitoring> {

    @ApiOperation("分页查询分析报告")
    @GetMapping("/page")
    public ResultBody<Page<SpecialProductMonitoring>> page(SpecialProductMonitoringCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }


    @ApiOperation("列表查询分析报告")
    @GetMapping("/list")
    public ResultBody<List<SpecialProductMonitoring>> list(SpecialProductMonitoringCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }


    @ApiOperation("保存/修改分析报告")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody SpecialProductMonitoring monitoring) {
        if (CommonUtils.isEmpty(monitoring.getId())) {
            bizService.create(monitoring);
        } else {
            bizService.update(monitoring);
        }
        return ResultBody.ok();
    }


    @ApiOperation("查看报告详情")
    @GetMapping("/getDetail")
    public ResultBody<SpecialProductMonitoring> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }


    @ApiOperation("删除分析报告")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

}
