package com.hxdi.nmjl.linker;

import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.enums.InoutBusinessType;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.service.inout.InoutDetailService;
import com.hxdi.nmjl.service.plan.ContractInfoService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 上下文服务：用于提供全局关联性查询，简化服务间调用
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/17 09:36
 */
@Component
@Slf4j
public class ContextService {

    @Resource
    private InoutDetailService inoutDetailService;

    @Resource
    private ContractInfoService contractInfoService;

    @Autowired
    private SystemNumberRuleClient systemNumberRuleClient;


    /**
     * 生成业务编号
     * @param code   规则编码
     * @return
     */
    public Object generateCode(String code) {
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode(code);
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        return businessCode.getValue();
    }

    /**
     * 查询价格
     *
     * @param bizId
     * @param bizType
     * @param catalogId
     * @param grade
     * @return
     */
    public BigDecimal findPrice(String bizId, String bizType, String catalogId, String grade) {
        String orderId = inoutDetailService.getOrderId(bizId);

        if (InoutBusinessType.IN_PURCHASE.getCode().equals(bizType)) {
            //采购计划
            if (CommonUtils.isNotEmpty(orderId)) {
                return contractInfoService.findPrice(1, orderId, catalogId, grade);
            }
        } else if (InoutBusinessType.OUT_SALE.getCode().equals(bizType)) {
            //销售订单
            if (CommonUtils.isNotEmpty(orderId)) {
                return contractInfoService.findPrice(2, orderId, catalogId, grade);
            }
        }

        return BigDecimal.ZERO;
    }


}
