package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.base.ClientInfoCondition;
import com.hxdi.nmjl.domain.base.ClientInfo;
import com.hxdi.nmjl.service.base.ClientInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户信息控制器
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "客户信息管理")
@RequestMapping("/client")
public class ClientInfoController extends BaseController<ClientInfoService, ClientInfo> {

    /**
     * 新增客户信息
     *
     * @param ClientInfo 实体
     * @return 新增结果
     */
    @Log(value = "保存或更新客户", saveReqParam = true)
    @PostMapping("/saveOrUpdate")
    @ApiOperation("新增客户信息")
    public ResultBody<Void> save(@RequestBody ClientInfo ClientInfo) {
        if (ClientInfo.getId() == null) {
            bizService.createV1(ClientInfo);
        } else {
            bizService.updateV1(ClientInfo, false);
        }

        return ResultBody.OK();
    }

    @Log(value = "删除客户", saveReqParam = true)
    @PostMapping("/delete")
    @ApiOperation("删除客户信息")
    public ResultBody<Void> delete(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.OK();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/get")
    @ApiOperation("单条数据查询")
    public ResultBody<ClientInfo> selectOne(@RequestParam String id) {
        return ResultBody.<ClientInfo>OK().data(bizService.selectOne(id));
    }


    @GetMapping("/page")
    @ApiOperation("分页查询")
    public ResultBody<Page<ClientInfo>> page(ClientInfoCondition condition) {
        return ResultBody.<Page<ClientInfo>>OK().data(bizService.getPage(condition));
    }

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public ResultBody<List<ClientInfo>> list(ClientInfoCondition condition) {
        return ResultBody.<List<ClientInfo>>OK().data(bizService.getList(condition));
    }

    @GetMapping("/search")
    @ApiOperation("列表查询-用于选择控件")
    public ResultBody<List<ClientInfo>> searchList(ClientInfoCondition condition) {
        return ResultBody.<List<ClientInfo>>OK().data(bizService.searchList(condition));
    }

}
