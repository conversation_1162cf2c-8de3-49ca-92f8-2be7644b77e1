<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.StorageCapacityMapper">

    <!-- 结果映射 -->
    <resultMap id="StorageCapacityResultMap" type="com.hxdi.nmjl.domain.inventory.StorageCapacity">
        <!-- 主键 -->
        <id property="id" column="ID" />
        <!-- 军供站ID -->
        <result property="storeId" column="STORE_ID" />
        <!-- 军供站名称 -->
        <result property="storeName" column="STORE_NAME" />
        <!-- 最大库容量 -->
        <result property="maxCap" column="MAX_CAP" />
        <!-- 已使用容量 -->
        <result property="usedCap" column="USED_CAP" />
        <!-- 可用容量 -->
        <result property="availableCap" column="AVAILABLE_CAP" />
        <!-- 管理单位ID -->
        <result property="orgId" column="ORG_ID" />
        <!-- 仓房ID -->
        <result property="stId" column="ST_ID" />
        <!-- 仓房名称 -->
        <result property="stName" column="ST_NAME" />
        <!-- 管理单位名称 -->
        <result property="orgName" column="ORG_NAME" />
        <!-- 业务状态：1-闲置，2-租用，3-政策性占用 -->
        <result property="state" column="STATE" />
        <!-- 状态:1-有效，0-删除 -->
        <result property="enabled" column="ENABLED" />
        <!-- 附件 -->
        <result property="attachments" column="ATTACHEMENTS" />
        <!-- 审批状态：0-未审核，1-已审核，2-驳回 -->
        <result property="approveStatus" column="APPROVE_STATUS" />
        <!-- 审批人 -->
        <result property="approver" column="APPROVER" />
        <!-- 审批时间 -->
        <result property="approveTime" column="APPROVE_TIME" />
        <!-- 审批意见 -->
        <result property="approveOpinion" column="APPROVE_OPINION" />
        <!-- 创建时间 -->
        <result property="createTime" column="CREATE_TIME" />
        <!-- 更新时间 -->
        <result property="updateTime" column="UPDATE_TIME" />
        <!-- 创建id -->
        <result property="createId" column="CREATE_ID" />
        <!-- 更新id -->
        <result property="updateId" column="UPDATE_ID" />
        <!-- 租户id -->
        <result property="tenantId" column="TENANT_ID" />
        <!-- 组织 -->
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, STORE_ID, STORE_NAME, MAX_CAP, USED_CAP, AVAILABLE_CAP,
        ORG_ID, ST_ID, ST_NAME, ORG_NAME, STATE, ENABLED, ATTACHEMENTS,
        APPROVE_STATUS, APPROVER, APPROVE_TIME, APPROVE_OPINION,
        CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <!-- 列表查询 -->
    <select id="getList" parameterType="com.hxdi.nmjl.condition.inventory.StorageCapacityCondition"
            resultMap="StorageCapacityResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_STORAGE_CAPACITY
        <where>
            ENABLED = 1
            <!-- 军供站名称 -->
            <if test="condition.storeId != null and condition.storeId != ''">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                AND ST_ID = #{condition.stId}
            </if>
            <!-- 业务状态 -->
            <if test="condition.state != null">
                AND STATE = #{condition.state}
            </if>
            <!-- 审批状态 -->
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 分页查询 -->
    <select id="getPages" parameterType="com.hxdi.nmjl.condition.inventory.StorageCapacityCondition"
            resultMap="StorageCapacityResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_STORAGE_CAPACITY
        <where>
            ENABLED = 1
            <!-- 军供站名称 -->
            <if test="condition.storeId != null and condition.storeId != ''">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                AND ST_ID = #{condition.stId}
            </if>
            <!-- 业务状态 -->
            <if test="condition.state != null">
                AND STATE = #{condition.state}
            </if>
            <!-- 审批状态 -->
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
