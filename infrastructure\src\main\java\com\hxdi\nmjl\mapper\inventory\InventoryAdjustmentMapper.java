package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inventory.InventoryAdjustment;
import com.hxdi.nmjl.condition.inventory.InventoryAdjustmentCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 库存调整服务数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@Mapper
public interface InventoryAdjustmentMapper extends SuperMapper<InventoryAdjustment> {

    /**
     * 分页查询
     *
     * @param page      分页参数
     * @param condition 查询条件
     * @return 分页结果
     */
    @DataPermission
    Page<InventoryAdjustment> selectPageV1(Page<InventoryAdjustment> page, @Param("condition") InventoryAdjustmentCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表结果
     */
    @DataPermission
    List<InventoryAdjustment> selectListV1(@Param("condition") InventoryAdjustmentCondition condition);
}

