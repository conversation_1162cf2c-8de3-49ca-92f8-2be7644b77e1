package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.hxdi.nmjl.domain.plan.ProductionOrderTrace;
import com.hxdi.nmjl.mapper.plan.ProductionOrderTraceMapper;
import com.hxdi.nmjl.service.plan.ProductionOrderTraceService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class ProductionOrderTraceServiceImpl extends BaseServiceImpl<ProductionOrderTraceMapper, ProductionOrderTrace> implements ProductionOrderTraceService{

    @Override
    public void generateTrace(List<ProductionOrderTrace> traceList) {
        this.saveBatch(traceList);
    }

    @Override
    public List<ProductionOrderTrace> getTraceList(String orderId) {
        return this.list(Wrappers.<ProductionOrderTrace>lambdaQuery().eq(ProductionOrderTrace::getOrderId, orderId));
    }
}
