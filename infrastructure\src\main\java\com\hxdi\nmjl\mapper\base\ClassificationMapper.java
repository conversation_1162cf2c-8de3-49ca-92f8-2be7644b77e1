package com.hxdi.nmjl.mapper.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.base.Classification;
import com.hxdi.nmjl.condition.base.ClassificationCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物资分类信息表 Mapper 接口
 * <AUTHOR>
 * @date 2021-03-04
 */
@Mapper
public interface ClassificationMapper extends SuperMapper<Classification> {

    /**
     * 查找最大的排序数值
     * @param parentId
     * @return
     */
    Integer selectMaxSeqNumber(String parentId);

    /**
     * 分页
     * @param page
     * @param condition
     * @return
     */
    Page<Classification> selectPageV1(Page<Classification> page, @Param("condition") ClassificationCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    List<Classification> selectListV1(@Param("condition") ClassificationCondition condition);

    /**
     * rabbitMq推送
     * @return
     */
    List<Classification> selectAll();

    /**
     * 逻辑删除
     * @param id
     */
    void softDeleteById(String id);
}
