package com.hxdi.nmjl.service.inout.delivery;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.delivery.TransportRoute;

import java.util.List;

/**
 * 运输轨迹表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-23 11:18:48
 */
public interface TransportRouteService extends IBaseService<TransportRoute> {
    /**
     * 根据主键查询
     *
     * @param deliveryId 主键
     * @return 运输轨迹列表
     */
    List<TransportRoute> getListByPid(String deliveryId);
}

