package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hxdi.nmjl.domain.plan.SaleOrderItem;

import java.util.List;

public interface SaleOrderItemService extends IService<SaleOrderItem> {


    /**
     * 根据订单ID查询订单详情
     *
     * @param orderId
     * @return
     */
    List<SaleOrderItem> getList(String orderId);

    /**
     * 根据订单ID列表批量查询订单详情
     *
     * @param IdList
     * @return
     */
    List<SaleOrderItem> getList(List<String> IdList);

    void removeV1(String id);

    /**
     * 根据订单ID、品种ID、质量等级查询订单详情
     *
     * @param orderId
     * @param catalogId
     * @param grade
     * @return
     */
    SaleOrderItem listByCatalogIdAndGrade(String orderId, String catalogId, String grade);
}
