package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.emergency.EmergencyResponseItem;
import com.hxdi.nmjl.mapper.emergency.EmergencyResponseItemMapper;
import com.hxdi.nmjl.service.emergency.EmergencyResponseItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class EmergencyResponseItemServiceImpl extends BaseServiceImpl<EmergencyResponseItemMapper, EmergencyResponseItem> implements EmergencyResponseItemService {
    @Override
    public List<EmergencyResponseItem> getList(List<String> responseId){
        return baseMapper.selectList(Wrappers.<EmergencyResponseItem>lambdaQuery().in(EmergencyResponseItem::getResponseId, responseId));
    }
}
