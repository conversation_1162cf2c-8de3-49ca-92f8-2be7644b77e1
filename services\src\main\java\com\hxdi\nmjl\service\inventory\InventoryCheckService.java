package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.InventoryCheck;
import com.hxdi.nmjl.condition.inventory.InventoryCheckCondition;

import java.util.List;

/**
 * 盘点记录管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
public interface InventoryCheckService extends IBaseService<InventoryCheck> {

    /**
     * 生成盘点记录
     * 根据盘点计划配置的时间规则自动生成盘点记录
     */
    void timedGenerate();

    /**
     * 立即生成盘点记录
     * 不考虑时间条件，立即生成盘点记录
     */
    void generateNow(String checkConfigId);

    /**
     * 更新
     *
     * @param inventoryCheck 盘点记录
     */
    void update(InventoryCheck inventoryCheck);


    /**
     * 查询详情
     *
     * @param id 盘点记录ID
     * @return 盘点记录
     */
    InventoryCheck getDetail(String id);

    /**
     * 删除盘点记录
     *
     * @param id 盘点记录ID
     */
    void delete(String id);


    /**
     * 分页查询盘点记录
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<InventoryCheck> selectPage(InventoryCheckCondition condition);

    /**
     * 查询盘点记录列表
     *
     * @param condition 查询条件
     * @return 盘点记录列表
     */
    List<InventoryCheck> selectList(InventoryCheckCondition condition);

    /**
     * 提交
     *
     * @param id 盘点记录ID
     */
    void submit(String id);

    /**
     * 审核
     *
     * @param id   盘点记录ID
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id 盘点记录ID
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 查询已审核的盘点记录分页列表
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<InventoryCheck> getApprovedPage(InventoryCheckCondition condition);
}
