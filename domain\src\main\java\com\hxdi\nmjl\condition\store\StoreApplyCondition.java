package com.hxdi.nmjl.condition.store;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "店铺申请查询条件")
@Getter
@Setter
public class StoreApplyCondition extends QueryCondition {


    @ApiModelProperty(value = "申请编号")
    private String applyNo;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "申请类型：1-政策性；2-军民融合")
    private Integer applyType;

    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

}

