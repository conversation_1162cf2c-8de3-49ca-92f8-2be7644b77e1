package com.hxdi.nmjl.service.quality.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.quality.QualityItem;
import com.hxdi.nmjl.mapper.quality.QualityItemMapper;
import com.hxdi.nmjl.service.quality.QualityItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Service
public class QualityItemServiceImpl extends BaseServiceImpl<QualityItemMapper, QualityItem> implements QualityItemService {

    @Override
    public List<QualityItem> getList(Serializable schemaId) {
        LambdaQueryWrapper<QualityItem> wrapper=new LambdaQueryWrapper<QualityItem>()
                .eq(QualityItem::getSchemaId,schemaId);
        return baseMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdate(List<QualityItem> itemList) {
        if(CommonUtils.isNotEmpty(itemList)){

            List<QualityItem> update=new ArrayList<>();
            List<QualityItem> insert=new ArrayList<>();
            // 根据有无Id判断是否为更新
            itemList.forEach(qualityItem -> {
                if(CommonUtils.isNotEmpty(qualityItem.getId())){
                    update.add(qualityItem);
                }else {
                    insert.add(qualityItem);
                }
            });
            if(CommonUtils.isNotEmpty(update)){
                updateBatchById(update);
            }
            if(CommonUtils.isNotEmpty(insert)){
                saveBatch(insert);
            }
        }
    }
}
