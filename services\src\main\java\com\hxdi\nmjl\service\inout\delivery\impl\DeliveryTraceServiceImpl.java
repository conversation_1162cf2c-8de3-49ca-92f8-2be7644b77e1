package com.hxdi.nmjl.service.inout.delivery.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryTrace;
import com.hxdi.nmjl.mapper.inout.delivery.DeliveryTraceMapper;
import com.hxdi.nmjl.service.inout.delivery.DeliveryTraceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 配送状态跟踪Service实现类
 *
 * <AUTHOR>
 * @since 2025/4/23 11:10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class DeliveryTraceServiceImpl extends BaseServiceImpl<DeliveryTraceMapper, DeliveryTrace> implements DeliveryTraceService {

    @Override
    public List<DeliveryTrace> getListByPid(String deliveryId) {
        return baseMapper.selectList(Wrappers.<DeliveryTrace>lambdaQuery()
                .eq(DeliveryTrace::getDeliveryId, deliveryId));
    }

}
