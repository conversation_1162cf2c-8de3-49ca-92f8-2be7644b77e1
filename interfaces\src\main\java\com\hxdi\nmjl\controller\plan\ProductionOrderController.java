package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.ProductionOrderCondition;
import com.hxdi.nmjl.domain.plan.ProductionOrder;
import com.hxdi.nmjl.domain.plan.ProductionOrderTrace;
import com.hxdi.nmjl.service.plan.ProductionOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产订单控制层
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "生产订单管理")
@RequestMapping("/productionOrder")
public class ProductionOrderController extends BaseController<ProductionOrderService, ProductionOrder> {

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("查询详情")
    @GetMapping("/selectOne")
    public ResultBody<ProductionOrder> selectOne(String id) {
        return ResultBody.<ProductionOrder>OK().data(bizService.getDetail(id));
    }

    @ApiOperation("分页查询生产订单")
    @GetMapping("/page")
    public ResultBody<Page<ProductionOrder>> page(ProductionOrderCondition condition) {
        return ResultBody.<Page<ProductionOrder>>OK().data(bizService.pageV1(condition));
    }

    @ApiOperation("列表查询生产订单（带数据权限）")
    @GetMapping("/list")
    public ResultBody<List<ProductionOrder>> listV1(ProductionOrderCondition condition) {
        return ResultBody.<List<ProductionOrder>>OK().data(bizService.listV1(condition));
    }

    @ApiOperation("列表查询生产订单(包含明细)")
    @GetMapping("/listV2")
    public ResultBody<List<ProductionOrder>> listV2(ProductionOrderCondition condition) {
        return ResultBody.<List<ProductionOrder>>OK().data(bizService.listV2(condition));
    }

    @ApiOperation("保存/修改生产订单")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody ProductionOrder productionOrder) {
        if (CommonUtils.isEmpty(productionOrder.getId())) {
            bizService.createV1(productionOrder);
        } else {
            bizService.updateV1(productionOrder);
        }
        return ResultBody.OK();
    }

    @ApiOperation("提交生产订单")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submitV1(id);
        return ResultBody.OK();
    }

    @ApiOperation("删除生产订单")
    @GetMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        bizService.removeV1(id);
        return ResultBody.OK();
    }

    @ApiOperation("填报生产订单")
    @PostMapping("/fillOrder")
    public ResultBody<Void> fillOrder(@RequestBody List<ProductionOrderTrace> traceList) {
        bizService.fillOrder(traceList);
        return ResultBody.OK();
    }

    @ApiOperation("更新订单结算状态")
    @GetMapping("/updateSettlementStatus")
    public ResultBody<Void> updateSettlementStatus(@RequestParam String orderIds) {
        bizService.updateSettlementStatus(orderIds);
        return ResultBody.OK();
    }


}
