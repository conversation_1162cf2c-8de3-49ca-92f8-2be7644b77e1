<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.specialproduct.SpecialProductInfoMapper">

    <sql id="Base_Column_List">
        ID, PRODUCT_NAME, PRODUCT_CODE, SPEC, UNIT, GRADE, EXPIRS, ORIGIN,
    PRODUCT_DATE, LINKER, MOBILE, CLIENT_NAME, BIZ_LICENSE, FOOD_LICENSE,
    EMAIL, CLIENT_ID, APPROVE_STATUS, PUBLISH_STATE, ENABLED, ATTACHMENTS,
    CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="SPEC" jdbcType="VARCHAR" property="spec"/>
        <result column="UNIT" jdbcType="VARCHAR" property="unit"/>
        <result column="GRADE" jdbcType="VARCHAR" property="grade"/>
        <result column="EXPIRS" jdbcType="INTEGER" property="expirs"/>
        <result column="ORIGIN" jdbcType="VARCHAR" property="origin"/>
        <result column="PRODUCT_DATE" jdbcType="TIMESTAMP" property="productDate"/>
        <result column="LINKER" jdbcType="VARCHAR" property="linker"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="CLIENT_NAME" jdbcType="VARCHAR" property="clientName"/>
        <result column="BIZ_LICENSE" jdbcType="VARCHAR" property="bizLicense"/>
        <result column="FOOD_LICENSE" jdbcType="VARCHAR" property="foodLicense"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="CLIENT_ID" jdbcType="VARCHAR" property="clientId"/>
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus"/>
        <result column="PUBLISH_STATE" jdbcType="INTEGER" property="publishState"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="ATTACHMENTS" jdbcType="VARCHAR" property="attachments"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_SPECIAL_PRODUCT_INFO
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.productNameLike)">
                and PRODUCT_NAME like concat('%', #{condition.productNameLike}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.grade)">
                and GRADE = #{condition.grade}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.originLike)">
                and ORIGIN like concat('%', #{condition.originLike}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientNameLike)">
                and CLIENT_NAME like concat('%', #{condition.clientNameLike}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
                and CLIENT_ID = #{condition.clientId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.publishState)">
                and PUBLISH_STATE = #{condition.publishState}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo">
        select <include refid="Base_Column_List"/>
        from B_SPECIAL_PRODUCT_INFO
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.productNameLike)">
                and PRODUCT_NAME like concat('%', #{condition.productNameLike}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.grade)">
                and GRADE = #{condition.grade}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.originLike)">
                and ORIGIN like concat('%', #{condition.originLike}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientNameLike)">
                and CLIENT_NAME like concat('%', #{condition.clientNameLike}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
                and CLIENT_ID = #{condition.clientId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.publishState)">
                and PUBLISH_STATE = #{condition.publishState}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
</mapper>
