<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryAllocationDetailMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryAllocationDetail">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="allocateId" column="ALLOCATE_ID" jdbcType="VARCHAR"/>
            <result property="batchNum" column="BATCH_NUM" jdbcType="VARCHAR"/>
            <result property="catalogId" column="CATALOG_ID" jdbcType="VARCHAR"/>
            <result property="catalogName" column="CATALOG_NAME" jdbcType="VARCHAR"/>
            <result property="brand" column="BRAND" jdbcType="VARCHAR"/>
            <result property="grade" column="GRADE" jdbcType="VARCHAR"/>
            <result property="specification" column="SPECIFICATION" jdbcType="VARCHAR"/>
            <result property="productDate" column="PRODUCT_DATE" jdbcType="TIMESTAMP"/>
            <result property="planQty" column="PLAN_QTY" jdbcType="DECIMAL"/>
            <result property="completedQty" column="COMPLETED_QTY" jdbcType="DECIMAL"/>
            <result property="reserveLevel" column="RESERVE_LEVEL" jdbcType="VARCHAR"/>
            <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
            <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ALLOCATE_ID,BATCH_NUM,
        CATALOG_ID,CATALOG_NAME,BRAND,
        GRADE,SPECIFICATION,PRODUCT_DATE,
        PLAN_QTY,COMPLETED_QTY,RESERVE_LEVEL,
        TENANT_ID,DATA_HIERARCHY_ID
    </sql>
</mapper>
