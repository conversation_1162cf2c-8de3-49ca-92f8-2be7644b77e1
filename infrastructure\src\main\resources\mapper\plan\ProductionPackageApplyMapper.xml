<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ProductionPackageApplyMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.ProductionPackageApply">
    <!--@mbg.generated-->
    <!--@Table B_PRODUCTION_PACKAGE_APPLY-->
    <id column="ID" property="id" />
    <result column="ORDER_ID" property="orderId" />
    <result column="ORDER_CODE" property="orderCode" />
    <result column="APPLY_CODE" property="applyCode" />
    <result column="APPLY_COMPANY_NAME" property="applyCompanyName" />
    <result column="APPLY_DATE" property="applyDate" />
    <result column="APPLICANT" property="applicant" />
    <result column="APPLICANT_TEL" property="applicantTel" />
    <result column="CATALOG_ID" property="catalogId" />
    <result column="CATALOG_NAME" property="catalogName" />
    <result column="NET_WEIGHT" property="netWeight" />
    <result column="SPECIFICATION" property="specification" />
    <result column="GRADE" property="grade" />
    <result column="EXPIRATION_DATE" property="expirationDate" />
    <result column="ORIGIN" property="origin" />
    <result column="INGREDIENTS" property="ingredients" />
    <result column="FOOD_LICENSE_NO" property="foodLicenseNo" />
    <result column="PRODUCTION_DESC" property="productionDesc" />
    <result column="STORAGE_METHOD" property="storageMethod" />
    <result column="USE_METHOD" property="useMethod" />
    <result column="PRODUCT_ADDR" property="productAddr" />
    <result column="FACTORY_NAME" property="factoryName" />
    <result column="FACTORY_TEL" property="factoryTel" />
    <result column="FACTORY_SITE_URL" property="factorySiteUrl" />
    <result column="PACKAGE_DESC" property="packageDesc" />
    <result column="PACKAGE_TECHNICS" property="packageTechnics" />
    <result column="ISUSE_LOGO" property="isuseLogo" />
    <result column="ISUSE_QUALITY_QRCODE" property="isuseQualityQrcode" />
    <result column="ISUSE_TAG" property="isuseTag" />
    <result column="APPROVE_STATUS" property="approveStatus" />
    <result column="APPROVER" property="approver" />
    <result column="APPROVE_TIME" property="approveTime" />
    <result column="APPROVE_OPINION" property="approveOpinion" />
    <result column="ENABLED" property="enabled" />
    <result column="PRODUCT_QUALITY_REPORT" property="productQualityReport" />
    <result column="PRODUCT_COMPOSITION_REPORT" property="productCompositionReport" />
    <result column="ATTACHEMENTS" property="attachements" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ORDER_ID, APPLY_CODE, APPLY_COMPANY_NAME, APPLY_DATE, APPLICANT, APPLICANT_TEL, 
    CATALOG_ID, "CATALOG_NAME", NET_WEIGHT, SPECIFICATION, GRADE, EXPIRATION_DATE, ORIGIN, 
    INGREDIENTS, FOOD_LICENSE_NO, PRODUCTION_DESC, STORAGE_METHOD, USE_METHOD, PRODUCT_ADDR, 
    FACTORY_NAME, FACTORY_TEL, FACTORY_SITE_URL, PACKAGE_DESC, PACKAGE_TECHNICS, ISUSE_LOGO, 
    ISUSE_QUALITY_QRCODE, ISUSE_TAG, APPROVE_STATUS, APPROVER, APPROVE_TIME, APPROVE_OPINION, 
    ENABLED, PRODUCT_QUALITY_REPORT, PRODUCT_COMPOSITION_REPORT, ATTACHEMENTS, CREATE_TIME, 
    UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, ORDER_CODE
  </sql>

  <select id="listV1" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM B_PRODUCTION_PACKAGE_APPLY
    <where>
      enabled = 1
      <if test="@plugins.OGNL@isNotEmpty(condition.search)">
        AND (ORDER_CODE LIKE CONCAT('%', #{condition.search}, '%') OR APPLY_CODE LIKE CONCAT('%', #{condition.search}, '%') OR APPLY_COMPANY_NAME LIKE CONCAT('%', #{condition.search}, '%') OR APPLICANT LIKE CONCAT('%', #{condition.search}, '%'))
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
        AND APPLY_DATE &gt;= #{condition.startTime}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
        AND APPLY_DATE &lt;= #{condition.endTime}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
        AND APPROVE_STATUS = #{condition.approveStatus}
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
    </select>

  <select id="pageV1" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM B_PRODUCTION_PACKAGE_APPLY
    <where>
      enabled = 1
      <if test="@plugins.OGNL@isNotEmpty(condition.search)">
        AND (ORDER_CODE LIKE CONCAT('%', #{condition.search}, '%') OR APPLY_CODE LIKE CONCAT('%', #{condition.search}, '%') OR APPLY_COMPANY_NAME LIKE CONCAT('%', #{condition.search}, '%') OR APPLICANT LIKE CONCAT('%', #{condition.search}, '%'))
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
        AND APPLY_DATE &gt;= #{condition.startTime}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
        AND APPLY_DATE &lt;= #{condition.endTime}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
        AND APPROVE_STATUS = #{condition.approveStatus}
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>