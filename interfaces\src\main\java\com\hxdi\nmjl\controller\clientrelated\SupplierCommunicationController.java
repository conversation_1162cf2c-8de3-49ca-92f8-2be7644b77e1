package com.hxdi.nmjl.controller.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.SupplierCommunicationCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierCommunication;
import com.hxdi.nmjl.service.clientrelated.SupplierCommunicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商沟通记录管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-11
 */
@Api(tags = "供应商沟通")
@RestController
@RequestMapping("/supplierCommunication")
public class SupplierCommunicationController extends BaseController<SupplierCommunicationService, SupplierCommunication> {

    @ApiOperation("新增/修改沟通记录")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody SupplierCommunication record) {
        if (CommonUtils.isEmpty(record.getId())) {
            bizService.create(record);
        } else {
            bizService.update(record);
        }
        return ResultBody.OK();
    }

    @ApiOperation("查询沟通记录详情")
    @GetMapping("/get")
    public ResultBody<SupplierCommunication> getDetail(@RequestParam String id) {
        return ResultBody.<SupplierCommunication>OK().data(bizService.getDetail(id));
    }

    @ApiOperation("删除沟通记录")
    @DeleteMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        bizService.changeState(id, 7);
        return ResultBody.OK();
    }

    @ApiOperation("分页查询沟通记录")
    @GetMapping("/page")
    public ResultBody<Page<SupplierCommunication>> pages(SupplierCommunicationCondition condition) {
        return ResultBody.<Page<SupplierCommunication>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询沟通记录")
    @GetMapping("/list")
    public ResultBody<List<SupplierCommunication>> lists(SupplierCommunicationCondition condition) {
        return ResultBody.<List<SupplierCommunication>>OK().data(bizService.lists(condition));
    }

    @ApiOperation("更新沟通状态")
    @PostMapping("/updateStatus")
    public ResultBody<Void> updateStatus(@RequestParam String id, @RequestParam Integer status) {
        bizService.updateStatus(id, status);
        return ResultBody.OK();
    }

    @ApiOperation("批量更新沟通状态")
    @PostMapping("/batchUpdateStatus")
    public ResultBody<Void> batchUpdateStatus(@RequestParam List<String> ids, @RequestParam Integer status) {
        bizService.batchUpdateStatus(ids, status);
        return ResultBody.OK();
    }

    @ApiOperation("处理沟通记录")
    @PostMapping("/handle")
    public ResultBody<Void> handleRecord(@RequestParam String id, 
                                        @RequestParam String result,
                                        @RequestParam String handlerId,
                                        @RequestParam String handlerName) {
        bizService.handleRecord(id, result, handlerId, handlerName);
        return ResultBody.OK();
    }

    @ApiOperation("关闭沟通记录")
    @PostMapping("/close")
    public ResultBody<Void> closeRecord(@RequestParam String id) {
        bizService.closeRecord(id);
        return ResultBody.OK();
    }

    @ApiOperation("根据供应商ID查询沟通记录数量")
    @GetMapping("/countBySupplierId")
    public ResultBody<Long> countBySupplierId(@RequestParam String supplierId) {
        return ResultBody.<Long>OK().data(bizService.countBySupplierId(supplierId));
    }

    @ApiOperation("启用/禁用沟通记录")
    @PostMapping("/changeState")
    public ResultBody<Void> changeState(@RequestParam String id, @RequestParam Integer enabled) {
        bizService.changeState(id, enabled);
        return ResultBody.OK();
    }
}
