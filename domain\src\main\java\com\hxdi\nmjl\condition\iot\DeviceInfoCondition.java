package com.hxdi.nmjl.condition.iot;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: nmjl-service
 * @description: 设备信息查询接口
 * @author: 王贝强
 * @create: 2025-04-16 18:25
 */
@Setter
@Getter
@ApiModel(description="设备信息查询条件")
public class DeviceInfoCondition extends QueryCondition {

    @ApiModelProperty(value="仓房ID")
    private String stId;

    @ApiModelProperty(value="仓房名称")
    private String stName;

    @ApiModelProperty(value="设备状态：字典SBZT")
    private Integer deviceState;

    @ApiModelProperty(value="设备IP")
    private String ipa;

}
