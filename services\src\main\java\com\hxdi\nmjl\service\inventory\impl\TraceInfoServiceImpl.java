package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inventory.TraceInfo;
import com.hxdi.nmjl.mapper.inventory.TraceInfoMapper;
import com.hxdi.nmjl.service.inventory.TraceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 追溯管理服务
 */
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class TraceInfoServiceImpl extends BaseServiceImpl<TraceInfoMapper, TraceInfo> implements TraceInfoService {

    @Override
    public boolean exists(String inventoryId, String batchNum) {
        long count = baseMapper.selectCount(Wrappers.<TraceInfo>lambdaQuery().eq(TraceInfo::getInventoryId, inventoryId).eq(TraceInfo::getBatchNum, batchNum));
        return count > 0;
    }
}
