package com.hxdi.nmjl.service.emergency;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.emergency.EmergencyScheduleItemCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyScheduleItem;

import java.math.BigDecimal;
import java.util.List;

/**

 紧急调度项目服务接口
 <AUTHOR>
 @version 1.0
 @since 2025/7/21
 */
public interface EmergencyScheduleItemService extends IBaseService<EmergencyScheduleItem> {


    /**
     删除紧急调度项目
      @param scheduleId 调度 ID
     */
    void removeBatch(String scheduleId);

    /**
     获取紧急调度项目详情
     @param itemId 项目 ID
     @return 紧急调度项目
     */
    EmergencyScheduleItem getDetail(String itemId);

    /**
     获取紧急调度项目分页列表
     @param condition 查询条件
     @return 分页列表
     */
    Page<EmergencyScheduleItem> pages(EmergencyScheduleItemCondition condition);
    /**
     获取紧急调度项目列表
     @param condition 查询条件
     @return 项目列表
     */
    List<EmergencyScheduleItem> lists(EmergencyScheduleItemCondition condition);
}