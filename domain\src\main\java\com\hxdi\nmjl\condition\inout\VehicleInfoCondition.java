package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 车辆管理查询条件
 *
 * <AUTHOR>
 * @since 2025/4/22 16:27
 */
@Getter
@Setter
@ApiModel(description = "车辆管理查询条件")
public class VehicleInfoCondition extends QueryCondition {
    @ApiModelProperty(value = "所属机构")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "车辆状态")
    private Integer vehicleState;

    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "司机")
    private String driver;
}
