package com.hxdi.nmjl.condition.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel("库存容量转换条件")
public class StorageCapacityConversionCondition extends QueryCondition {
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "客户Id")
    private String clientId;

    //调配日期
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "审批状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "业务状态：1-闲置，2-租用，3-政策性占用")
    private Integer state;
}
