package com.hxdi.nmjl.dto.inventory;

import com.hxdi.nmjl.enums.InoutBusinessType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 库存新增或更新对象
 * @author: 王贝强
 * @create: 2025-04-10 17:38
 */
@Setter
@Getter
public class InventoryDTO implements Serializable {

    private static final long serialVersionUID = 7128950238166846574L;

    /**
     * 库点ID
     */
    private String storeId;

    /**
     * 仓房ID
     */
    @NotBlank(message = "仓房ID不能为空")
    private String stId;

    @ApiModelProperty(value = "货位ID")
    @NotBlank(message = "货位ID不能为空")
    private String locId;

    @ApiModelProperty(value = "品种ID")
    @NotBlank(message = "品种ID不能为空")
    private String catalogId;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "质量等级")
    @NotBlank(message = "质量等级不能为空")
    private String grade;

    @ApiModelProperty(value = "储备性质")
    @NotBlank(message = "储备性质不能为空")
    private Integer reserveLevel;

    @ApiModelProperty(value = "生产日期")
    private Date productionDate;

    /**
     * 管理单位ID
     */
    private String manageUnitId;


    @ApiModelProperty(value = "库存变更数量")
    @NotBlank(message = "库存变更数量不能为空")
    private BigDecimal changeQty;


    /***********业务相关信息开始************/
    /**
     * 出入库类型： 01-入库，02-出库
     */
    private String inoutType;

    private String bizType;

    private String mainBizId;

    private String subBizId;

    private String batchNum;
    /***********业务相关信息结束************/




    /*****************库存更新回写数据开始***************/
    /**
     * 库存ID
     */
    private String inventoryId;
    /**
     * 当前库存数量，未更新之前
     */
    private BigDecimal currentInventoryQty;
    /*****************库存更新回写数据结束***************/


    /**
     * 判定是否是入库类型
     * @return
     */
    public boolean isInType() {
        return InoutBusinessType.IN.getCode().equals(inoutType);
    }
}
