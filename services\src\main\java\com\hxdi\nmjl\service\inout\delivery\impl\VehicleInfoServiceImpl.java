package com.hxdi.nmjl.service.inout.delivery.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.inout.delivery.VehicleInfo;
import com.hxdi.nmjl.mapper.inout.delivery.VehicleInfoMapper;
import com.hxdi.nmjl.service.inout.delivery.VehicleInfoService;
import com.hxdi.nmjl.condition.inout.VehicleInfoCondition;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 车辆管理服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-22 14:52:10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class VehicleInfoServiceImpl extends BaseServiceImpl<VehicleInfoMapper, VehicleInfo> implements VehicleInfoService {

    @Override
    public void saveOrUpdateV1(VehicleInfo vehicleInfo) {
        if (CommonUtils.isEmpty(vehicleInfo.getId())) {
            verifyVehicleInfo(vehicleInfo);
            baseMapper.insert(vehicleInfo);
        } else {
            VehicleInfo existingVehicle = getById(vehicleInfo.getId());
            // 车辆状态 1-空闲,2-在途,3-维修,4-停用
            if (existingVehicle.getVehicleState().equals(2)) {
                BizExp.pop("车辆在运输，无法修改车辆信息");
            }
            baseMapper.updateById(vehicleInfo);
        }
    }

    @Override
    public VehicleInfo getDetail(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public void remove(String id) {
        VehicleInfo existingVehicle = getById(id);
        if (existingVehicle.getVehicleState().equals(2)) {
            BizExp.pop("车辆在运输，无法删除");
        }
        VehicleInfo updatingVehicle = new VehicleInfo();
        updatingVehicle.setId(id);
        updatingVehicle.setEnabled(0);
        baseMapper.updateById(updatingVehicle);
    }

    @Override
    public Page<VehicleInfo> pages(VehicleInfoCondition condition) {
        Page<VehicleInfo> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<VehicleInfo> lists(VehicleInfoCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void changeState(Integer deliveryState, String vehicleNo) {
        VehicleInfo vehicleInfo = baseMapper.selectOne(Wrappers.<VehicleInfo>lambdaQuery()
                .eq(VehicleInfo::getVehicleNo, vehicleNo)
                .eq(VehicleInfo::getEnabled, 1));

        VehicleInfo updatingVehicle = new VehicleInfo();
        updatingVehicle.setId(vehicleInfo.getId());
        updatingVehicle.setVehicleNo(vehicleInfo.getVehicleNo());
        // 车辆状态 1-空闲,2-在途,3-维修,4-停用
        if (deliveryState.equals(1)) {
            // 在途
            updatingVehicle.setVehicleState(2);
        } else if (deliveryState.equals(4)) {
            // 空闲
            updatingVehicle.setVehicleState(1);
        }
        baseMapper.updateById(updatingVehicle);
    }


    /**
     * 检验数据有效性
     *
     * @param vehicleInfo 车辆管理信息
     */
    private void verifyVehicleInfo(VehicleInfo vehicleInfo) {
        Long count = baseMapper.selectCount(Wrappers.<VehicleInfo>lambdaQuery()
                .eq(VehicleInfo::getVehicleNo, vehicleInfo.getVehicleNo())
                .eq(VehicleInfo::getIdCard, vehicleInfo.getIdCard())
                .eq(VehicleInfo::getEnabled, 1));
        if (count > 0) {
            BizExp.pop("该车辆信息已经存在");
        }

    }
}

