package com.hxdi.nmjl.service.emergency.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.Query;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.RedisCacheObjectProcessUtil;
import com.hxdi.common.core.utils.Strcat;
import com.hxdi.nmjl.condition.emergency.CommPlanCondition;
import com.hxdi.nmjl.domain.emergency.CommPlan;
import com.hxdi.nmjl.domain.emergency.CommSchema;
import com.hxdi.nmjl.service.emergency.CommPlanService;
import com.hxdi.nmjl.service.emergency.CommSchemaService;
import com.hxdi.nmjl.mapper.emergency.CommSchemaMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_COMM_SCHEMA(沟通方案表)】的数据库操作Service实现
* @createDate 2025-08-15 09:22:38
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class CommSchemaServiceImpl extends BaseServiceImpl<CommSchemaMapper, CommSchema> implements CommSchemaService{

    @Resource
    private CommPlanService commPlanService;

    @Autowired
    private RedisCacheObjectProcessUtil cacheObjectProcessUtil;

    @Override
    public boolean saveOrUpdate(CommSchema entity) {
        if (CommonUtils.isNotEmpty(entity.getId())) {
            // 更新沟通方案
            baseMapper.updateById(entity);
            // 更新或保存关联的沟通计划
            if (CommonUtils.isNotEmpty(entity.getDetailList())) {
                entity.getDetailList().forEach(item -> {
                    // 设置关联id
                    item.setSchemaId(entity.getId());
                    CommPlan commPlan = new CommPlan();
                    BeanUtil.copyProperties(item, commPlan);
                    commPlanService.saveOrUpdate(commPlan);
                });
            }
        } else {
            // 新增沟通方案
            baseMapper.insert(entity);

            // 保存关联的沟通计划
            if (CommonUtils.isNotEmpty(entity.getDetailList())) {
                entity.getDetailList().forEach(item -> {
                    // 设置关联id
                    item.setSchemaId(entity.getId());
                    CommPlan commPlan = new CommPlan();
                    BeanUtil.copyProperties(item, commPlan);
                    commPlanService.saveOrUpdate(commPlan);
                });
            }
        }

        return true;
    }

    @Override
    public Page<CommSchema> getPage(Query condition) {
        Page<CommSchema> page = super.getPage(condition);
        page.getRecords().forEach(item->{
            //转换沟通对象
            String targetAudience = item.getTargetAudience();
            if (targetAudience != null && !targetAudience.isEmpty()) {
                String[] codes = targetAudience.split(",");
                Strcat.StrcatBuilder joiner = Strcat.joinWithDelimiter(",");
                for (String code : codes) {
                    String name = cacheObjectProcessUtil.getDictName("GHGTGLGTDX", code);
                    joiner.join(name);
                }
                item.setTargetAudience(joiner.toString());
            }
        });
        return page;
    }

    @Override
    public CommSchema getById(Serializable id) {
        CommSchema commSchema = baseMapper.selectById(id);
        CommPlanCondition commPlanCondition = new CommPlanCondition();
        commPlanCondition.setSchemaId((String) id);
        List<CommPlan> list = commPlanService.getList(commPlanCondition);
        list.forEach(item->{
            //转换沟通对象
            String targetAudience = item.getCommMode();
            if (targetAudience != null && !targetAudience.isEmpty()) {
                String[] codes = targetAudience.split(",");
                Strcat.StrcatBuilder joiner = Strcat.joinWithDelimiter(",");
                for (String code : codes) {
                    // todo 填充字典类型编码itemkey
                    String name = cacheObjectProcessUtil.getDictName("GHGTGLGTFS", code);
                    joiner.join(name);
                }
                item.setCommMode(joiner.toString());
            }
        });
        commSchema.setDetailList(list);
        return commSchema;
    }

    /**
     * 将沟通方式数字代码转换为中文名称
     *
     * @param code 数字代码
     * @return 对应的沟通方式名称
     */
    private String commMode_coverCodeToName(String code) {
        switch (code) {
            case "1":
                return "视频会议";
            case "2":
                return "线上同步";
            case "3":
                return "现场会议";
            case "4":
                return "线上直播";
            case "5":
                return "线上问卷";
            case "6":
                return "社区走访";
            case "7":
                return "其他方式";
            default:
                return null;
        }
    }

    @Override
    public void commit(String id) {
        CommSchema commSchema = baseMapper.selectById(id);
        if(commSchema.getEnabled()==0){
            commSchema.setEnabled(1);
        }
        baseMapper.updateById(commSchema);
    }
}




