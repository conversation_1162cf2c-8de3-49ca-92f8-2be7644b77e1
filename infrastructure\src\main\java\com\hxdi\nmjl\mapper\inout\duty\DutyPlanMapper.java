package com.hxdi.nmjl.mapper.inout.duty;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;

import com.hxdi.nmjl.domain.inout.duty.DutyPlan;
import com.hxdi.nmjl.condition.inout.DutyCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_DUTY_PLAN】的数据库操作Mapper
* @createDate 2025-04-16 09:17:50
* @Entity com.hxdi.nmjl.domain.inout.duty.BDutyPlan
*/
@Mapper
public interface DutyPlanMapper extends SuperMapper<DutyPlan> {


    /**
     * 分页查询
     * @return
     */
    @DataPermission
    Page<DutyPlan> selectPageV1(Page<DutyPlan> page, @Param("condition") DutyCondition dutyCondition);

    /**
     * 列表查询
     * @return
     */
    @DataPermission
    List<DutyPlan> selectListV1(@Param("condition") DutyCondition dutyCondition);
}
