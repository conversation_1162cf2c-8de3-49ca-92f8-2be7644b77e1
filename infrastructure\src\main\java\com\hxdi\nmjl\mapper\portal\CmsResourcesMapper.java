package com.hxdi.nmjl.mapper.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.portal.PortalCondition;
import com.hxdi.nmjl.condition.portal.ResourceCondition;
import com.hxdi.nmjl.domain.portal.CmsData;
import com.hxdi.nmjl.domain.portal.CmsResources;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CmsResourcesMapper extends SuperMapper<CmsResources> {

    /**
     * 分页查询
     * @param page
     * @param condition
     */
    Page<CmsResources> selectPageV1(Page<CmsResources> page, @Param("condition") ResourceCondition condition);

    /**
     * 门户查询
     * @param page
     * @param condition
     */
    Page<CmsResources> selectPageForPortal(Page<CmsResources> page, @Param("condition") PortalCondition condition);
}
