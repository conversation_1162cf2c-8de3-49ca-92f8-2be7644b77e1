package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 仓房/油罐信息表
 */
@TableName(value = "C_STORE_HOUSE")
@Getter
@Setter
public class StoreHouse extends Entity<StoreHouse> {

    private static final long serialVersionUID = -811458703567238625L;

    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    /**
     * 库点id
     */
    @NotNull(message = "请选择库点信息")
    @TableField(value = "STORE_ID")
    private String storeId;

    /**
     * 仓房名
     */
    @NotEmpty(message = "仓房名不能为空")
    @TableField(value = "NAME")
    private String name;

    /**
     * 编码
     */
    @TableField(value = "ST_CODE")
    private String stCode;

    /**
     * 仓储类型:1-仓房，2-油罐
     */
    @TableField(value = "ST_TYPE")
    private Integer stType;

    /**
     * 用途类型(1:储备 2：收纳3：中转4：气调 5：低温 6：准低温)
     */
    @TableField(value = "APPLY_TYPE")
    private String applyType;

    /**
     * 容量
     */
    @TableField(value = "CAPACITY")
    private BigDecimal capacity;

    /**
     * 容量单位
     */
    @TableField(value = "UNIT")
    private String unit;

    /**
     * 建成日期
     */
    @TableField(value = "BUILD_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date buildDate;

    /**
     * 建筑类型
     */
    @TableField(value = "BUILD_TYPE")
    private String buildType;

    /**
     * 高度(米)
     */
    @TableField(value = "HEIGHTS")
    private BigDecimal heights;

    /**
     * 宽度(米)
     */
    @TableField(value = "WIDTHS")
    private BigDecimal widths;

    /**
     * 长度(米)
     */
    @TableField(value = "LENGTHS")
    private BigDecimal lengths;

    /**
     * 仓房状态
     */
    @TableField(value = "STATE")
    private Integer state;

    /**
     * 描述
     */
    @TableField(value = "REMARKS")
    private String remarks;

    /**
     * 是否启用:0-禁用，1-启用，7-删除
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 数据权限字段
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;


    /**
     * -----------------扩展
     */

    @TableField(exist = false)
    private String storeName;

}
