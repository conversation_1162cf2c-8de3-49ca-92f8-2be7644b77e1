package com.hxdi.nmjl;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @ClassName: WebApplication
 * @Author: laiz<PERSON><PERSON>
 * @Date: 2021/2/15 10:53 下午
 * @Description:
 */
@SpringBootApplication(scanBasePackages = "com.hxdi")
@EnableFeignClients(basePackages = {"com.hxdi.nmjl.feign"})
@EnableDiscoveryClient
@RefreshScope
@MapperScan("com.hxdi.nmjl.mapper")
@EnableScheduling
@EnableCaching
public class WebApplication {
    public static void main(String[] args) {
        SpringApplication.run(WebApplication.class, args);
    }
}
