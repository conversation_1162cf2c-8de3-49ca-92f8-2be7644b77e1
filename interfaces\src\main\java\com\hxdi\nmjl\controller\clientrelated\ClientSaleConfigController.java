package com.hxdi.nmjl.controller.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.clientrelated.ClientSaleConfigCondition;
import com.hxdi.nmjl.domain.clientrelated.ClientSaleConfig;
import com.hxdi.nmjl.service.clientrelated.ClientSaleConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <客户销售配置接口>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "客户销售配置")
@RestController
@RequestMapping("/clientSaleConfig")
public class ClientSaleConfigController extends BaseController<ClientSaleConfigService, ClientSaleConfig> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<ClientSaleConfig>> getPages(ClientSaleConfigCondition condition) {
        return ResultBody.<Page<ClientSaleConfig>>OK().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<ClientSaleConfig>> getList(ClientSaleConfigCondition condition) {
        return ResultBody.<List<ClientSaleConfig>>OK().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<ClientSaleConfig> getDetail(@RequestParam String id) {
        return ResultBody.<ClientSaleConfig>OK().data(bizService.getDetail(id));
    }

    @ApiOperation("删除客户销售配置信息")
    @PostMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.<Boolean>OK().data(bizService.delete(id));
    }

    @ApiOperation(value = "添加/修改客户销售配置")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody ClientSaleConfig clientSaleConfig) {

        if (CommonUtils.isEmpty(clientSaleConfig.getId())) {
            bizService.add(clientSaleConfig);
        } else {
            bizService.update(clientSaleConfig);
        }
        return ResultBody.OK();
    }
}
