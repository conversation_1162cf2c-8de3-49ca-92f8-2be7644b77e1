package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.emergency.EmergencyEventCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyEvent;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.emergency.EmergencyEventMapper;
import com.hxdi.nmjl.service.emergency.EmergencyEventService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应急事件管理服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class EmergencyEventServiceImpl extends BaseServiceImpl<EmergencyEventMapper, EmergencyEvent> implements EmergencyEventService {

    @Resource
    private EmergencyEventMapper emergencyEventMapper;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public Page<EmergencyEvent> getPages(EmergencyEventCondition condition) {
        Page<EmergencyEvent> page = condition.newPage();
        return emergencyEventMapper.getPages(condition, page);
    }

    @Override
    public List<EmergencyEvent> getList(EmergencyEventCondition condition) {
        return emergencyEventMapper.getList(condition);
    }

    @Override
    public EmergencyEvent getDetail(String id) {
        return emergencyEventMapper.selectById(id);
    }

    @Override
    public void add(EmergencyEvent emergencyEvent) {
        // 校验事件名称是否重复
        if (CommonUtils.isNotEmpty(emergencyEvent.getEventName())) {
            Long count = emergencyEventMapper.selectCount(Wrappers.<EmergencyEvent>lambdaQuery()
                    .eq(EmergencyEvent::getEventName, emergencyEvent.getEventName())
                    .eq(EmergencyEvent::getEnabled, StrPool.State.ENABLE));

            if (count > 0) {
                BizExp.pop("该应急事件名称已存在且处于启用状态");
            }
        }

        //生成事件编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("EMERGENCY_EVENT_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        emergencyEvent.setEventCode((String) businessCode.getValue());

        // 设置基础信息
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        String userId = baseUserDetails.getUserId();
        String tenantId = baseUserDetails.getTenantId();
        String dataHierarchyId = baseUserDetails.getDataHierarchyId();

        emergencyEvent.setCreateId(userId);
        emergencyEvent.setUpdateId(userId);
        emergencyEvent.setTenantId(tenantId);
        emergencyEvent.setDataHierarchyId(dataHierarchyId);

        if (!this.save(emergencyEvent)) {
            BizExp.pop("应急事件信息保存失败");
        }
    }

    @Override
    public void update(EmergencyEvent emergencyEvent) {
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        emergencyEvent.setUpdateId(baseUserDetails.getUserId());

        if (!this.updateById(emergencyEvent)) {
            BizExp.pop("应急事件信息更新失败");
        }
    }

    @Override
    public boolean delete(String id) {
        EmergencyEvent emergencyEvent = this.getById(id);
        if (emergencyEvent == null) {
            BizExp.pop("应急事件信息不存在");
        }
        if (emergencyEvent.getEnabled() == 0) {
            BizExp.pop("该应急事件已被删除");
        }

        return this.removeById(id);
    }
}
