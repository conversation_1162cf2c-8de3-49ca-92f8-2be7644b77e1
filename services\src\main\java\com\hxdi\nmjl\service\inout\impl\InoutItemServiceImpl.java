package com.hxdi.nmjl.service.inout.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inout.InoutItem;
import com.hxdi.nmjl.mapper.inout.InoutItemMapper;
import com.hxdi.nmjl.service.inout.InoutItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <出入库明细项服务实现>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/14 23:47
 */
@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class InoutItemServiceImpl extends BaseServiceImpl<InoutItemMapper, InoutItem> implements InoutItemService {

    @Override
    public InoutItem getStatInfo(String mainId) {
        List<InoutItem> list = baseMapper.selectStatInfo(mainId);
        if (list.size() == 1) {
            return list.get(0);
        }

        if (list.size() > 1) {
            InoutItem mergeItem = new InoutItem();
            mergeItem.setInoutDetailId(mainId);
            mergeItem.setCatalogName(list.get(0).getCatalogName());
            mergeItem.setGrade(list.get(0).getGrade());
            mergeItem.setSpecification(list.get(0).getSpecification());
            mergeItem.setQty(list.stream().map(InoutItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            return mergeItem;
        }

        return new InoutItem();
    }

    @Override
    public List<InoutItem> getListByMainId(String mainId) {
        return baseMapper.selectList(Wrappers.<InoutItem>lambdaQuery().eq(InoutItem::getInoutDetailId, mainId));
    }

    @Override
    public List<InoutItem> getListByMainIds(List<String> detailIds) {
        return baseMapper.selectList(Wrappers.<InoutItem>lambdaQuery().in(InoutItem::getInoutDetailId, detailIds));
    }
}

