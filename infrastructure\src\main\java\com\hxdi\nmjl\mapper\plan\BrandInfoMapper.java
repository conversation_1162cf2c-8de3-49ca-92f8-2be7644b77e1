package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.BrandInfo;
import com.hxdi.nmjl.condition.plan.BrandInfoCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BrandInfoMapper extends SuperMapper<BrandInfo> {

    List<BrandInfo> selectListV1(@Param("condition") BrandInfoCondition condition);

    Page<BrandInfo> selectPageV1(Page<BrandInfo> page, @Param("condition") BrandInfoCondition condition);
}
