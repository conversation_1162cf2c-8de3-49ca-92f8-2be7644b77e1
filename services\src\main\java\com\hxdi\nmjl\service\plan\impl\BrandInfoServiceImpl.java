package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.BrandInfoCondition;
import com.hxdi.nmjl.domain.plan.BrandActivity;
import com.hxdi.nmjl.domain.plan.BrandAuthorizing;
import com.hxdi.nmjl.domain.plan.BrandCrisis;
import com.hxdi.nmjl.domain.plan.BrandInfo;
import com.hxdi.nmjl.mapper.plan.BrandInfoMapper;
import com.hxdi.nmjl.service.plan.BrandActivityService;
import com.hxdi.nmjl.service.plan.BrandAuthorizingService;
import com.hxdi.nmjl.service.plan.BrandCrisisService;
import com.hxdi.nmjl.service.plan.BrandInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class BrandInfoServiceImpl extends BaseServiceImpl<BrandInfoMapper, BrandInfo> implements BrandInfoService {


    @Resource
    private BrandCrisisService brandCrisisService;

    @Resource
    private BrandAuthorizingService brandAuthorizingService;

    @Resource
    private BrandActivityService brandActivityService;

    @Override
    public List<BrandInfo> lists(BrandInfoCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public Page<BrandInfo> pages(BrandInfoCondition condition) {
        Page<BrandInfo> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public BrandInfo getDetail(String id) {
        BrandInfo brandInfo = baseMapper.selectById(id);
        if (brandInfo.getEnabled() == 0) {
            throw new BaseException("未找到该品牌信息");
        }
        return brandInfo;
    }

    @Override
    public void create(BrandInfo brandInfo) {
        //设置企业信息
        BaseUserDetails user = SecurityHelper.obtainUser();
        brandInfo.setOrgId(user.getOrganId());
        brandInfo.setOrgName(user.getOrganName());
        this.save(brandInfo);
    }

    @Override
    public void update(BrandInfo brandInfo) {
        if (CommonUtils.isEmpty(brandInfo.getId())) {
            throw new BaseException("企业ID不能为空");
        }
        BrandInfo info = this.getById(brandInfo.getId());
        if (info == null) {
            throw new BaseException("修改的企业信息不存在");
        }
        this.updateById(brandInfo);
    }

    @Override
    public void remove(String id) {
        // 检查品牌危机模块是否存在关联数据
        if (brandCrisisService.count(new QueryWrapper<BrandCrisis>().eq("BRAND_ID", id)) > 0) {
            throw new BaseException("品牌危机模块存在关联数据，无法删除");
        }

        // 检查品牌授权模块是否存在关联数据
        if (brandAuthorizingService.count(new QueryWrapper<BrandAuthorizing>().eq("BRAND_ID", id)) > 0) {
            throw new BaseException("品牌授权模块存在关联数据，无法删除");
        }

        // 检查品牌活动模块是否存在关联数据
        if (brandActivityService.count(new QueryWrapper<BrandActivity>().eq("BRAND_ID", id)) > 0) {
            throw new BaseException("品牌活动模块存在关联数据，无法删除");
        }

        // 所有检查通过，执行逻辑删除操作
        BrandInfo info = this.getById(id);
        info.setEnabled(0);
        this.updateById(info);
    }
}