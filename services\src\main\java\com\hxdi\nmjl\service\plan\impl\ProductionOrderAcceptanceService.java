package com.hxdi.nmjl.service.plan.impl;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.ProductionOrder;
import com.hxdi.nmjl.domain.plan.ProductionOrderAcceptance;
import com.hxdi.nmjl.domain.plan.ProductionOrderItem;

import java.util.List;

public interface ProductionOrderAcceptanceService extends IBaseService<ProductionOrderAcceptance> {

    /**
     * 创建生产订单验收
     *
     * @param order
     * @param qualityInspection
     */
    void create(ProductionOrder order, List<ProductionOrderItem> qualityInspection);

}
