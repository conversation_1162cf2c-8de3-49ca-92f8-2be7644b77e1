package com.hxdi.nmjl.condition.base;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CatalogCondition extends QueryCondition {

    @ApiModelProperty(value = "品种分类ID")
    private String classificationId;

    @ApiModelProperty(value = "关键字：商品名称、分类名称、品牌字段查询")
    private String keywords;

}
