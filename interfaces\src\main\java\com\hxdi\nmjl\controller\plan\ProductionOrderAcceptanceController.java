package com.hxdi.nmjl.controller.plan;

import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.plan.ProductionOrderAcceptanceCondition;
import com.hxdi.nmjl.domain.plan.ProductionOrderAcceptance;
import com.hxdi.nmjl.service.plan.impl.ProductionOrderAcceptanceService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 生产订单交付验收信息表控制层
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "生产订单交付验收信息表")
@RequestMapping("/productionOrderAcceptance")
public class ProductionOrderAcceptanceController extends CommonApiController<ProductionOrderAcceptanceService, ProductionOrderAcceptance, ProductionOrderAcceptanceCondition> {

}
