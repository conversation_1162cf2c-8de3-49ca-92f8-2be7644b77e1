package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.inventory.StorageCapacityCondition;
import com.hxdi.nmjl.domain.inventory.StorageCapacity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StorageCapacityMapper extends SuperMapper<StorageCapacity> {
    @DataPermission
    List<StorageCapacity> getList(@Param("condition") StorageCapacityCondition condition);
    @DataPermission
    Page<StorageCapacity> getPages(@Param("condition") StorageCapacityCondition condition, @Param("page") Page<StorageCapacity> page);


}
