package com.hxdi.nmjl.domain.mobilization;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**

 动员商品信息
 */
@Getter
@Setter
@TableName(value = "B_MOBILIZED_PRODUCT")
public class MobilizedProduct implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;
    /**
     动员企业 id
     */
    @TableField(value = "ENTERPRISE_ID")
    private String enterpriseId;
    /**
     品类 id
     */
    @TableField (value = "CLASSIFICATION_ID")
    private String classificationId;
    /**
     品类名称
     */
    @TableField (value = "CLASSIFICATION_NAME")
    private String classificationName;
    /**
     品种 id
     */
    @TableField (value = "CATALOG_ID")
    private String catalogId;
    /**
     品种名称
     */
    @TableField (value = "CATALOG_NAME")
    private String catalogName;
    /**
     质量等级：字典：YZLDJ/LZLDJ
     */
    @TableField (value = "GRADE")
    private String grade;
    /**
     规格
     */
    @TableField (value = "SPECIFICATION")
    private String specification;
    /**
     单价
     */
    @TableField (value = "PRICE")
    private BigDecimal price;
    /**
     产地
     */
    @TableField (value = "ORIGIN")
    private String origin;
    /**
     计量单位
     */
    @TableField (value = "UNIT")
    private String unit;
    /**
     平均日产能
     */
    @TableField (value = "AVG_PRODUCT_CAP")
    private String avgProductCap;
    /**
     生产地域
     */
    @TableField (value = "AREA")
    private String area;

    /**
     * 状态：0 - 待确认，1 - 已确认
     */
    private Integer state;

    /**
     状态：1 - 有效，0 - 删除
     */
    @TableField (value = "ENABLED")
    private Integer enabled;
    /**
     创建时间
     */
    @TableField (value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     更新时间
     */
    @TableField (value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     创建 id
     */
    @TableField (value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;
    /**
     更新 id
     */
    @TableField (value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;
    /**
     租户 id
     */
    @TableField (value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;
    /**
     组织
     */
    @TableField (value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
