package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 调度配送单查询条件
 *
 * <AUTHOR>
 * @since 2025/4/23 11:33
 */
@Getter
@Setter
public class DeliveryOrderCondition extends QueryCondition {

    @ApiModelProperty(value = "配送单号")
    private String deliveryCode;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "调度开始日期")
    private Date deliveryStartDate;

    @ApiModelProperty(value = "调度结束日期")
    private Date deliveryEndDate;
}
