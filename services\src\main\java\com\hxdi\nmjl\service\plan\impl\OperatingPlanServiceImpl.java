package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.OperatingPlanCondition;
import com.hxdi.nmjl.domain.plan.OperatingPlan;
import com.hxdi.nmjl.domain.plan.OperatingPlanDetail;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.OperatingPlanMapper;
import com.hxdi.nmjl.service.plan.OperatingPlanDetailService;
import com.hxdi.nmjl.service.plan.OperatingPlanService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 整体经营计划管理服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class OperatingPlanServiceImpl extends BaseServiceImpl<OperatingPlanMapper, OperatingPlan> implements OperatingPlanService {

    @Resource
    private OperatingPlanMapper operatingPlanMapper;

    @Resource
    private OperatingPlanDetailService operatingPlanDetailService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public Page<OperatingPlan> getPages(OperatingPlanCondition condition) {
        Page<OperatingPlan> page = condition.newPage();
        return operatingPlanMapper.getPages(condition, page);
    }

    @Override
    public List<OperatingPlan> getList(OperatingPlanCondition condition) {
        return operatingPlanMapper.getList(condition);
    }

    @Override
    public OperatingPlan getDetail(String id) {
        OperatingPlan operatingPlan = operatingPlanMapper.selectById(id);
        operatingPlan.setDetailList(operatingPlanDetailService.getList(Collections.singletonList(operatingPlan.getPlanCode())));
        return operatingPlan;
    }

    @Override
    public void add(OperatingPlan operatingPlan) {
        // 校验计划编号是否重复
        if (CommonUtils.isNotEmpty(operatingPlan.getPlanCode())) {
            Long count = operatingPlanMapper.selectCount(Wrappers.<OperatingPlan>lambdaQuery()
                    .eq(OperatingPlan::getPlanCode, operatingPlan.getPlanCode())
                    .eq(OperatingPlan::getEnabled, StrPool.State.ENABLE));

            if (count > 0) {
                BizExp.pop("该经营计划编号已存在且处于启用状态");
            }
        }

        // 生成计划编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("OPERATING_PLAN_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        operatingPlan.setPlanCode((String) businessCode.getValue());

        // 设置基础信息
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        String userId = baseUserDetails.getUserId();
        String tenantId = baseUserDetails.getTenantId();
        String dataHierarchyId = baseUserDetails.getDataHierarchyId();
        operatingPlan.setCreateId(userId);
        operatingPlan.setUpdateId(userId);
        operatingPlan.setTenantId(tenantId);
        operatingPlan.setDataHierarchyId(dataHierarchyId);

        List<OperatingPlanDetail> detailList = operatingPlan.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            BizExp.pop("请填写明细!");
        }
        if (!this.save(operatingPlan)) {
            BizExp.pop("整体经营计划信息保存失败");
        }
        for (OperatingPlanDetail operatingPlanDetail : detailList) {
            operatingPlanDetail.setPlanCode(operatingPlan.getPlanCode());
        }
        if (!operatingPlanDetailService.saveBatch(detailList)) {
            BizExp.pop("明细保存失败");
        }

    }

    @Override
    public void update(OperatingPlan operatingPlan) {
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        operatingPlan.setUpdateId(baseUserDetails.getUserId());

        List<OperatingPlanDetail> detailList = operatingPlan.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            BizExp.pop("请填写明细!");
        }
        //先删除旧明细
        operatingPlanDetailService.remove(Wrappers.<OperatingPlanDetail>lambdaQuery().eq(OperatingPlanDetail::getPlanCode, operatingPlan.getPlanCode()));
        for (OperatingPlanDetail operatingPlanDetail : detailList) {
            operatingPlanDetail.setPlanCode(operatingPlanDetail.getPlanCode());
        }
        if (!operatingPlanDetailService.saveBatch(detailList)) {
            BizExp.pop("明细保存失败");
        }
        if (!this.updateById(operatingPlan)) {
            BizExp.pop("整体经营计划信息更新失败");
        }
    }

    @Override
    public boolean delete(String id) {
        OperatingPlan operatingPlan = this.getById(id);
        if (operatingPlan == null) {
            BizExp.pop("整体经营计划信息不存在");
        }
        if (operatingPlan.getApproveStatus() != null) {
            BizExp.pop("已提交数据无法删除!");
        }
        return this.update(Wrappers.<OperatingPlan>lambdaUpdate().eq(OperatingPlan::getId, id).set(OperatingPlan::getEnabled, 0));
    }

    @Override
    public void approve(String id, String approveOpinion) {
        // 审批通过
        changeApproveStatus(id, 1, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 2, approveOpinion);
    }

    @Override
    public void submit(String id) {
        // 审核状态：0-未审核，1-审核通过，2-驳回
        changeApproveStatus(id, 0, null);
    }

    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        OperatingPlan operatingPlan = baseMapper.selectById(id);
        if (operatingPlan.getEnabled() == 0) {
            BizExp.pop("经营计划信息不存在！");
        }

        // 校验状态：已审核的不能重复审核
        if (approveStatus != 0 && operatingPlan.getApproveStatus() != null && operatingPlan.getApproveStatus() != 0) {
            BizExp.pop("该经营计划信息已审核，无法重复操作！");
        }

        operatingPlan.setApproveStatus(approveStatus);

        if (approveStatus == 1 || approveStatus == 2) {
            if(approveStatus == 1){
                operatingPlan.setState(2);
            }
            operatingPlan.setApprover(SecurityHelper.obtainUser().getNickName());
            operatingPlan.setApproveTime(new Date());
            operatingPlan.setApproveOpinion(approveOpinion);
        } else {
            // 清除审核信息
            operatingPlan.setApprover("");
            operatingPlan.setApproveOpinion("");
        }
        baseMapper.updateById(operatingPlan);
    }

    @Override
    public void split(OperatingPlan operatingPlan) {
        operatingPlan.setState(1);
        OperatingPlan operatingPlanFather = baseMapper.selectById(operatingPlan.getPid());
        operatingPlan.setFatherPlanCode(operatingPlanFather.getPlanCode());
        if(CommonUtils.isNotEmpty(operatingPlan.getId())){
            update(operatingPlan);
        }else{
            add(operatingPlan);
        }
    }

    @Override
    public void issue(String id) {
        OperatingPlan operatingPlan = baseMapper.selectById(id);
        if (operatingPlan == null || operatingPlan.getEnabled() == 0) {
            BizExp.pop("经营计划信息不存在！");
        }
        operatingPlan.setState(2);
        baseMapper.updateById(operatingPlan);
    }

    @Override
    public void finished(String id) {
        OperatingPlan operatingPlan = baseMapper.selectById(id);
        if (operatingPlan == null || operatingPlan.getEnabled() == 0) {
            BizExp.pop("经营计划信息不存在！");
        }
        if(operatingPlan.getState() != 2){
            BizExp.pop("计划未生效，请先下达计划！");
        }
        if(operatingPlan.getFinishPercent()!= null && !operatingPlan.getFinishPercent().isEmpty()){
            operatingPlan.setFinishPercent(operatingPlan.getFinishPercent().replace("%", ""));
            if(!"100".equals(operatingPlan.getFinishPercent())){
                BizExp.pop("计划完成度必须为100%");
            }
        }else{
            BizExp.pop("计划完成度不能为空");
        }
        //判断是父计划还是子计划
        if ("0".equals(operatingPlan.getPid())) {
            operatingPlan.setState(3);
            baseMapper.updateById(operatingPlan);
            //更新子计划状态
            LambdaQueryWrapper<OperatingPlan> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OperatingPlan::getPid, operatingPlan.getId())
                    .eq(OperatingPlan::getEnabled, 1);
            List<OperatingPlan> childPlans = baseMapper.selectList(queryWrapper);
            if (!childPlans.isEmpty()) {
                childPlans.forEach(childPlan -> {
                    childPlan.setState(3); // 子计划也设为“已完成”
                    baseMapper.updateById(childPlan);
                });
            }
        } else {
            // 子计划逻辑：先更新当前子计划状态为2
            operatingPlan.setState(3);
            baseMapper.updateById(operatingPlan);
            //获取当前子计划的pid
            String parentId = operatingPlan.getPid();
            OperatingPlan parentPlan = baseMapper.selectById(parentId);
            if (parentPlan == null || parentPlan.getEnabled() == 0) {
                BizExp.pop("父计划信息不存在！");
            }
            // 2. 查询该父计划下的所有子计划（包含当前子计划）
            LambdaQueryWrapper<OperatingPlan> childWrapper = new LambdaQueryWrapper<>();
            childWrapper.eq(OperatingPlan::getPid, parentId) // 父ID一致
                    .eq(OperatingPlan::getEnabled, 1);   // 仅查询启用的子计划
            List<OperatingPlan> allChildPlans = baseMapper.selectList(childWrapper);
            // 3. 判断所有子计划的state是否都为3
            boolean allChildFinished = allChildPlans.stream()
                    .allMatch(child -> child.getState() == 3); // 检查每个子计划的状态

            // 4. 如果所有子计划都已完成，更新父计划状态为已完成
            if (allChildFinished) {
                parentPlan.setState(3);
                baseMapper.updateById(parentPlan);
            }

        }

    }
}
