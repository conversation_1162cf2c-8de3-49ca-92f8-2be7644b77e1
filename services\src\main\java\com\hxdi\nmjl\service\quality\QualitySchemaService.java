package com.hxdi.nmjl.service.quality;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.quality.QualitySchema;
import com.hxdi.nmjl.dto.quality.QualitySchemaAndItems;
import com.hxdi.nmjl.condition.quality.QuatitySchemaCondition;

import java.io.Serializable;
import java.util.List;

public interface QualitySchemaService extends IBaseService<QualitySchema> {

    /**
     * 新增或修改(有id则更新，否则新增)
     * @param data
     */
    void insertOrUpdate(QualitySchemaAndItems data);

    /**
     * 改变质检方案状态(启用/禁用/删除)，删除后无法再次启用
     * @param schemaId 方案Id
     * @param status 状态(0-禁用，1-启用，7-删除)
     */
    void changeStatus(Serializable schemaId,Integer status);

    /**
     * 根据质检方案Id获取质检方案及对应的质检标准项
     * @param schemaId 方案Id
     * @return
     */
    QualitySchemaAndItems getSchemaAndItems(Serializable schemaId);

    /**
     * 根据品种Id查询对应品种默认质检方案及质检标准项(带用户权限控制)
     * @param classificationId 品类Id
     * @return
     */
    QualitySchemaAndItems selectDefaultByClassificationId(Serializable classificationId);

    /**
     * 根据条件查询质检方案列表(带用户权限控制)
     * @param data 查询条件
     * @return
     */
    List<QualitySchema> getList(QuatitySchemaCondition data);

    /**
     * 根据条件分页查询质检方案(带用户权限控制)
     * @param data 查询条件
     * @return
     */
    Page<QualitySchema> getPage(QuatitySchemaCondition data);
}
