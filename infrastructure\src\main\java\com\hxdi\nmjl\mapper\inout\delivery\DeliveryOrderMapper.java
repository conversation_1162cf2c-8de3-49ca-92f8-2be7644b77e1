package com.hxdi.nmjl.mapper.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryOrder;
import com.hxdi.nmjl.condition.inout.DeliveryOrderCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调度配送单表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-23 10:23:26
 */
@Mapper
public interface DeliveryOrderMapper extends SuperMapper<DeliveryOrder> {

    @DataPermission
    Page<DeliveryOrder> selectPageV1(Page<DeliveryOrder> page, @Param("condition") DeliveryOrderCondition condition);

    @DataPermission
    List<DeliveryOrder> selectListV1(@Param("condition") DeliveryOrderCondition condition);

    @DataPermission
    DeliveryOrder selectOneV1(@Param("condition") DeliveryOrderCondition condition);
}
