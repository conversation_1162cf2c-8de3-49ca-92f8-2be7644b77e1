package com.hxdi.nmjl.controller.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.ReceiptCondition;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.domain.inout.opt.ReceiptOrder;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.service.inout.opt.ReceiptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/receipt")
@Api(tags = "领用审核接口")
@Validated
public class ReceiptController extends BaseController<ReceiptService, ReceiptOrder> {


    @ApiOperation(value = "库存商品列表")
    @GetMapping("/inventoryList")
    public ResultBody<Inventory> getInventoryList(InventoryCondition dto) {
        return ResultBody.ok().data(bizService.getlists(dto));
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody ReceiptOrder receiptOrder) {
        if (CommonUtils.isEmpty(receiptOrder.getId())) {
            bizService.create(receiptOrder);
        } else {
            bizService.updating(receiptOrder);
        }
        return ResultBody.ok();
    }


    @ApiOperation(value = "查询")
    @GetMapping("/query")
    public ResultBody<ReceiptOrder> query(@RequestParam String Id) {
        return ResultBody.ok().data(bizService.detail(Id));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String Id) {
        bizService.delete(Id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<ReceiptOrder>> page(ReceiptCondition receiptCondition) {
        return ResultBody.ok().data(bizService.getPage(receiptCondition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<ReceiptOrder>> list(ReceiptCondition receiptCondition) {
        return ResultBody.ok().data(bizService.getList(receiptCondition));
    }

    @ApiOperation(value = "审核")
    @GetMapping("/review")
    public ResultBody review(@RequestParam("Id") String Id, @RequestParam("approveOpinion") String approveOpinion) {
        bizService.approve(Id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @GetMapping("/rejected")
    public ResultBody rejected(@RequestParam("Id") String Id, @RequestParam("approveOpinion") String approveOpinion) {
        bizService.rejected(Id, approveOpinion);
        return ResultBody.ok();
    }


}
