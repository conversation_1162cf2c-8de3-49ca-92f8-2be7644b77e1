<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryAdjustmentMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryAdjustment">
        <!--@Table B_INVENTORY_ADJUSTMENT-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="storeId" column="STORE_ID" jdbcType="VARCHAR"/>
        <result property="storeName" column="STORE_NAME" jdbcType="VARCHAR"/>
        <result property="bizId" column="BIZ_ID" jdbcType="VARCHAR"/>
        <result property="bizType" column="BIZ_TYPE" jdbcType="VARCHAR"/>
        <result property="type" column="TYPE" jdbcType="VARCHAR"/>
        <result property="applicant" column="APPLICANT" jdbcType="VARCHAR"/>
        <result property="applicationTime" column="APPLICATION_TIME" jdbcType="DATE"/>
        <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>
        <result property="attachments" column="ATTACHMENTS" jdbcType="VARCHAR"/>
        <result property="approveStatus" column="APPROVE_STATUS" jdbcType="INTEGER"/>
        <result property="approver" column="APPROVER" jdbcType="VARCHAR"/>
        <result property="approveTime" column="APPROVE_TIME" jdbcType="TIMESTAMP"/>
        <result property="approveOpinion" column="APPROVE_OPINION" jdbcType="VARCHAR"/>
        <result property="enabled" column="ENABLED" jdbcType="INTEGER"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createId" column="CREATE_ID" jdbcType="VARCHAR"/>
        <result property="updateId" column="UPDATE_ID" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        STORE_ID,
        STORE_NAME,
        BIZ_ID,
        BIZ_TYPE,
        TYPE,
        APPLICANT,
        APPLICATION_TIME,
        REMARKS,
        ATTACHMENTS,
        APPROVE_STATUS,
        APPROVER,
        APPROVE_TIME,
        APPROVE_OPINION,
        ENABLED,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from B_INVENTORY_ADJUSTMENT
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.applyStartTime) and @plugins.OGNL@isNotEmpty(condition.applyEndTime)">
                AND START_TIME BETWEEN #{condition.applyStartTime} AND #{condition.applyEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.type)">
                AND TYPE = #{condition.type}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY APPLICATION_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from B_INVENTORY_ADJUSTMENT
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.applyStartTime)">
                AND APPLICATION_TIME >= #{condition.applyStartTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.applyEndTime)">
                AND APPLICATION_TIME &lt;= #{condition.applyEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.type)">
                AND TYPE = #{condition.type}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY APPLICATION_TIME DESC
    </select>

</mapper>

