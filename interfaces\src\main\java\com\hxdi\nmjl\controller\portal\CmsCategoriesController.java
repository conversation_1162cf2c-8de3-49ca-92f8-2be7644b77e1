package com.hxdi.nmjl.controller.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.portal.CategoriesCondition;
import com.hxdi.nmjl.domain.portal.CmsCategories;
import com.hxdi.nmjl.service.portal.CmsCategoriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 门户-栏目控制层
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/19 09:30
 */
@RestController
@RequestMapping("/cmsCategories")
@Api(tags = "门户-栏目表")
public class CmsCategoriesController extends BaseController<CmsCategoriesService, CmsCategories> {

    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody CmsCategories categories) {
        if (CommonUtils.isEmpty(categories.getId())) {
            bizService.create(categories);
        } else {
            bizService.update(categories);
        }
        return ResultBody.ok();
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResultBody delete(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<CmsCategories> getDetail(String categoryId) {
        return ResultBody.ok().data(bizService.getDetail(categoryId));
    }

    @ApiOperation("查询树结构")
    @GetMapping("/tree")
    public ResultBody<List<CmsCategories>> tree(@RequestParam(required = false) Integer module) {
        return ResultBody.<List<CmsCategories>>OK().data(bizService.tree(module));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<CmsCategories>> pages(CategoriesCondition condition) {
        return ResultBody.<Page<CmsCategories>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<CmsCategories>> lists(CategoriesCondition condition) {
        return ResultBody.<List<CmsCategories>>OK().data(bizService.lists(condition));
    }


}
