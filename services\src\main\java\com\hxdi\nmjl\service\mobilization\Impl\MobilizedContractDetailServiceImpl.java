package com.hxdi.nmjl.service.mobilization.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.mobilization.MobilizedContractDetail;
import com.hxdi.nmjl.mapper.mobilization.MobilizedContractDetailMapper;
import com.hxdi.nmjl.service.mobilization.MobilizedContractDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MobilizedContractDetailServiceImpl extends BaseServiceImpl<MobilizedContractDetailMapper, MobilizedContractDetail> implements MobilizedContractDetailService {

    @Override
    public List<MobilizedContractDetail> getList(String contractId) {
        LambdaQueryWrapper<MobilizedContractDetail> wrapper = new LambdaQueryWrapper<MobilizedContractDetail>()
                .eq(MobilizedContractDetail::getContractId, contractId);
        return this.list(wrapper);
    }

    @Override
    public void remove(String contractId) {
        //根据合同ID删除合同明细
        this.remove(new LambdaQueryWrapper<MobilizedContractDetail>().eq(MobilizedContractDetail::getContractId, contractId));
    }




}
