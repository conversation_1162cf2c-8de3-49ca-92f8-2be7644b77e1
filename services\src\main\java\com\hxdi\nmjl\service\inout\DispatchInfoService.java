package com.hxdi.nmjl.service.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.DispatchInfo;
import com.hxdi.nmjl.condition.inout.DispatchCondition;

import java.util.List;

public interface DispatchInfoService extends IBaseService<DispatchInfo> {
    /**
     * 新增一条或多条拆分记录
     * @param dispatchInfo
     */
    void create(List<DispatchInfo> dispatchInfo);

    void removeByIdList(List<String> dispatchIdList);

    void removeByTaskId(String taskId);


    /**
     * 提交拆分记录，开始执行库存拆分（注意：此操作会更新当前库存数量）
     * @param dispatchIdList
     */
    void doSubmit(List<String> dispatchIdList);

    List<DispatchInfo> listV1(DispatchCondition condition);

    Page<DispatchInfo> PageV1(DispatchCondition condition);

}
