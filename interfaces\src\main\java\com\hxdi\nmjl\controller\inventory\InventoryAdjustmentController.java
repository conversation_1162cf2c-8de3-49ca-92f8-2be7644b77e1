package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inventory.InventoryAdjustment;
import com.hxdi.nmjl.service.inventory.InventoryAdjustmentService;
import com.hxdi.nmjl.condition.inventory.InventoryAdjustmentCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <库存调整管理接口>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21 14:46
 */
@RestController
@RequestMapping("/inventoryAdjustment")
@Api(tags = "库存调整管理")
public class InventoryAdjustmentController extends BaseController<InventoryAdjustmentService, InventoryAdjustment> {

    @ApiOperation(value = "保存或更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody InventoryAdjustment inventoryAdjustment) {
        bizService.saveOrUpdateV1(inventoryAdjustment);
        return ResultBody.ok();
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<InventoryAdjustment> getDetail(@RequestParam @NotNull String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResultBody delete(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<InventoryAdjustment>> pages(InventoryAdjustmentCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<InventoryAdjustment>> lists(InventoryAdjustmentCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation(value = "审核")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @PostMapping("/reject")
    public ResultBody reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "提交")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }
}
