package com.hxdi.nmjl.mapper.inout.duty;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.duty.DutyCheck;
import com.hxdi.nmjl.condition.inout.DutyCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_DUTY_CHECK】的数据库操作Mapper
* @createDate 2025-04-16 09:20:02
* @Entity com.hxdi.nmjl.domain.inout.duty.BDutyCheck
*/
public interface DutyCheckMapper extends SuperMapper<DutyCheck> {

    @DataPermission
    Page<DutyCheck> selectPageV1(Page<DutyCheck> pages, @Param("condition") DutyCondition dutyCondition);

    @DataPermission
    List<DutyCheck> selectListV1(@Param("condition") DutyCondition dutyCondition);
}
