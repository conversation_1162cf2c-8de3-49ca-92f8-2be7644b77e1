package com.hxdi.nmjl.controller.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inout.opt.ProcessRecord;
import com.hxdi.nmjl.service.inout.opt.ProcessRecordService;
import com.hxdi.nmjl.condition.inout.ProcessRecordCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 加工工艺备案接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@RestController
@RequestMapping("/processRecord")
@Api(tags = "加工工艺备案")
public class ProcessRecordController extends BaseController<ProcessRecordService, ProcessRecord> {

    @ApiOperation(value = "保存或更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody ProcessRecord processRecord) {
        bizService.saveOrUpdateV1(processRecord);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<ProcessRecord>> pages(ProcessRecordCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<ProcessRecord>> lists(ProcessRecordCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }


    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<ProcessRecord> getDetail(@RequestParam @NotNull(message = "id不能为空") String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

    @ApiOperation(value = "审核")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @PostMapping("/reject")
    public ResultBody reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "提交")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }
}
