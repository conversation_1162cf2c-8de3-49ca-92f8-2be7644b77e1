package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 监督检查事项
 */
@ApiModel(description = "监督检查事项")
@Getter
@Setter
@TableName("B_INSPECTION_ITEM")
public class InspectionItem  extends BModel {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("MAIN_ID")
    @ApiModelProperty(value = "关联ID")
    private String mainId;

    @TableField("ITEM_TITLE")
    @ApiModelProperty(value = "事项描述")
    private String itemTitle;

    @TableField("REQUIREMENT")
    @ApiModelProperty(value = "检查要求")
    private String requirement;

    @TableField("DEAL_REQUIREMENT")
    @ApiModelProperty(value = "整改要求")
    private String dealRequirement;

    @TableField("DEAL_TIME")
    @ApiModelProperty(value = "整改时间")
    private String dealTime;

    @TableField("RESULT")
    @ApiModelProperty(value = "检查结果：0-正常，1-有问题，2-需整改")
    private Integer result;

    @TableField("STATE")
    @ApiModelProperty(value = "整改状态：0-无需整改，1-待整改，2-已整改")
    private Integer state;

    @TableField("NOTES")
    @ApiModelProperty(value = "备注")
    private String notes;

    @TableField("SORTS")
    @ApiModelProperty(value = "排序")
    private String sorts;

}
