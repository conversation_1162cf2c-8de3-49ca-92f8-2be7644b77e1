<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ProductionOrderAcceptanceMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.ProductionOrderAcceptance">
    <!--@mbg.generated-->
    <!--@Table B_PRODUCTION_ORDER_ACCEPTANCE-->
    <id column="ID" property="id" />
    <result column="ACCEPTANCE_NO" property="acceptanceNo" />
    <result column="ORDER_ID" property="orderId" />
    <result column="ORDER_CODE" property="orderCode" />
    <result column="CONTRACT_ID" property="contractId" />
    <result column="CONTRACT_CODE" property="contractCode" />
    <result column="STORE_ID" property="storeId" />
    <result column="STORE_NAME" property="storeName" />
    <result column="ORG_ID" property="orgId" />
    <result column="ORG_NAME" property="orgName" />
    <result column="CLIENT_ID" property="clientId" />
    <result column="CLIENT_NAME" property="clientName" />
    <result column="CATALOG_ID" property="catalogId" />
    <result column="CATALOG_NAME" property="catalogName" />
    <result column="BATCH_NO" property="batchNo" />
    <result column="DELIVERY_QTY" property="deliveryQty" />
    <result column="ACCEPTANCE_QTY" property="acceptanceQty" />
    <result column="QUALIFIED_QTY" property="qualifiedQty" />
    <result column="UNQUALIFIED_QTY" property="unqualifiedQty" />
    <result column="DELIVERY_DATE" property="deliveryDate" />
    <result column="ACCEPTANCE_DATE" property="acceptanceDate" />
    <result column="ACCEPTOR" property="acceptor" />
    <result column="ACCEPTANCE_RESULT" property="acceptanceResult" />
    <result column="QUALITY_INSPECTION_ID" property="qualityInspectionId" />
    <result column="QUALITY_INSPECTION_NO" property="qualityInspectionNo" />
    <result column="UNQUALIFIED_REASON" property="unqualifiedReason" />
    <result column="HANDLE_METHOD" property="handleMethod" />
    <result column="HANDLE_STATUS" property="handleStatus" />
    <result column="HANDLE_DATE" property="handleDate" />
    <result column="HANDLE_PERSON" property="handlePerson" />
    <result column="HANDLE_REMARK" property="handleRemark" />
    <result column="ATTACHMENTS" property="attachments" />
    <result column="STATE" property="state" />
    <result column="ENABLED" property="enabled" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ACCEPTANCE_NO, ORDER_ID, ORDER_CODE, CONTRACT_ID, CONTRACT_CODE, STORE_ID, STORE_NAME, 
    ORG_ID, ORG_NAME, CLIENT_ID, CLIENT_NAME, CATALOG_ID, "CATALOG_NAME", BATCH_NO, DELIVERY_QTY, 
    ACCEPTANCE_QTY, QUALIFIED_QTY, UNQUALIFIED_QTY, DELIVERY_DATE, ACCEPTANCE_DATE, ACCEPTOR, 
    ACCEPTANCE_RESULT, QUALITY_INSPECTION_ID, QUALITY_INSPECTION_NO, UNQUALIFIED_REASON, 
    HANDLE_METHOD, HANDLE_STATUS, HANDLE_DATE, HANDLE_PERSON, HANDLE_REMARK, ATTACHMENTS, 
    "STATE", ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
  </sql>
</mapper>