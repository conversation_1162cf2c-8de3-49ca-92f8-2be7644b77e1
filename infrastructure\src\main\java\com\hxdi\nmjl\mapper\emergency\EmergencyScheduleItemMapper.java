package com.hxdi.nmjl.mapper.emergency;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.emergency.EmergencyScheduleItemCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyScheduleItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 紧急调度项目Mapper接口
 */
@Mapper
public interface EmergencyScheduleItemMapper extends SuperMapper<EmergencyScheduleItem> {


    Page<EmergencyScheduleItem> selectPageV1(Page<EmergencyScheduleItem> page, @Param("condition") EmergencyScheduleItemCondition condition);

    List<EmergencyScheduleItem> selectListV1(@Param("condition") EmergencyScheduleItemCondition condition);
}