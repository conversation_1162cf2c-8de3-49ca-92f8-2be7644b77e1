package com.hxdi.nmjl.domain.inout.delivery;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 调度配送单表实体类
 *
 * <AUTHOR>
 * @since 2025-04-23 10:23:26
 */
@Getter
@Setter
@TableName("B_DELIVERY_ORDER")
@ApiModel(description = "调度配送单表")
public class DeliveryOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键Id")
    private String id;
    /**
     * 配送单号
     */
    @TableField(value = "DELIVERY_CODE")
    @ApiModelProperty(value = "配送单号")
    private String deliveryCode;
    /**
     * 订单号
     */
    @TableField(value = "ORDER_NO")
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;
    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;
    /**
     * 品牌
     */
    @TableField(value = "BRAND")
    @ApiModelProperty(value = "品牌")
    private String brand;
    /**
     * 规格
     */
    @TableField(value = "SPECIFICATIONS")
    @ApiModelProperty(value = "规格")
    private String specifications;
    /**
     * 等级
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value = "等级")
    private String grade;
    /**
     * 数量
     */
    @TableField(value = "QTY")
    @ApiModelProperty(value = "数量")
    private BigDecimal qty;
    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;
    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;
    /**
     * 取货地址编号
     */
    @TableField(value = "ORI_ADDR_CODE")
    @ApiModelProperty(value = "取货地址编号")
    private String oriAddrCode;
    /**
     * 取货地址
     */
    @TableField(value = "ORI_ADDR")
    @ApiModelProperty(value = "取货地址")
    private String oriAddr;
    /**
     * 取货详细地址
     */
    @TableField(value = "ORI_DETAIL_ADDR")
    @ApiModelProperty(value = "取货详细地址")
    private String oriDetailAddr;
    /**
     * 取货日期
     */
    @TableField(value = "PICKUP_DATE")
    @ApiModelProperty(value = "取货日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickupDate;
    /**
     * 联系人
     */
    @TableField(value = "LINKER")
    @ApiModelProperty(value = "联系人")
    private String linker;
    /**
     * 联系电话
     */
    @TableField(value = "MOBILE")
    @ApiModelProperty(value = "联系电话")
    private String mobile;
    /**
     * 客户ID
     */
    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;
    /**
     * 客户名称
     */
    @TableField(value = "CLIENT_NAME")
    @ApiModelProperty(value = "客户名称")
    private String clientName;
    /**
     * 收货地址编号
     */
    @TableField(value = "RCV_ADDR_CODE")
    @ApiModelProperty(value = "收货地址编号")
    private String rcvAddrCode;
    /**
     * 收货地址
     */
    @TableField(value = "RCV_ADDR")
    @ApiModelProperty(value = "收货地址")
    private String rcvAddr;
    /**
     * 收货详细地址
     */
    @TableField(value = "RCV_DETAIL_ADDR")
    @ApiModelProperty(value = "收货详细地址")
    private String rcvDetailAddr;
    /**
     * 送达日期
     */
    @TableField(value = "RCV_DATE")
    @ApiModelProperty(value = "送达日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date rcvDate;
    /**
     * 收货联系人
     */
    @TableField(value = "RCV_MAN")
    @ApiModelProperty(value = "收货联系人")
    private String rcvMan;
    /**
     * 收货联系电话
     */
    @TableField(value = "RCV_PHONE")
    @ApiModelProperty(value = "收货联系电话")
    private String rcvPhone;
    /**
     * 车牌号
     */
    @TableField(value = "VEHICLE_NO")
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    /**
     * 司机
     */
    @TableField(value = "DRIVER")
    @ApiModelProperty(value = "司机")
    private String driver;
    /**
     * 司机联系电话
     */
    @TableField(value = "DRIVER_PHONE")
    @ApiModelProperty(value = "司机联系电话")
    private String driverPhone;
    /**
     * 司机身份证号
     */
    @TableField(value = "ID_CARD")
    @ApiModelProperty(value = "司机身份证号")
    private String idCard;
    /**
     * 随车人员
     */
    @TableField(value = "SCRY")
    @ApiModelProperty(value = "随车人员")
    private String scry;
    /**
     * 随车人员电话
     */
    @TableField(value = "SCRY_PHONE")
    @ApiModelProperty(value = "随车人员电话")
    private String scryPhone;
    /**
     * 配送状态：字典PSDZT 0-未调度,1-已调度,2-已接单,3-运输中,4-已送达
     */
    @TableField(value = "DELIVERY_STATE")
    @ApiModelProperty(value = "配送状态：字典PSDZT 0-未调度 1-已调度,2-已接单,3-运输中,4-已送达")
    private Integer deliveryState;
    /**
     * 状态：0-删除，1-有效
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态：0-删除，1-有效")
    private Integer enabled;
    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;
    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
    /**
     * 调度日期
     */
    @TableField(value = "DELIVERY_DATE")
    @ApiModelProperty(value = "调度日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    /*******************以下非数据库字段********************/

    @TableField(exist = false)
    @ApiModelProperty(value = "配送状态跟踪")
    private List<DeliveryTrace> deliveryTraceList;

    @TableField(exist = false)
    @ApiModelProperty(value = "运输轨迹")
    private List<TransportRoute> transportRouteList;
}

