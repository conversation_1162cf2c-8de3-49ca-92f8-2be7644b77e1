package com.hxdi.nmjl.service.iot.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.properties.CommonProperties;
import com.hxdi.common.core.utils.DateUtils;
import com.hxdi.nmjl.condition.iot.TemHumDayRecordCondition;
import com.hxdi.nmjl.domain.iot.TemHumDayRecord;
import com.hxdi.nmjl.domain.iot.TemHumRecord;
import com.hxdi.nmjl.mapper.iot.TemHumDayRecordMapper;
import com.hxdi.nmjl.service.iot.TemHumDayRecordService;
import com.hxdi.nmjl.service.iot.TemHumRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TemHumDayRecordServiceImpl extends BaseServiceImpl<TemHumDayRecordMapper, TemHumDayRecord> implements TemHumDayRecordService {

    @Resource
    private TemHumRecordService recordService;

    @Autowired
    private CommonProperties commonProperties;

    @Override
    public void  generateDayRecord() {
        Date yesterday = DateUtils.getBeforeDay(new Date());

        // 查询前一天的数据
        LambdaQueryWrapper<TemHumRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(TemHumRecord::getInspectTime, DateUtils.getStartOfDay(yesterday))
                    .le(TemHumRecord::getInspectTime, DateUtils.getEndOfDay(yesterday));

        TemHumDayRecordCondition condition = new TemHumDayRecordCondition();
        condition.setStartTime(DateUtils.format(DateUtils.getStartOfDay(yesterday), DateUtils.LOCAL_DATE_TIME));
        condition.setEndTime(DateUtils.format(DateUtils.getEndOfDay(yesterday), DateUtils.LOCAL_DATE_TIME));
        List<TemHumRecord> records = baseMapper.selectV2(condition,commonProperties.getTemHumRowNum());

        if (records.isEmpty()) {
            log.info("前一天({})无温湿度记录数据", DateUtils.format(yesterday, DateUtils.LOCAL_DATE));
            return;
        }

        // 按仓房分组并处理
        List<TemHumDayRecord> dayRecordList = records.stream()
                .filter(rec -> rec.getStId() != null && !rec.getStId().trim().isEmpty())
                .collect(Collectors.groupingBy(TemHumRecord::getStId))
                .entrySet().stream()
                .map(entry -> processStRecord(entry.getKey(), entry.getValue(), yesterday))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!dayRecordList.isEmpty()) {
            this.saveBatch(dayRecordList);
            log.info("成功生成 {} 个仓房的日统计记录", dayRecordList.size());
        }
    }

    @Override
    public List<TemHumDayRecord> listV1(TemHumDayRecordCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public Page<TemHumDayRecord> pageV1(TemHumDayRecordCondition condition) {
        Page<TemHumDayRecord> page = condition.newPage();
        baseMapper.selectPageV1(condition, page);
        return page;
    }

    private TemHumDayRecord processStRecord(String stId, List<TemHumRecord> recordList, Date statisticalDate) {
        try {
            if (recordList.size() < 3) {
                log.warn("仓房 {} 记录数量不足3条，无法计算去极值平均值", stId);
                return null;
            }

            // 计算温度平均值(去除最大值和最小值)
            double avgWd = calculateTrimmedAverage(recordList, TemHumRecord::getCw);
            double avgSd = calculateTrimmedAverage(recordList, TemHumRecord::getCs);

            // 获取第一条记录作为基础信息来源
            TemHumRecord firstRecord = recordList.get(0);

            // 生成日统计记录
            TemHumDayRecord dayRecord = new TemHumDayRecord();
            dayRecord.setStId(stId);
            dayRecord.setStoreId(firstRecord.getStoreId());
            dayRecord.setStoreName(firstRecord.getStoreName());
            dayRecord.setStName(firstRecord.getStName());
            dayRecord.setWd(String.format("%.1f", avgWd));
            dayRecord.setSd(String.format("%.1f", avgSd));
            dayRecord.setStatisticalDate(DateUtils.format(statisticalDate, DateUtils.LOCAL_DATE));
            dayRecord.setDataHierarchyId(firstRecord.getDataHierarchyId());

            return dayRecord;
        } catch (Exception e) {
            log.error("仓房 {} 生成日统计信息失败: {}", stId, e.getMessage(), e);
            return null;
        }
    }

    private double calculateTrimmedAverage(List<TemHumRecord> recordList, Function<TemHumRecord, String> valueExtractor) {
        return recordList.stream()
                .map(valueExtractor)
                .filter(Objects::nonNull)
                .mapToDouble(value -> {
                    try {
                        return Double.parseDouble(value);
                    } catch (NumberFormatException e) {
                        log.warn("数值解析失败: {}", value);
                        return Double.NaN;
                    }
                })
                .filter(value -> !Double.isNaN(value))
                .sorted()
                .skip(1)
                .limit(Math.max(0, recordList.size() - 2))
                .average()
                .orElse(0.0);
    }
}
