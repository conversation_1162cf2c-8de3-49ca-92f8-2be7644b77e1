package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * 用于保管账报表
 */
@ApiModel(description="库存统计表")
@Getter
@Setter
@TableName(value = "B_INVENTORY_STAT")
public class InventoryStat implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 库存ID
     */
    @TableField(value = "INVENTORY_ID")
    @ApiModelProperty(value="库存ID")
    private String inventoryId;

    /**
     * 统计日期
     */
    @TableField(value = "REPORT_DATE")
    @ApiModelProperty(value="统计日期")
    private Date reportDate;

    /**
     * 起始库存数量
     */
    @TableField(value = "START_QTY")
    @ApiModelProperty(value="起始库存数量")
    private BigDecimal startQty;

    /**
     * 总入库数量
     */
    @TableField(value = "IN_QTY")
    @ApiModelProperty(value="总入库数量")
    private BigDecimal inQty;

    /**
     * 总出库数量
     */
    @TableField(value = "OUT_QTY")
    @ApiModelProperty(value="总出库数量")
    private BigDecimal outQty;

    /**
     * 终止库存数量
     */
    @TableField(value = "FINAL_QTY")
    @ApiModelProperty(value="终止库存数量")
    private BigDecimal finalQty;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID")
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}