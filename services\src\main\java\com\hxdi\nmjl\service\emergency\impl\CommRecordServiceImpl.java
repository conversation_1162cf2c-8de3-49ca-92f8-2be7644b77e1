package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.Query;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.RedisCacheObjectProcessUtil;
import com.hxdi.common.core.utils.Strcat;
import com.hxdi.nmjl.domain.emergency.CommRecord;
import com.hxdi.nmjl.domain.emergency.CommSchema;
import com.hxdi.nmjl.mapper.emergency.CommRecordMapper;
import com.hxdi.nmjl.service.emergency.CommRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* <AUTHOR>
* @description 针对表【B_COMM_RECORD(《应急粮油供需信息沟通记录》主表)】的数据库操作Service实现
* @createDate 2025-08-15 09:22:34
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class CommRecordServiceImpl extends BaseServiceImpl<CommRecordMapper, CommRecord> implements CommRecordService{

    @Autowired
    private RedisCacheObjectProcessUtil cacheObjectProcessUtil;

    @Override
    public Page<CommRecord> getPage(Query condition) {
        Page<CommRecord> page = super.getPage(condition);
        page.getRecords().forEach(item->{
            //转换沟通类型
            String commType = item.getCommType();
            if (commType != null && !commType.isEmpty()) {
                String[] codes = commType.split(",");
                Strcat.StrcatBuilder joiner = Strcat.joinWithDelimiter(",");
                for (String code : codes) {
                    String name = cacheObjectProcessUtil.getDictName("GLGTGTLX", code);
                    joiner.join(name);
                }
                item.setCommType(joiner.toString());
            }
        });
        return page;
    }

    @Override
    public void submit(String id) {
        CommRecord commRecord = baseMapper.selectById(id);
        commRecord.setRecordStatus(2);
        baseMapper.updateById(commRecord);
    }
}




