<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryCheckDetailMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryCheckDetail">
        <!--@Table B_INVENTORY_CHECK_DETAIL-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="checkId" column="CHECK_ID" jdbcType="VARCHAR"/>
        <result property="inventoryId" column="INVENTORY_ID" jdbcType="VARCHAR"/>
        <result property="currentInventoryQty" column="CURRENT_INVENTORY_QTY" jdbcType="DECIMAL"/>
        <result property="checkQty" column="CHECK_QTY" jdbcType="DECIMAL"/>
        <result property="diffQty" column="DIFF_QTY" jdbcType="DECIMAL"/>
        <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 扩展的结果映射，包含详细信息 -->
    <resultMap id="DetailResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryCheckDetail">
        <!-- 盘点明细表字段 -->
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="checkId" column="CHECK_ID" jdbcType="VARCHAR"/>
        <result property="inventoryId" column="INVENTORY_ID" jdbcType="VARCHAR"/>
        <result property="currentInventoryQty" column="CURRENT_INVENTORY_QTY" jdbcType="DECIMAL"/>
        <result property="checkQty" column="CHECK_QTY" jdbcType="DECIMAL"/>
        <result property="diffQty" column="DIFF_QTY" jdbcType="DECIMAL"/>
        <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>

        <!-- 盘点记录表字段 -->
        <result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
        <result property="approveStatus" column="APPROVE_STATUS" jdbcType="INTEGER"/>
        <result property="checkStatus" column="CHECK_STATUS" jdbcType="INTEGER"/>

        <!-- 库存表字段通过嵌套对象映射 -->
        <association property="inventory" javaType="com.hxdi.nmjl.domain.inventory.Inventory">
            <id property="id" column="I_ID" jdbcType="VARCHAR"/>
            <result property="storeId" column="STORE_ID" jdbcType="VARCHAR"/>
            <result property="storeName" column="STORE_NAME" jdbcType="VARCHAR"/>
            <result property="stId" column="ST_ID" jdbcType="VARCHAR"/>
            <result property="stName" column="ST_NAME" jdbcType="VARCHAR"/>
            <result property="locId" column="LOC_ID" jdbcType="VARCHAR"/>
            <result property="locName" column="LOC_NAME" jdbcType="VARCHAR"/>
            <result property="catalogId" column="CATALOG_ID" jdbcType="VARCHAR"/>
            <result property="catalogName" column="CATALOG_NAME" jdbcType="VARCHAR"/>
            <result property="specifications" column="SPECIFICATIONS" jdbcType="VARCHAR"/>
            <result property="grade" column="GRADE" jdbcType="VARCHAR"/>
            <result property="reserveLevel" column="RESERVE_LEVEL" jdbcType="INTEGER"/>
            <result property="province" column="PROVINCE" jdbcType="VARCHAR"/>
            <result property="city" column="CITY" jdbcType="VARCHAR"/>
            <result property="county" column="COUNTY" jdbcType="VARCHAR"/>
            <result property="productionDate" column="PRODUCTION_DATE" jdbcType="TIMESTAMP"/>
            <result property="manageUnitId" column="MANAGE_UNIT_ID" jdbcType="VARCHAR"/>
            <result property="manageUnitName" column="MANAGE_UNIT_NAME" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        CHECK_ID,
        INVENTORY_ID,
        CURRENT_INVENTORY_QTY,
        CHECK_QTY,
        DIFF_QTY,
        REMARKS,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>

    <select id="selectPageV1" resultMap="DetailResultMap">
        SELECT
        d.ID,
        d.CHECK_ID,
        d.INVENTORY_ID,
        d.CURRENT_INVENTORY_QTY,
        d.CHECK_QTY,
        d.DIFF_QTY,
        d.REMARKS ,
        c.START_TIME,
        c.APPROVE_STATUS,
        c.CHECK_STATUS,
        i.ID AS I_ID,
        i.STORE_ID,
        i.ST_ID,
        i.LOC_ID,
        i.CATALOG_ID,
        i.CATALOG_NAME,
        i.SPECIFICATIONS,
        i.GRADE,
        i.RESERVE_LEVEL,
        i.PROVINCE,
        i.CITY,
        i.COUNTY,
        i.PRODUCTION_DATE,
        i.MANAGE_UNIT_ID
        FROM B_INVENTORY_CHECK_DETAIL d
        LEFT JOIN B_INVENTORY_CHECK c ON d.CHECK_ID = c.ID
        LEFT JOIN B_INVENTORY_BASE i ON d.INVENTORY_ID = i.ID
        <where>
            d.DIFF_QTY != 0 AND c.CHECK_STATUS = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
                AND i.ST_ID = #{condition.stId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catelogId)">
                AND i.CATALOG_ID = #{condition.catelogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime) and @plugins.OGNL@isNotEmpty(condition.endTime)">
                AND c.START_TIME BETWEEN #{condition.startTime} AND #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.checkStatus)">
                AND c.CHECK_STATUS = #{condition.checkStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND c.APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY c.START_TIME DESC
    </select>

    <!-- 列表查询方法，只查询盘点明细表 -->
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_INVENTORY_CHECK_DETAIL
        WHERE CHECK_ID = #{checkId}
    </select>

</mapper>

