package com.hxdi.nmjl.service.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.emergency.EmergencyTaskItemCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyTaskItem;


import java.util.List;

/**
 * 紧急任务项服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/22
 */
public interface EmergencyTaskItemService extends IBaseService<EmergencyTaskItem> {

    void create(EmergencyTaskItem taskItem);

    /**
     * 创建紧急任务项
     * @param taskItem 紧急任务项实体
     */
    void create(EmergencyTaskItem taskItem, String s);

    /**
     * 更新紧急任务项
     * @param taskItem 紧急任务项实体
     */
    void update(EmergencyTaskItem taskItem);

    /**
     * 获取紧急任务项详情
     * @param id 紧急任务项ID
     * @return 紧急任务项实体
     */
    EmergencyTaskItem getDetail(String id);

    /**
     * 获取紧急任务项分页列表
     * @param condition 查询条件
     * @return 分页列表
     */
    Page<EmergencyTaskItem> pages(EmergencyTaskItemCondition condition);

    /**
     * 获取紧急任务项列表
     * @param condition 查询条件
     * @return 紧急任务项列表
     */
    List<EmergencyTaskItem> lists(EmergencyTaskItemCondition condition);

    /**
     * 批量删除紧急任务项
     * @param taskId 紧急任务项ID
     */
    void removeBatch(String taskId);
}