package com.hxdi.nmjl.config;

import com.hxdi.common.core.constants.CommonConstants;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: nmjl-service
 * @description: RabbitMQ配置类
 * @author: 王贝强
 * @create: 2025-03-26 16:04
 */
@Configuration
public class RabbitMqConfiguration {

    //数据变更队列
    @Bean
    public Queue dataSyncboundQueue() {
        return new Queue(CommonConstants.MQ_BUSINESS_QUEUE_BUS, true);
    }

    // 绑定Exchange
    @Bean
    public DirectExchange dataExchange() {
        return new DirectExchange(CommonConstants.MQ_DATASYNC_EXCHANGE, true, false);
    }

    @Bean
    public Binding dataSyncBinding() {
        return BindingBuilder.bind(dataSyncboundQueue())
                .to(dataExchange())
                .with(CommonConstants.MQ_BUSINESS_DATASYNC_KEY);
    }

}
