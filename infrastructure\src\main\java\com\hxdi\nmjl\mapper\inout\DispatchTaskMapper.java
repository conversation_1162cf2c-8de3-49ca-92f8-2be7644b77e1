package com.hxdi.nmjl.mapper.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.DispatchTask;
import com.hxdi.nmjl.condition.inout.DispatchCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DispatchTaskMapper extends SuperMapper<DispatchTask> {
    @DataPermission
    List<DispatchTask> listV1(@Param("condition") DispatchCondition condition);
    @DataPermission
    Page<DispatchTask> PageV1(@Param("condition") DispatchCondition condition,@Param("page") Page<DispatchTask> page);
}