<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.TrademarksMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.Trademarks">
    <!--@mbg.generated-->
    <!--@Table C_TRADEMARKS-->
    <id column="BRAND_ID" property="brandId" />
    <result column="BRAND_NAME" property="brandName" />
    <result column="CLASSIFICATION_CODE" property="classificationId" />
    <result column="SEQ" property="seq" />
    <result column="UPDATE_TIME" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BRAND_ID, BRAND_NAME, CLASSIFICATION_CODE, SEQ, UPDATE_TIME
  </sql>
</mapper>
