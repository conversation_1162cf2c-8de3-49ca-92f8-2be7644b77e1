package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.inventory.StorageCapacityCondition;
import com.hxdi.nmjl.condition.inventory.StorageCapacityConversionCondition;
import com.hxdi.nmjl.domain.inventory.StorageCapacity;
import com.hxdi.nmjl.domain.inventory.StorageCapacityConversion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @Data 2025/7/19 16:13
 * @Description: 库容转换记录服务实现类
 */
@Mapper
public interface StorageCapacityConversionMapper extends SuperMapper<StorageCapacityConversion> {
    @DataPermission
    List<StorageCapacityConversion> getList(@Param("condition") StorageCapacityConversionCondition condition);
    @DataPermission
    Page<StorageCapacityConversion> getPages(@Param("condition") StorageCapacityConversionCondition condition, @Param("page") Page<StorageCapacityConversion> page);
}

