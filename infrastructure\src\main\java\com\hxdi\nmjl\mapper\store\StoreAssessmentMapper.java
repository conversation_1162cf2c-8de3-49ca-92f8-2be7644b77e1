package com.hxdi.nmjl.mapper.store;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.store.StoreAssessmentCondition;
import com.hxdi.nmjl.domain.store.StoreAssessment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StoreAssessmentMapper extends SuperMapper<StoreAssessment> {

    Page<StoreAssessment> selectPageV1(Page<StoreAssessment> page, @Param("condition") StoreAssessmentCondition condition);

    List<StoreAssessment> selectListV1(@Param("condition") StoreAssessmentCondition condition);
}
