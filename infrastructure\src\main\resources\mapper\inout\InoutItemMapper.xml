<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.InoutItemMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.InoutItem">
        <!--@mbg.generated-->
        <!--@Table B_INOUT_ITEM-->
        <id column="ID" property="id" />
        <result column="INOUT_DETAIL_ID" property="inoutDetailId" />
        <result column="CATALOG_ID" property="catalogId" />
        <result column="CATALOG_NAME" property="catalogName" />
        <result column="BRAND" property="brand" />
        <result column="GRADE" property="grade" />
        <result column="SPECIFICATION" property="specification" />
        <result column="PRODUCT_DATE" property="productDate" />
        <result column="QTY" property="qty" />
        <result column="ST_ID" property="stId" />
        <result column="ST_NAME" property="stName" />
        <result column="LOC_ID" property="locId" />
        <result column="LOC_NAME" property="locName" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, INOUT_DETAIL_ID, CATALOG_ID, "CATALOG_NAME", BRAND, GRADE, SPECIFICATION, PRODUCT_DATE,
        QTY, ST_ID, ST_NAME, LOC_ID, LOC_NAME, CREATE_TIME, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <select id="selectStatInfo" resultMap="BaseResultMap">
        select CATALOG_NAME, GRADE, SPECIFICATION, sum(QTY) as QTY
        from B_INOUT_ITEM
        where INOUT_DETAIL_ID = #{mainId}
        group by CATALOG_NAME, GRADE, SPECIFICATION
	</select>
</mapper>
