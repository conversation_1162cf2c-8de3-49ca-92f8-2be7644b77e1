<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.ModelsMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.Models">
    <!--@mbg.generated-->
    <!--@Table C_MODELS-->
    <result column="MODEL_ID" property="modelId" />
    <result column="CLASSIFICATION_CODE" property="classificationCode" />
    <result column="MODEL_NAME" property="modelName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    MODEL_ID, CLASSIFICATION_CODE, MODEL_NAME
  </sql>
</mapper>