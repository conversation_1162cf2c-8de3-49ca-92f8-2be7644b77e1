package com.hxdi.nmjl.service.mobilization;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.mobilization.MobilizedContractDetail;

import java.util.List;


public interface MobilizedContractDetailService extends IBaseService<MobilizedContractDetail> {

    /**
     * 获取列表
     * @param contractId 合同ID
     * @return 合同详情列表
     */
    List<MobilizedContractDetail> getList(String contractId);

    /**
     * 删除计划详情
     * @param contractId  合同ID
     */
    void remove(String contractId);

}
