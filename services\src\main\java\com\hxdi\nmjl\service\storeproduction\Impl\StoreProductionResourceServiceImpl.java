package com.hxdi.nmjl.service.storeproduction.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionResourceCondition;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlan;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionResource;
import com.hxdi.nmjl.mapper.storeproduction.StoreProductionResourceMapper;
import com.hxdi.nmjl.service.storeproduction.StoreProductionPlanService;
import com.hxdi.nmjl.service.storeproduction.StoreProductionResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**

 生产资源服务实现类
 <AUTHOR>
 */
@Service
@Transactional (rollbackFor = Exception.class)
@Slf4j
public class StoreProductionResourceServiceImpl extends BaseServiceImpl<StoreProductionResourceMapper, StoreProductionResource> implements StoreProductionResourceService {

    @Autowired
    private StoreProductionPlanService storeProductionPlanService;

    @Override
    public void create(StoreProductionResource resource) {
        //根据计划编号查询计划信息
        QueryWrapper<StoreProductionPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plan_no", resource.getPlanNo());
        StoreProductionPlan plan = storeProductionPlanService.getOne(queryWrapper);
        if (plan.getApproveStatus() != 1) {
            throw new BaseException("计划暂未审核通过");
        }
        this.save(resource);
    }
    @Override
    public void update(StoreProductionResource resource) {
        StoreProductionResource savedResource = this.getById(resource.getId ());
        if (savedResource == null) {
            throw new BaseException("资源不存在");
        }
        //根据计划编号查询计划信息
        QueryWrapper<StoreProductionPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plan_no", resource.getPlanNo());
        StoreProductionPlan plan = storeProductionPlanService.getOne(queryWrapper);
        if (plan.getApproveStatus() != 1) {
            throw new BaseException("计划暂未审核通过");
        }
        this.updateById(resource);
    }
    @Override
    public StoreProductionResource getDetail(String resourceId) {
        StoreProductionResource resource = baseMapper.selectById(resourceId);
        if (resource == null || resource.getEnabled () != 1) {
            throw new BaseException ("资源不存在或已失效");
        }
        return resource;
    }


    @Override
    public Page<StoreProductionResource> pages(StoreProductionResourceCondition condition) {
        Page<StoreProductionResource> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }
    @Override
    public List<StoreProductionResource> lists(StoreProductionResourceCondition condition) {
        return baseMapper.selectListV1(condition);
    }
    @Override
    public void remove(String resourceId) {
        StoreProductionResource resource = this.getById (resourceId);
        if (resource == null) {
            throw new BaseException("资源不存在");
        }
        resource.setEnabled(0);
        this.updateById(resource);
    }
}
