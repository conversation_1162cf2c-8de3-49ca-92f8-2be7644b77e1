package com.hxdi.nmjl.dto.base;

import com.hxdi.common.core.model.ITree;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ClassificationTree implements ITree<String, ClassificationTree> {

    private String id;
    private String parentId;
    private String classificationName;
    private String preImg;
    private Integer leafIs;
    private Integer level;
    private Integer seq;
    private String dataHierarchyId;
    private List<ClassificationTree> children;

    public ClassificationTree() {}

    public static ClassificationTree newVirtualNode(String classificationName) {
        ClassificationTree node = new ClassificationTree();
        node.id = "";
        node.leafIs = 0;
        node.classificationName = classificationName;
        return node;
    }
}
