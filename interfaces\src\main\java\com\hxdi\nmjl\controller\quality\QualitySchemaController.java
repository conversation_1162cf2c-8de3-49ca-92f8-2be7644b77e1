package com.hxdi.nmjl.controller.quality;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.quality.QuatitySchemaCondition;
import com.hxdi.nmjl.domain.quality.QualitySchema;
import com.hxdi.nmjl.dto.quality.QualitySchemaAndItems;
import com.hxdi.nmjl.service.quality.QualitySchemaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;

/**
 * 质检方案控制器
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "质检方案")
@RequestMapping("/qualitySchema")
public class QualitySchemaController extends BaseController<QualitySchemaService, QualitySchema> {
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/get")
    @ApiOperation(value = "单条查询")
    public ResultBody<QualitySchema> selectOne(String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "新增或修改质检方案及标准项")
    public ResultBody insertOrUpdate(@RequestBody QualitySchemaAndItems data) {
        bizService.insertOrUpdate(data);
        return ResultBody.ok();
    }

    @PostMapping("/changeStatus")
    @ApiOperation(value = "改变质检方案状态(启用->1,禁用->0,删除->7)，删除后无法再次启用")
    public ResultBody<QualitySchema> changeStatus(@RequestParam("id") Serializable schemaId, @RequestParam("status") Integer status) {
        bizService.changeStatus(schemaId, status);
        return ResultBody.ok();
    }

    @GetMapping("/getDefaultByClassificationId")
    @ApiOperation(value = "根据粮食品类id获取该品类的默认质检方案及对应质检标准项")
    public ResultBody<QualitySchemaAndItems> getDefaultByClassificationId(String classificationId) {
        QualitySchemaAndItems schemaAndItems = bizService.selectDefaultByClassificationId(classificationId);
        return ResultBody.ok().data(schemaAndItems);
    }

    @GetMapping("/getList")
    @ApiOperation(value = "根据条件查询质检方案列表（带权限控制）")
    public ResultBody<QualitySchema> getList(QuatitySchemaCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @GetMapping("/getPage")
    @ApiOperation(value = "根据条件分页查询质检方案（带权限控制）")
    public ResultBody<QualitySchema> getPage(QuatitySchemaCondition condition) {
        return ResultBody.ok().data(bizService.getPage(condition));
    }

    @GetMapping("/getSchemaAndItems")
    @ApiOperation(value = "根据质检方案id获取质检方案及标准项")
    public ResultBody<QualitySchemaAndItems> getSchemaAndItems(String schemaId) {
        return ResultBody.ok().data(bizService.getSchemaAndItems(schemaId));
    }

}
