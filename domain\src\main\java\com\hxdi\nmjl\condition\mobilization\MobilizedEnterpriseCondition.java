package com.hxdi.nmjl.condition.mobilization;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "动员企业查询条件")
@Getter
@Setter
public class MobilizedEnterpriseCondition extends QueryCondition {

    @ApiModelProperty(value = "管理单位ID")
    private String unitId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "动员企业编号")
    private String enterpriseCode;

    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    @ApiModelProperty(value = "企业类型:字典DYQYLX")
    private String type;

    @ApiModelProperty(value = "备案状态：1-正常，2-撤销，3-观察")
    private Integer state;

    @ApiModelProperty(value = "地区编码 ','分割")
    private String areaCode;

    @TableField(value = "APPROVE_STATUS")
    @ApiModelProperty(value = "0-未提交，1-待审核，2-已审核，3-驳回")
    private Integer approveStatus;

}