<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.plan.BrandActivityPerformMapper">

    <sql id="Base_Column_List">
        ID, ACTIVITY_ID, NAME, AMOUNT, DATA, MEMO, PERFORM_TIME
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.BrandActivityPerform">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="ACTIVITY_ID" jdbcType="VARCHAR" property="activityId"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="DATA" jdbcType="INTEGER" property="data"/>
        <result column="MEMO" jdbcType="VARCHAR" property="memo"/>
        <result column="PERFORM_TIME" jdbcType="TIMESTAMP" property="performTime"/>
    </resultMap>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_BRAND_ACTIVITY_PERFORM
        <where>
            <if test="@plugins.OGNL@isNotEmpty(activityId)">
                and ACTIVITY_ID = #{activityId}
            </if>
        </where>
    </select>
</mapper>