package com.hxdi.nmjl.service.mobilization.Impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.mobilization.MobilizedContractCondition;
import com.hxdi.nmjl.domain.mobilization.MobilizedContract;
import com.hxdi.nmjl.domain.mobilization.MobilizedContractDetail;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.mobilization.MobilizedContractMapper;
import com.hxdi.nmjl.service.mobilization.MobilizationContractService;
import com.hxdi.nmjl.service.mobilization.MobilizedContractDetailService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MobilizationContractServiceImpl extends BaseServiceImpl<MobilizedContractMapper, MobilizedContract> implements MobilizationContractService {

    @Resource
    private MobilizedContractDetailService mobilizedContractDetailService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public Page<MobilizedContract> pages(MobilizedContractCondition condition) {

        Page<MobilizedContract> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<MobilizedContract> lists(MobilizedContractCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void create(MobilizedContract contract) {

        // 生成协议编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("MOBILIZED_CONTRACT_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        contract.setContractCode((String) businessCode.getValue());

        // 设置合同信息
        BaseUserDetails user = SecurityHelper.obtainUser();
        if (CommonUtils.isNotEmpty(user)) {
            contract.setStoreId(user.getOrganId());
            contract.setStoreName(user.getOrganName());
            contract.setOrgId(user.getOrganId());
            contract.setOrgName(user.getOrganName());
        }
        // 新增时默认合同未生效
        contract.setState(0);
        contract.setApproveStatus(0);
        //保存合同信息
        this.save(contract);

        //保存合同明细
        String contractId = contract.getId();
        if (contractId != null && contract.getDetailList() != null && !contract.getDetailList().isEmpty()) {
            // 为每个明细设置合同ID
            for (MobilizedContractDetail detail : contract.getDetailList()) {
                detail.setContractId(contractId);
            }
            // 保存明细
            mobilizedContractDetailService.saveBatch(contract.getDetailList());
        } else {
            throw new BaseException("合同明细不能为空");
        }
    }

    @Override
    public void update(MobilizedContract contract) {
        MobilizedContract savedContract = this.getById(contract.getId());

        //只有未提交和被驳回的合同可以修改
        if (savedContract.getApproveStatus() == 0 || savedContract.getApproveStatus() == 3) {
            if (savedContract.getApproveStatus() == 3) {
                contract.setApproveStatus(0);
                contract.setApproveOpinion("");
            }
            this.updateById(contract);
            mobilizedContractDetailService.remove(contract.getId());
            // 为每个明细设置合同ID
            for (MobilizedContractDetail detail : contract.getDetailList()) {
                detail.setContractId(contract.getId());
            }
            mobilizedContractDetailService.saveBatch(contract.getDetailList());
        } else {
            throw new BaseException("只有未提交和被驳回的合同可以修改！");
        }
    }

    @Override
    public MobilizedContract getDetail(String id) {
        // 查询合同详情
        MobilizedContract mobilizedContract = baseMapper.selectById(id);
        // 查询合同明细
        List<MobilizedContractDetail> lists = mobilizedContractDetailService.getList(mobilizedContract.getId());
        mobilizedContract.setDetailList(lists);
        return mobilizedContract;
    }

    @Override
    public void remove(String contractId) {
        MobilizedContract contract = baseMapper.selectById(contractId);
        if (contractId == null) {
            throw new BaseException("合同不存在！");
        }
        if (contract.getState().equals(0)) {
            contract.setEnabled(0);
            baseMapper.updateById(contract);
        } else {
            BizExp.pop("合同已提交，不能删除！");
        }
    }

    @Override
    public void submit(String contractId) {
        MobilizedContract contract = baseMapper.selectById(contractId);
        if (contract.getApproveStatus() != 0) {
            throw new BaseException("合同已提交!");
        }
        contract.setApproveStatus(1);
        contract.setState(0);
        this.updateById(contract);
    }

    @Override
    public void approve(String contractId, Integer approveStatus, String approveOpinion) {
        MobilizedContract contract = baseMapper.selectById(contractId);
        if (contract.getApproveStatus() == 2) {
            throw new BaseException("合同已审核!");
        }
        contract.setApproveStatus(approveStatus);
        contract.setApproveTime(new Date());
        contract.setApprover(SecurityHelper.obtainUser().getNickName());
        contract.setApproveOpinion(approveOpinion);

        if (contract.getApproveStatus() == 2) {
            // 审核通过后合同生效
            contract.setState(1);
        }
        this.updateById(contract);
    }

    @Override
    public void finish(String contractId) {
        MobilizedContract mobilizedContract = baseMapper.selectById(contractId);
        if (mobilizedContract.getState() != 1) {
            throw new RuntimeException("合同未生效，不能完成");
        }
        mobilizedContract.setState(2);
        this.updateById(mobilizedContract);
    }

    @Override
    public void terminate(String contractId) {
        MobilizedContract mobilizedContract = baseMapper.selectById(contractId);
        if (mobilizedContract.getState() != 1) {
            throw new RuntimeException("合同未生效，不能终止");
        }
        mobilizedContract.setState(3);
        this.updateById(mobilizedContract);
    }
}
