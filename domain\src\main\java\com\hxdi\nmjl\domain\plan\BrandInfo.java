package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 品牌信息
 */
@Getter
@Setter
@TableName(value = "B_BRAND_INFO")
public class BrandInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND_NAME")
    private String brandName;

    /**
     * 品牌简称
     */
    @TableField(value = "ABBR_NAME")
    private String abbrName;

    /**
     * 归属单位id
     */
    @TableField(value = "ORG_ID")
    private String orgId;

    /**
     * 归属单位名称
     */
    @TableField(value = "ORG_NAME")
    private String orgName;

    /**
     * 品牌类型:1-自有，2-外部
     */
    @TableField(value = "BRAND_TYPE")
    private Integer brandType;

    /**
     * 成立日期
     */
    @TableField(value = "FOUNDING_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date foundingDate;

    /**
     * 产品类别: 字典CPLB
     */
    @TableField(value = "PRODUCT_CATEGORY")
    private String productCategory;

    /**
     * 品牌LOGO
     */
    @TableField(value = "LOGO")
    private String logo;

    /**
     * 品牌理念
     */
    @TableField(value = "BRAND_VALUES")
    private String brandValues;

    /**
     * 品牌目标
     */
    @TableField(value = "OBJECTIVES")
    private String objectives;

    /**
     * 品牌描述
     */
    @TableField(value = "BRAND_DESC")
    private String brandDesc;

    /**
     * 品牌标语
     */
    @TableField(value = "SLOGAN")
    private String slogan;

    /**
     * 状态（1-有效 0-禁用，7删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    private String attachments;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}