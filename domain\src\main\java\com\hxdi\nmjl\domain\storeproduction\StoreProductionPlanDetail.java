package com.hxdi.nmjl.domain.storeproduction;


import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

/**
 * 生产计划明细表
 */
@Getter
@Setter
@TableName(value = "B_STORE_PRODUCTION_PLAN_DETAIL")
public class StoreProductionPlanDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 生产计划ID
     */
    @TableField(value = "PLAN_ID")
    private String planId;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    private String catalogId;

    /**
     * 品种代码
     */
    @TableField(value = "CATALOG_CODE")
    private String catalogCode;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    private String catalogName;

    /**
     * 分类ID
     */
    @TableField(value = "CLASSIFICATION_ID")
    private String classificationId;

    /**
     * 分类名称
     */
    @TableField(value = "CLASSIFICATION_NAME")
    private String classificationName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    private String specification;

    /**
     * 计量单位
     */
    @TableField(value = "UNIT")
    private String unit;

    /**
     * 计划数量
     */
    @TableField(value = "PLAN_QUANTITY")
    private BigDecimal planQuantity;

    /**
     * 完成数量
     */
    @TableField(value = "COMPLETE_QUANTITY")
    private BigDecimal completeQuantity;
}

