package com.hxdi.nmjl.domain.inout.opt;

import java.io.Serializable;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
*
* @TableName B_RECEIPT_DETAIL
*/
@ApiModel(description = "领用申请单明细")
@TableName("B_RECEIPT_DETAIL")
@Getter
@Setter
public class ReceiptDetail implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("RECEIPT_ID")
    @ApiModelProperty(value = "领用单ID")
    private String receiptId;

    @TableField("INVENTORY_ID")
    @ApiModelProperty(value = "库存ID")
    private String inventoryId;

    @TableField("RECEIPT_QTY")
    @ApiModelProperty(value = "领用数量")
    private BigDecimal receiptQty;

    @TableField("REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID",fill= FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    @TableField("BRAND")
    @ApiModelProperty(value = "品牌")
    private String brand;

    @TableField("CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    @TableField("CATALOG_NAME")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    @TableField("UNIT")
    @ApiModelProperty(value = "计量单位")
    private String unit;

    @TableField("SPECIFICATIONS")
    @ApiModelProperty(value = "规格")
    private BigDecimal specifications;

    @TableField("GRADE")
    @ApiModelProperty(value = "质量等级")
    private String grade;



}
