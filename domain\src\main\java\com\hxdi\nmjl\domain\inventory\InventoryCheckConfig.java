package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 盘点计划
 */
@ApiModel(description = "盘点计划")
@Getter
@Setter
@TableName(value = "B_INVENTORY_CHECK_CONFIG")
public class InventoryCheckConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 计划名称
     */
    @TableField(value = "NAME")
    @ApiModelProperty(value = "计划名称")
    private String name;
    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;
    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;
    /**
     * 周期
     */
    @TableField(value = "PERIODS")
    @ApiModelProperty(value = "周期")
    private Integer periods;
    /**
     * 时间单位：1-天，2-月
     */
    @TableField(value = "TIME_UNIT")
    @ApiModelProperty(value = "时间单位：1-天，2-月")
    private Integer timeUnit;
    /**
     * 首次任务开始时间
     */
    @TableField(value = "START_TIME")
    @ApiModelProperty(value = "首次任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;
    /**
     * 状态
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态")
    private Integer enabled;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;
    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;
    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;


}
