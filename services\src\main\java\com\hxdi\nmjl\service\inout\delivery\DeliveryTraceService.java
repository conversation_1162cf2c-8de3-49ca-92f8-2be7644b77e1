package com.hxdi.nmjl.service.inout.delivery;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryTrace;

import java.util.List;

/**
 * 配送状态跟踪Service接口
 *
 * <AUTHOR>
 * @since 2025/4/23 11:08
 */
public interface DeliveryTraceService extends IBaseService<DeliveryTrace> {
    /**
     * 根据主键查询
     *
     * @param deliveryId 主键
     * @return 配送状态跟踪列表
     */
    List<DeliveryTrace> getListByPid(String deliveryId);
}
