package com.hxdi.nmjl.controller.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryEvent;
import com.hxdi.nmjl.service.inout.delivery.DeliveryEventService;
import com.hxdi.nmjl.condition.inout.DeliveryEventCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 配送事件记录控制层
 *
 * <AUTHOR>
 * @since 2025/4/23 15:47
 */
@Api(tags = "配送事件记录")
@RestController
@RequestMapping("/deliveryEvent")
public class DeliveryEventController extends BaseController<DeliveryEventService, DeliveryEvent> {

    @ApiOperation(value = "保存或更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody DeliveryEvent deliveryEvent) {
        bizService.saveOrUpdateV1(deliveryEvent);
        return ResultBody.ok();
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<DeliveryEvent> getDetail(@RequestParam @NotNull(message = "id不能为空") String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResultBody delete(@RequestParam @NotNull(message = "id不能为空") String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<DeliveryEvent>> pages(DeliveryEventCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<DeliveryEvent>> lists(DeliveryEventCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }
}
