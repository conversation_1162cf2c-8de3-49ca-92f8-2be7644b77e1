package com.hxdi.nmjl.service.inout.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CalculationUtil;
import com.hxdi.nmjl.condition.inout.InoutTaskCondition;
import com.hxdi.nmjl.domain.inout.InoutTask;
import com.hxdi.nmjl.enums.InoutBusinessType;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inout.InoutTaskMapper;
import com.hxdi.nmjl.service.inout.InoutTaskService;
import com.hxdi.nmjl.service.plan.SaleOrderService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 出入库任务单服务实现类
 * @date 2025-04-08 16:01:28
 */

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class InoutTaskServiceImpl extends BaseServiceImpl<InoutTaskMapper, InoutTask> implements InoutTaskService {

    /**
     * 锁对象
     */
    private static final Lock lock = new ReentrantLock();

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private SaleOrderService saleOrderService;

    @Override
    public void create(InoutTask inoutTask) {
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("INOUT_TASK_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        inoutTask.setTaskCode((String) businessCode.getValue());
        inoutTask.setStoreId(SecurityHelper.obtainUser().getOrganId());
        inoutTask.setStoreName(SecurityHelper.obtainUser().getOrganName());
        inoutTask.setCompletedQty(BigDecimal.ZERO);
        inoutTask.setOrgId(SecurityHelper.obtainUser().getPid());
        inoutTask.setState(0);
        this.save(inoutTask);
    }

    @Override
    public void updating(InoutTask inoutTask) {
        this.updateById(inoutTask);
    }

    @Override
    public void remove(String taskId) {
        InoutTask inoutTask = getById(taskId);
        if (inoutTask.getState().equals(0)) {
            this.removeById(taskId);
        } else {
            BizExp.pop("该任务单正在执行中，不能删除");
        }
    }

    @Override
    public void updateCompletedQty(String taskCode, BigDecimal changeQty) {
        lock.lock();
        try {
            InoutTask savedTask = getByCode(taskCode);
            if (savedTask == null) {
                BizExp.pop("没有找到相关任务单据信息！");
            }
            if (savedTask.getState().equals(0)) {
                savedTask.setState(1);
            }

            //判断出入库数量是否已经超过计划数量
            if (CalculationUtil.add(savedTask.getCompletedQty(), changeQty).compareTo(savedTask.getPlanQty()) > 0) {
                BizExp.pop("出入库数量已经超过当前任务单数量！");
            }

            savedTask.setCompletedQty(CalculationUtil.add(savedTask.getCompletedQty(), changeQty));

            if (savedTask.getCompletedQty().compareTo(savedTask.getPlanQty()) >= 0) {
                savedTask.setState(2);
            }

            baseMapper.updateById(savedTask);

            //销售出库完成后更新销售订单完成数量
            if (savedTask.getInoutBizType().equals(InoutBusinessType.OUT_SALE.getCode())) {
                saleOrderService.updateProcess(savedTask.getOrderId(), savedTask.getCatalogId(), savedTask.getGrade(), changeQty);
            }

        } finally {
            lock.unlock();
        }
    }

    @Override
    public List<String> getCatalogIds(String orderId, String inoutType) {
        List<InoutTask> list = this.list(Wrappers.<InoutTask>lambdaQuery().eq(InoutTask::getOrderId, orderId).eq(InoutTask::getInoutType, inoutType).eq(InoutTask::getEnabled, 1));
        if (list.isEmpty()) {
            return new ArrayList<>();
        }
        return list.stream().map(InoutTask::getCatalogId).collect(Collectors.toList());
    }

    @Override
    public Page<InoutTask> getPage(InoutTaskCondition condition) {
        Page<InoutTask> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public InoutTask detail(String uniqueKey) {
        InoutTask inoutTask = this.getById(uniqueKey);
        if (inoutTask == null) {
            inoutTask = getByCode(uniqueKey);
        }

        return inoutTask;
    }

    @Override
    public InoutTask getByCode(String taskCode) {
        return baseMapper.selectOne(Wrappers.<InoutTask>lambdaQuery().eq(InoutTask::getTaskCode, taskCode));
    }


    @Override
    public List<InoutTask> getList(InoutTaskCondition condition) {
        return baseMapper.selectListV1(condition);
    }
}

