package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.BrandAuthorizingCondition;
import com.hxdi.nmjl.domain.base.ClientInfo;
import com.hxdi.nmjl.domain.plan.BrandAuthorizing;
import com.hxdi.nmjl.domain.plan.BrandInfo;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.BrandAuthorizingMapper;
import com.hxdi.nmjl.service.base.ClientInfoService;
import com.hxdi.nmjl.service.plan.BrandAuthorizingService;
import com.hxdi.nmjl.service.plan.BrandInfoService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 品牌授权服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class BrandAuthorizingServiceImpl extends BaseServiceImpl<BrandAuthorizingMapper, BrandAuthorizing> implements BrandAuthorizingService {

    @Resource
    private ClientInfoService clientInfoService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private BrandInfoService brandInfoService;

    @Override
    public void create(BrandAuthorizing authorizing) {
        // 生成授权协议编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("LICENSE_NO");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        authorizing.setLicenseNo((String) businessCode.getValue());


        // 设置品牌名称
        BrandInfo info = brandInfoService.getById(authorizing.getBrandId());
        authorizing.setBrandName(info.getBrandName());
        // 设置被授权企业名称
        ClientInfo clientInfo = clientInfoService.getById(authorizing.getClientId());
        authorizing.setClientName(clientInfo.getName());
        // 保存
        baseMapper.insert(authorizing);
    }

    @Override
    public void update(BrandAuthorizing authorizing) {
        authorizing.setApproveStatus(null);
        baseMapper.updateById(authorizing);
    }

    @Override
    public BrandAuthorizing getDetail(String authorizeId) {
        BrandAuthorizing brandAuthorizing = this.getById(authorizeId);
        if (CommonUtils.isEmpty(brandAuthorizing)) {
            throw new BaseException("品牌授权记录不存在");
        }
        return brandAuthorizing;
    }

    @Override
    public Page<BrandAuthorizing> pages(BrandAuthorizingCondition condition) {
        Page<BrandAuthorizing> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<BrandAuthorizing> lists(BrandAuthorizingCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void approve(String authorizeId, Integer approveStatus, String opinion) {
        BrandAuthorizing authorizing = baseMapper.selectById(authorizeId);
        if (authorizing.getEnabled() == 0) {
            throw new BaseException("品牌授权记录不存在");
        }
        // 校验状态：已审核的不能重复审核
        if (authorizing.getApproveStatus() == 1) {
            throw new BaseException("该授权记录已审核，无法重复操作");
        }

        BaseUserDetails user = SecurityHelper.obtainUser();
        authorizing.setApproveStatus(approveStatus);
        authorizing.setApproveOpinion(opinion);
        authorizing.setApprover(user.getNickName());
        authorizing.setApproveTime(new Date());
        // 审核通过时更新状态
        if (approveStatus == 1) {
            authorizing.setAuthState(1);
        } else if (approveStatus == 2) {
            authorizing.setAuthState(0);
        }
        baseMapper.updateById(authorizing);
    }

    @Override
    public void remove(String authorizeId) {
        BrandAuthorizing authorizing = baseMapper.selectById(authorizeId);
        // 逻辑删除
        authorizing.setEnabled(0);
        baseMapper.updateById(authorizing);
    }

    @Override
    public void updateInvalidState(String authorizeId) {
        BrandAuthorizing authorizing = baseMapper.selectById(authorizeId);
        if (authorizing == null) {
            throw new BaseException("品牌授权记录不存在");
        }
        authorizing.setAuthState(0);
        baseMapper.updateById(authorizing);
    }

    @Override
    public void checkAndUpdateExpiredState(LocalDateTime date) {
        baseMapper.checkAndUpdateExpiredState(date);
    }

    @Override
    public void submit(String authorizeId) {
        BrandAuthorizing brandAuthorizing = baseMapper.selectById(authorizeId);
        brandAuthorizing.setApproveStatus(0);
        baseMapper.updateById(brandAuthorizing);
    }

}