package com.hxdi.nmjl.mapper.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.base.ProductionSupplier;
import com.hxdi.nmjl.condition.clientrelated.SupplierCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/04/21
 */
@Mapper
public interface SupplierMapper extends SuperMapper<ProductionSupplier> {

    void changeStatus(@Param("id") String id, @Param("enabled") Integer enabled);

    Page<ProductionSupplier> selectPageV1(Page<ProductionSupplier> page, @Param("condition") SupplierCondition condition);

    List<ProductionSupplier> selectListV1(@Param("condition") SupplierCondition condition);
}
