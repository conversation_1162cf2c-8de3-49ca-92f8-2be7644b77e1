<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.opt.ProcessRecordMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.opt.ProcessRecord">
        <!--@Table B_PROCESS_RECORD-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="storeId" column="STORE_ID" jdbcType="VARCHAR"/>
        <result property="storeName" column="STORE_NAME" jdbcType="VARCHAR"/>
        <result property="catalogId" column="CATALOG_ID" jdbcType="VARCHAR"/>
        <result property="catalogName" column="CATALOG_NAME" jdbcType="VARCHAR"/>
        <result property="specifications" column="SPECIFICATIONS" jdbcType="VARCHAR"/>
        <result property="endDate" column="END_DATE" jdbcType="DATE"/>
        <result property="ylxxDesc" column="YLXX_DESC" jdbcType="VARCHAR"/>
        <result property="gylcDesc" column="GYLC_DESC" jdbcType="VARCHAR"/>
        <result property="attachments" column="ATTACHMENTS" jdbcType="VARCHAR"/>
        <result property="bar" column="BAR" jdbcType="VARCHAR"/>
        <result property="approveStatus" column="APPROVE_STATUS" jdbcType="INTEGER"/>
        <result property="approver" column="APPROVER" jdbcType="VARCHAR"/>
        <result property="approveTime" column="APPROVE_TIME" jdbcType="TIMESTAMP"/>
        <result property="approveOpinion" column="APPROVE_OPINION" jdbcType="VARCHAR"/>
        <result property="executiveStandard" column="EXECUTIVE_STANDARD" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createId" column="CREATE_ID" jdbcType="VARCHAR"/>
        <result property="updateId" column="UPDATE_ID" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        STORE_ID,
        STORE_NAME,
        CATALOG_ID,
        "CATALOG_NAME",
        SPECIFICATIONS,
        END_DATE,
        YLXX_DESC,
        GYLC_DESC,
        ATTACHMENTS,
        BAR,
        APPROVE_STATUS,
        APPROVER,
        APPROVE_TIME,
        APPROVE_OPINION,
        EXECUTIVE_STANDARD,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_PROCESS_RECORD
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeName)">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogName)">
                AND "CATALOG_NAME" LIKE CONCAT('%', #{condition.catalogName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime) and @plugins.OGNL@isNotEmpty(condition.endTime)">
                AND END_DATE BETWEEN #{condition.startTime} AND #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 列表查询 -->
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_PROCESS_RECORD
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeName)">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogName)">
                AND "CATALOG_NAME" LIKE CONCAT('%', #{condition.catalogName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime) and @plugins.OGNL@isNotEmpty(condition.endTime)">
                AND END_DATE BETWEEN #{condition.startTime} AND #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>

