package com.hxdi.nmjl.vo.plan;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: nmjl-service
 * @description: 销售订单按月统计明细VO
 * @author: 王贝强
 * @create: 2025-08-29 16:13
 */
@Getter
@Setter
public class SaleOrderStatVOItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 统计月份
     */
    private String statMonth;

    /**
     * 订单总数
     */
    private Integer orderCount;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;
}
