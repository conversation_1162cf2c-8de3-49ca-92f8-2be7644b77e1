package com.hxdi.nmjl.service.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryEvent;
import com.hxdi.nmjl.condition.inout.DeliveryEventCondition;

import java.util.List;

/**
 * 配送事件记录Service接口
 *
 * <AUTHOR>
 * @since 2025/4/23 11:08
 */
public interface DeliveryEventService extends IBaseService<DeliveryEvent> {

    /**
     * 保存或更新
     *
     * @param deliveryEvent 实体
     */
    void saveOrUpdateV1(DeliveryEvent deliveryEvent);

    /**
     * 查询详情
     *
     * @param id 主键
     * @return 实体
     */
    DeliveryEvent getDetail(String id);

    /**
     * 删除
     *
     * @param id 主键
     * @return 是否成功
     */
    void remove(String id);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<DeliveryEvent> pages(DeliveryEventCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<DeliveryEvent> lists(DeliveryEventCondition condition);
}
