package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.vo.inventory.InventoryCoreDataVO;
import com.hxdi.nmjl.vo.inventory.InventoryOverviewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 库存表控制器
 * @author: 王贝强
 * @create: 2025-03-10 15:12
 */
@RestController
@RequestMapping("/inventory")
@Api(tags = "库存管理")
@Validated
public class InventoryController extends BaseController<InventoryService, Inventory> {

    @ApiOperation(value = "单条查询")
    @GetMapping("/get")
    public ResultBody<Inventory> get(@RequestParam @NotNull(message = "id不能为空") String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

    @GetMapping("/getQuantity")
    @ApiOperation(value = "根据货位ID查询库存数量")
    public ResultBody<String> getQuantity(@RequestParam @NotNull(message = "货位ID不能为空") String locId) {
        return ResultBody.ok().data(bizService.getQuantity(locId));
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "根据货位ID查询库存关键数据")
    public ResultBody<InventoryCoreDataVO> getDetail(@RequestParam @NotNull(message = "货位ID不能为空") String locId) {
        return ResultBody.ok().data(bizService.getDetail(locId));
    }

    @ApiOperation(value = "明细库存查询列表")
    @GetMapping("/DetailList")
    public ResultBody<List<Inventory>> DetailInventoryList(InventoryCondition dto) {
        return ResultBody.ok().data(bizService.getList(dto));
    }

    @ApiOperation(value = "明细库存查询列表（包含批次信息）")
    @GetMapping("/DetailListV1")
    public ResultBody<List<Inventory>> DetailInventoryListV1(InventoryCondition dto) {
        return ResultBody.ok().data(bizService.getListV1(dto));
    }

    @ApiOperation(value = "明细库存查询分页")
    @GetMapping("/DetailPage")
    public ResultBody<Page<Inventory>> DetailInventoryPage(InventoryCondition dto) {
        return ResultBody.ok().data(bizService.getPage(dto));
    }

    @ApiOperation(value = "品种库存查询列表(聚合查询：根据品种信息聚合库存数据，同一个品种聚合为一条数据)")
    @GetMapping("/CatalogList")
    public ResultBody<List<Inventory>> CatalogInventoryList(InventoryCondition dto) {
        return ResultBody.ok().data(bizService.getListWithGroupByCatalog(dto));
    }


    @ApiOperation(value = "品种库存查询分页(聚合查询：根据品种信息聚合库存数据，同一个品种聚合为一条数据)")
    @GetMapping("/CatalogPage")
    public ResultBody<Page<Inventory>> CatalogInventoryPage(InventoryCondition dto) {
        return ResultBody.ok().data(bizService.getPageWithGroupByCatalog(dto));
    }

    @GetMapping("/getInventoryOverviewList")
    @ApiOperation(value = "库存总览列表")
    public ResultBody<List<InventoryOverviewVO>> getInventoryOverviewList(@RequestParam @NotNull(message = "军供站ID不能为空") String storeId) {
        return ResultBody.<List<InventoryOverviewVO>>OK().data(bizService.getInventoryOverviewList(storeId));
    }
}
