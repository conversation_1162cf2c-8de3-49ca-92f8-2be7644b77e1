package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.BrandActivityPerform;

import java.util.List;

/**
 * 品牌传播活动执行记录服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/7 16:30
 */
public interface BrandActivityPerformService extends IBaseService<BrandActivityPerform> {

    /**
     * 获取活动执行记录列表
     * @param activityId 活动ID
     * @return 执行记录列表
     */
    List<BrandActivityPerform> getList(String activityId);

    /**
     * 批量添加活动执行记录
     * @param performList 执行记录列表
     */
    void insertBatch(List<BrandActivityPerform> performList);

    /**
     * 删除活动执行记录
     * @param performId 执行记录ID
     */
    void remove(String performId);

}