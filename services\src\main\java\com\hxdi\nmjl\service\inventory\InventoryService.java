package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.domain.bigscreen.BigScreenConfig;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.dto.inventory.InventoryDTO;
import com.hxdi.nmjl.vo.bigscreen.InventorySumAndCapacityVO;
import com.hxdi.nmjl.vo.inventory.InventoryCoreDataVO;
import com.hxdi.nmjl.vo.inventory.InventoryOverviewVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 库存表服务接口
 *
 * @author: 王贝强
 * @create: 2025-03-10 15:12
 */
public interface InventoryService extends IBaseService<Inventory> {


    /**
     * 品种库存列表分页查询（聚合查询：根据品种信息聚合库存数据，同一个品种聚合为一条数据）
     *
     * @param dto
     * @return
     */
    Page<Inventory> getPageWithGroupByCatalog(InventoryCondition dto);

    /**
     * 品种库存列表查询（聚合查询：根据品种信息聚合库存数据，同一个品种聚合为一条数据）
     *
     * @param dto
     * @return
     */
    List<Inventory> getListWithGroupByCatalog(InventoryCondition dto);

    /**
     * 品类库存列表查询（聚合查询：根据品类信息聚合库存数据，同一个品类聚合为一条数据）
     *
     * @param condition
     * @return
     */
    List<Inventory> getListWithGroupByClassification(InventoryCondition condition);

    /**
     * 实时库存总览接口
     *
     * @param storeId
     * @return
     */
    List<InventoryOverviewVO> getInventoryOverviewList(String storeId);

    /**
     * 大屏库存统计接口：按地区，品种，仓房分组返回汇聚后的库存数据(吨)
     *
     * @param condition
     * @return
     */
    Page<Inventory> getBigScreenSummaryPage(InventoryCondition condition);

    /**
     * 大屏库存总览接口：根据品类配置，返回对应的统计数量(吨)
     *
     * @param areaCode
     * @param configList
     * @return
     */
    Map<String, BigDecimal> getBigScreenSummary(List<String> areaCode, List<BigScreenConfig> configList);

    /**
     * 大屏统计仓储能力总览： 根据军供站ID列表查询库存数量与仓容(吨)
     * 根据传入的地区编码，查询下属所有军供站的库存数量与仓容，并按下一级地区进行分组汇总
     *
     * @param areaCode
     * @return
     */
    List<InventorySumAndCapacityVO> getInventorySumAndCapacity(String areaCode);

    /**
     * 大屏统计仓储能力分页查询： 根据军供站ID列表查询库存数量与仓容(吨)
     * 根据传入的地区编码，查询下属所有军供站的库存数量与仓容，并以仓房为单位进行统计
     *
     * @param areaCode
     * @param storeId
     * @return
     */
    Page<InventorySumAndCapacityVO> getInventorySumAndCapacityPage(String areaCode, String storeId);


    /**
     * 根据货位ID查询库存数量
     *
     * @param locId
     * @return
     */
    String getQuantity(String locId);

    /**
     * 根据货位ID查询关键库存数据
     *
     * @param locId
     * @return
     */
    InventoryCoreDataVO getDetail(String locId);


    /**
     * 明细库存列表分页查询
     *
     * @param dto
     * @return
     */
    Page<Inventory> getPage(InventoryCondition dto);

    /**
     * 明细库存列表（走用户权限查询）
     *
     * @param dto
     * @return
     */
    List<Inventory> getList(InventoryCondition dto);

    /**
     * 明细库存列表（包含批次信息）
     *
     * @param dto
     * @return
     */
    List<Inventory> getListV1(InventoryCondition dto);

    /**
     * 根据仓库ID查询存在库存的仓房列表
     *
     * @param storeId
     * @return
     */
    List<Inventory> getInventoryStorehouseList(String storeId);

    /**
     * 批量更新库存数据
     *
     * @param dtoList
     * @return
     */
    List<InventoryDTO> updateBatch(List<InventoryDTO> dtoList);

}
