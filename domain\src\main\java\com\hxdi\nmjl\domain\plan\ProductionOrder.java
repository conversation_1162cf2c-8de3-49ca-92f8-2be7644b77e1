package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@TableName(value = "B_PRODUCTION_ORDER")
@ApiModel(description = "生产订单")
public class ProductionOrder extends Entity<ProductionOrder> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 订单编号
     */
    @TableField(value = "ORDER_CODE")
    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    /**
     * 父订单
     */
    @TableField(value = "PID")
    @ApiModelProperty(value = "父订单")
    private String pid;

    /**
     * 生产批次号
     */
    @TableField(value = "BATCH_NO")
    @ApiModelProperty(value = "生产批次号")
    private String batchNo;

    /**
     * 合同ID
     */
    @TableField(value = "CONTRACT_ID")
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 合同编号
     */
    @TableField(value = "CONTRACT_CODE")
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 订单日期
     */
    @TableField(value = "ORDER_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "订单日期")
    private Date orderTime;

    /**
     * 签订单位ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "签订单位ID")
    private String orgId;

    /**
     * 签订单位名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "签订单位名称")
    private String orgName;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "军供站ID")
    private String storeId;

    /**
     * 军供站名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "军供站名称")
    private String storeName;

    /**
     * 客户ID
     */
    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    /**
     * 客户名称
     */
    @TableField(value = "CLIENT_NAME")
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    /**
     * 开始日期
     */
    @TableField(value = "START_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    /**
     * 预期结束日期
     */
    @TableField(value = "EXPECTED_COMPLETION_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预期结束日期")
    private Date expectedCompletionDate;

    /**
     * 计划完成数量
     */
    @TableField(value = "PLAN_QTY")
    @ApiModelProperty(value = "计划完成数量")
    private BigDecimal planQty;

    /**
     * 实际完成数量
     */
    @TableField(value = "COMPLETED_QTY")
    @ApiModelProperty(value = "实际完成数量")
    private BigDecimal completedQty;

    /**
     * 交货方式:1-汽运
     */
    @TableField(value = "DELIVERY_METHOD")
    @ApiModelProperty(value = "交货方式:1-汽运")
    private Integer deliveryMethod;

    /**
     * 交货地址
     */
    @TableField(value = "RCV_ADDR")
    @ApiModelProperty(value = "交货地址")
    private String rcvAddr;

    /**
     * 交货详细地址
     */
    @TableField(value = "RCV_DETAIL_ADDR")
    @ApiModelProperty(value = "交货详细地址")
    private String rcvDetailAddr;

    /**
     * 订单状态：0-待开始，1-进行中，2-已检验，3-已交付
     */
    @TableField(value = "STATE")
    @ApiModelProperty(value = "订单状态：0-待开始，1-进行中，2-已检验，3-已交付")
    private Integer state;

    /**
     * 结算状态：0-待结算，1-已结算
     */
    @TableField(value = "SETTLEMENT_STATUS")
    @ApiModelProperty(value = "结算状态：0-待结算，1-已结算")
    private Integer settlementStatus;

    /**
     * 备注
     */
    @TableField(value = "NOTES")
    @ApiModelProperty(value = "备注")
    private String notes;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHEMENTS")
    @ApiModelProperty(value = "附件")
    private String attachements;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 生产订单明细
     */
    @TableField(exist = false)
    private List<ProductionOrderItem> detailList;
}