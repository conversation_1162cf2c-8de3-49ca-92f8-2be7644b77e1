package com.hxdi.nmjl.condition.emergency;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 紧急调度项目查询条件
 */
@ApiModel(description = "紧急调度项目查询条件")
@Getter
@Setter
public class EmergencyScheduleItemCondition extends QueryCondition {

    @ApiModelProperty(value = "调度ID")
    private String scheduleId;

    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "质量等级")
    private String grade;

}
