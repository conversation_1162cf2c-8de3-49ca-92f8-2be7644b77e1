<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.TraceInfoMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.TraceInfo">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum" />
        <result column="inventory_id" jdbcType="VARCHAR" property="inventoryId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="data_hierarchy_id" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        id, batch_num, inventory_id, create_time, update_time, tenant_id, data_hierarchy_id
    </sql>
</mapper>
