package com.hxdi.nmjl.condition.storeproduction;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "门店生产资源查询条件")
@Getter
@Setter
public class StoreProductionResourceCondition extends QueryCondition {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "计划编号")
    private String planNo;

    @ApiModelProperty(value = "资源类型：1-设备，2-人员，3-原材料")
    private Integer resourceType;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}

