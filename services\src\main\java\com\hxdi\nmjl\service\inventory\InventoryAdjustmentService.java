package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.InventoryAdjustment;
import com.hxdi.nmjl.condition.inventory.InventoryAdjustmentCondition;

import java.util.List;


/**
 * 库存调整服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
public interface InventoryAdjustmentService extends IBaseService<InventoryAdjustment> {

    /**
     * 保存并更新
     *
     * @param inventoryAdjustment 库存调整
     */
    void saveOrUpdateV1(InventoryAdjustment inventoryAdjustment);

    /**
     * 查询详情
     *
     * @param id 调整ID
     * @return 库存调整详情
     */
    InventoryAdjustment getDetail(String id);

    /**
     * 删除库存调整
     *
     * @param id 调整ID
     */
    void remove(String id);


    /**
     * 审核
     *
     * @param id   调整ID
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id   调整ID
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     *
     * @param id   调整ID
     */
    void submit(String id);

    /**
     * 分页查询
     *
     * @param condition 条件
     * @return 分页结果
     */
    Page<InventoryAdjustment> pages(InventoryAdjustmentCondition condition);

    /**
     * 查询列表
     *
     * @param condition 条件
     * @return 列表
     */
    List<InventoryAdjustment> lists(InventoryAdjustmentCondition condition);
}
