package com.hxdi.nmjl.condition.emergency;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.model.Query;
import com.hxdi.common.core.mybatis.annotation.Between;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.annotation.ORDER;
import com.hxdi.common.core.mybatis.base.support.OrderItem;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class EmergencyLossCondition extends Query {
    private static final long serialVersionUID = 1L;


    @LIKE(value = "TASK_NAME,UNIT_NAME,CLASSIFICATION_NAME", logic = "OR", join = true)
    private String keywords;

    @EQ
    @ApiModelProperty(value = "上报类型：1-军供站，2-动员企业")
    private Integer reportType;

    @EQ
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @EQ
    @ApiModelProperty(value = "业务状态：0-待确认，1-已确认")
    private Integer state;

    @ApiModelProperty(value = "创建时间")
    @Between
    private RangeBean<Date> createTime;

    @EQ
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private final Integer enabled = 1;

    @ORDER
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private final OrderItem orderItem = OrderItem.desc("create_time");

}
