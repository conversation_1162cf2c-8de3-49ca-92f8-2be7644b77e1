package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.plan.CaseInfoCondition;
import com.hxdi.nmjl.domain.plan.CaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CaseInfoMapper extends SuperMapper<CaseInfo> {

    /**
     * 查询列表
     * @param condition
     * @return
     */
    @DataPermission
    List<CaseInfo> selectListV1(@Param("condition") CaseInfoCondition condition);

    /**
     * 分页查询
     * @param condition
     * @return
     */
    @DataPermission
    Page<CaseInfo> selectPageV1(Page<CaseInfo> page, @Param("condition") CaseInfoCondition condition);
}
