package com.hxdi.nmjl.enums;

import lombok.Getter;

/**
 * @program: nmjl-service
 * @description: 出入库业务类型
 * @author: 王贝强
 * @create: 2025-04-11 14:14
 */
@Getter
public enum InoutBusinessType {
    /**
     * ------出入库类型------
     */
    IN("01","入库"),
    OUT("02","出库"),

    /**
     * -----------业务类型------------
     */
    // 入库...
    IN_PURCHASE("0101","采购计划"),
    IN_ADJUSTMENT("0102", "库存调整-溢余"),
    IN_DISPATCH("0103", "库存拆分-调入"),
    IN_OTHER("0107","其他入库"),

    // 出库...
    OUT_SALE("0201", "销售订单"),
    OUT_ADJUSTMENT("0202", "库存调整-损耗"),
    OUT_DISPATCH("0203", "库存拆分-调出");


    private final String code;
    private final String label;

    InoutBusinessType(String code, String label) {
        this.code = code;
        this.label = label;
    }
}
