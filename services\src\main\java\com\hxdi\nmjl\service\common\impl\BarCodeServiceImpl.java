package com.hxdi.nmjl.service.common.impl;

import cn.hutool.core.img.ImgUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Maps;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.JsonConverter;
import com.hxdi.nmjl.domain.common.BarCode;
import com.hxdi.nmjl.domain.common.BarInfo;
import com.hxdi.nmjl.enums.BarCodeEnum;
import com.hxdi.nmjl.enums.BusinessType;
import com.hxdi.nmjl.mapper.common.BarCodeMapper;
import com.hxdi.nmjl.service.common.BarCodeGenerator;
import com.hxdi.nmjl.service.common.BarCodeService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.utils.IdEncryptionUtil;
import com.hxdi.nmjl.vo.base.BaseQRDetailVO;
import com.hxdi.nmjl.vo.inventory.InventoryCoreDataVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.util.Map;

/**
 * @program: nmjl-service
 * @description: 系统条码服务实现
 * @author: 王贝强
 * @create: 2025-08-20 15:21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class BarCodeServiceImpl extends BaseServiceImpl<BarCodeMapper, BarCode> implements BarCodeService {

    @Resource
    private InventoryService inventoryService;

    @Override
    public String getDetail(BarInfo info) {
        if (info != null && info.getBtp() != null) {
            if (CommonUtils.isBlank(info.getBid())) {
                throw new BaseException("业务ID不能为空");
            }
            if (info.getBtp().equals(BusinessType.STORE_LOCATION.getCode())) {
                BaseQRDetailVO detailVO = new BaseQRDetailVO();
                detailVO.setBusinessType(info.getBtp());
                InventoryCoreDataVO inventory = inventoryService.getDetail(info.getBid());
                detailVO.setInventory(inventory);
                return JsonConverter.toJson(detailVO);
            }else if(info.getBtp().equals(BusinessType.QUALITY_TRACE.getCode())){
                BaseQRDetailVO detailVO = new BaseQRDetailVO();
                detailVO.setBusinessType(info.getBtp());
            }
        }
        return "";
    }

    @Override
    public String getDetailByUrl(String openId) {
        LambdaQueryWrapper<BarCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BarCode::getOpenId, openId).last("limit 1");
        BarCode barCode = getOne(queryWrapper);
        if (barCode != null) {
            BarInfo barInfo = new BarInfo();
            barInfo.setBtp(barCode.getBusinessType());
            barInfo.setBid(barCode.getBusinessId());
            return getDetail(barInfo);
        } else {
            Map<String, String> map = Maps.newHashMap();
            map.put("businessId", "-1");
            map.put("openId", openId);
            map.put("message", "条码不存在");
            return JsonConverter.toJson(map);
        }
    }

    @Override
    public void generateBarCode(String businessId, BusinessType businessType, String ext) {
        // 生成条码信息
        String barCode = BarCodeGenerator.generateDefault(businessId, businessType);
        String id = IdWorker.getIdStr();
        String openId = IdEncryptionUtil.encrypt(id);

        // 创建BarCode对象
        BarCode code = new BarCode();
        code.setId(id);
        code.setBusinessId(businessId);
        code.setBusinessType(businessType.getCode());
        code.setBarCodeType(BarCodeEnum.QR_CODE.getCode());
        code.setBarCode(barCode);
        code.setOpenId(openId);

        save(code);
    }

    @Override
    public String getBarCode(String businessId, String businessTypeCode) {
        BarCode barCode = get(businessId, businessTypeCode);
        if (barCode == null || CommonUtils.isBlank(barCode.getBarCode())) {
            throw new BaseException("条码信息不存在");
        }
        return barCode.getBarCode();
    }

    @Override
    public String getBarUrl(String businessId, String businessTypeCode) {
        BarCode barCode = get(businessId, businessTypeCode);
        if (barCode == null || CommonUtils.isBlank(barCode.getOpenId())) {
            throw new BaseException("条码外链URL不存在");
        }
        String openId = barCode.getOpenId();
        return BarCodeGenerator.generateByUrl(BarCodeEnum.QR_CODE, openId);
    }

    @Override
    public void getBarCodeImage(String businessId, String businessTypeCode, HttpServletRequest request, HttpServletResponse response) {
        String base64Content = getBarCode(businessId, businessTypeCode);
        if (CommonUtils.isBlank(base64Content)) {
            throw new BaseException("条码信息不存在");
        }
        // 生成条码图片并写入响应
        writeImg(response, base64Content);
    }

    private void writeImg(HttpServletResponse response, String base64Str) {
        BufferedImage image = ImgUtil.toImage(base64Str);
        try {
            response.setContentType("image/png");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "inline; filename=barcode_url.png");
            try (ServletOutputStream os = response.getOutputStream()) {
                ImgUtil.write(image, "png", os);
                os.flush();
            }
        } catch (Exception e) {
            throw new BaseException("生成条码外链图片失败", e);
        }
    }

    @Override
    public void getBarUrlImage(String businessId, String businessTypeCode, HttpServletRequest request, HttpServletResponse response) {
        String base64Url = getBarUrl(businessId, businessTypeCode);
        if (CommonUtils.isBlank(base64Url)) {
            throw new BaseException("条码外链URL不存在");
        }
        // 生成条码图片并写入响应
        writeImg(response, base64Url);
    }

    /**
     * 获取条码信息，如果不存在则生成新的条码
     *
     * @param businessId       业务ID
     * @param businessTypeCode 业务类型代码
     * @return 条码对象
     */
    private BarCode get(String businessId, String businessTypeCode) {
        if (CommonUtils.isBlank(businessId) || CommonUtils.isBlank(businessTypeCode)) {
            throw new BaseException("业务ID或业务类型不能为空");
        }
        if (BusinessType.fromCode(businessTypeCode) == BusinessType.NOOP) {
            throw new BaseException("业务类型不存在或不支持");
        }

        LambdaQueryWrapper<BarCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BarCode::getBusinessId, businessId)
                .eq(BarCode::getBusinessType, businessTypeCode)
                .eq(BarCode::getBarCodeType, BarCodeEnum.QR_CODE.getCode())
                .last("limit 1");

        BarCode barCode = getOne(queryWrapper);
        // 如果条码存在，返回Base64编码的条码字符串
        if (barCode != null) {
            return barCode;
        }
        // 如果条码不存在，生成新的条码并返回
        generateBarCode(businessId, BusinessType.fromCode(businessTypeCode), "");
        return getOne(queryWrapper);
    }
}