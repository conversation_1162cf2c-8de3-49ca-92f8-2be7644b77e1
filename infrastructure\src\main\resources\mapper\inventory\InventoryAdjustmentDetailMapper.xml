<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryAdjustmentDetailMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryAdjustmentDetail">
        <!--@Table B_INVENTORY_ADJUSTMENT_DETAIL-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="pid" column="PID" jdbcType="VARCHAR"/>
        <result property="inventoryId" column="INVENTORY_ID" jdbcType="VARCHAR"/>
        <result property="beforeQty" column="BEFORE_QTY" jdbcType="DECIMAL"/>
        <result property="adjustQty" column="ADJUST_QTY" jdbcType="DECIMAL"/>
        <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        PID,
        INVENTORY_ID,
        BEFORE_QTY,
        ADJUST_QTY,
        REMARKS,
        TENANT_ID,
        DATA_HIERARCHY_ID

    </sql>


</mapper>

