package com.hxdi.nmjl.service.inout.opt.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inout.opt.OptData;
import com.hxdi.nmjl.mapper.inout.opt.OptDataMapper;
import com.hxdi.nmjl.service.inout.opt.OptDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class OptDataServiceImpl  extends BaseServiceImpl<OptDataMapper, OptData> implements OptDataService {
}
