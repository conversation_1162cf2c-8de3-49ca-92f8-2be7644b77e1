package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(description = "合同信息查询条件")
public class ContractCondition extends QueryCondition {

    @ApiModelProperty(value = "计划ID")
    private String planId;

    @ApiModelProperty(value = "招标ID")
    private String bidId;

    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "客户ID")
    private String clientId;

    @ApiModelProperty(value = "业务类型：1-采购合同，2-销售合同")
    private String bizType;

    @ApiModelProperty(value = "业务状态：0-待提交 1-进行中，2-已完成")
    private Integer state;

    @ApiModelProperty(value = "模糊查询：合同名称或合同编号")
    private String search;

    @ApiModelProperty(value = "签订开始时间")
    private Date startTime;

    @ApiModelProperty(value = "签订结束时间")
    private Date endTime;

    @ApiModelProperty(value = "审核状态 0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "组织ID(后端条件，请勿使用)")
    private List<String> dataHierarchyId;
}
