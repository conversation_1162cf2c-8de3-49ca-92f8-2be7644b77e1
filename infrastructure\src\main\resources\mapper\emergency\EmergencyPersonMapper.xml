<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyPersonMapper">

    <sql id="Base_Column_List">
        ID, ORG_ID, ORG_NAME, NAME, SEX, UNIT_NAME,
        JOB_TITLE, BUSINESS, OFFICE_TEL, MOBILE,
        EMAIL, NOTES, ENABLED, CREATE_TIME,
        UPDATE_TIME, CREATE_ID, UPDATE_ID,
        TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyPerson">
        <id column="ID" property="id"/>
        <result column="ORG_ID" property="orgId"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="NAME" property="name"/>
        <result column="SEX" property="sex"/>
        <result column="UNIT_NAME" property="unitName"/>
        <result column="JOB_TITLE" property="jobTitle"/>
        <result column="BUSINESS" property="business"/>
        <result column="OFFICE_TEL" property="officeTel"/>
        <result column="MOBILE" property="mobile"/>
        <result column="EMAIL" property="email"/>
        <result column="NOTES" property="notes"/>
        <result column="ENABLED" property="enabled"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
    </resultMap>

    <!-- 列表查询 -->
    <select id="getList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_EMERGNECY_PERSON
        <where>
            ENABLED = 1
            <if test="condition.name != null and condition.name != ''">
                AND NAME LIKE CONCAT('%', #{condition.name}, '%')
            </if>
            <if test="condition.orgName != null and condition.orgName != ''">
                AND ORG_NAME LIKE CONCAT('%', #{condition.orgName}, '%')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 分页查询 -->
    <select id="getPages" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_EMERGNECY_PERSON
        <where>
            ENABLED = 1
            <if test="condition.name != null and condition.name != ''">
                AND NAME LIKE CONCAT('%', #{condition.name}, '%')
            </if>
            <if test="condition.orgName != null and condition.orgName != ''">
                AND ORG_NAME LIKE CONCAT('%', #{condition.orgName}, '%')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
