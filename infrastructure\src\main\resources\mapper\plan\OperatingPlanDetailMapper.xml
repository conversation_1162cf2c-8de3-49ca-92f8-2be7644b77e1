<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.OperatingPlanDetailMapper">

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, PLAN_CODE, PROJECT_ID, PROJECT_NAME, OBJECTIVED_DESC,
        RESULT, FINISH_DATE
    </sql>

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.OperatingPlanDetail">
        <id column="ID" property="id"/>
        <result column="PLAN_CODE" property="planCode"/>
        <result column="PROJECT_ID" property="projectId"/>
        <result column="PROJECT_NAME" property="projectName"/>
        <result column="OBJECTIVED_DESC" property="objectivedDesc"/>
        <result column="RESULT" property="result"/>
        <result column="FINISH_DATE" property="finishDate"/>
    </resultMap>

</mapper>
