package com.hxdi.nmjl.service.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionInfo;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionInfoCondition;

import java.util.List;

/**
 * 库存生产信息服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/5
 */
public interface StoreProductionInfoService extends IBaseService<StoreProductionInfo> {

    /**
     * 新增库存生产信息
     * @param info 库存生产信息实体
     */
    void create(StoreProductionInfo info);

    /**
     * 修改库存生产信息
     * @param info 库存生产信息实体
     */
    void update(StoreProductionInfo info);

    /**
     * 获取库存生产信息详情
     * @param id 库存生产信息ID
     * @return 库存生产信息实体
     */
    StoreProductionInfo getDetail(String id);

    /**
     * 分页查询库存生产信息
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<StoreProductionInfo> pages(StoreProductionInfoCondition condition);

    /**
     * 列表查询库存生产信息
     * @param condition 查询条件
     * @return 库存生产信息列表
     */
    List<StoreProductionInfo> lists(StoreProductionInfoCondition condition);

    /**
     * 删除库存生产信息
     * @param id 库存生产信息ID
     */
    void remove(String id);
}