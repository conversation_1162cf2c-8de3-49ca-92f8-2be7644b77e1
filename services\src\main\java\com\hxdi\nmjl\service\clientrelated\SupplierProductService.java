package com.hxdi.nmjl.service.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.clientrelated.SupplierProductCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierProduct;

import java.util.List;

/**
 * 供应商产品管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
public interface SupplierProductService extends IBaseService<SupplierProduct> {


    /**
     * 新增
     *
     * @param product 供应商产品
     */
    void create(SupplierProduct product);

    /**
     * 更新
     *
     * @param product 供应商产品
     */
    void update(SupplierProduct product);

    /**
     * 修改状态
     *
     * @param id      供应商产品ID
     * @param enabled 状态：0-删除 1-正常
     */
    void changeState(String id, Integer enabled);

    /**
     * 查询详情
     *
     * @param id 供应商产品ID
     * @return 供应商产品
     */
    SupplierProduct getByUniqueKey(String id);

    /**
     * 查询详情
     *
     * @param supplierId 供应商ID
     * @return 列表数据
     */
    List<SupplierProduct> getDetail(String supplierId);


    /**
     * 分页
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<SupplierProduct> pages(SupplierProductCondition condition);

    /**
     * 列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<SupplierProduct> lists(SupplierProductCondition condition);

    /**
     * 根据主表ID删除
     *
     * @param mainId 供应商ID
     */
    void removeByMainId(String mainId);

}
