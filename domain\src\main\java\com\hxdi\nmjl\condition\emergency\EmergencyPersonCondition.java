package com.hxdi.nmjl.condition.emergency;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "应急人员查询参数")
@Getter
@Setter
public class EmergencyPersonCondition extends QueryCondition {

    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    @ApiModelProperty(value = "姓名")
    private String name;


}
