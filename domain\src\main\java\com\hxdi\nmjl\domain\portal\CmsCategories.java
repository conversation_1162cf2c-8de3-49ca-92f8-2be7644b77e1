package com.hxdi.nmjl.domain.portal;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 栏目表
 */
@Getter
@Setter
@TableName("CMS_CATEGORIES")
public class CmsCategories implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    /**
    * 模块：1-公文类别，2-资料类别，3-供应商类别
    */
    @ApiModelProperty(value="模块：1-公文类别，2-资料类别，3-供应商类别")
    private Integer module;
    private String moduleName;

    /**
    * 父级
    */
    @ApiModelProperty(value="父级")
    private String pid;

    /**
    * 栏目名称
    */
    @ApiModelProperty(value="栏目名称")
    private String name;

    /**
    * 栏目路径
    */
    @ApiModelProperty(value="栏目路径")
    private String categoryPath;

    /**
     * 栏目路径名称
     */
    private String categoryPathName;

    /**
    * 叶子节点:0-否，1-是
    */
    @ApiModelProperty(value="叶子节点:0-否，1-是")
    private Integer leafIs;

    /**
    * 首页展示:0-否，1-是
    */
    private Integer showHeadIs;

    /**
    * 排序
    */
    @ApiModelProperty(value="排序")
    private Integer sorts;

    /**
    * 状态：0-禁用，1-有效
    */
    @ApiModelProperty(value="状态：0-禁用，1-有效")
    private Integer enabled;

    /**
     * 页面资源路径
     */
    @ApiModelProperty(value="页面资源路径")
    private String uri;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
    * 更新时间
    */
    @ApiModelProperty(value="更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
    * 创建id
    */
    @ApiModelProperty(value="创建id")
    @TableField(fill = FieldFill.INSERT)
    private String createId;

    /**
    * 更新id
    */
    @ApiModelProperty(value="更新id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
    * 租户id
    */
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
    * 组织
    */
    @ApiModelProperty(value="组织")
    @TableField(fill = FieldFill.INSERT)
    private String dataHierarchyId;




    /*******************以下非实体字段*******************/
    @TableField(exist = false)
    private List<CmsCategories> children;
}
