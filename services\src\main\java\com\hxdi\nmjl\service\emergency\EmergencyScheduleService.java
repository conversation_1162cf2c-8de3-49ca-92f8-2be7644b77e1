package com.hxdi.nmjl.service.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.emergency.EmergencyScheduleCondition;
import com.hxdi.nmjl.domain.emergency.EmergencySchedule;

import java.util.List;

/**

 应急调度表服务接口
 <AUTHOR>
 @version 1.0
 @since 2025/7/21
 */
public interface EmergencyScheduleService extends IBaseService<EmergencySchedule> {

    /**
     创建应急调度
     @param emergencySchedule 应急调度信息
     */
    void create(EmergencySchedule emergencySchedule);

    /**
     更新应急调度
     @param emergencySchedule 应急调度信息
     */
    void update(EmergencySchedule emergencySchedule);

    /**
     获取应急调度详情
     @param scheduleId 调度 ID
     @return 应急调度详情
     */
    EmergencySchedule getDetail(String scheduleId);

    /**
     获取应急调度分页列表
     @param condition 查询条件
     @return 分页列表
     */
    Page<EmergencySchedule> pages(EmergencyScheduleCondition condition);

    /**
     获取应急调度列表
     @param condition 查询条件
     @return 调度列表
     */
    List<EmergencySchedule> lists(EmergencyScheduleCondition condition);

    /**
     标记调度为已完成
     @param scheduleId 调度 ID
     */
    void complete(String scheduleId);

    /**
     删除应急调度
     @param scheduleId 调度 ID
     */
    void remove(String scheduleId);
}