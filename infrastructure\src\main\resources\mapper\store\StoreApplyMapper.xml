<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.store.StoreApplyMapper">

    <sql id="Base_Column_List">
        ID, APPLY_NO, ENTERPRISE_NAME, CREDIT_CODE, FR, LEGAL_PERSON_ID_CARD,
        FZR, APPLY_TYPE,
        ORG_ID, ORG_NAME, PROVINCE_CODE, CITY_CODE, COUNTY_CODE, AREA, DETAIL_ADDR,
        POST_CODE, REGISTRE_DATE, MOBILE, EMAIL, LON, LAT, ATTACHMENT,
        APPROVE_STATUS, APPROVER, APPROVE_TIME, APPROVE_OPINION, ENABLED,
        CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.store.StoreApply">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="APPLY_NO" jdbcType="VARCHAR" property="applyNo" />
        <result column="ENTERPRISE_NAME" jdbcType="VARCHAR" property="enterpriseName" />
        <result column="CREDIT_CODE" jdbcType="VARCHAR" property="creditCode" />
        <result column="FR" jdbcType="VARCHAR" property="fr" />
        <result column="LEGAL_PERSON_ID_CARD" jdbcType="VARCHAR" property="legalPersonIdCard" />
        <result column="FZR" jdbcType="VARCHAR" property="fzr" />
        <result column="APPLY_TYPE" jdbcType="INTEGER" property="applyType" />
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
        <result column="PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode" />
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
        <result column="COUNTY_CODE" jdbcType="VARCHAR" property="countyCode" />
        <result column="AREA" jdbcType="VARCHAR" property="area" />
        <result column="DETAIL_ADDR" jdbcType="VARCHAR" property="detailAddr" />
        <result column="POST_CODE" jdbcType="VARCHAR" property="postCode" />
        <result column="REGISTRE_DATE" jdbcType="DATE" property="registreDate" />
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
        <result column="EMAIL" jdbcType="VARCHAR" property="email" />
        <result column="LON" jdbcType="VARCHAR" property="lon" />
        <result column="LAT" jdbcType="VARCHAR" property="lat" />
        <result column="ATTACHMENT" jdbcType="VARCHAR" property="attachment" />
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus" />
        <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_APPLY
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.applyNo)">
                AND APPLY_NO = LIKE CONCAT('%', #{condition.applyNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseName)">
                AND ENTERPRISE_NAME LIKE CONCAT('%', #{condition.enterpriseName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.creditCode)">
                AND CREDIT_CODE = #{condition.creditCode}
            </if>
            <if test="condition.applyType != null">
                AND APPLY_TYPE = #{condition.applyType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_APPLY
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.applyNo)">
                AND APPLY_NO = LIKE CONCAT('%', #{condition.applyNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseName)">
                AND ENTERPRISE_NAME LIKE CONCAT('%', #{condition.enterpriseName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.creditCode)">
                AND CREDIT_CODE LIKE CONCAT('%', #{condition.creditCode}, '%')
            </if>
            <if test="condition.applyType != null">
                AND APPLY_TYPE = #{condition.applyType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
