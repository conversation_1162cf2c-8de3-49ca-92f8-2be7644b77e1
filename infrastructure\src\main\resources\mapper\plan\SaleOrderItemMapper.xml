<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.SaleOrderItemMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.SaleOrderItem">
        <!--@mbg.generated-->
        <!--@Table B_SALE_ORDER_ITEM-->
        <id column="ID" property="id"/>
        <result column="ORDER_ID" property="orderId"/>
        <result column="CONTRACT_ID" property="contractId"/>
        <result column="CATALOG_ID" property="catalogId"/>
        <result column="CATALOG_NAME" property="catalogName"/>
        <result column="BRAND" property="brand"/>
        <result column="GRADE" property="grade"/>
        <result column="SPECIFICATION" property="specification"/>
        <result column="PRODUCT_DATE" property="productDate"/>
        <result column="ORDER_QTY" property="orderQty"/>
        <result column="ORDER_PACK_QTY" property="orderPackQty"/>
        <result column="COMPLETED_QTY" property="completedQty"/>
        <result column="RESERVE_LEVEL" property="reserveLevel"/>
        <result column="PRICE" property="price"/>
        <result column="BATCH_NO" property="batchNo"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        ORDER_ID,
        CATALOG_ID,
        "CATALOG_NAME",
        BRAND,
        GRADE,
        SPECIFICATION,
        PRODUCT_DATE,
        ORDER_QTY,
        RESERVE_LEVEL,
        PRICE,
        BATCH_NO,
        TENANT_ID,
        DATA_HIERARCHY_ID,
        COMPLETED_QTY,
        CONTRACT_ID,
        ORDER_PACK_QTY
    </sql>
</mapper>