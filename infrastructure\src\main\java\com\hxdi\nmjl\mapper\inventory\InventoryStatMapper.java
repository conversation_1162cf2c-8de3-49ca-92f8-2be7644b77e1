package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inventory.InventoryStat;
import com.hxdi.nmjl.condition.inventory.InventoryStatCondition;
import com.hxdi.nmjl.vo.inventory.InventoryStatResVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InventoryStatMapper extends SuperMapper<InventoryStat> {

   @DataPermission(alias = "s")
   List<InventoryStatResVO> listV1(@Param("condition")InventoryStatCondition condition);

   @DataPermission(alias = "s")
   Page<InventoryStatResVO> pageV1(@Param("condition")InventoryStatCondition condition,@Param("page")Page<InventoryStatResVO> page);

   @DataPermission(alias = "s")
   List<InventoryStatResVO> detailListV1(@Param("condition")InventoryStatCondition condition);

   @DataPermission(alias = "s")
   Page<InventoryStatResVO> detailPageV1(@Param("condition")InventoryStatCondition condition,@Param("page")Page<InventoryStatResVO> page);
}
