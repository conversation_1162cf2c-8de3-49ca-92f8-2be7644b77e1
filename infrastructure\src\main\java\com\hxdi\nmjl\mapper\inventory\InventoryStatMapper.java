package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inventory.InventoryStat;
import com.hxdi.nmjl.condition.inventory.InventoryStatCondition;
import com.hxdi.nmjl.vo.inventory.InventoryStatResVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InventoryStatMapper extends SuperMapper<InventoryStat> {

    /**
     * 保管总账列表查询（按仓房、品种、月度进行分组）
     * @param condition
     * @return
     */
   @DataPermission(alias = "tmp")
   List<InventoryStatResVO> listV1(@Param("condition")InventoryStatCondition condition);

   /**
    * 保管总账分页查询（按仓房、品种、月度进行分组）
    * @param condition
    * @param page
    * @return
    */
   @DataPermission(alias = "tmp")
   Page<InventoryStatResVO> pageV1(@Param("condition")InventoryStatCondition condition,@Param("page")Page<InventoryStatResVO> page);

   /**
    * 保管明细账列表查询（按货位、品种、日期进行分组）
    * @param condition
    * @return
    */
   @DataPermission(alias = "tmp")
   List<InventoryStatResVO> detailListV1(@Param("condition")InventoryStatCondition condition);

   /**
    * 保管明细账分页查询（按货位、品种、日期进行分组）
    * @param condition
    * @param page
    * @return
    */
   @DataPermission(alias = "tmp")
   Page<InventoryStatResVO> detailPageV1(@Param("condition")InventoryStatCondition condition,@Param("page")Page<InventoryStatResVO> page);
}
