<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.iot.TemHumRecordMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.iot.TemHumRecord">
    <!--@mbg.generated-->
    <!--@Table B_TEM_HUM_RECORD-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
    <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
    <result column="ST_ID" jdbcType="VARCHAR" property="stId" />
    <result column="ST_NAME" jdbcType="VARCHAR" property="stName" />
    <result column="SERIAL" jdbcType="VARCHAR" property="serial" />
    <result column="DEVICE_NAME" jdbcType="VARCHAR" property="deviceName" />
    <result column="DEVICE_STATE" jdbcType="INTEGER" property="deviceState" />
    <result column="CW" jdbcType="VARCHAR" property="cw" />
    <result column="CS" jdbcType="VARCHAR" property="cs" />
    <result column="QW" jdbcType="VARCHAR" property="qw" />
    <result column="QS" jdbcType="VARCHAR" property="qs" />
    <result column="INSPECT_TIME" jdbcType="TIMESTAMP" property="inspectTime" />
    <result column="ERR_IS" jdbcType="INTEGER" property="errIs" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="MESSAGE_ID" jdbcType="VARCHAR" property="messageId" />
    <result column="NODE_ID" jdbcType="INTEGER" property="nodeId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, STORE_ID, STORE_NAME, ST_ID, ST_NAME, SERIAL, DEVICE_NAME, DEVICE_STATE, CW, "CS", QW, QS,INSPECT_TIME, ERR_IS, CREATE_TIME, DATA_HIERARCHY_ID, MESSAGE_ID, NODE_ID, AREA
  </sql>

  <select id="getList" resultMap="BaseResultMap">
    Select <include refid="Base_Column_List" />
    from B_TEM_HUM_RECORD
    <where>
      <if test="condition.deviceId != null and condition.deviceId !='' ">
        and ID = #{condition.deviceId}
      </if>
      <if test="condition.startTime != null and condition.startTime !='' ">
        and INSPECT_TIME &gt;= #{condition.startTime}
      </if>
      <if test="condition.stId != null and condition.stId !='' ">
        and ST_ID = #{condition.stId}
      </if>
      <if test="condition.deviceName != null and condition.deviceName !='' ">
        and DEVICE_NAME = #{condition.deviceName}
      </if>
      <if test="condition.endTime != null and condition.endTime !='' ">
        and INSPECT_TIME &lt;= #{condition.endTime}
      </if>
      <if test="condition.errIs != null and condition.errIs !='' ">
        and ERR_IS = #{condition.errIs}
      </if>
      <if test="condition.area != null and condition.area !='' ">
        and AREA like concat('%',#{condition.area},'%')
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>

  <select id="getPage" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_TEM_HUM_RECORD
    <where>
      <if test="condition.deviceId != null and condition.deviceId !='' ">
        and ID = #{condition.deviceId}
      </if>
      <if test="condition.startTime != null and condition.startTime !='' ">
        and INSPECT_TIME &gt;= #{condition.startTime}
      </if>
      <if test="condition.stId != null and condition.stId !='' ">
        and ST_ID = #{condition.stId}
      </if>
      <if test="condition.deviceName != null and condition.deviceName !='' ">
        and DEVICE_NAME = #{condition.deviceName}
      </if>
      <if test="condition.endTime != null and condition.endTime !='' ">
        and INSPECT_TIME &lt;= #{condition.endTime}
      </if>
      <if test="condition.errIs != null and condition.errIs !='' ">
        and ERR_IS = #{condition.errIs}
      </if>
      <if test="condition.area != null and condition.area !='' ">
        and AREA like concat('%',#{condition.area},'%')
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>
