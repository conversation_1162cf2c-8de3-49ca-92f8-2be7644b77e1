package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 只记录关键信息，其他信息通过历史数据关联
 */
@ApiModel(description="货位卡")
@Getter
@Setter
@TableName(value = "B_LOCATION_CARD")
public class LocationCard implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="军供站ID")
    private String storeId;

    /**
     * 仓库
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value="仓库")
    private String storeName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value="仓房ID")
    private String stId;

    /**
     * 仓房
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value="仓房")
    private String stName;

    /**
     * 货位ID
     */
    @TableField(value = "LOC_ID")
    @ApiModelProperty(value="货位ID")
    private String locId;

    /**
     * 货位名称
     */
    @TableField(value = "LOC_NAME")
    @ApiModelProperty(value="货位名称")
    private String locName;

    /**
     * 货位状态
     */
    @TableField(value = "LOC_STATE")
    @ApiModelProperty(value="货位状态")
    private Integer locState;

    /**
     * 库存ID
     */
    @TableField(value = "INVENTORY_ID")
    @ApiModelProperty(value="库存ID")
    private String inventoryId;

    /**
     * 生产批次
     */
    @TableField(value = "BATCH_NUM")
    @ApiModelProperty(value="生产批次")
    private String batchNum;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value="状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建日期")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}
