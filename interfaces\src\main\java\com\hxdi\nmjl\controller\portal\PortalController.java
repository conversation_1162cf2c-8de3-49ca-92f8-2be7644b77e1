package com.hxdi.nmjl.controller.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.portal.PortalCondition;
import com.hxdi.nmjl.condition.portal.ResourceCondition;
import com.hxdi.nmjl.domain.portal.CmsResources;
import com.hxdi.nmjl.service.portal.CmsResourcesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 门户网站
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/21 14:28
 */
@RestController
@RequestMapping("/portal")
@Api(tags = "门户网站")
public class PortalController extends BaseController<CmsResourcesService, CmsResources> {

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<CmsResources> getDetail(String resourceId) {
        return ResultBody.<CmsResources>OK().data(bizService.getDetail(resourceId));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<CmsResources>> pages(PortalCondition condition) {
        return ResultBody.<Page<CmsResources>>OK().data(bizService.pageForPortal(condition));
    }

}
