package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.DateUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.inventory.InventoryStatCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inventory.InventoryLog;
import com.hxdi.nmjl.domain.inventory.InventoryStat;
import com.hxdi.nmjl.enums.InoutBusinessType;
import com.hxdi.nmjl.mapper.inventory.InventoryStatMapper;
import com.hxdi.nmjl.service.inventory.InventoryLogService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.service.inventory.InventoryStatService;
import com.hxdi.nmjl.utils.RedisKeys;
import com.hxdi.nmjl.vo.inventory.InventoryStatResVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryStatServiceImpl extends BaseServiceImpl<InventoryStatMapper, InventoryStat> implements InventoryStatService {

    @Resource
    private InventoryService inventoryService;

    @Resource
    private InventoryLogService logService;

    @Override
    public void createOrUpdate(String InventoryId, String inoutType, BigDecimal changeQty) {
        if (CommonUtils.isEmpty(InventoryId) || CommonUtils.isEmpty(inoutType) || CommonUtils.isEmpty(changeQty)) {
            throw new BaseException("库存ID、统计日期不能为空");
        }
        //只取当日数据
        Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
        LambdaQueryWrapper<InventoryStat> wrapper = new LambdaQueryWrapper<InventoryStat>()
                .eq(InventoryStat::getInventoryId, InventoryId)
                .eq(InventoryStat::getReportDate, date)
                .last(" limit 1");
        InventoryStat Stat = getOne(wrapper);
        if (CommonUtils.isNotEmpty(Stat)) {
            if (Objects.equals(changeQty, BigDecimal.ZERO) || changeQty.compareTo(BigDecimal.ZERO) < 0) {
                //变化数量<=0，不更新
                return;
            }

            //入库
            if (inoutType.equals(InoutBusinessType.IN.getCode())) {
                Stat.setInQty(Stat.getInQty().add(changeQty));
                Stat.setFinalQty(Stat.getFinalQty().add(changeQty));
            }
            //出库
            if (inoutType.equals(InoutBusinessType.OUT.getCode())) {
                Stat.setOutQty(Stat.getOutQty().add(changeQty));
                Stat.setFinalQty(Stat.getFinalQty().subtract(changeQty));
            }

            updateById(Stat);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBatch() {
        //日期取当天（只保留日期部分）
        Date Day = DateUtils.parseDate(DateUtils.format(new Date(), DateUtils.LOCAL_DATE));

        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<Inventory>()
                .eq(Inventory::getEnabled, StrPool.State.ENABLE);
        List<Inventory> list = inventoryService.list(wrapper);
        List<InventoryStat> statList = new ArrayList<>(list.size());
        list.forEach(inventory -> {
            InventoryStat stat = new InventoryStat();
            stat.setInventoryId(inventory.getId());
            stat.setReportDate(Day);
            stat.setStartQty(inventory.getInventoryQty());
            stat.setInQty(BigDecimal.ZERO);
            stat.setOutQty(BigDecimal.ZERO);
            stat.setFinalQty(inventory.getInventoryQty());
            stat.setDataHierarchyId(inventory.getDataHierarchyId());
            statList.add(stat);
        });
        saveBatch(statList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch() {
        // 计算前一天的开始时间和结束时间
        Date date = new Date();
        Date startOfYestDay = DateUtils.getStartOfDay(DateUtils.getBeforeDay(date));// 前一天的开始时间
        Date endOfYestDay = DateUtils.getEndOfDay(DateUtils.getBeforeDay(date));    // 前一天的结束时间
        //获取前一天的时间（只包含日期部分）
        String yestDay = DateUtils.format(DateUtils.getBeforeDay(date), DateUtils.LOCAL_DATE);

        //查询条件
        LambdaQueryWrapper<InventoryStat> statWrapper = new LambdaQueryWrapper<InventoryStat>()
                .eq(InventoryStat::getReportDate, yestDay);

        LambdaQueryWrapper<Inventory> inventoryWrapper = new LambdaQueryWrapper<Inventory>()
                .eq(Inventory::getEnabled, StrPool.State.ENABLE);

        LambdaQueryWrapper<InventoryLog> logWrapper = new LambdaQueryWrapper<InventoryLog>()
                .eq(InventoryLog::getEnabled, StrPool.State.ENABLE)
                .ge(InventoryLog::getCreateTime, startOfYestDay)
                .le(InventoryLog::getCreateTime, endOfYestDay);


        // 获取当前的库存信息
        Map<String, Inventory> inventoryMap = inventoryService.list(inventoryWrapper)
                .stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);
        // 获取前一天生成的库存统计信息
        List<InventoryStat> statList = this.list(statWrapper);
        //获取前一日的库存日志
        List<InventoryLog> logs = logService.list(logWrapper);
        // 根据库存Id分组，获取每条库存对应的当天的出入库数量
        Map<String, InventoryLogSummary> logSummaryMap = new HashMap<>();
        logs.forEach(log -> logSummaryMap.computeIfAbsent(log.getInventoryId(), k -> new InventoryLogSummary()).addLog(log));

        // 更新库存统计信息
        statList.forEach(stat -> {
            Inventory inventory = inventoryMap.get(stat.getInventoryId());
            //设置终止库存数量
            if (CommonUtils.isNotEmpty(inventory)) {
                //当前库存数量即为前一天的终止数量
                stat.setFinalQty(inventory.getInventoryQty());
            } else {
                //如果库存被删除则默认已出完，终止库存数量置为0
                stat.setFinalQty(BigDecimal.ZERO);
            }
            //设置入库数量和出库数量，同时设置权限字段（在更新时设置权限，即用户默认查看的最新数据截止到前一天）
            if (CommonUtils.isNotEmpty(logSummaryMap.get(stat.getInventoryId()))) {
                InventoryLogSummary summary = logSummaryMap.get(stat.getInventoryId());
                stat.setInQty(summary.inQtyTotal);
                stat.setOutQty(summary.outQtyTotal);
                stat.setDataHierarchyId(summary.DATA_HIERARCHY_ID);
            }
        });
        //批量更新库存统计表
        updateBatchById(statList);
    }

    @Override
    public List<InventoryStatResVO> listV1(InventoryStatCondition condition) {
        return baseMapper.listV1(condition);
    }

    @Override
    public Page<InventoryStatResVO> pageV1(InventoryStatCondition condition) {
        Page<InventoryStatResVO> page = condition.newPage();
        return baseMapper.pageV1(condition, page);
    }

    @Override
    public List<InventoryStatResVO> detailListV1(InventoryStatCondition condition) {
        List<InventoryStatResVO> resList = baseMapper.detailListV1(condition);
        resList.forEach(item -> {
            Optional<Catalog> catalog = CacheProvider.optional(RedisKeys.CATALOG.key(), item.getCatalogId());
            item.setSpecification(CommonUtils.getOptionalValue(catalog, Catalog::getSpecification));
            item.setBrand(CommonUtils.getOptionalValue(catalog, Catalog::getBrand));
            item.setFullName(CommonUtils.getOptionalValue(catalog, Catalog::getFullName));
            item.setMaxStorageTime(CommonUtils.getOptionalValue(catalog, Catalog::getMaxStorageTime, null));
        });
        return resList;
    }

    @Override
    public Page<InventoryStatResVO> detailPageV1(InventoryStatCondition condition) {
        Page<InventoryStatResVO> page = condition.newPage();
        Page<InventoryStatResVO> resPage = baseMapper.detailPageV1(condition, page);
        List<InventoryStatResVO> resList = resPage.getRecords();
        resList.forEach(item -> {
            Optional<Catalog> catalog = CacheProvider.optional(RedisKeys.CATALOG.key(), item.getCatalogId());
            item.setSpecification(CommonUtils.getOptionalValue(catalog, Catalog::getSpecification));
            item.setBrand(CommonUtils.getOptionalValue(catalog, Catalog::getBrand));
            item.setFullName(CommonUtils.getOptionalValue(catalog, Catalog::getFullName));
            item.setMaxStorageTime(CommonUtils.getOptionalValue(catalog, Catalog::getMaxStorageTime, null));
        });
        resPage.setRecords(resList);
        return resPage;
    }


    // 定义一个类来临时存储id对应库存的总的出库和入库数量及权限信息
    protected static class InventoryLogSummary {
        protected BigDecimal inQtyTotal = BigDecimal.ZERO;
        protected BigDecimal outQtyTotal = BigDecimal.ZERO;
        protected String DATA_HIERARCHY_ID;

        protected void addLog(InventoryLog log) {
            if (InoutBusinessType.IN.getCode().equals(log.getInoutType())) {
                inQtyTotal = inQtyTotal.add(log.getChangeQty());
            } else if (InoutBusinessType.OUT.getCode().equals(log.getInoutType())) {
                outQtyTotal = outQtyTotal.add(log.getChangeQty());
            }
            // 仅当 DATA_HIERARCHY_ID 为空时才进行赋值，以防止被后续的 null 值覆盖
            if (CommonUtils.isEmpty(DATA_HIERARCHY_ID)) {
                DATA_HIERARCHY_ID = log.getDataHierarchyId();
            }
        }
    }


}
