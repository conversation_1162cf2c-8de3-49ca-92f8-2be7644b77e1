package com.hxdi.nmjl.condition.emergency;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel(description = "应急预案查询参数")
@Getter
@Setter

public class EmergencyResponseCondition extends QueryCondition {
    @ApiModelProperty(value = "预案编号")
    private String responseCode;

    @ApiModelProperty(value = "预案级别：1-一级，2-二级，3-三级")
    private Integer responseLevel;

    @ApiModelProperty(value = "发布状态:0-待发布，1-已发布")
    private Integer publishState;

    @ApiModelProperty(value = "审核状态")
    private Integer approveStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
