<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.clientrelated.ClientSaleConfigMapper">

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, CLIENT_ID, PAYMENT_PERIOD, DISCOUNT_RATE,
        TARGET_AMOUNT, REFUND_RATE, REFUND_MAX,
        START_TIME, END_TIME, ENABLED,
        CREATE_TIME, UPDATE_TIME,
        CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,CLIENT_NAME
    </sql>

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.clientrelated.ClientSaleConfig">
        <id column="ID" property="id"/>
        <result column="CLIENT_ID" property="clientId"/>
        <result column="PAYMENT_PERIOD" property="paymentPeriod"/>
        <result column="DISCOUNT_RATE" property="discountRate"/>
        <result column="TARGET_AMOUNT" property="targetAmount"/>
        <result column="REFUND_RATE" property="refundRate"/>
        <result column="REFUND_MAX" property="refundMax"/>
        <result column="START_TIME" property="startTime"/>
        <result column="END_TIME" property="endTime"/>
        <result column="ENABLED" property="enabled"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
        <result column="CLIENT_NAME" property="clientName"/>
    </resultMap>

    <!-- 列表查询 -->
    <select id="getList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_CLIENT_SALE_CONFIG
        <where>
            enabled = 1
            <if test="condition.clientName != null and condition.clientName != ''">
                AND CLIENT_NAME LIKE CONCAT('%', #{condition.clientName}, '%')
            </if>
            <!-- 开始时间 -->
            <if test="condition.startTime != null">
                AND START_TIME &gt;= #{condition.startTime}
            </if>
            <!-- 结束时间 -->
            <if test="condition.endTime != null">
                AND END_TIME &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 分页查询 -->
    <select id="getPages" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_CLIENT_SALE_CONFIG
        <where>
            enabled = 1
            <if test="condition.clientName != null and condition.clientName != ''">
                AND CLIENT_NAME LIKE CONCAT('%', #{condition.clientName}, '%')
            </if>
            <!-- 开始时间 -->
            <if test="condition.startTime != null">
                AND START_TIME &gt;= #{condition.startTime}
            </if>
            <!-- 结束时间 -->
            <if test="condition.endTime != null">
                AND END_TIME &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
