package com.hxdi.nmjl.domain.quality;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(description = "质检结果")
@TableName(value = "B_QUALITY_INSPECTION")
public class QualityInspection implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 检验编号
     */
    @TableField(value = "INSPECTION_NO")
    @ApiModelProperty(value = "检验编号")
    private String inspectionNo;

    /**
     * 检验名称
     */
    @TableField(value = "NAME")
    @ApiModelProperty(value = "检验名称")
    private String name;

    /**
     * 检验方案ID
     */
    @TableField(value = "SCHEMA_ID")
    @ApiModelProperty(value = "检验方案ID")
    private String schemaId;

    /**
     * 检验方案名称
     */
    @TableField(value = "SCHEMA_NAME")
    @ApiModelProperty(value = "检验方案名称")
    private String schemaName;

    /**
     * 检验报告类型:字典JYBGLX
     */
    @TableField(value = "INSPECT_TYPE")
    @ApiModelProperty(value = "检验报告类型:字典JYBGLX")
    private String inspectType;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    /**
     * 机构ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    /**
     * 机构名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 质检机构统一社会信用代码
     */
    @TableField(value = "CREDIT_CODE")
    @ApiModelProperty(value = "机构代码")
    private String creditCode;

    /**
     * 合同ID
     */
    @TableField(value = "CONTRACT_ID")
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 合同编码
     */
    @TableField(value = "CONTRACT_CODE")
    @ApiModelProperty(value = "合同编码")
    private String contractCode;

    /**
     * 订单ID
     */
    @TableField(value = "ORDER_ID")
    @ApiModelProperty(value = "订单ID")
    private String orderId;

    /**
     * 订单编码
     */
    @TableField(value = "ORDER_CODE")
    @ApiModelProperty(value = "订单编码")
    private String orderCode;

    /**
     * 生产批次
     */
    @TableField(value = "BATCH_NUM")
    @ApiModelProperty(value = "生产批次")
    private String batchNum;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    /**
     * 质量等级：字典：YZLDJ/LZLDJ
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value = "质量等级：字典：YZLDJ/LZLDJ")
    private String grade;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    @ApiModelProperty(value = "规格")
    private String specification;

    /**
     * 生产日期
     */
    @TableField(value = "PRODUCT_DATE")
    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND")
    @ApiModelProperty(value = "品牌名称")
    private String brand;

    /**
     * 样品编号
     */
    @TableField(value = "SAMPLE_NO")
    @ApiModelProperty(value = "样品编号")
    private String sampleNo;

    /**
     * 抽样日期
     */
    @TableField(value = "SAMPLING_DATE")
    @ApiModelProperty(value = "抽样日期")
    private String samplingDate;

    /**
     * 抽样人
     */
    @TableField(value = "SAMPLER")
    @ApiModelProperty(value = "抽样人")
    private String sampler;

    /**
     * 样品数量
     */
    @TableField(value = "SAMPLE_QTY")
    @ApiModelProperty(value = "样品数量")
    private BigDecimal sampleQty;

    /**
     * 检验机构ID
     */
    @TableField(value = "QUALITY_ORG_ID")
    @ApiModelProperty(value = "检验机构ID")
    private String qualityOrgId;

    /**
     * 检验机构名称
     */
    @TableField(value = "QUALITY_ORG_NAME")
    @ApiModelProperty(value = "检验机构名称")
    private String qualityOrgName;

    /**
     * 检测日期
     */
    @TableField(value = "INSPECT_TIME")
    @ApiModelProperty(value = "检测日期")
    private Date inspectTime;

    /**
     * 检验人
     */
    @TableField(value = "INSPECT_PERSON")
    @ApiModelProperty(value = "检验人")
    private String inspectPerson;

    /**
     * 检验结果:1-合格，0-不合格
     */
    @TableField(value = "INSPECT_RESULT")
    @ApiModelProperty(value = "检验结果:1-合格，0-不合格")
    private String inspectResult;

    /**
     * 检验说明
     */
    @TableField(value = "INSPECT_DESC")
    @ApiModelProperty(value = "检验说明")
    private String inspectDesc;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}