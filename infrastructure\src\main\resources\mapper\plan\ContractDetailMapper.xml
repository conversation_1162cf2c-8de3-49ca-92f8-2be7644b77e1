<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ContractDetailMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.ContractDetail">
    <!--@mbg.generated-->
    <!--@Table B_CONTRACT_DETAIL-->
    <id column="ID" property="id" />
    <result column="CONTRACT_ID" property="contractId" />
    <result column="CLASSIFICATION_ID" property="classificationId" />
    <result column="CLASSIFICATION_NAME" property="classificationName" />
    <result column="CATALOG_ID" property="catalogId" />
    <result column="CATALOG_NAME" property="catalogName" />
    <result column="BRAND" property="brand" />
    <result column="GRADE" property="grade" />
    <result column="SPECIFICATION" property="specification" />
    <result column="CONTRACT_PACK_QTY" property="contractPackQty" />
    <result column="CONTRACT_QTY" property="contractQty" />
    <result column="COMPLETED_QTY" property="completedQty" />
    <result column="RESERVE_LEVEL" property="reserveLevel" />
    <result column="PRICE" property="price" />
    <result column="REQUIREMENT" property="requirement" />
    <result column="SUPPLY_FREQUENCY" property="supplyFrequency" />
    <result column="FIRST_DELIVERY_TIME" property="firstDeliveryTime" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONTRACT_ID, CLASSIFICATION_ID, CLASSIFICATION_NAME, CATALOG_ID, CATALOG_NAME, 
    BRAND, GRADE, SPECIFICATION, CONTRACT_PACK_QTY, CONTRACT_QTY, COMPLETED_QTY, 
    RESERVE_LEVEL, PRICE, REQUIREMENT, SUPPLY_FREQUENCY, FIRST_DELIVERY_TIME,
    TENANT_ID, DATA_HIERARCHY_ID
  </sql>
</mapper>
