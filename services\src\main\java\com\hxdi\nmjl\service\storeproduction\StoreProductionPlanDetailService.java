package com.hxdi.nmjl.service.storeproduction;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlanDetail;

import java.util.List;

/**
 * 生产计划明细服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/5
 */
public interface StoreProductionPlanDetailService extends IBaseService<StoreProductionPlanDetail> {

    /**
     * 更新生产计划明细
     * @param detail 生产计划明细实体
     */
    void update(StoreProductionPlanDetail detail);

    /**
     * 根据计划ID获取明细列表
     * @param planId 计划ID
     * @return 生产计划明细列表
     */
    List<StoreProductionPlanDetail> getList(String planId);

    /**
     * 根据计划ID删除明细
     * @param planId 计划ID
     */
    void remove(String planId);

}