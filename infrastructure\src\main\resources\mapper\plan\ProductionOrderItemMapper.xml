<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ProductionOrderItemMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.ProductionOrderItem">
    <!--@mbg.generated-->
    <!--@Table B_PRODUCTION_ORDER_ITEM-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId" />
    <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="GRADE" jdbcType="VARCHAR" property="grade" />
    <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification" />
    <result column="ORDER_PACK_QTY" jdbcType="INTEGER" property="orderPackQty" />
    <result column="ORDER_QTY" jdbcType="DECIMAL" property="orderQty" />
    <result column="COMPLETED_QTY" jdbcType="DECIMAL" property="completedQty" />
    <result column="COMPLETED_PACK_QTY" jdbcType="INTEGER" property="completedPackQty" />
    <result column="RETURN_QTY" jdbcType="DECIMAL" property="returnQty" />
    <result column="RESERVE_LEVEL" jdbcType="VARCHAR" property="reserveLevel" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="PRODUCT_DATE" jdbcType="VARCHAR" property="productDate" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ORDER_ID, CATALOG_ID, "CATALOG_NAME", GRADE, SPECIFICATION, ORDER_PACK_QTY, ORDER_QTY, 
    COMPLETED_QTY, COMPLETED_PACK_QTY, RETURN_QTY, RESERVE_LEVEL, PRICE, PRODUCT_DATE, 
    CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, BRAND
  </sql>
</mapper>