package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.clientrelated.SupplierCondition;
import com.hxdi.nmjl.domain.base.ProductionSupplier;
import com.hxdi.nmjl.service.base.SupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@RestController
@RequestMapping("/supplier")
@Api(tags = "供应商管理")
public class SupplierController extends BaseController<SupplierService, ProductionSupplier> {

    @Log(value = "保存或更新供应商", saveReqParam = true)
    @ApiOperation(value = "保存或更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdateV1(@RequestBody ProductionSupplier supplier) {
        bizService.saveOrUpdateV1(supplier);
        return ResultBody.OK();
    }

    @Log(value = "删除供应商", saveReqParam = true)
    @ApiOperation(value = "删除")
    @PostMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        bizService.changeState(id, 7);
        return ResultBody.OK();
    }

    @Log(value = "启用或禁用供应商", saveReqParam = true)
    @ApiOperation(value = "启用/禁用")
    @PostMapping("/change/state")
    public ResultBody<Void> changeState(@RequestParam String id, @RequestParam Integer enabled) {
        bizService.changeState(id, enabled);
        return ResultBody.OK();
    }

    @ApiOperation("审批计划")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id,
                                    @RequestParam Integer approveStatus,
                                    @RequestParam(required = false) String approveOpinion) {
        bizService.approve(id, approveStatus, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<ProductionSupplier> getDetail(String id) {
        return ResultBody.<ProductionSupplier>OK().data(bizService.getDetail(id));
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<ProductionSupplier>> pages(SupplierCondition condition) {
        return ResultBody.<Page<ProductionSupplier>>OK().data(bizService.pages(condition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<ProductionSupplier>> lists(SupplierCondition condition) {
        return ResultBody.<List<ProductionSupplier>>OK().data(bizService.lists(condition));
    }

}


