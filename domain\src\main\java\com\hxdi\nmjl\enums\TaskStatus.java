package com.hxdi.nmjl.enums;

import lombok.Getter;

/**
 * @program: nmjl-service
 * @description: 任务执行状态
 * @author: 王贝强
 * @create: 2025-04-23 15:33
 */
@Getter
public enum TaskStatus {
    UNEXECUTED(0,"未执行"),
    EXECUTING(1,"执行中"),
    COMPLETED(2,"已完成");

    private final Integer code;
    private final String label;

    TaskStatus(Integer code, String label) {
        this.code = code;
        this.label = label;
    }
}
