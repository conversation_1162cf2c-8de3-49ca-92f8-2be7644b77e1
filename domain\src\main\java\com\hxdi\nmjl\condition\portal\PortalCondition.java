package com.hxdi.nmjl.condition.portal;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PortalCondition extends QueryCondition {

    /**
     * 栏目id
     */
    @ApiModelProperty(value="栏目id")
    private String categoryId;
    /**
     * 模块
     */
    @ApiModelProperty(value="模块")
    private Integer module;

    /**
     * 标题
     */
    @ApiModelProperty(value="标题")
    private String title;

    @ApiModelProperty(value="是否公开:0-否，1-是")
    private Integer publicIs;

    /**
     * 是否轮播展示：0-否，1-是
     */
    @ApiModelProperty(value="是否轮播展示：0-否，1-是")
    private Integer displayIs;

}
