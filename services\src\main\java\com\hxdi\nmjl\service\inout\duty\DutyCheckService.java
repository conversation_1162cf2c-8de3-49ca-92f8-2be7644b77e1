package com.hxdi.nmjl.service.inout.duty;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.duty.DutyCheck;
import com.hxdi.nmjl.condition.inout.DutyCondition;

import java.util.List;

public interface DutyCheckService extends IBaseService<DutyCheck> {
    /**
     * 创建
     * @param dutyCheck
     */
    void create(DutyCheck dutyCheck);

    /**
     * 更新
     * @param dutyCheck
     */
    void updating(DutyCheck dutyCheck);

    /**
     * 查询
     * @param dutyId
     * @return
     */
    DutyCheck detail(String dutyId);

    /**
     * 删除
      * @param dutyId
     */
    void delete(String dutyId);

    /**
     * 分页查询
     * @param dutyCondition
     * @return
     */
    Page<DutyCheck> getPage(DutyCondition dutyCondition);

    /**
     * 列表查询
     * @param dutyCondition
     * @return
     */
    List<DutyCheck> getList(DutyCondition dutyCondition);
}
