package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inventory.InventoryAlarmConfig;
import com.hxdi.nmjl.service.inventory.InventoryAlarmConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存预警配置表控制层
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "库存预警配置")
@RequestMapping("/inventoryWarningConfig")
public class InventoryAlarmConfigController extends BaseController<InventoryAlarmConfigService, InventoryAlarmConfig> {

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/selectOne")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResultBody<InventoryAlarmConfig> get(String id) {
        return ResultBody.<InventoryAlarmConfig>OK().data(bizService.getById(id));
    }

    @GetMapping("/list")
    @ApiOperation(value = "列表查询")
    public ResultBody<List<InventoryAlarmConfig>> list(InventoryAlarmConfig condition) {
        return ResultBody.<List<InventoryAlarmConfig>>OK().data(bizService.listV1(condition));
    }

    @GetMapping("/page")
    @ApiOperation(value = "分页查询")
    public ResultBody<Page<InventoryAlarmConfig>> page(InventoryAlarmConfig condition, Page<InventoryAlarmConfig> page) {
        return ResultBody.<Page<InventoryAlarmConfig>>OK().data(bizService.pageV1(condition, page));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新")
    public ResultBody<Void> saveOrUpdate(@RequestBody InventoryAlarmConfig inventoryAlarmConfig) {
        bizService.saveOrUpdate(inventoryAlarmConfig);
        return ResultBody.OK();
    }

    @PostMapping("/generateWarningMsg")
    @ApiOperation(value = "生成预警信息(定时任务接口，请勿直接调用)")
    public ResultBody<Void> generateWarningMsg() {
        bizService.generateWarningMsg();
        return ResultBody.OK();
    }

}
