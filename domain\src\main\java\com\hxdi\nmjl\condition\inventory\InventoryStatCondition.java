package com.hxdi.nmjl.condition.inventory;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 库存保管账查询条件
 * @author: 王贝强
 * @create: 2025-04-21 17:04
 */
@Getter
@Setter
public class InventoryStatCondition extends QueryCondition {

    @ApiModelProperty(value="统计开始时间")
    private Date startTime;

    @ApiModelProperty(value="统计截止时间")
    private Date endTime;

    @ApiModelProperty(value="仓房ID")
    private String stId;

    @ApiModelProperty(value="仓房名称")
    private String stName;

    @ApiModelProperty(value="品种ID")
    private String catalogId;

    @ApiModelProperty(value="品种名称")
    private String catalogName;


}
