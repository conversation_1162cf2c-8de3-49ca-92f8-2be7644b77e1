package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inventory.InventoryCheckConfig;
import com.hxdi.nmjl.mapper.inventory.InventoryCheckConfigMapper;
import com.hxdi.nmjl.service.inventory.InventoryCheckConfigService;
import com.hxdi.nmjl.condition.inventory.InventoryCheckConfigCondition;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 盘点任务管理实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryCheckConfigServiceImpl extends BaseServiceImpl<InventoryCheckConfigMapper, InventoryCheckConfig> implements InventoryCheckConfigService {

    @Override
    public void create(InventoryCheckConfig config) {
        verifyConfig(config);
        baseMapper.insert(config);
    }

    @Override
    public void update(InventoryCheckConfig config) {
        baseMapper.updateById(config);
    }

    @Override
    public InventoryCheckConfig getByUniqueKey(String uniqueKey) {
        InventoryCheckConfig checkConfig = super.getById(uniqueKey);
        if (checkConfig == null) {
            checkConfig = baseMapper.selectOne(Wrappers.<InventoryCheckConfig>lambdaQuery()
                    .eq(InventoryCheckConfig::getStoreId, uniqueKey)
                    .ne(InventoryCheckConfig::getEnabled, StrPool.State.DELETE));
        }
        return checkConfig;
    }

    @Override
    public Page<InventoryCheckConfig> pages(InventoryCheckConfigCondition condition) {
        Page<InventoryCheckConfig> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<InventoryCheckConfig> lists(InventoryCheckConfigCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void changeState(String id, Integer state) {
        InventoryCheckConfig checkConfig = getByUniqueKey(id);
        if (StrPool.State.DELETE == checkConfig.getEnabled().intValue()) {
            return;
        }
        InventoryCheckConfig updatingCheckConfig = new InventoryCheckConfig();
        updatingCheckConfig.setId(id);
        updatingCheckConfig.setEnabled(state);
        baseMapper.updateById(updatingCheckConfig);
    }

    @Override
    public void updateToNextPlanTime(String checkConfigId) {
        InventoryCheckConfig checkConfig = getByUniqueKey(checkConfigId);
        LocalDate nextTime = checkConfig.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        if (checkConfig.getTimeUnit() == 1) {
            // 天数 + 周期
            nextTime = nextTime.plusDays(checkConfig.getPeriods());
        } else if (checkConfig.getTimeUnit() == 2) {
            // 月份 + 周期
            nextTime = nextTime.plusMonths(checkConfig.getPeriods());
        }

        checkConfig.setStartTime(Date.from(nextTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        this.updateById(checkConfig);
    }

    /**
     * 验证数据有效性
     */
    private void verifyConfig(InventoryCheckConfig checkConfig) {
        long count = baseMapper.selectCount(Wrappers.<InventoryCheckConfig>lambdaQuery()
                .eq(InventoryCheckConfig::getStoreId, checkConfig.getStoreId())
                .ne(InventoryCheckConfig::getEnabled, StrPool.State.DELETE));

        if (count > 0) {
            throw new BaseException("盘点任务已存在，请勿重复创建");
        }
    }
} 