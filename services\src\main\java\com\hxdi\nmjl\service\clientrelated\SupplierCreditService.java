package com.hxdi.nmjl.service.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.clientrelated.SupplierCreditCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierCredit;

import java.util.List;

/**
 * 供应商信用管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
public interface SupplierCreditService extends IBaseService<SupplierCredit> {

    /**
     * 新增
     *
     * @param credit 供应商信用信息
     */
    void create(SupplierCredit credit);

    /**
     * 更新
     *
     * @param credit 供应商信用信息
     */
    void update(SupplierCredit credit);

    /**
     * 查询详情
     *
     * @param supplierId 供应商ID
     * @return 供应商信用信息
     */
    List<SupplierCredit> getDetail(String supplierId);

    /**
     * 修改状态
     *
     * @param id      主键Id
     * @param enabled 状态：0-删除 1-正常
     */
    void changeState(String id, Integer enabled);

    /**
     * 查询详情
     *
     * @param uniqueKey 供应商信用Id
     * @return 供应商信用信息
     */
    SupplierCredit getByUniqueKey(String uniqueKey);


    /**
     * 分页
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<SupplierCredit> pages(SupplierCreditCondition condition);

    /**
     * 列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<SupplierCredit> lists(SupplierCreditCondition condition);

    /**
     * 根据主表ID删除
     *
     * @param mainId 供应商ID
     */
    void removeByMainId(String mainId);

}
