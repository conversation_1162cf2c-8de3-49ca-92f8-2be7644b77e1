package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.InspectionItem;

import java.util.List;

public interface InspectionItemService extends IBaseService<InspectionItem> {

    /**
     * 获取列表
     * @param pid 计划ID或记录ID
     * @return
     */
    List<InspectionItem> getListByPid(String pid);

    /**
     * 更新监督检查事项列表
     * @param pid   计划ID或记录ID
     * @param detailList
     */
    void updateList(String pid, List<InspectionItem> detailList);
}
