package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 沟通方案表
 */
@ApiModel(description = "沟通方案表")
@TableName("B_COMM_SCHEMA")
@Getter
@Setter
public class CommSchema extends BModel implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 沟通方案名称
     */
    @TableField("NAME")
    @LIKE
    @ApiModelProperty(value = "沟通方案名称")
    private String name;

    /**
     * 沟通目标
     */
    @TableField("COMM_GOAL")
    @ApiModelProperty(value = "沟通目标")
    private String commGoal;

    /**
     * 沟通对象
     */
    @TableField("TARGET_AUDIENCE")
    @ApiModelProperty(value = "沟通对象")
    private String targetAudience;

    /**
     * 沟通策略
     */
    @TableField("STRATEGY")
    @ApiModelProperty(value = "沟通策略")
    private String strategy;

    /**
     * 启用状态(1-启用，0-禁用)
     */
    @TableField("ENABLED")
    @ApiModelProperty(value = "启用状态(1-启用，0-禁用)")
    private Integer enabled;


    @ApiModelProperty(value = "沟通计划")
    @TableField(exist = false)
    private List<CommPlan> detailList;


}