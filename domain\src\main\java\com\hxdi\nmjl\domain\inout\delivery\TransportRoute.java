package com.hxdi.nmjl.domain.inout.delivery;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 运输轨迹表实体类
 *
 * <AUTHOR>
 * @since 2025-04-23 11:18:48
 */
@Getter
@Setter
@TableName("B_TRANSPORT_ROUTE")
@ApiModel(description = "运输轨迹表")
public class TransportRoute implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 配送单ID
     */
    @TableField(value = "DELIVERY_ID")
    @ApiModelProperty(value = "配送单ID")
    private String deliveryId;
    /**
     * 经度
     */
    @TableField(value = "LON")
    @ApiModelProperty(value = "经度")
    private String lon;
    /**
     * 纬度
     */
    @TableField(value = "LAT")
    @ApiModelProperty(value = "纬度")
    private String lat;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;


}

