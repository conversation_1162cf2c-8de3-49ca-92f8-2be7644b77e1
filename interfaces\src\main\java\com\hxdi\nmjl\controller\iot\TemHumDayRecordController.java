package com.hxdi.nmjl.controller.iot;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.iot.TemHumDayRecordCondition;
import com.hxdi.nmjl.domain.iot.TemHumDayRecord;
import com.hxdi.nmjl.service.iot.TemHumDayRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 温湿度数据统计表控制层
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "温湿度数据统计")
@RequestMapping("/temHumStatistic")
public class TemHumDayRecordController extends BaseController<TemHumDayRecordService, TemHumDayRecord> {

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/get")
    public TemHumDayRecord selectOne(Integer id) {
        return bizService.getById(id);
    }


    @GetMapping("/list")
    @ApiOperation("根据条件查询列表")
    public ResultBody<List<TemHumDayRecord>> list(TemHumDayRecordCondition condition) {
        return ResultBody.<List<TemHumDayRecord>>OK().data(bizService.listV1(condition));
    }

    @GetMapping("/page")
    @ApiOperation("根据条件查询分页列表")
    public ResultBody<Page<TemHumDayRecord>> page(TemHumDayRecordCondition condition) {
        return ResultBody.<Page<TemHumDayRecord>>OK().data(bizService.pageV1(condition));
    }

    @PostMapping("/generateDayRecord")
    @ApiOperation("生成日统计记录(定时任务接口，请勿直接调用)")
    public void generateDayRecord() {
        bizService.generateDayRecord();
    }
}
