package com.hxdi.nmjl.controller.quality;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.quality.QualityInspectionCondition;
import com.hxdi.nmjl.domain.quality.QualityInspection;
import com.hxdi.nmjl.service.quality.QualityInspectionService;
import com.hxdi.nmjl.vo.quality.QualityInspectionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 质检检验信息控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/qualityInspection")
@Api(tags = "质检检验信息")
public class QualityInspectionController extends BaseController<QualityInspectionService, QualityInspection> {

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存/更新检验结果")
    public ResultBody saveOrUpdate(@RequestBody QualityInspectionVO result) {
        bizService.SaveOrUpdate(result);
        return ResultBody.ok();
    }


    @GetMapping("/get")
    @ApiOperation(value = "根据Id查询检验结果")
    public ResultBody<QualityInspection> selectOne(String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "根据Id查询检验结果及明细数据")
    public ResultBody<QualityInspectionVO> getDetail(String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @GetMapping("/list")
    @ApiOperation(value = "查询质检结果列表")
    public ResultBody<List<QualityInspection>> getList(QualityInspectionCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @GetMapping("/page")
    @ApiOperation(value = "分页查询质检结果列表")
    public ResultBody<Page<QualityInspection>> getPage(QualityInspectionCondition condition) {
        return ResultBody.ok().data(bizService.getPage(condition));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除质检结果")
    public ResultBody delete(@RequestParam("id") String id) {
        bizService.removeid(id);
        return ResultBody.ok();
    }

}
