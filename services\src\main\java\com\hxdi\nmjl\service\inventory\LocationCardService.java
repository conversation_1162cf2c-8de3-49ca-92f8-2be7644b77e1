package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.LocationCard;
import com.hxdi.nmjl.condition.inventory.LocationCardCondition;
import com.hxdi.nmjl.vo.inventory.LocationCardDetailResVO;

import java.io.Serializable;
import java.util.List;

public interface LocationCardService extends IBaseService<LocationCard> {


    /**
     * 创建或更新货位卡(存在则更新)
     * @param locationCard
     */
    void createOrUpdate(LocationCard locationCard);

    List<LocationCard> getList(LocationCardCondition condition);

    Page<LocationCard> getPage(LocationCardCondition condition);

    /**
     * 根据货位ID获取货位卡信息
     * @param locId
     * @return
     */
    LocationCard getDetailByLocId(String locId);

    List<LocationCard> getByInventoryIdList(List<String> inventoryId);

    /**
     * 根据货位卡ID获取货位卡信息（包含库存信息、库存变更记录等 ）
     * @param id 货位卡ID
     * @return
     */
    LocationCardDetailResVO getDetail(Serializable id);
}
