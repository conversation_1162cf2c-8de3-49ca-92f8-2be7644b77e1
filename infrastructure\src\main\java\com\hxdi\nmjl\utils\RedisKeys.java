package com.hxdi.nmjl.utils;

/**
 * @program: nmjl-service
 * @description: redis 键枚举工具类
 * @author: 王贝强
 * @create: 2025-03-25 14:07
 */
public enum RedisKeys {
    //*********************<<<基础数据缓存>>>*******************

    /**
     * 品种目录
     */
    CATALOG(Prefix.CATALOG),

    /**
     * 粮油品类
     */
    CLASSIFICATION(Prefix.CLASSIFICATION),

    /**
     * 组织机构：管理单位与军供站
     */
    ORGANIZATION(Prefix.ORGANIZATION),

    /**
     * 仓房/油罐
     */
    STORE_HOUSE(Prefix.STORE_HOUSE),

    /**
     * 货位
     */
    STORE_LOCATION(Prefix.STORE_LOCATION),

    /**
     * 温湿度检测设备
     */
    TEM_HUM_DEVICE(Prefix.TEM_HUM_DEVICE),

    /**
     * 客户信息
     */
    CLIENT_INFO(Prefix.CLIENT_INFO),


    //*********************>>>基础数据缓存<<<*******************

    /**
     * 不匹配键
     */
    NOOP("noop");


    /**
     * 键值
     */
    private final String key;


    RedisKeys(String key) {
        this.key = key;
    }

    /**
     * 获取键值
     * @return
     */
    public String key() {
        return key;
    }


    /**
     * 获取键
     * @param code
     * @return
     */
    public String key(Object code) {
        if(code == null) {
            return key;
        }

        return key + code;
    }


    /**
     * 匹配键
     * @param key
     * @return
     */
    public static RedisKeys match(String key) {
        for (RedisKeys redisKey : RedisKeys.values()) {
            if (redisKey.key().equals(key)) {
                return redisKey;
            }
        }
        return NOOP;
    }



    public interface Prefix {
        String CATALOG = "catalog";
        String CLASSIFICATION = "classification";
        String ORGANIZATION = "organization";
        String STORE_HOUSE = "store_house";
        String STORE_LOCATION = "store_location";
        String TEM_HUM_DEVICE = "tem_hum_device";
        String CLIENT_INFO = "client_info";
    }
}
