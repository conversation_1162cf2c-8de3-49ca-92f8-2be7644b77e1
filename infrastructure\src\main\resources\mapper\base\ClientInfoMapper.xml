<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.ClientInfoMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.ClientInfo">
    <!--@mbg.generated-->
    <!--@Table B_CLIENT_INFO-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="CLIENT_TYPE" jdbcType="VARCHAR" property="clientType" />
    <result column="COMPANY_TYPE" jdbcType="VARCHAR" property="companyType" />
    <result column="ORIGIN" jdbcType="INTEGER" property="origin" />
    <result column="REF_ID" jdbcType="VARCHAR" property="refId" />
    <result column="PROVINCE_ID" jdbcType="VARCHAR" property="provinceId" />
    <result column="PROVINCE" jdbcType="VARCHAR" property="province" />
    <result column="CITY_ID" jdbcType="VARCHAR" property="cityId" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="COUNTY_ID" jdbcType="VARCHAR" property="countyId" />
    <result column="COUNTY" jdbcType="VARCHAR" property="county" />
    <result column="DETAIL_ADDR" jdbcType="VARCHAR" property="detailAddr" />
    <result column="CREDIT_CODE" jdbcType="VARCHAR" property="creditCode" />
    <result column="REGISTER_TYPE" jdbcType="CHAR" property="registerType" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="LEGAL" jdbcType="VARCHAR" property="legal" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="FAX" jdbcType="VARCHAR" property="fax" />
    <result column="MAIL" jdbcType="VARCHAR" property="mail" />
    <result column="POST_CODE" jdbcType="VARCHAR" property="postCode" />
    <result column="BANK" jdbcType="VARCHAR" property="bank" />
    <result column="ACCOUNT" jdbcType="VARCHAR" property="account" />
    <result column="BANK_CREDIT_LEVEL" jdbcType="CHAR" property="bankCreditLevel" />
    <result column="FIXED_ASSETS" jdbcType="VARCHAR" property="fixedAssets" />
    <result column="REGISTERED_CAPITAL" jdbcType="DECIMAL" property="registeredCapital" />
    <result column="EMPLOYMENTS" jdbcType="INTEGER" property="employments" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="GRADE" jdbcType="INTEGER" property="grade" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, NAME, CLIENT_TYPE, COMPANY_TYPE, ORIGIN, REF_ID, PROVINCE_ID, PROVINCE, CITY_ID,
    CITY, COUNTY_ID, COUNTY, DETAIL_ADDR, CREDIT_CODE, REGISTER_TYPE, BUSINESS_NO, LEGAL,
    PHONE, FAX, MAIL, POST_CODE, BANK, ACCOUNT, BANK_CREDIT_LEVEL, FIXED_ASSETS, REGISTERED_CAPITAL,
    EMPLOYMENTS, REMARKS, GRADE, ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID,
    TENANT_ID, DATA_HIERARCHY_ID
  </sql>

  <select id="selectListV1" resultMap="BaseResultMap">
    select c.*
    from B_CLIENT_INFO c
    inner join B_CLIENT_RELATION r on c.ID = r.CLIENT_ID
    <where>
      <if test="client.name != null and client.name != '' ">
        and c.NAME like concat('%',#{client.name},'%')
      </if>
      <if test="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(client.clientType) !=null">
        AND c.CLIENT_TYPE IN
        <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(client.clientType)" item="type" open="(" separator="," close=")">
          #{type}
        </foreach>
      </if>
      <if test="client.companyType != null and client.companyType != '' ">
        and c.COMPANY_TYPE = #{client.companyType}
      </if>
      <if test="client.provinceId != null and client.provinceId != '' ">
        and c.PROVINCE_ID = #{client.provinceId}
      </if>
      <if test="client.cityId != null and client.cityId != '' ">
        and c.CITY_ID = #{client.cityId}
      </if>
      <if test="client.countyId != null and client.countyId != '' ">
        and c.COUNTY_ID = #{client.countyId}
      </if>
      and c.ENABLED=1
    </where>
  </select>

  <select id="selectListV2" resultMap="BaseResultMap">
    select c.*
    from B_CLIENT_INFO c
    <where>
      <if test="client.name != null and client.name != '' ">
        and c.NAME like concat('%',#{client.name},'%')
      </if>
      <if test="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(client.clientType) !=null">
        AND c.CLIENT_TYPE IN
        <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(client.clientType)" item="type" open="(" separator="," close=")">
          #{type}
        </foreach>
      </if>
      <if test="client.companyType != null and client.companyType != '' ">
        and c.COMPANY_TYPE = #{client.companyType}
      </if>
      <if test="client.provinceId != null and client.provinceId != '' ">
        and c.PROVINCE_ID = #{client.provinceId}
      </if>
      <if test="client.cityId != null and client.cityId != '' ">
        and c.CITY_ID = #{client.cityId}
      </if>
      <if test="client.countyId != null and client.countyId != '' ">
        and c.COUNTY_ID = #{client.countyId}
      </if>
      and c.ENABLED=1
    </where>
  </select>

  <select id="selectPageV1" resultMap="BaseResultMap">
    select c.*
    from B_CLIENT_INFO c
    inner join B_CLIENT_RELATION r on c.ID = r.CLIENT_ID
    <where>
      <if test="client.name != null and client.name != '' ">
        and c.NAME like concat('%',#{client.name},'%')
      </if>
      <if test="client.clientType != null and client.clientType != '' ">
        and c.CLIENT_TYPE = #{client.clientType}
      </if>
      <if test="client.companyType != null and client.companyType != '' ">
        and c.COMPANY_TYPE = #{client.companyType}
      </if>
      <if test="client.provinceId != null and client.provinceId != '' ">
        and c.PROVINCE_ID = #{client.provinceId}
      </if>
      <if test="client.cityId != null and client.cityId != '' ">
        and c.CITY_ID = #{client.cityId}
      </if>
      <if test="client.countyId != null and client.countyId != '' ">
        and c.COUNTY_ID = #{client.countyId}
      </if>
       and c.ENABLED = 1
    </where>
    order by CREATE_TIME DESC
  </select>

  <update id="deleteSoft">
    update B_CLIENT_INFO set ENABLED = 7, update_time = sysdate, update_id = #{userId} where id = #{clientId}
  </update>

  <select id="selectPageV2" resultMap="BaseResultMap">
    select c.*
    from B_CLIENT_INFO c
    <where>
      <if test="client.name != null and client.name != '' ">
        and c.NAME like concat('%',#{client.name},'%')
      </if>
      <if test="client.clientType != null and client.clientType != '' ">
        and c.CLIENT_TYPE = #{client.clientType}
      </if>
      <if test="client.companyType != null and client.companyType != '' ">
        and c.COMPANY_TYPE = #{client.companyType}
      </if>
      <if test="client.provinceId != null and client.provinceId != '' ">
        and c.PROVINCE_ID = #{client.provinceId}
      </if>
      <if test="client.cityId != null and client.cityId != '' ">
        and c.CITY_ID = #{client.cityId}
      </if>
      <if test="client.countyId != null and client.countyId != '' ">
        and c.COUNTY_ID = #{client.countyId}
      </if>
      and c.ENABLED = 1
    </where>
    order by CREATE_TIME DESC
  </select>

  <select id="searchList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from B_CLIENT_INFO
    <where>
      ENABLED=1
      <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
        and (NAME like concat('%',#{condition.keywords},'%') or CREDIT_CODE like concat('%',#{condition.keywords},'%'))
      </if>
    </where>
    order by CREATE_TIME DESC
    limit 10
  </select>
</mapper>
