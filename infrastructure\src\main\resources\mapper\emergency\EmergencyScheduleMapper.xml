<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyScheduleMapper">

    <sql id="Base_Column_List">
        ID, SCHEDULE_NO, EVENT_CODE, EVENT_NAME, SCHEDULE_DATE, PROVINCE_CODE, CITY_CODE,
    COUNTY_CODE, AREA, DETAIL_ADDR, START_TIME, END_TIME, UNIT_TYPE, UNIT_ID, UNIT_NAME,
    UNIT_CONTACTS, UNIT_CONTACTS_TEL, UNIT_ADDR, UNIT_DETAIL_ADDR, RCV_ORG_ID, RCV_ORG_NAME,
    PRINCIPAL, MOBILE, SCHEDULE_STATE, ENABLED, ATTACHMENT, CREATE_TIME, UPDATE_TIME,
    CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencySchedule">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="SCHEDULE_NO" jdbcType="VARCHAR" property="scheduleNo"/>
        <result column="EVENT_CODE" jdbcType="VARCHAR" property="eventCode"/>
        <result column="EVENT_NAME" jdbcType="VARCHAR" property="eventName"/>
        <result column="SCHEDULE_DATE" jdbcType="TIMESTAMP" property="scheduleDate"/>
        <result column="PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode"/>
        <result column="COUNTY_CODE" jdbcType="VARCHAR" property="countyCode"/>
        <result column="AREA" jdbcType="VARCHAR" property="area"/>
        <result column="DETAIL_ADDR" jdbcType="VARCHAR" property="detailAddr"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="UNIT_TYPE" jdbcType="INTEGER" property="unitType"/>
        <result column="UNIT_ID" jdbcType="VARCHAR" property="unitId"/>
        <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName"/>
        <result column="UNIT_CONTACTS" jdbcType="VARCHAR" property="unitContacts"/>
        <result column="UNIT_CONTACTS_TEL" jdbcType="VARCHAR" property="unitContactsTel"/>
        <result column="UNIT_ADDR" jdbcType="VARCHAR" property="unitAddr"/>
        <result column="UNIT_DETAIL_ADDR" jdbcType="VARCHAR" property="unitDetailAddr"/>
        <result column="RCV_ORG_ID" jdbcType="VARCHAR" property="rcvOrgId"/>
        <result column="RCV_ORG_NAME" jdbcType="VARCHAR" property="rcvOrgName"/>
        <result column="PRINCIPAL" jdbcType="VARCHAR" property="principal"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="SCHEDULE_STATE" jdbcType="INTEGER" property="scheduleState"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="ATTACHMENT" jdbcType="VARCHAR" property="attachment"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_EMERGENCY_SCHEDULE
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.scheduleNo)">
                AND SCHEDULE_NO LIKE CONCAT('%', #{condition.scheduleNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.eventCode)">
                AND EVENT_CODE LIKE CONCAT('%', #{condition.eventCode}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.eventName)">
                AND EVENT_NAME LIKE CONCAT('%', #{condition.eventName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.unitType)">
                AND UNIT_TYPE = #{condition.unitType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.scheduleState)">
                AND SCHEDULE_STATE = #{condition.scheduleState}
            </if>
        order by CREATE_TIME desc
        </where>
    </select>
    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_EMERGENCY_SCHEDULE
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.scheduleNo)">
                AND SCHEDULE_NO LIKE CONCAT('%', #{condition.scheduleNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.eventCode)">
                AND EVENT_CODE LIKE CONCAT('%', #{condition.eventCode}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.eventName)">
                AND EVENT_NAME LIKE CONCAT('%', #{condition.eventName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.unitType)">
                AND UNIT_TYPE = #{condition.unitType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.scheduleState)">
                AND SCHEDULE_STATE = #{condition.scheduleState}
            </if>
            order by CREATE_TIME desc
        </where>
    </select>
</mapper>
