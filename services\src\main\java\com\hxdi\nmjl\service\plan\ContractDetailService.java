package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.ContractDetail;

import java.util.List;

public interface ContractDetailService extends IBaseService<ContractDetail> {


    /**
     * 查询合同明细
     * @param contractId
     * @return
     */
    List<ContractDetail> getList(String contractId);

    /**
     * 根据合同ID列表查询合同明细
     * @param contractIds
     * @return
     */
    List<ContractDetail> getListByContractIds(List<String> contractIds);

    /**
     * 删除合同明细
     * @param contractId
     */
    void removeByContractId(String contractId);

    /**
     * 根据合同ID、品种ID、质量等级查询合同明细
     * @param contractId
     * @param catalogId
     * @param grade
     * @return
     */
    ContractDetail selectByCatalogIdAndGrade(String contractId, String catalogId, String grade);
}
