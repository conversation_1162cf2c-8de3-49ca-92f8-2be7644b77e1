package com.hxdi.nmjl.service.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionResourceCondition;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionResource;

import java.util.List;

/**

 生产资源服务接口
 <AUTHOR>
 @version 1.0
 @since 2025/8/5
 */
public interface StoreProductionResourceService extends IBaseService<StoreProductionResource> {
    /**
     新增生产资源
     @param resource 生产资源实体
     */
    void create(StoreProductionResource resource);
    /**
     修改生产资源
     @param resource 生产资源实体
     */
    void update(StoreProductionResource resource);
    /**
     获取资源详情
     @param resourceId 资源 ID
     @return 生产资源实体
     */
    StoreProductionResource getDetail(String resourceId);
    /**
     分页查询资源
     @param condition 查询条件
     @return 分页结果
     */
    Page<StoreProductionResource> pages(StoreProductionResourceCondition condition);
    /**
     列表查询资源
     @param condition 查询条件
     @return 资源列表
     */
    List<StoreProductionResource> lists(StoreProductionResourceCondition condition);
    /**
     删除资源
     @param resourceId 资源 ID
     */
    void remove(String resourceId);
}
