package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.utils.CachedBeanCopyUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 库存基础信息，静态不发生变化数据
 */
@ApiModel(description = "库存基础信息，静态不发生变化数据")
@Getter
@Setter
@TableName(value = "B_INVENTORY_BASE")
public class InventoryBase implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 库存ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    @ApiModelProperty(value = "库存ID")
    private String id;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "军供站ID")
    private String storeId;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value = "仓房ID")
    private String stId;

    /**
     * 货位ID
     */
    @TableField(value = "LOC_ID")
    @ApiModelProperty(value = "货位ID")
    private String locId;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATIONS")
    @ApiModelProperty(value = "规格")
    private String specifications;

    /**
     * 质量等级
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value = "质量等级")
    private String grade;

    /**
     * 储备性质
     */
    @TableField(value = "RESERVE_LEVEL")
    @ApiModelProperty(value = "储备性质")
    private Integer reserveLevel;

    /**
     * 省
     */
    @TableField(value = "PROVINCE")
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "CITY")
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @TableField(value = "COUNTY")
    @ApiModelProperty(value = "区")
    private String county;

    /**
     * 生产日期
     */
    @TableField(value = "PRODUCTION_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "生产日期")
    private Date productionDate;

    /**
     * 管理机构id
     */
    @TableField(value = "MANAGE_UNIT_ID")
    @ApiModelProperty(value = "管理机构id")
    private String manageUnitId;

    /**
     * 历史标记：1-是，0-否
     */
    @TableField(value = "HIS_FLG")
    @ApiModelProperty(value = "历史标记：1-是，0-否")
    private Integer hisFlg;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;


    /**********************以下非实体字段**********************/
    @TableField(exist = false)
    private String storeName;
    @TableField(exist = false)
    private String stName;
    @TableField(exist = false)
    private String locName;
    @TableField(exist = false)
    private String manageUnitName;

    /**
     * 转换为库存对象后, 可以补充额外信息如：仓房名称、货位名称等
     *
     * @return
     */
    public Inventory toInventory() {
        return CachedBeanCopyUtils.copy(this, new Inventory());
    }
}
