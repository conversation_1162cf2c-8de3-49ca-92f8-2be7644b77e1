package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.InspectionPlan;

/**
 * 监督检查计划服务接口
 */
public interface InspectionPlanService extends IBaseService<InspectionPlan> {


    /**
     * 取消计划
     * @param id
     */
    void cancel(String id);

    /**
     * 修改计划状态
     * @param code
     * @param state
     */
    void updateState(String code, Integer state);
}
