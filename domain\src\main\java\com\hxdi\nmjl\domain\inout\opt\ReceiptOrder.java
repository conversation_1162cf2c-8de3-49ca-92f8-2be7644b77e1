package com.hxdi.nmjl.domain.inout.opt;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* 领用申请单
* @TableName B_RECEIPT_ORDER
*/
@ApiModel(description = "领用申请单")
@TableName("B_RECEIPT_ORDER")
@Setter
@Getter
public class ReceiptOrder implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("RECEIPT_CODE")
    @ApiModelProperty(value = "领用单号")
    private String receiptCode;

    @TableField("STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @TableField("STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @TableField("APPLICANT")
    @ApiModelProperty(value = "申请人")
    private String applicant;

    @TableField("PLAN_DATE")
    @ApiModelProperty(value = "计划领用日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDate;

    @TableField("REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableField("ATTACHEMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态")
    private Integer enabled;

    @TableField("APPROVE_STATUS")
    @ApiModelProperty(value = "审核状态:0未审核 1已审核 2驳回")
    private Integer approveStatus;

    @TableField("APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;

    @TableField("APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID",fill= FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    /**
     * ------------------非数据库字段-----------------
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "领用申请单详情")
    private List<ReceiptDetail> detailList;

}
