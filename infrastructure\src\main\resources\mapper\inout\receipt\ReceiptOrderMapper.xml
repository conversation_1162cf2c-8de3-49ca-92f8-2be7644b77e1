<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.receipt.ReceiptOrderMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.opt.ReceiptOrder">
            <result column="ID" property="id" />
            <result column="RECEIPT_CODE" property="receiptCode" />
            <result column="STORE_ID" property="storeId" />
            <result column="STORE_NAME" property="storeName" />
            <result column="APPLICANT" property="applicant" />
            <result column="PLAN_DATE" property="planDate" />
            <result column="REMARKS" property="remarks" />
            <result column="ATTACHEMENTS" property="attachments" />
            <result column="ENABLED" property="enabled" />
            <result column="APPROVE_STATUS" property="approveStatus" />
            <result column="APPROVER" property="approver" />
            <result column="APPROVE_TIME" property="approveTime" />
            <result column="CREATE_TIME" property="createTime" />
            <result column="UPDATE_TIME" property="updateTime" />
            <result column="CREATE_ID" property="createId" />
            <result column="UPDATE_ID" property="updateId" />
            <result column="TENANT_ID" property="tenantId" />
            <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
            <result column="APPROVE_OPINION" property="approveOpinion" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,RECEIPT_CODE,STORE_ID,STORE_NAME,APPLICANT,PLAN_DATE,
        REMARKS,ATTACHEMENTS,ENABLED,APPROVE_STATUS,APPROVER,
        APPROVE_TIME,CREATE_TIME,UPDATE_TIME,CREATE_ID,UPDATE_ID,
        TENANT_ID,DATA_HIERARCHY_ID,APPROVE_OPINION
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from B_RECEIPT_ORDER
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
    </select>
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from B_RECEIPT_ORDER
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
    </select>



</mapper>
