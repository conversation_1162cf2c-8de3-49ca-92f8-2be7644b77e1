package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.plan.BrandActivityCondition;
import com.hxdi.nmjl.domain.plan.BrandActivity;
import com.hxdi.nmjl.domain.plan.BrandActivityPerform;
import com.hxdi.nmjl.domain.plan.BrandInfo;
import com.hxdi.nmjl.mapper.plan.BrandActivityMapper;
import com.hxdi.nmjl.service.plan.BrandActivityPerformService;
import com.hxdi.nmjl.service.plan.BrandActivityService;
import com.hxdi.nmjl.service.plan.BrandInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class BrandActivityServiceImpl extends BaseServiceImpl<BrandActivityMapper, BrandActivity> implements BrandActivityService {

    @Resource
    private BrandActivityPerformService brandActivityPerformService;

    @Resource
    private BrandInfoService brandInfoService;

    @Override
    public void create(BrandActivity activity) {
        // 设置创建信息
        BaseUserDetails user = SecurityHelper.obtainUser();
        activity.setTenantId(user.getTenantId());
        activity.setCompletedRate(0);
        BrandInfo info = brandInfoService.getById(activity.getBrandId());
        activity.setBrandName(info.getBrandName());
        activity.setOrgId(info.getOrgId());
        activity.setOrgName(info.getOrgName());
        this.save(activity);

        for (BrandActivityPerform perform : activity.getPerformList()) {
            perform.setActivityId(activity.getId());
        }
        brandActivityPerformService.insertBatch(activity.getPerformList());
    }

    @Override
    public void update(BrandActivity activity) {
        // 查询原活动信息
        BrandActivity savedActivity = baseMapper.selectById(activity.getId());
        if (savedActivity == null) {
            throw new BaseException("品牌传播活动不存在");
        }
        // 校验状态：已审核的活动不允许修改
        if (savedActivity.getApproveStatus() == 1) {
            throw new BaseException("已审核的活动不允许修改");
        }
        //更新活动信息
        this.updateById(activity);
        //更新活动执行信息
        for (BrandActivityPerform perform : activity.getPerformList()) {
            perform.setActivityId(activity.getId());
            brandActivityPerformService.remove(perform.getId());
        }
        brandActivityPerformService.saveBatch(activity.getPerformList());
    }

    @Override
    public BrandActivity getDetail(String activityId) {
        BrandActivity activity = baseMapper.selectById(activityId);
        if (activity == null) {
            throw new BaseException("品牌传播活动不存在");
        }
        activity.setPerformList(brandActivityPerformService.getList(activityId));
        return activity;
    }

    @Override
    public Page<BrandActivity> pages(BrandActivityCondition condition) {
        Page<BrandActivity> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<BrandActivity> lists(BrandActivityCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void approve(String activityId, Integer approveStatus, String opinion) {
        BrandActivity activity = baseMapper.selectById(activityId);
        if (activity.getEnabled() == 0) {
            throw new BaseException("品牌传播活动不存在");
        }
        // 校验状态：已审核的不能重复审核
        if (activity.getApproveStatus() != 0) {
            throw new BaseException("该活动已审核，无法重复操作");
        }
        BaseUserDetails user = SecurityHelper.obtainUser();
        activity.setApproveStatus(approveStatus);
        activity.setApproveOpinion(opinion);
        activity.setApprover(user.getNickName());
        activity.setApproveTime(new Date());

        this.updateById(activity);
    }

    @Override
    public void remove(String activityId) {
        BrandActivity activity = baseMapper.selectById(activityId);
        if (activity == null) {
            throw new BaseException("品牌传播活动不存在");
        }
        // 校验状态：已审核的活动不能删除
        if (activity.getApproveStatus() == 1) {
            throw new BaseException("已审核的活动不能删除");
        }
        // 逻辑删除（更新状态为0）
        activity.setEnabled(0);
        baseMapper.updateById(activity);
    }


    @Override
    public void updateCompletedRate(String activityId, Integer rate) {
        if (rate < 0 || rate > 100) {
            throw new BaseException("目标达成率必须在0-100之间");
        }
        BrandActivity activity = baseMapper.selectById(activityId);
        if (activity == null) {
            throw new BaseException("品牌传播活动不存在");
        }
        if (activity.getApproveStatus() != 1) {
            throw new BaseException("品牌传播活动未通过审核");
        }
        activity.setCompletedRate(rate);
        this.updateById(activity);
    }

    @Override
    public void submit(String activityId) {
        BrandActivity brandActivity = baseMapper.selectById(activityId);
        brandActivity.setApproveStatus(0);
        this.updateById(brandActivity);
    }
}
