<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
    <property scope="context" resource="app.properties"/>
    <property scope="system" name="LOG_LEVEL" value="${log.level:error}"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%15thread] %highlight(%5level) %cyan(%-60.60logger{60}) %file:%line : %highlight(%msg) %n</pattern>
        </encoder>
    </appender>

    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/startup.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM}/history.out.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%15thread] %5level [%-65.65logger{65}] %file:%line : %msg %n</pattern>
        </encoder>
        <!--<filter class="ch.qos.logback.classic.filter.LevelFilter">-->
        <!--<level>info</level>-->
        <!--<onMatch>ACCEPT</onMatch>-->
        <!--<onMismatch>DENY</onMismatch>-->
        <!--</filter>-->
    </appender>

    <!--开发环境:打印控制台-->
    <springProfile name="local,dev">
        <logger name="com.hxdi" level="${LOG_LEVEL}"/>
        <logger name="com.hxdi.database.interceptor" level="info"/>
        <root level="info">
            <appender-ref ref="console"/>
        </root>
    </springProfile>

    <!--测试生产环境:打印控制台-->
    <springProfile name="dev,test,uat,release">
        <logger name="com.hxdi" level="${LOG_LEVEL}"/>
        <logger name="com.hxdi.database.interceptor" level="info"/>
        <root level="info">
            <appender-ref ref="file"/>
        </root>
    </springProfile>
</configuration>
