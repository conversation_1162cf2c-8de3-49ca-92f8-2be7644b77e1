package com.hxdi.nmjl.mapper.storeproduction;

import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlanDetail;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionPlanDetailCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产计划明细表Mapper接口
 */
public interface StoreProductionPlanDetailMapper extends SuperMapper<StoreProductionPlanDetail> {

    List<StoreProductionPlanDetail> selectListV1(@Param("condition") StoreProductionPlanDetailCondition condition);
}