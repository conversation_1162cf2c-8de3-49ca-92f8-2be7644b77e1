package com.hxdi.nmjl.service.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.mobilization.MobilizedProduct;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCondition;

import java.util.List;

/**
 * 动员商品服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/02 10:30
 */
public interface MobilizedProductService extends IBaseService<MobilizedProduct> {

    /**
     * 分页查询
     * @param condition 查询条件
     * @return
     */
    Page<MobilizedProduct> pages(MobilizedProductCondition condition);

    /**
     * 列表查询
     * @param condition 列表查询条件
     * @return
     */
    List<MobilizedProduct> lists(MobilizedProductCondition condition);



    /**
     * 新增
     * @param mobilizedProduct
     */
    void create(MobilizedProduct mobilizedProduct);

    /**
     * 修改
     * @param mobilizedProduct
     */
    void update(MobilizedProduct mobilizedProduct);

    /**
     * 删除
     * @param goodsId
     */
    void remove(String goodsId);

    /**
     * 查看详情
     * @param goodsId
     * @return
     */
    MobilizedProduct getDetail(String goodsId);

    /**
     * 确认
     * @param id
     * @return
     */
    void approve(String id);
}
