package com.hxdi.nmjl.mapper.inout;

import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.InoutItem;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_INOUT_ITEM】的数据库操作Mapper
* @createDate 2025-04-08 16:11:46
* @Entity com.hxdi.nmjl.domain.inventory.BInoutItem
*/
@Mapper
public interface InoutItemMapper extends SuperMapper<InoutItem> {

    /**
     * 查询统计信息
     * @param mainId
     * @return
     */
    List<InoutItem> selectStatInfo(String mainId);
}




