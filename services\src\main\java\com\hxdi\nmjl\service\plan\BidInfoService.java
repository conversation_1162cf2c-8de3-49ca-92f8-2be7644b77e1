package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.plan.BidInfoCondition;
import com.hxdi.nmjl.domain.plan.BidInfo;

import java.util.List;

/**
 * 招标信息服务接口
 */
public interface BidInfoService extends IBaseService<BidInfo> {

    /**
     * 查询招标详情
     *
     * @param bidId 招标ID
     * @return BidInfo
     */
    BidInfo getDetail(String bidId);

    /**
     * 分页查询招标信息
     *
     * @param condition 查询条件
     * @return Page<BidInfo>
     */
    Page<BidInfo> getPages(BidInfoCondition condition);

    /**
     * 列表查询招标信息
     *
     * @param condition 查询条件
     * @return List<BidInfo>
     */
    List<BidInfo> getList(BidInfoCondition condition);

    /**
     * 新增招标信息
     *
     * @param bidInfo 招标信息
     * @return boolean
     */
    boolean add(BidInfo bidInfo);

    /**
     * 根据选择的筹措计划查询已招标的标的物（品类+质量等级）
     *
     * @param planId
     * @return
     */
    List<String> getExistingBidObject(String planId);

    /**
     * 更新招标信息
     *
     * @param bidInfo 招标信息
     * @return boolean
     */
    void updateV1(BidInfo bidInfo);

    /**
     * 删除招标信息
     *
     * @param bidId 招标ID
     * @return boolean
     */
    void deleteV1(String bidId);

    /**
     * 修改招标流程状态
     *
     * @param id
     * @param flowState
     */
    void changeFlowState(String id, Integer flowState);

    /**
     * 审核
     *
     * @param id             调整ID
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id             调整ID
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     *
     * @param id 调整ID
     */
    void submit(String id);

    /**
     * 中标
     *
     * @param id 招标ID
     */
    void winBid(String id);

    /**
     * 流标
     *
     * @param id 调整ID
     */
    void flow(String id);

    /**
     * 判断是否存在关联的招标信息
     *
     * @param id 筹措计划ID
     * @return boolean
     */
    boolean existsBidForDetail(String id);
}
