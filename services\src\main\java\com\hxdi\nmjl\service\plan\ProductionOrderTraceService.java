package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.ProductionOrderTrace;

import java.util.List;

public interface ProductionOrderTraceService extends IBaseService<ProductionOrderTrace> {

    /**
     * 根据订单详情生成对应的订单跟踪记录
     * @param traceList 订单跟踪记录列表
     */
    void generateTrace(List<ProductionOrderTrace> traceList);

    /**
     * 根据订单ID获取订单跟踪记录列表
     * @param orderId 订单ID
     * @return 订单跟踪记录列表
     */
    List<ProductionOrderTrace> getTraceList(String orderId);
}
