package com.hxdi.nmjl.mapper.base;

import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.clientrelated.ClientRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ClientRelationMapper extends SuperMapper<ClientRelation> {

    /**
     * 根据客户id和单位id删除对应单条关系
     * @param clientId
     * @param oid
     */
    void deleteByClientIdAndOrgId(@Param("cid") String clientId, @Param("oid") String oid);

    /**
     * 根据客户id删除对应的所有关系
     * @param clientId
     */
    void deleteAll(@Param("cid") String clientId);
}
