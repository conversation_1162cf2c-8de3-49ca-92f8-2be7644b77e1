package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.ProductionSupplier;
import com.hxdi.nmjl.condition.clientrelated.SupplierCondition;

import java.util.List;

/**
 * 供应商管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
public interface SupplierService extends IBaseService<ProductionSupplier> {

    /**
     * 保存并更新
     *
     * @param supplier 供应商(包含供应商信用、供应商产品)
     */
    void saveOrUpdateV1(ProductionSupplier supplier);

    /**
     * 修改状态：启用，禁用，删除
     *
     * @param id      供应商ID
     * @param enabled 状态：0-禁用，1-启用，2-删除
     */
    void changeState(String id, Integer enabled);


    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 供应商分页
     */
    Page<ProductionSupplier> pages(SupplierCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 供应商列表
     */
    List<ProductionSupplier> lists(SupplierCondition condition);

    ProductionSupplier getDetail(String id);

    /**
     * 更新供应商综合评分
     * @param supplier 供应商
     */
    void updateAvgScore(ProductionSupplier supplier);

    /**
     * 审核（状态变更）
     *
     * @param planId        计划ID
     * @param approveStatus 审核状态
     * @param opinion       审核意见
     */
    void approve(String planId, Integer approveStatus, String opinion);
}
