package com.hxdi.nmjl.service.plan;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.plan.ContractCondition;
import com.hxdi.nmjl.domain.plan.ContractInfo;
import com.hxdi.nmjl.domain.plan.ProductionOrderTrace;
import com.hxdi.nmjl.domain.plan.SaleOrderItem;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ContractInfoService extends IBaseService<ContractInfo> {

    /**
     * 创建
     *
     * @param contract
     */
    void create(ContractInfo contract);

    void updateProcessV1(List<SaleOrderItem> itemList);

    /**
     * 更新合同进度
     */
    void updateProcess(List<ProductionOrderTrace> traceList);

    /**
     * 更新
     *
     * @param contract
     */
    void updating(ContractInfo contract);

    /**
     * 删除
     *
     * @param id
     */
    void remove(String id);

    /**
     * 提交
     *
     * @param id
     */
    void submit(String id);

    /**
     * 审核通过
     *
     * @param id
     * @param approveOpinion
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id
     * @param approveOpinion
     */
    void reject(String id, String approveOpinion);

    /**
     * 查询详情
     *
     * @param contractId
     * @return
     */
    ContractInfo getDetail(String contractId);

    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    Page<ContractInfo> pages(ContractCondition condition);

    /**
     * 列表查询
     *
     * @param condition
     * @return
     */
    List<ContractInfo> lists(ContractCondition condition);

    /**
     * 列表查询(用于质量管理：下级库点查询时，显示上级单位的合同；其他情况，走默认数据权限)
     *
     * @param condition
     * @return
     */
    List<ContractInfo> listsV2(ContractCondition condition);

    /**
     * 列表查询(包含是否结算字段)
     *
     * @param condition
     * @return
     */
    List<ContractInfo> listsV3(ContractCondition condition);


    /**
     * 分页查询未完成的合同信息
     *
     * @param condition
     * @return
     */
    Page<ContractInfo> getUncompletedPageList(ContractCondition condition);

    /**
     * 查询所有有效合同中对应标的完成数量
     *
     * @param bidId 招标ID
     * @return <品类ID, 完成数量>
     */
    Map<String, BigDecimal> getActiveContractTargetCompletion(String bidId);


    /**
     * 根据合同id查询关联计划中的库点信息（如果关联计划为父计划，则查询子计划对应的所有库点信息）
     *
     * @param contractId 合同ID
     * @return <库点ID, 库点名称>
     */
    Map<String, String> getStoreHouseList(String contractId);

    /**
     * 查询合同商品价格
     *
     * @param type      1-采购合同，2-销售合同
     * @param orderId   订单ID
     * @param catalogId 品种ID
     * @param Grade     质量等级
     * @return
     */
    BigDecimal findPrice(int type, String orderId, String catalogId, String Grade);
}
