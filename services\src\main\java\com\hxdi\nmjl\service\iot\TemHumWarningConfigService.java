package com.hxdi.nmjl.service.iot;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.iot.TemHumWarningConfigCondition;
import com.hxdi.nmjl.domain.iot.TemHumRecord;
import com.hxdi.nmjl.domain.iot.TemHumWarningConfig;

import java.util.List;

public interface TemHumWarningConfigService extends IBaseService<TemHumWarningConfig> {

    /**
     * 保存或更新
     *
     * @param config
     */
    void saveOrUpdateV1(TemHumWarningConfig config);

    /**
     * 列表查询
     *
     * @param condition
     * @return
     */
    List<TemHumWarningConfig> listV1(TemHumWarningConfigCondition condition);

    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    IPage<TemHumWarningConfig> pageV1(TemHumWarningConfigCondition condition);

    /**
     * 生成预警信息(在设备上报数据时自动触发（单仓房）)
     * @param stId 仓房ID
     * @param recordList 温湿度检测记录列表
     */
    void generateWarningMsg(String stId, List<TemHumRecord> recordList);
}
