package com.hxdi.nmjl.controller.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.OptCondition;
import com.hxdi.nmjl.domain.inout.duty.DutyPlan;
import com.hxdi.nmjl.domain.inout.opt.OptInfo;
import com.hxdi.nmjl.service.inout.opt.OptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/opt")
@Api(tags = "作业管理接口")
@Validated
public class OptController extends BaseController<OptService, OptInfo> {

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody OptInfo optInfo) {
        if (CommonUtils.isEmpty(optInfo.getId())) {
            bizService.create(optInfo);
        } else {
            bizService.updating(optInfo);
        }
        return ResultBody.ok();
    }


    @ApiOperation(value = "查询")
    @GetMapping("/query")
    public ResultBody<OptInfo> query(@RequestParam("optId") String optId) {
        return ResultBody.ok().data(bizService.detail(optId));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam("optId") String optId) {
        bizService.delete(optId);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<OptInfo>> page(OptCondition optCondition) {
        return ResultBody.ok().data(bizService.getPage(optCondition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<DutyPlan>> list(OptCondition optCondition) {
        return ResultBody.ok().data(bizService.getList(optCondition));
    }
}
