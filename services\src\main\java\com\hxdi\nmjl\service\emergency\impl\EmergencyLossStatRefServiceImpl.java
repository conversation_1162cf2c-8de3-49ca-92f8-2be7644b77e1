package com.hxdi.nmjl.service.emergency.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStat;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStatRef;
import com.hxdi.nmjl.mapper.emergency.EmergencyLossStatMapper;
import com.hxdi.nmjl.mapper.emergency.EmergencyLossStatRefMapper;
import com.hxdi.nmjl.service.emergency.EmergencyLossStatRefService;
import com.hxdi.nmjl.service.emergency.EmergencyLossStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <应急损耗统计关联服务实现>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:49
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class EmergencyLossStatRefServiceImpl extends BaseServiceImpl<EmergencyLossStatRefMapper, EmergencyLossStatRef> implements EmergencyLossStatRefService {


    @Override
    public void save(String statId, String lossId) {
        EmergencyLossStatRef entity = new EmergencyLossStatRef();
        entity.setLossId(lossId);
        entity.setStatId(statId);
        this.save(entity);
    }
}
