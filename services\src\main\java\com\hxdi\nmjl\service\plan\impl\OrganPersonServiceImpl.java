package com.hxdi.nmjl.service.plan.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.plan.OrganPerson;
import com.hxdi.nmjl.mapper.plan.OrganPersonMapper;
import com.hxdi.nmjl.service.plan.OrganPersonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class OrganPersonServiceImpl extends BaseServiceImpl<OrganPersonMapper, OrganPerson> implements OrganPersonService {


}
