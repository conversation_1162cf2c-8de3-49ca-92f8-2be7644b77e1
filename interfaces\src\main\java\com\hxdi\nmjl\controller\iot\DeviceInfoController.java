package com.hxdi.nmjl.controller.iot;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.iot.DeviceInfo;
import com.hxdi.nmjl.service.iot.DeviceInfoService;
import com.hxdi.nmjl.condition.iot.DeviceInfoCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 温湿度检测设备控制器
*
* <AUTHOR>
*/
@RestController
@Api(tags = "温湿度检测设备管理")
@RequestMapping("/device")
public class DeviceInfoController extends BaseController<DeviceInfoService , DeviceInfo> {

    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<DeviceInfo> selectOne(String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

    @ApiOperation(value = "删除")
    @GetMapping("/remove")
    public ResultBody remove(String id){
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setId(id);
        deviceInfo.setEnabled(0);
        bizService.updateById(deviceInfo);
        return ResultBody.ok();
    }

    @ApiOperation(value = "变更设备状态：字典SBZT")
    @PostMapping("/changeDeviceState")
    public ResultBody changeDeviceState(@RequestBody DeviceInfo deviceInfo) {
        bizService.changeDeviceState(deviceInfo.getId(),deviceInfo.getSerial(),deviceInfo.getDeviceState());
        return ResultBody.ok();
    }

    @ApiOperation(value = "新增或更新")
    @PostMapping("/insertOrUpdate")
    public ResultBody insertOrUpdate(@RequestBody DeviceInfo deviceInfo) {
        bizService.insertOrUpdate(deviceInfo);
        return ResultBody.ok();
    }

    @ApiOperation(value = "根据条件查询设备列表（带用户权限控制）")
    @GetMapping("/getList")
    public ResultBody<List<DeviceInfo>> getList(DeviceInfoCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation(value = "根据条件查询设备列表分页（带用户权限控制）")
    @GetMapping("/getPage")
    public ResultBody<List<DeviceInfo>> getPage(DeviceInfoCondition condition) {
        return ResultBody.ok().data(bizService.getPage(condition));
    }

}
