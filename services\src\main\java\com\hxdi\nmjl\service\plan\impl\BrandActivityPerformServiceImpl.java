package com.hxdi.nmjl.service.plan.impl;

import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.plan.BrandActivity;
import com.hxdi.nmjl.domain.plan.BrandActivityPerform;
import com.hxdi.nmjl.mapper.plan.BrandActivityMapper;
import com.hxdi.nmjl.mapper.plan.BrandActivityPerformMapper;
import com.hxdi.nmjl.service.plan.BrandActivityPerformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class BrandActivityPerformServiceImpl extends BaseServiceImpl<BrandActivityPerformMapper, BrandActivityPerform> implements BrandActivityPerformService {

    @Resource
    private BrandActivityMapper brandActivityMapper;

    @Override
    public List<BrandActivityPerform> getList(String activityId) {
        return baseMapper.selectListV1(activityId);
    }

    @Override
    public void insertBatch(List<BrandActivityPerform> performList) {
        if (performList == null || performList.isEmpty()) {
            return;
        }
        // 校验活动是否存在
        String activityId = performList.get(0).getActivityId();
        BrandActivity activity = brandActivityMapper.selectById(activityId);
        if (activity == null) {
            throw new BaseException("品牌传播活动不存在，无法保存执行记录");
        }
        this.saveBatch(performList);
    }


    @Override
    public void remove(String performId) {
        if (performId == null) {
            throw new BaseException("执行记录ID不能为空");
        }
        BrandActivityPerform perform = baseMapper.selectById(performId);
        if (perform == null) {
            return;
        }
        baseMapper.deleteById(performId);
    }


}