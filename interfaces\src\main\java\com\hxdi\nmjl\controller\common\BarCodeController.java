package com.hxdi.nmjl.controller.common;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.common.BarCode;
import com.hxdi.nmjl.domain.common.BarInfo;
import com.hxdi.nmjl.service.common.BarCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @program: nmjl-service
 * @description: 系统条码控制器
 * @author: 王贝强
 * @create: 2025-08-20 15:53
 */
@RestController
@Api(tags = "系统条码")
@RequestMapping("/barCode")
public class BarCodeController extends BaseController<BarCodeService, BarCode> {

    @ApiOperation("根据二维码获取详情信息")
    @GetMapping("/getDetail")
    public ResultBody<String> getDetail(BarInfo barInfo) {
        return ResultBody.<String>OK().data(bizService.getDetail(barInfo));
    }

    @ApiOperation("根据外链获取详情信息")
    @GetMapping("/getDetailByUrl")
    public ResultBody<String> getDetailByUrl(String openId) {
        return ResultBody.<String>OK().data(bizService.getDetailByUrl(openId));
    }

    @ApiOperation("获取条码字符串(Base64编码)")
    @GetMapping("/getBarCode")
    public ResultBody<String> getBarCode(String businessId, String businessTypeCode) {
        String barCode = bizService.getBarCode(businessId, businessTypeCode);
        return ResultBody.<String>OK().data(barCode);
    }

    @ApiOperation("下载条码图片")
    @GetMapping("/getBarCodeImage")
    public ResultBody<Void> getBarImage(@RequestParam("businessId") String businessId,
                                        @RequestParam("businessTypeCode") String businessTypeCode,
                                        HttpServletRequest request, HttpServletResponse response) {
        bizService.getBarCodeImage(businessId, businessTypeCode, request, response);
        return ResultBody.OK();
    }

    @ApiOperation("获取条码外链URL(Base64编码)")
    @GetMapping("/getBarUrl")
    public ResultBody<String> getBarUrl(String businessId, String businessTypeCode) {
        String barUrl = bizService.getBarUrl(businessId, businessTypeCode);
        return ResultBody.<String>OK().data(barUrl);
    }

    @ApiOperation("下载条码外链URL图片")
    @GetMapping("/getBarUrlImage")
    public ResultBody<Void> getBarUrlImage(@RequestParam("businessId") String businessId,
                                           @RequestParam("businessTypeCode") String businessTypeCode,
                                           HttpServletRequest request,
                                           HttpServletResponse response) {
        bizService.getBarUrlImage(businessId, businessTypeCode, request, response);
        return ResultBody.OK();
    }


}
