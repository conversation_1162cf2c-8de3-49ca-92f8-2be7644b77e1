package com.hxdi.nmjl.domain.inout.duty;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <值班人员信息实体>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21 14:32
 */
@ApiModel(description = "值班人员信息")
@Getter
@Setter
@TableName("B_DUTY_EMP")
public class DutyEmp implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("DUTY_PLAN_ID")
    @ApiModelProperty(value = "计划ID")
    private String dutyPlanId;

    @TableField("PHASE")
    @ApiModelProperty(value = "班次：1-白班，2-夜班")
    private Integer phase;

    @TableField("START_TIME")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private Date startTime;

    @TableField("END_TIME")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private Date endTime;

    @TableField("JOB_TITLE")
    @ApiModelProperty(value = "岗位")
    private String jobTitle;

    @TableField("EMP_NAME")
    @ApiModelProperty(value = "人员姓名")
    private String empName;

    @TableField("MOBILE")
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    @TableField("REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
