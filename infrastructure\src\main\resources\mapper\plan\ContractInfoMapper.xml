<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ContractInfoMapper">
    <resultMap id="ContractInfoResultMap" type="com.hxdi.nmjl.domain.plan.ContractInfo">
        <id property="id" column="ID"/>
        <result property="planId" column="PLAN_ID"/>
        <result property="bidId" column="BID_ID"/>
        <result property="projectName" column="PROJECT_NAME"/>
        <result property="contractCode" column="CONTRACT_CODE"/>
        <result property="originCode" column="ORIGIN_CODE"/>
        <result property="name" column="NAME"/>
        <result property="bizType" column="BIZ_TYPE"/>
        <result property="orgId" column="ORG_ID"/>
        <result property="orgName" column="ORG_NAME"/>
        <result property="clientId" column="CLIENT_ID"/>
        <result property="clientName" column="CLIENT_NAME"/>
        <result property="creditCode" column="CREDIT_CODE"/>
        <result property="signer" column="SIGNER"/>
        <result property="sidCard" column="SID_CARD"/>
        <result property="signedDate" column="SIGNED_DATE"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="contractQty" column="CONTRACT_QTY"/>
        <result property="completedQty" column="COMPLETED_QTY"/>
        <result property="budgetAmount" column="BUDGET_AMOUNT"/>
        <result property="performanceBondAmount" column="PERFORMANCE_BOND_AMOUNT"/>
        <result property="settlementMethod" column="SETTLEMENT_METHOD"/>
        <result property="rcvAddr" column="RCV_ADDR"/>
        <result property="rcvDetailAddr" column="RCV_DETAIL_ADDR"/>
        <result property="orgBankNo" column="ORG_BANK_NO"/>
        <result property="orgBank" column="ORG_BANK"/>
        <result property="clientBankNo" column="CLIENT_BANK_NO"/>
        <result property="clientBank" column="CLIENT_BANK"/>
        <result property="reserveLevel" column="RESERVE_LEVEL"/>
        <result property="state" column="STATE"/>
        <result property="enabled" column="ENABLED"/>
        <result property="attachements" column="ATTACHEMENTS"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="createId" column="CREATE_ID"/>
        <result property="updateId" column="UPDATE_ID"/>
        <result property="tenantId" column="TENANT_ID"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID"/>
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus"/>
        <result column="APPROVER" jdbcType="VARCHAR" property="approver"/>
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime"/>
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        PLAN_ID,
        BID_ID,
        CONTRACT_CODE,
        ORIGIN_CODE,
        NAME,
        BIZ_TYPE,
        ORG_ID,
        ORG_NAME,
        CLIENT_ID,
        CLIENT_NAME,
        CREDIT_CODE,
        SIGNER,
        SID_CARD,
        SIGNED_DATE,
        START_DATE,
        END_DATE,
        CONTRACT_QTY,
        COMPLETED_QTY,
        BUDGET_AMOUNT,
        PERFORMANCE_BOND_AMOUNT,
        SETTLEMENT_METHOD,
        RCV_ADDR,
        RCV_DETAIL_ADDR,
        ORG_BANK_NO,
        ORG_BANK,
        CLIENT_BANK_NO,
        CLIENT_BANK,
        RESERVE_LEVEL,
        STATE,
        ENABLED,
        ATTACHEMENTS,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID,
        PROJECT_NAME,
        APPROVE_STATUS,
        APPROVER,
        APPROVE_TIME,
        APPROVE_OPINION
    </sql>

    <select id="selectPageV1" resultMap="ContractInfoResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_CONTRACT_INFO
        <where>
            enabled = 1
            <if test="condition.planId != null and condition.planId != ''">
                and PLAN_ID = #{condition.planId}
            </if>
            <if test="condition.bidId != null and condition.bidId != ''">
                and BID_ID = #{condition.bidId}
            </if>
            <if test="condition.orgId != null and condition.orgId != ''">
                and ORG_ID = #{condition.orgId}
            </if>
            <if test="condition.clientId != null and condition.clientId != ''">
                and CLIENT_ID = #{condition.clientId}
            </if>
            <if test="condition.state != null">
                and STATE = #{condition.state}
            </if>
            <if test="condition.approveStatus != null">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="condition.bizType != null and condition.bizType != ''">
                and BIZ_TYPE = #{condition.bizType}
            </if>
            <if test="condition.search != null and condition.search != ''">
                and (NAME like concat('%', #{condition.search}, '%') or
                     CONTRACT_CODE like concat('%', #{condition.search}, '%'))
            </if>
            <if test="condition.startTime != null">
                and SIGNED_DATE &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and SIGNED_DATE &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="ContractInfoResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_CONTRACT_INFO
        <where>
            enabled = 1
            <if test="condition.planId != null and condition.planId != ''">
                and PLAN_ID = #{condition.planId}
            </if>
            <if test="condition.bidId != null and condition.bidId != ''">
                and BID_ID = #{condition.bidId}
            </if>
            <if test="condition.orgId != null and condition.orgId != ''">
                and ORG_ID = #{condition.orgId}
            </if>
            <if test="condition.clientId != null and condition.clientId != ''">
                and CLIENT_ID = #{condition.clientId}
            </if>
            <if test="condition.state != null">
                and STATE = #{condition.state}
            </if>
            <if test="condition.approveStatus != null">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="condition.bizType != null and condition.bizType != ''">
                and BIZ_TYPE = #{condition.bizType}
            </if>
            <if test="condition.search != null and condition.search != ''">
                and (NAME like concat('%', #{condition.search}, '%') or
                     CONTRACT_CODE like concat('%', #{condition.search}, '%'))
            </if>
            <if test="condition.startTime != null">
                and SIGNED_DATE &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and SIGNED_DATE &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV2" resultMap="ContractInfoResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_CONTRACT_INFO
        <where>
            enabled = 1
            <if test="condition.planId != null and condition.planId != ''">
                and PLAN_ID = #{condition.planId}
            </if>
            <if test="condition.bidId != null and condition.bidId != ''">
                and BID_ID = #{condition.bidId}
            </if>
            <if test="condition.orgId != null and condition.orgId != ''">
                and ORG_ID = #{condition.orgId}
            </if>
            <if test="condition.clientId != null and condition.clientId != ''">
                and CLIENT_ID = #{condition.clientId}
            </if>
            <if test="condition.state != null">
                and STATE = #{condition.state}
            </if>
            <if test="condition.approveStatus != null">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="condition.bizType != null and condition.bizType != ''">
                and BIZ_TYPE = #{condition.bizType}
            </if>
            <if test="condition.search != null and condition.search != ''">
                and (NAME like concat('%', #{condition.search}, '%') or
                     CONTRACT_CODE like concat('%', #{condition.search}, '%'))
            </if>
            <if test="condition.startTime != null">
                and SIGNED_DATE &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and SIGNED_DATE &lt;= #{condition.endTime}
            </if>
            and DATA_HIERARCHY_ID in
            <foreach collection="condition.dataHierarchyId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectCatalogPrice" resultType="java.math.BigDecimal">
        select t1.PRICE
        from B_CONTRACT_INFO t
                 inner join B_CONTRACT_DETAIL t1 on t.ID = t1.CONTRACT_ID
        where t.CONTRACT_CODE = #{contractCode}
          and t1.CATALOG_ID = #{catalogId}
        limit 1
    </select>
</mapper>
