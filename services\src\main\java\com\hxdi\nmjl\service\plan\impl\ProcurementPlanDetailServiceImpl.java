package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.plan.ProcurementPlanDetailCondition;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlan;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlanDetail;
import com.hxdi.nmjl.mapper.plan.ProcurementPlanDetailMapper;
import com.hxdi.nmjl.service.plan.BidInfoService;
import com.hxdi.nmjl.service.plan.ProcurementPlanDetailService;
import com.hxdi.nmjl.service.plan.ProcurementPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 粮食采购计划详情服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/26 15:30
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ProcurementPlanDetailServiceImpl extends BaseServiceImpl<ProcurementPlanDetailMapper, GrainProcurementPlanDetail> implements ProcurementPlanDetailService {

    @Resource
    private ProcurementPlanService procurementPlanService;

    @Resource
    private BidInfoService bidInfoService;

    @Override
    public List<GrainProcurementPlanDetail> getList(String planId) {
        return baseMapper.selectList(Wrappers.<GrainProcurementPlanDetail>lambdaQuery().eq(GrainProcurementPlanDetail::getPlanId, planId));
    }

    @Override
    public void update(GrainProcurementPlanDetail planDetail) {
        GrainProcurementPlan plan = procurementPlanService.getById(planDetail.getPlanId());
        if (plan.getState() != 0) {
            throw new BaseException("计划已提交，不能修改");
        }
        baseMapper.updateById(planDetail);
    }

    @Override
    public void remove(String planId) {
        //根据计划ID删除计划明细
        baseMapper.delete(Wrappers.<GrainProcurementPlanDetail>lambdaQuery().eq(GrainProcurementPlanDetail::getPlanId, planId));
    }

    @Override
    public Page<GrainProcurementPlanDetail> getPage(ProcurementPlanDetailCondition condition) {
        Page<GrainProcurementPlanDetail> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public boolean checkAllDetailsHaveBids(String planId) {
        // 查询该计划的所有详情
        List<GrainProcurementPlanDetail> details = this.getList(planId);
        if (details != null && details.size() <= 1) {
            return true;
        }
        // 检查每个详情是否都有对应的招标信息
        if (details != null) {
            for (GrainProcurementPlanDetail detail : details) {
                boolean hasBid = bidInfoService.existsBidForDetail(detail.getId());
                if (!hasBid) {
                    return false; // 如果有任何一个详情没有招标信息，返回 false
                }
            }
        }
        return true; // 所有详情都有招标信息，返回 true
    }
}
