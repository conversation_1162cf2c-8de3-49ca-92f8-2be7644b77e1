<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.CatalogMapper">
	<resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.Catalog">
		<!--@mbg.generated-->
		<!--@Table C_CATALOG-->
		<id column="ID" property="id"/>
		<result column="CLASSIFICATION_ID" property="classificationId"/>
		<result column="CLASSIFICATION_NAME" property="classificationName"/>
		<result column="CATALOG_CODE" property="catalogCode"/>
		<result column="CATALOG_NAME" property="catalogName"/>
		<result column="SPECIFICATION" property="specification"/>
		<result column="BRAND" property="brand"/>
		<result column="UNIT" property="unit"/>
		<result column="PACK_UNIT" property="packUnit"/>
		<result column="ENABLED" property="enabled"/>
		<result column="PRE_IMG" property="preImg"/>
		<result column="ATTACHMENT" property="attachment"/>
		<result column="MAX_STORAGE_TIME" property="maxStorageTime"/>
		<result column="REMARK" property="remark"/>
		<result column="CATEGORY" property="category"/>
		<result column="CLASSIFICATION_PATH" property="classificationPath"/>
		<result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
		<result column="CREATE_TIME" property="createTime"/>
		<result column="UPDATE_TIME" property="updateTime"/>
		<result column="TENANT_ID" property="tenantId"/>
	</resultMap>
	<sql id="Base_Column_List">
		<!--@mbg.generated-->
		ID,CLASSIFICATION_ID,CLASSIFICATION_NAME,CATALOG_CODE,CATALOG_NAME,
		SPECIFICATION,BRAND,UNIT,PACK_UNIT,ENABLED,PRE_IMG,ATTACHMENT,MAX_STORAGE_TIME,
		REMARK,CATEGORY,CLASSIFICATION_PATH,DATA_HIERARCHY_ID,CREATE_TIME,UPDATE_TIME,TENANT_ID
	</sql>

	<select id="selectPageV1" resultMap="BaseResultMap">
		select <include refid="Base_Column_List"/> from c_catalog
		<where>
			enabled = 1
			<if test="@plugins.OGNL@isNotEmpty(condition.classificationId)">
				and classification_id = #{condition.classificationId}
			</if>
			<if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
				and (classification_name like concat('%', #{condition.keywords}, '%')
					or catalog_name like concat('%', #{condition.keywords}, '%')
					or brand like concat('%', #{condition.keywords}, '%')
				)
			</if>
		</where>
		order by CREATE_TIME DESC
	</select>

	<select id="selectListV1" resultMap="BaseResultMap">
		select <include refid="Base_Column_List"/> from c_catalog
		<where>
			enabled = 1
			<if test="@plugins.OGNL@isNotEmpty(condition.classificationId)">
				and classification_id = #{condition.classificationId}
			</if>
		</where>
		order by CREATE_TIME DESC
	</select>

	<select id="selectCountV1" resultType="java.lang.Long">
		select count(*) from c_catalog
		<where>
			enabled = 1
			<if test="@plugins.OGNL@isNotEmpty(catalog.classificationId)">
				and classification_id = #{catalog.classificationId}
			</if>
			<if test="@plugins.OGNL@isNotEmpty(catalog.catalogCode)">
				and catalog_code = #{catalog.catalogCode}
			</if>
			<if test="@plugins.OGNL@isNotEmpty(catalog.catalogName)">
				and catalog_name = #{catalog.catalogName}
			</if>
			<if test="@plugins.OGNL@isNotEmpty(catalog.brand)">
				and brand = #{catalog.brand}
			</if>
			<if test="@plugins.OGNL@isNotEmpty(catalog.specification)">
				and specification = #{catalog.specification}
			</if>
		</where>
	</select>

	<delete id="softDeleteById">
		update c_catalog set enabled = 0 where id = #{id}
	</delete>

	<select id="selectListByClassIds" resultMap="BaseResultMap">
		select ID,CLASSIFICATION_ID,CLASSIFICATION_NAME,
			CATALOG_CODE,CATALOG_NAME,SPECIFICATION,BRAND,UNIT,PACK_UNIT,ENABLED
		from c_catalog
		where enabled = 1 and classification_id in
		<foreach collection="classificationIds" item="clsId" open="(" separator="," close=")">
			#{clsId}
		</foreach>
		order by CLASSIFICATION_ID, ID
	</select>
</mapper>
