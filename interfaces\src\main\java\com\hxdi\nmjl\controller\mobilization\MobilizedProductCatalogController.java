package com.hxdi.nmjl.controller.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCatalogCondition;
import com.hxdi.nmjl.domain.mobilization.MobilizedProductCatalog;
import com.hxdi.nmjl.service.mobilization.MobilizedProductCatalogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动员品种目录
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/11 18:16
 */
@Api(tags = "动员品种目录")
@RestController
@RequestMapping ("/product/catalog")
public class MobilizedProductCatalogController extends BaseController<MobilizedProductCatalogService, MobilizedProductCatalog> {

    @ApiOperation ("分页查询")
    @GetMapping ("/page")
    public ResultBody<Page<MobilizedProductCatalog>> page(MobilizedProductCatalogCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }
    @ApiOperation ("列表查询")
    @GetMapping ("/list")
    public ResultBody<List<MobilizedProductCatalog>> list(MobilizedProductCatalogCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }
    @ApiOperation ("保存/修改动员品种目录")
    @Log("保存/修改动员品种目录")
    @PostMapping ("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody MobilizedProductCatalog catalog) {
        if(CommonUtils.isEmpty(catalog.getId())) {
            bizService.create(catalog);
        } else {
            bizService.update(catalog);
        }
        return ResultBody.ok();
    }

    @ApiOperation ("删除动员品种")
    @Log("删除动员品种")
    @DeleteMapping ("/remove")
    public ResultBody remove (@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok ();
    }
    @ApiOperation ("查看详情")
    @GetMapping ("/detail")
    public ResultBody<MobilizedProductCatalog> detail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation ("确认")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id) {
        bizService.approve(id);
        return ResultBody.ok();
    }
}
