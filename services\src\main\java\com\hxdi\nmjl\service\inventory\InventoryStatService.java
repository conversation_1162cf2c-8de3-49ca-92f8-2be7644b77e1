package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.InventoryStat;
import com.hxdi.nmjl.condition.inventory.InventoryStatCondition;
import com.hxdi.nmjl.vo.inventory.InventoryStatResVO;

import java.math.BigDecimal;
import java.util.List;

public interface InventoryStatService extends IBaseService<InventoryStat> {

    /**
     * 统计日志按天统计（如果当天存在统计日志，则更新，不存在则新增）
     * @param InventoryId 库存ID
     * @param inoutType 出入库类型（1：入库，2：出库）
     * @param changeQty 库存变化数量
     */
    void createOrUpdate(String InventoryId,String inoutType,BigDecimal changeQty);

    /**
     * 定时任务批量创建统计日志（新建当天的统计日志）（step 1）
     */
    void createBatch();

    /**
     * 定时任务更新统计日志（更新前一天统计日志）（step 2）
     */
    void updateBatch();

    /**
     * 根据条件查询库存保管总账列表
     * @return
     */
    List<InventoryStatResVO> listV1(InventoryStatCondition condition);

    /**
     * 根据条件查询库存保管总账分页列表
     * @return
     */
    Page<InventoryStatResVO> pageV1(InventoryStatCondition condition);

    /**
     * 根据条件查询库存保管明细账列表
     * @return
     */
    List<InventoryStatResVO> detailListV1(InventoryStatCondition condition);

    /**
     * 根据条件查询库存保管明细账分页列表
     * @return
     */
    Page<InventoryStatResVO> detailPageV1(InventoryStatCondition condition);

}
