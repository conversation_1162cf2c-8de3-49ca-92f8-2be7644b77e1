package com.hxdi.nmjl.mapper.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.emergency.EmergencyResponseCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应急预案 Mapper 接口
 */
@Mapper
public interface EmergencyResponseMapper extends SuperMapper<EmergencyResponse> {
    @DataPermission
    List<EmergencyResponse> getList(@Param("condition") EmergencyResponseCondition condition);
    @DataPermission
    Page<EmergencyResponse> getPages(@Param("condition") EmergencyResponseCondition condition, @Param("page") Page<EmergencyResponse> page);
}
