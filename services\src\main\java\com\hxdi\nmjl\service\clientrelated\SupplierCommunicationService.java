package com.hxdi.nmjl.service.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.base.SupplierCommunicationCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierCommunication;

import java.util.List;

/**
 * 供应商沟通记录管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-11
 */
public interface SupplierCommunicationService extends IBaseService<SupplierCommunication> {

    /**
     * 新增沟通记录
     *
     * @param record 沟通记录信息
     */
    void create(SupplierCommunication record);

    /**
     * 更新沟通记录
     *
     * @param record 沟通记录信息
     */
    void update(SupplierCommunication record);

    /**
     * 修改状态：启用，禁用，删除
     *
     * @param id      沟通记录ID
     * @param enabled 状态：0-禁用，1-启用，7-删除
     */
    void changeState(String id, Integer enabled);

    /**
     * 查询详情
     *
     * @param id 沟通记录ID
     * @return 沟通记录信息
     */
    SupplierCommunication getDetail(String id);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<SupplierCommunication> pages(SupplierCommunicationCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<SupplierCommunication> lists(SupplierCommunicationCondition condition);

    /**
     * 更新沟通状态
     *
     * @param id     沟通记录ID
     * @param status 沟通状态：1-待处理，2-处理中，3-已处理，4-已关闭
     */
    void updateStatus(String id, Integer status);

    /**
     * 批量更新沟通状态
     *
     * @param ids    沟通记录ID列表
     * @param status 沟通状态：1-待处理，2-处理中，3-已处理，4-已关闭
     */
    void batchUpdateStatus(List<String> ids, Integer status);

    /**
     * 处理沟通记录
     *
     * @param id          沟通记录ID
     * @param result      处理结果
     * @param handlerId   处理人ID
     * @param handlerName 处理人名称
     */
    void handleRecord(String id, String result, String handlerId, String handlerName);

    /**
     * 关闭沟通记录
     *
     * @param id 沟通记录ID
     */
    void closeRecord(String id);

    /**
     * 根据供应商ID查询沟通记录数量
     *
     * @param supplierId 供应商ID
     * @return 记录数量
     */
    Long countBySupplierId(String supplierId);
}
