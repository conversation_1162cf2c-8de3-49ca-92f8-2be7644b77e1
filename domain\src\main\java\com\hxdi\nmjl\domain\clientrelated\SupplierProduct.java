package com.hxdi.nmjl.domain.clientrelated;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 供应商产品表
 */
@Setter
@Getter
@TableName("B_SUPPLIER_PRODUCT")
public class SupplierProduct extends Entity<SupplierProduct> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 供应商id
     */
    @TableField("SUPPLIER_ID")
    private String supplierId;

    /**
     * 品种ID
     */
    @TableField("FOODCATEGORY_ID")
    private String foodCategoryId;

    /**
     * 质量等级
     */
    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    /**
     * 产量
     */
    @TableField("CAPACITY")
    private BigDecimal capacity;

    /**
     * 生产日期
     */
    @TableField("PRODUCE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate produceDate;

    /**
     * 描述
     */
    @TableField("REMARKS")
    private String remarks;

    /**
     * 状态（1-正常, 0-删除）
     */
    @TableField("ENABLED")
    private Integer enabled;

    /**
     * 规格
     */
    @TableField("SPEC")
    private String spec;

    /**
     * 供应价格
     */
    @TableField("SUPPLIER_PRICE")
    private BigDecimal supplierPrice;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建者ID
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新者ID
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 数据权限字段
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * 品种名称
     */
    @TableField("FOODCATEGORY_NAME")
    private String foodCategoryName;
}
