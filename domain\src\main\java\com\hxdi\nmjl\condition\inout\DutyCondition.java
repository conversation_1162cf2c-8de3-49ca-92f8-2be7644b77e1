package com.hxdi.nmjl.condition.inout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "业务查询参数")
@Setter
@Getter
public class DutyCondition extends QueryCondition {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回，3-已下发")
    private String approveStatus;
}
