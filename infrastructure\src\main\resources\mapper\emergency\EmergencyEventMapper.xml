<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyEventMapper">

    <sql id="Base_Column_List">
        ID, EVENT_CODE, EVENT_NAME, EVENT_TYPE, EVENT_LEVEL,
        RESP_CODE, RESP_NAME, EVENT_TIME, PROVINCE_ID, CITY_ID,
        COUNTY_ID, AREA, LON, LAT, DESCS,
        END_TIME, STATE, ENABLED, CREATE_TIME, UPDATE_TIME,
        CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyEvent">
        <id column="ID" property="id"/>
        <result column="EVENT_CODE" property="eventCode"/>
        <result column="EVENT_NAME" property="eventName"/>
        <result column="EVENT_TYPE" property="eventType"/>
        <result column="EVENT_LEVEL" property="eventLevel"/>
        <result column="RESP_CODE" property="respCode"/>
        <result column="RESP_NAME" property="respName"/>
        <result column="EVENT_TIME" property="eventTime"/>
        <result column="PROVINCE_ID" property="provinceId"/>
        <result column="CITY_ID" property="cityId"/>
        <result column="COUNTY_ID" property="countyId"/>
        <result column="AREA" property="area"/>
        <result column="LON" property="lon"/>
        <result column="LAT" property="lat"/>
        <result column="DESCS" property="descs"/>
        <result column="END_TIME" property="endTime"/>
        <result column="STATE" property="state"/>
        <result column="ENABLED" property="enabled"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
    </resultMap>

    <!-- 列表查询 -->
    <select id="getList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_EMERGENCY_EVENT
        <where>
            ENABLED = 1
            <if test="condition.eventName != null and condition.eventName != ''">
                AND EVENT_NAME LIKE CONCAT('%', #{condition.eventName}, '%')
            </if>
            <if test="condition.eventLevel != null and condition.eventLevel != ''">
                AND EVENT_LEVEL = #{condition.eventLevel}
            </if>
            <if test="condition.respCode != null">
                AND RESP_CODE = #{condition.respCode}
            </if>
            <if test="condition.eventCode != null">
                AND EVENT_CODE = #{condition.eventCode}
            </if>
            <if test="condition.provinceId != null and condition.provinceId != '' ">
                and PROVINCE_ID = #{condition.provinceId}
            </if>
            <if test="condition.cityId != null and condition.cityId != '' ">
                and CITY_ID = #{condition.cityId}
            </if>
            <if test="condition.countyId != null and condition.countyId != '' ">
                and COUNTY_ID = #{condition.countyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 分页查询 -->
    <select id="getPages" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_EMERGENCY_EVENT
        <where>
            ENABLED = 1
            <if test="condition.eventName != null and condition.eventName != ''">
                AND EVENT_NAME LIKE CONCAT('%', #{condition.eventName}, '%')
            </if>
            <if test="condition.eventCode != null">
                AND EVENT_CODE = #{condition.eventCode}
            </if>
            <if test="condition.eventLevel != null and condition.eventLevel != ''">
                AND EVENT_LEVEL = #{condition.eventLevel}
            </if>
            <if test="condition.respCode != null">
                AND RESP_CODE = #{condition.respCode}
            </if>
            <if test="condition.provinceId != null and condition.provinceId != '' ">
                and PROVINCE_ID = #{condition.provinceId}
            </if>
            <if test="condition.cityId != null and condition.cityId != '' ">
                and CITY_ID = #{condition.cityId}
            </if>
            <if test="condition.countyId != null and condition.countyId != '' ">
                and COUNTY_ID = #{condition.countyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
