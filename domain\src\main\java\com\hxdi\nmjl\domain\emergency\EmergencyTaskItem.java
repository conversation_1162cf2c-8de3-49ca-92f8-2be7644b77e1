package com.hxdi.nmjl.domain.emergency;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 紧急任务项表
 */
@Getter
@Setter
@TableName(value = "B_EMERGENCY_TASK_ITEM")
public class EmergencyTaskItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调度id
     */
    @TableField(value = "SCHEDULIE_ID")
    private String scheduleId;

    /**
     * 任务id
     */
    @TableField(value = "TASK_ID")
    private String taskId;

    /**
     * 品种id
     */
    @TableField(value = "CATALOG_ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    private String catalogName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    private String specification;

    /**
     * 质量等级
     */
    @TableField(value = "GRADE")
    private String grade;

    /**
     * 数量
     */
    @TableField(value = "QTY")
    private BigDecimal qty;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}