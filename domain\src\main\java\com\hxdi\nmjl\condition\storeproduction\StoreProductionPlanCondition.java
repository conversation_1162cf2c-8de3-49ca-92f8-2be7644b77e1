package com.hxdi.nmjl.condition.storeproduction;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "门店生产计划查询条件")
@Getter
@Setter
public class StoreProductionPlanCondition extends QueryCondition {

    @ApiModelProperty(value = "计划编号")
    private String planNo;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "计划类型：1-日常计划，2-应急计划，3-专项计划")
    private Integer planType;

    @ApiModelProperty(value = "计划开始日期")
    private Date startDate;

    @ApiModelProperty(value = "计划结束日期")
    private Date endDate;

    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}

