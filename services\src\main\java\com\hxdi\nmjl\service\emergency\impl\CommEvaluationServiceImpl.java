package com.hxdi.nmjl.service.emergency.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.emergency.CommEvaluation;
import com.hxdi.nmjl.mapper.emergency.CommEvaluationMapper;
import com.hxdi.nmjl.service.emergency.CommEvaluationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* <AUTHOR>
* @description 针对表【B_COMM_EVALUATION(《沟通效果评估信息表》)】的数据库操作Service实现
* @createDate 2025-08-15 09:21:14
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class CommEvaluationServiceImpl extends BaseServiceImpl<CommEvaluationMapper, CommEvaluation> implements CommEvaluationService{

    @Override
    public void submit(String id) {
        CommEvaluation commEvaluation = baseMapper.selectById(id);
        commEvaluation.setEvaluateStatus(2);
        baseMapper.updateById(commEvaluation);
    }
}




