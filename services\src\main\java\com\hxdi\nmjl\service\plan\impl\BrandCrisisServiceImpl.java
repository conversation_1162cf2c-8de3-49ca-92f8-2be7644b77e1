package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.plan.BrandCrisisCondition;
import com.hxdi.nmjl.domain.plan.BrandCrisis;
import com.hxdi.nmjl.domain.plan.BrandInfo;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.BrandCrisisMapper;
import com.hxdi.nmjl.service.plan.BrandCrisisService;
import com.hxdi.nmjl.service.plan.BrandInfoService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class BrandCrisisServiceImpl extends BaseServiceImpl<BrandCrisisMapper, BrandCrisis> implements BrandCrisisService {

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private BrandInfoService brandInfoService;

    @Override
    public Page<BrandCrisis> pages(BrandCrisisCondition condition) {
        Page<BrandCrisis> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<BrandCrisis> lists(BrandCrisisCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public BrandCrisis getDetail(String id) {
        return baseMapper.selectById(id);
    }


    @Override
    public void create(BrandCrisis crisis) {
        // 生成协议编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("EVENT_NO");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        crisis.setEventNo((String) businessCode.getValue());

        BrandInfo info = brandInfoService.getById(crisis.getBrandId());
        crisis.setBrandName(info.getBrandName());
        this.save(crisis);
    }

    @Override
    public void update(BrandCrisis crisis) {
        BrandCrisis savedBrandCrisis = this.getById(crisis.getId());
        if (savedBrandCrisis == null) {
            throw new BaseException("要修改的危机不存在");
        }
        this.updateById(crisis);
    }

    @Override
    public void remove(String id) {
        BrandCrisis brandCrisis = baseMapper.selectById(id);
        if (brandCrisis == null) {
            throw new BaseException("要删除的危机不存在");
        }
        brandCrisis.setEnabled(0);
        this.updateById(brandCrisis);
    }
}
