package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 品牌授权
 */
@Getter
@Setter
@TableName(value = "B_BRAND_AUTHORIZING")
public class BrandAuthorizing implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 品牌id
     */
    @TableField(value = "BRAND_ID")
    private String brandId;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND_NAME")
    private String brandName;

    /**
     * 授权协议编号
     */
    @TableField(value = "LICENSE_NO")
    private String licenseNo;

    /**
     * 被授权企业id
     */
    @TableField(value = "CLIENT_ID")
    private String clientId;

    /**
     * 被授权企业名称
     */
    @TableField(value = "CLIENT_NAME")
    private String clientName;

    /**
     * 授权类型：字典PPSQLX
     */
    @TableField(value = "AUTH_TYPE")
    private String authType;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "APPLY_TIME")
    private Date applyTime;

    /**
     * 包装配额数量
     */
    @TableField(value = "APPLY_QTY")
    private Integer applyQty;

    /**
     * 协议有效期
     */
    @TableField(value = "VALID_DATE")
    private Date validDate;

    /**
     * 授权状态：0-无效，1-有效，2-已过期
     */
    @TableField(value = "AUTH_STATE")
    private Integer authState;

    /**
     * 备注
     */
    @TableField(value = "MEMO")
    private String memo;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @TableField(value = "APPROVE_STATUS")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    private String approveOpinion;

    /**
     * 状态（1-有效 0删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    private String attachments;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}