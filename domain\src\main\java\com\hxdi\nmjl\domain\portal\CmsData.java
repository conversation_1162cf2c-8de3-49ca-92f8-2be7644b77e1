package com.hxdi.nmjl.domain.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 门户-数据表（文章类容、图片等）
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/21 10:28
 */
@Getter
@Setter
@TableName("CMS_DATA")
public class CmsData implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    /**
     *  数据
     */
    private String dat;
}
