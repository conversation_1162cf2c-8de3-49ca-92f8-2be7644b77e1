package com.hxdi.nmjl.controller.plan;

import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.plan.InspectionRecordCondition;
import com.hxdi.nmjl.domain.plan.InspectionRecord;
import com.hxdi.nmjl.service.plan.InspectionRecordService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <监督检查记录管理>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "监督检查记录管理")
@RestController
@RequestMapping("/inspectionRecord")
public class InspectionRecordController extends CommonApiController<InspectionRecordService, InspectionRecord, InspectionRecordCondition> {

}
