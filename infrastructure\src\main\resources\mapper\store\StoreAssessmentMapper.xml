<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.store.StoreAssessmentMapper">
    <sql id="Base_Column_List">
        ID, APPLY_NO, ENTERPRISE_NAME, CREDIT_CODE, ORG_ID, ORG_NAME,
    APPLY_TYPE, ASSES_DESC, GRADE, APPROVE_STATUS, APPROVER,
    APPROVE_TIME, APPROVE_OPINION, ENABLED, CREATE_TIME, UPDATE_TIME,
    CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.store.StoreAssessment">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="APPLY_NO" jdbcType="VARCHAR" property="applyNo" />
        <result column="ENTERPRISE_NAME" jdbcType="VARCHAR" property="enterpriseName" />
        <result column="CREDIT_CODE" jdbcType="VARCHAR" property="creditCode" />
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
        <result column="APPLY_TYPE" jdbcType="INTEGER" property="applyType" />
        <result column="ASSES_DESC" jdbcType="VARCHAR" property="assesDesc" />
        <result column="GRADE" jdbcType="VARCHAR" property="grade" />
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus" />
        <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_ASSESSMENT
        <where>
            ENABLED = 1
            <if test="condition.applyNo != null and condition.applyNo != ''">
                AND APPLY_NO = #{condition.applyNo}
            </if>
            <if test="condition.enterpriseName != null and condition.enterpriseName != ''">
                AND ENTERPRISE_NAME LIKE CONCAT('%', #{condition.enterpriseName}, '%')
            </if>
            <if test="condition.creditCode != null and condition.creditCode != ''">
                AND CREDIT_CODE = #{condition.creditCode}
            </if>
            <if test="condition.orgId != null and condition.orgId != ''">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="condition.applyType != null">
                AND APPLY_TYPE = #{condition.applyType}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                AND GRADE = #{condition.grade}
            </if>
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_ASSESSMENT
        <where>
            ENABLED = 1
            <if test="condition.applyNo != null and condition.applyNo != ''">
                AND APPLY_NO = #{condition.applyNo}
            </if>
            <if test="condition.enterpriseName != null and condition.enterpriseName != ''">
                AND ENTERPRISE_NAME LIKE CONCAT('%', #{condition.enterpriseName}, '%')
            </if>
            <if test="condition.creditCode != null and condition.creditCode != ''">
                AND CREDIT_CODE = #{condition.creditCode}
            </if>
            <if test="condition.orgId != null and condition.orgId != ''">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="condition.assesType != null">
                AND ASSES_TYPE = #{condition.assesType}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                AND GRADE = #{condition.grade}
            </if>
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
