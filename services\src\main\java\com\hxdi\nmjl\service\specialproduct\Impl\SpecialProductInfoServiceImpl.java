package com.hxdi.nmjl.service.specialproduct.Impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.base.ClientInfo;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.specialproduct.SpecialProductInfoMapper;
import com.hxdi.nmjl.service.base.ClientInfoService;
import com.hxdi.nmjl.service.specialproduct.SpecialProductApprovalService;
import com.hxdi.nmjl.service.specialproduct.SpecialProductContentService;
import com.hxdi.nmjl.service.specialproduct.SpecialProductInfoService;
import com.hxdi.nmjl.service.specialproduct.SpecialProductMonitoringService;
import com.hxdi.nmjl.condition.specialproduct.SpecialProductInfoCondition;
import com.hxdi.nmjl.condition.specialproduct.SpecialProductMonitoringCondition;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 地方特色产品信息管理服务实现
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SpecialProductInfoServiceImpl extends BaseServiceImpl<SpecialProductInfoMapper, SpecialProductInfo> implements SpecialProductInfoService {

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private SpecialProductContentService specialProductContentService;

    @Resource
    private SpecialProductMonitoringService specialProductMonitoringService;

    @Resource
    private SpecialProductApprovalService specialProductApprovalService;

    @Resource
    private ClientInfoService clientInfoService;

    @Override
    public void create(SpecialProductInfo productInfo) {
        // 生成产品编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("SPECIAL_PRODUCT_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        productInfo.setProductCode((String) businessCode.getValue());
        ClientInfo clientInfo = clientInfoService.getById(productInfo.getClientId());
        productInfo.setClientName(clientInfo.getName());
        // 保存产品信息
        this.save(productInfo);
        // 创建产品内容信息
        if (productInfo.getSpecialProductContent() != null) {
            specialProductContentService.create(productInfo.getId(), productInfo.getSpecialProductContent());
        }
    }

    @Override
    public void update(SpecialProductInfo productInfo) {
        // 查询原产品信息
        SpecialProductInfo savedProduct = this.getById(productInfo.getId());
        if (savedProduct == null) {
            throw new BaseException("产品信息不存在");
        }
        // 校验状态
        if (savedProduct.getApproveStatus() != 2 && savedProduct.getPublishState() == 1) {
            throw new BaseException("已发布的产品不能修改");
        }
        ClientInfo clientInfo = clientInfoService.getById(productInfo.getClientId());
        productInfo.setClientName(clientInfo.getName());
        // 更新产品信息
        this.updateById(productInfo);
        // 更新产品内容信息
        if (productInfo.getSpecialProductContent() != null) {
            specialProductContentService.update(productInfo.getId(), productInfo.getSpecialProductContent());
        }
    }

    @Override
    public SpecialProductInfo getDetail(String productId) {
        SpecialProductInfo specialProductInfo = baseMapper.selectById(productId);
        if (specialProductInfo != null) {
            specialProductInfo.setSpecialProductContent(specialProductContentService.getDetail(productId));
            SpecialProductMonitoringCondition specialProductMonitoringCondition = new SpecialProductMonitoringCondition();
            specialProductMonitoringCondition.setProductId(productId);
            specialProductInfo.setSpecialProductMonitorings(specialProductMonitoringService.pages(specialProductMonitoringCondition));
        }
        return specialProductInfo;
    }

    @Override
    public Page<SpecialProductInfo> pages(SpecialProductInfoCondition condition) {
        Page<SpecialProductInfo> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<SpecialProductInfo> lists(SpecialProductInfoCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void approve(String productId, Integer approveStatus, String opinion) {
        // 查询产品信息
        SpecialProductInfo product = baseMapper.selectById(productId);
        if (product.getApproveStatus() == 1) {
            throw new BaseException("产品已审核通过，不能重复审核");
        }
        if (product.getApproveStatus() == 2 && approveStatus == 2) {
            throw new BaseException("产品已驳回，不能重复驳回");
        }
        // 更新审核信息
        product.setApproveStatus(approveStatus);
        // 审核通过
        if (approveStatus == 1) {
            product.setPublishState(0);
        }
        specialProductApprovalService.firstLevelApprove(productId, approveStatus, opinion);
        this.updateById(product);
    }

    @Override
    public void remove(String productId) {
        SpecialProductInfo product = baseMapper.selectById(productId);
        if (product == null) {
            throw new BaseException("产品信息不存在");
        }

        if (product.getApproveStatus() == 1 && product.getPublishState() == 1) {
            throw new BaseException("已发布的产品不能删除");
        }

        // 逻辑删除
        product.setEnabled(0);
        this.updateById(product);
    }

    @Override
    public void publish(String productId) {
        SpecialProductInfo product = baseMapper.selectById(productId);
        if (product == null) {
            throw new BaseException("产品信息不存在");
        }

        if (product.getApproveStatus() != 1) {
            throw new BaseException("产品未通过审核，不能发布");
        }

        if (product.getPublishState() == 1) {
            throw new BaseException("产品已发布，不能重复发布");
        }

        // 更新发布状态
        product.setPublishState(1);
        this.updateById(product);
    }

    @Override
    public void unpublish(String productId) {
        SpecialProductInfo product = baseMapper.selectById(productId);
        if (product == null) {
            throw new BaseException("产品信息不存在");
        }

        if (product.getPublishState() == 0) {
            throw new BaseException("产品未发布，无需操作");
        }

        // 更新发布状态
        product.setPublishState(0);
        this.updateById(product);
    }

    @Override
    public void submit(String productId) {
        SpecialProductInfo specialProductInfo = this.getById(productId);
        if (specialProductInfo == null) {
            throw new BaseException("产品信息不存在");
        }

        specialProductInfo.setApproveStatus(0);
        this.updateById(specialProductInfo);


        // 创建产品审核信息
        specialProductApprovalService.create(productId);
    }
}
