package com.hxdi.nmjl.mapper.iot;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.iot.TemHumRecord;
import com.hxdi.nmjl.condition.iot.TemHumRecordCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TemHumRecordMapper extends SuperMapper<TemHumRecord> {

    @DataPermission
    List<TemHumRecord> getList(@Param("condition") TemHumRecordCondition condition);

    @DataPermission
    Page<TemHumRecord> getPage(@Param("condition") TemHumRecordCondition condition, Page<TemHumRecord> page);
}
