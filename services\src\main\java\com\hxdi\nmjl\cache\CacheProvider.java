package com.hxdi.nmjl.cache;

import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 缓存访问门面类
 * 基于Spring Cache实现，保证数据一致性
 * 提供直接访问缓存数据的便捷方法
 */
@Slf4j
@Component
public class CacheProvider {

    private static CacheManager cacheManager;
    private static CachePreloadRunner cachePreloadRunner;

    public CacheProvider(CacheManager cacheManager, CachePreloadRunner cachePreloadRunner) {
        CacheProvider.cacheManager = cacheManager;
        CacheProvider.cachePreloadRunner = cachePreloadRunner;
    }

    /**
     * 根据缓存类型和ID获取缓存对象
     * @param cacheKey 缓存类型，如catalog, classification等
     * @param id 对象ID
     * @param <T> 返回类型
     * @return 缓存对象
     */
    public static <T> T getCacheObject(String cacheKey, String id) {
        try {
            if (CommonUtils.isEmpty(cacheKey)) {
                return null;
            }

            Cache cache = cacheManager.getCache(cacheKey);
            if (cache == null) {
                log.warn("缓存不存在: {}", cacheKey);
                return null;
            }

            RedisCache.ValueWrapper wrapper = cache.get(id);
            return wrapper != null ? (T) wrapper.get() : null;
        } catch (Exception e) {
            log.error("获取缓存数据异常, type: {}, id: {}", cacheKey, id, e);
            return null;
        }
    }

    /**
     * 获取可选的缓存对象
     * @param cacheKey 缓存类型
     * @param id 对象ID
     * @param <T> 返回类型
     * @return Optional包装的对象
     */
    public static <T> Optional<T> optional(String cacheKey, String id) {
        return Optional.ofNullable(getCacheObject(cacheKey, id));
    }

    /**
     * 获取缓存对象的值，如果缓存为null时返回null
     * @param cacheKey
     * @param id
     * @param func
     * @param <T>  缓存对象类型
     * @param <R>  预期获取的值类型
     * @return
     */
    public static  <T, R> R getValue(String cacheKey, String id, Function<T, R> func) {
        Optional<T> cacheObject = optional(cacheKey, id);
        return cacheObject.map(func).orElse(null);
    }

    /**
     * 设置缓存对象
     * @param type 缓存类型，如catalog, classification等
     * @param id 对象ID
     * @param value 对象值
     * @param <T> 对象类型
     */
    public static  <T> void put(String type, String id, T value) {
        try {
            String cacheKey = RedisKeys.match(type).key();
            if (Objects.equals(cacheKey, RedisKeys.NOOP.key())) {
                log.warn("不支持的缓存类型: {}", type);
                return;
            }

            Cache cache = cacheManager.getCache(cacheKey);
            if (cache == null) {
                log.warn("缓存不存在: {}", cacheKey);
                return;
            }

            cache.put(id, value);
        } catch (Exception e) {
            log.error("设置缓存数据失败, type: {}, id: {}", type, id, e);
        }
    }

    /**
     * 设置缓存对象（批量）
     * @param type 缓存类型，如catalog, classification等
     * @param dataMap 数据Map，key为ID，value为对象
     * @param <T> 对象类型
     */
    public static <T> void putAll(String type, Map<String, T> dataMap) {
        try {
            String cacheKey = RedisKeys.match(type).key();
            if (Objects.equals(cacheKey, RedisKeys.NOOP.key())) {
                log.warn("不支持的缓存类型: {}", type);
                return;
            }

            Cache cache = cacheManager.getCache(cacheKey);
            if (cache == null) {
                log.warn("缓存不存在: {}", cacheKey);
                return;
            }

            dataMap.forEach(cache::put);
        } catch (Exception e) {
            log.error("批量设置缓存数据失败, type: {}", type, e);
        }
    }

    /**
     * 删除缓存对象（type中的对应ID的缓存记录）
     * @param type 缓存类型，如catalog, classification等
     * @param id 对象ID
     */
    public static void evict(String type, String id) {
        try {
            String cacheKey = RedisKeys.match(type).key();
            if (Objects.equals(cacheKey, RedisKeys.NOOP.key())) {
                log.warn("不支持的缓存类型: {}", type);
                return;
            }

            Cache cache = cacheManager.getCache(cacheKey);
            if (cache == null) {
                log.warn("缓存不存在: {}", cacheKey);
                return;
            }

            cache.evict(id);
        } catch (Exception e) {
            log.error("删除缓存数据失败, type: {}, id: {}", type, id, e);
        }
    }

    /**
     * 清除指定类型的所有缓存
     * @param type 缓存类型，如catalog, classification等
     */
    public static void clear(String type) {
        try {
            String cacheKey = RedisKeys.match(type).key();
            if (Objects.equals(cacheKey, RedisKeys.NOOP.key())) {
                log.warn("不支持的缓存类型: {}", type);
                return;
            }

            Cache cache = cacheManager.getCache(cacheKey);
            if (cache == null) {
                log.warn("缓存不存在: {}", cacheKey);
                return;
            }

            cache.clear();
            log.info("清除缓存成功: {}", cacheKey);
        } catch (Exception e) {
            log.error("清除缓存数据失败, type: {}", type, e);
        }
    }

    /**
     * 判断缓存中是否存在指定对象
     * @param type 缓存类型，如catalog, classification等
     * @param id 对象ID
     * @return 是否存在
     */
    public static boolean exists(String type, String id) {
        try {
            String cacheKey = RedisKeys.match(type).key();
            if (Objects.equals(cacheKey, RedisKeys.NOOP.key())) {
                return false;
            }

            Cache cache = cacheManager.getCache(cacheKey);
            if (cache == null) {
                return false;
            }

            return cache.get(id) != null;
        } catch (Exception e) {
            log.error("检查缓存是否存在失败, type: {}, id: {}", type, id, e);
            return false;
        }
    }

    /**
     * 刷新指定类型的缓存
     * 先清除缓存，然后重新加载数据
     *
     * @param type 缓存类型
     */
    public static void refresh(String type) {
        try {
            String cacheKey = RedisKeys.match(type).key();
            if (cacheKey == null) {
                log.warn("不支持的缓存类型: {}", type);
                return;
            }

            Cache cache = cacheManager.getCache(cacheKey);
            if (cache == null) {
                log.warn("缓存不存在: {}", cacheKey);
                return;
            }

            cache.clear();

            // 根据类型重新加载缓存
            RedisKeys redisKeys = RedisKeys.match(type);
            if (redisKeys!=RedisKeys.NOOP) {
                cachePreloadRunner.LoadCache(type);
            }
        } catch (Exception e) {
            log.error("刷新缓存失败, type: {}", type, e);
        }
    }
}
