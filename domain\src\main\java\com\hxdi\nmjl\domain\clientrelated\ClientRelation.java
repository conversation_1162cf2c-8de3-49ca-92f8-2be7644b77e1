package com.hxdi.nmjl.domain.clientrelated;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 客户——组织关系表
 * @author: 王贝强
 * @create: 2025-04-14 14:07
 */
@Setter
@Getter
@ApiModel(value = "客户——组织关系表")
@TableName(value = "B_CLIENT_RELATION")
public class ClientRelation {
    @TableId(value = "ID")
    private Integer id;

    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织ID")
    private String dataHierarchyId;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人ID")
    private String createId;

}
