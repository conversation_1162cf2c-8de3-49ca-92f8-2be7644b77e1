<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.duty.DutyPlanMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.duty.DutyPlan">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="STORE_ID" property="storeId" />
        <result column="STORE_NAME" property="storeName" />
        <result column="PLAN_START_TIME" property="planStartTime" />
        <result column="PLAN_END_TIME" property="planEndTime" />
        <result column="FZR" property="fzr" />
        <result column="MOBILE" property="mobile" />
        <result column="REMARKS" property="remarks" />
        <result column="ATTACHMENTS" property="attachments" />
        <result column="ENABLED" property="enabled" />
        <result column="APPROVE_STATUS" property="approveStatus" />
        <result column="APPROVER" property="approver" />
        <result column="APPROVE_TIME" property="approveTime" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,STORE_ID,STORE_NAME,PLAN_START_TIME,PLAN_END_TIME,
        FZR,MOBILE,REMARKS,ATTACHEMENTS,ENABLED,
        APPROVE_STATUS,APPROVER,APPROVE_TIME,CREATE_TIME,UPDATE_TIME,
        CREATE_ID,UPDATE_ID,TENANT_ID,DATA_HIERARCHY_ID,APPROVE_OPINION,DUTY_PLAN_CODE
    </sql>
    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from B_DUTY_PLAN
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.name)">
                and NAME like concat('%',#{condition.name},'%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                and STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime)<EMAIL>@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
            ORDER BY CREATE_TIME DESC
        </where>
    </select>
    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.inout.duty.DutyPlan">
        SELECT <include refid="Base_Column_List"/> from B_DUTY_PLAN
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.name)">
                and NAME like concat('%',#{condition.name},'%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                and STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime)<EMAIL>@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>


</mapper>
