package com.hxdi.nmjl.mapper.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.base.SupplierCommunicationCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierCommunication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商沟通记录数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-11
 */
@Mapper
public interface SupplierCommunicationMapper extends SuperMapper<SupplierCommunication> {

    /**
     * 修改状态
     *
     * @param id      主键ID
     * @param enabled 状态：0-禁用，1-启用，7-删除
     */
    void changeStatus(@Param("id") String id, @Param("enabled") Integer enabled);

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param condition 查询条件
     * @return 分页结果
     */
    @DataPermission
    Page<SupplierCommunication> selectPageV1(Page<SupplierCommunication> page, @Param("condition") SupplierCommunicationCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表结果
     */
    @DataPermission
    List<SupplierCommunication> selectListV1(@Param("condition") SupplierCommunicationCondition condition);

    /**
     * 批量更新沟通状态
     *
     * @param ids    主键ID列表
     * @param status 沟通状态
     */
    void batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") Integer status);

    /**
     * 根据供应商ID查询沟通记录数量
     *
     * @param supplierId 供应商ID
     * @return 记录数量
     */
    Long countBySupplierId(@Param("supplierId") String supplierId);
}
