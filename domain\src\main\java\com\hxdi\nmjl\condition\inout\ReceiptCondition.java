package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ReceiptCondition extends QueryCondition {

    private Date planStartTime;

    private Date planEndTime;

    @ApiModelProperty(value = "审核状态:0未审核 1已审核 2驳回")
    private String approveStatus;

    @ApiModelProperty(value = "品种id")
    private String catalogId;

}
