package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.InventoryAlarmConfig;

import java.util.List;

public interface InventoryAlarmConfigService extends IBaseService<InventoryAlarmConfig> {
    /**
     * 列表查询
     *
     * @param condition
     * @return
     */
    List<InventoryAlarmConfig> listV1(InventoryAlarmConfig condition);

    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    Page<InventoryAlarmConfig> pageV1(InventoryAlarmConfig condition, Page<InventoryAlarmConfig> page);

    /**
     * 生成预警信息(定时任务接口，请勿直接调用)
     */
    void generateWarningMsg();
}
