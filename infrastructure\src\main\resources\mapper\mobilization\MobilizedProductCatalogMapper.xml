<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.mobilization.MobilizedProductCatalogMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.mobilization.MobilizedProductCatalog">
        <!--@mbg.generated-->
        <!--@Table B_MOBILIZED_PRODUCT_CATALOG-->
        <id column="ID" property="id" />
        <result column="CATEGORY_ID" property="categoryId" />
        <result column="CATALOG_CODE" property="catalogCode" />
        <result column="CATALOG_NAME" property="catalogName" />
        <result column="SPECIFICATION" property="specification" />
        <result column="UNIT" property="unit" />
        <result column="ENABLED" property="enabled" />
        <result column="STATE" property="state" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, CATEGORY_ID, CATALOG_CODE, "CATALOG_NAME", SPECIFICATION, UNIT, ENABLED, "STATE",
        CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from B_MOBILIZED_PRODUCT_CATALOG
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                and (CATALOG_NAME like concat('%',#{condition.keywords},'%') or CATALOG_CODE like concat('%',#{condition.keywords},'%'))
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                and STATE = #{condition.state}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.categoryId)">
                and CATEGORY_ID = #{condition.categoryId}
            </if>
        </where>
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from B_MOBILIZED_PRODUCT_CATALOG
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                and (CATALOG_NAME like concat('%',#{condition.keywords},'%') or CATALOG_CODE like concat('%',#{condition.keywords},'%'))
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                and STATE = #{condition.state}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.categoryId)">
                and CATEGORY_ID = #{condition.categoryId}
            </if>
        </where>
    </select>
</mapper>
