package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.ContractSettlement;

/**
 * 合同结算服务接口
 */
public interface ContractSettlementService extends IBaseService<ContractSettlement> {

    /**
     * 创建合同结算
     *
     * @param contractSettlement 合同结算实体
     */
    void create(ContractSettlement contractSettlement);

    /**
     * 提交合同结算
     *
     * @param contractSettlementId 结算ID
     * @param orderId              订单ID字符串 ','分隔
     */
    void submit(String contractSettlementId, String orderId);

    /**
     * 获取合同结算信息
     * @param contractId
     * @return
     */
    ContractSettlement get(String contractId);

}
