<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.StoreHouseMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.StoreHouse">
        <!--@mbg.generated-->
        <!--@Table C_STORE_HOUSE-->
        <id column="ID" property="id"/>
        <result column="STORE_ID" property="storeId"/>
        <result column="NAME" property="name"/>
        <result column="ST_CODE" property="stCode"/>
        <result column="ST_TYPE" property="stType"/>
        <result column="APPLY_TYPE" property="applyType"/>
        <result column="CAPACITY" property="capacity"/>
        <result column="UNIT" property="unit"/>
        <result column="BUILD_DATE" property="buildDate"/>
        <result column="BUILD_TYPE" property="buildType"/>
        <result column="HEIGHTS" property="heights"/>
        <result column="WIDTHS" property="widths"/>
        <result column="LENGTHS" property="lengths"/>
        <result column="STATE" property="state"/>
        <result column="REMARKS" property="remarks"/>
        <result column="ENABLED" property="enabled"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        STORE_ID,
        "NAME",
        ST_CODE,
        ST_TYPE,
        APPLY_TYPE,
        CAPACITY,
        UNIT,
        BUILD_DATE,
        BUILD_TYPE,
        HEIGHTS,
        WIDTHS,
        LENGTHS,
        "STATE",
        REMARKS,
        ENABLED,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM C_STORE_HOUSE
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stType)">
                AND ST_TYPE = #{condition.stType}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM C_STORE_HOUSE
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stType)">
                AND ST_TYPE = #{condition.stType}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>

    <select id="getCapacityByStoreId" resultType="com.hxdi.nmjl.dto.base.StoreCapacityDTO">
        SELECT STORE_ID, ROUND(SUM(CAPACITY) / 1000, 3) as CAPACITY
        FROM C_STORE_HOUSE
        <where>
            <if test="storeId != null and storeId != ''">
                AND STORE_ID IN
                <foreach item="item" index="index" collection="storeId" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by store_id
    </select>
</mapper>
