package com.hxdi.nmjl.service.inout.delivery.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryEvent;
import com.hxdi.nmjl.mapper.inout.delivery.DeliveryEventMapper;
import com.hxdi.nmjl.service.inout.delivery.DeliveryEventService;
import com.hxdi.nmjl.condition.inout.DeliveryEventCondition;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 配送事件记录Service实现类
 *
 * <AUTHOR>
 * @since 2025/4/23 11:11
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class DeliveryEventServiceImpl extends BaseServiceImpl<DeliveryEventMapper, DeliveryEvent> implements DeliveryEventService {

    @Override
    public void saveOrUpdateV1(DeliveryEvent deliveryEvent) {
        if (CommonUtils.isEmpty(deliveryEvent.getId())) {
            verifyEvent(deliveryEvent);
            baseMapper.insert(deliveryEvent);
        } else {
            baseMapper.updateById(deliveryEvent);
        }
    }


    @Override
    public DeliveryEvent getDetail(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Page<DeliveryEvent> pages(DeliveryEventCondition condition) {
        Page<DeliveryEvent> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<DeliveryEvent> lists(DeliveryEventCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void remove(String id) {
        DeliveryEvent deliveryEvent = new DeliveryEvent();
        deliveryEvent.setId(id);
        deliveryEvent.setEnabled(StrPool.State.DISABLE);
        baseMapper.updateById(deliveryEvent);
    }

    private void verifyEvent(DeliveryEvent deliveryEvent) {
        Long count = baseMapper.selectCount(Wrappers.<DeliveryEvent>lambdaQuery()
                .eq(DeliveryEvent::getDeliveryId, deliveryEvent.getDeliveryId())
                .eq(DeliveryEvent::getVehicleNo, deliveryEvent.getVehicleNo())
                .eq(DeliveryEvent::getEnabled, 1));
        if (count > 0) {
            BizExp.pop("该调度沟通记录已经存在");
        }
    }
}
