package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.plan.PackageApplyCondition;
import com.hxdi.nmjl.domain.plan.ProductionPackageApply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductionPackageApplyMapper extends SuperMapper<ProductionPackageApply> {

    @DataPermission
    List<ProductionPackageApply> listV1(@Param("condition") PackageApplyCondition condition);

    @DataPermission
    Page<ProductionPackageApply> pageV1(Page<ProductionPackageApply> page, @Param("condition") PackageApplyCondition condition);
}