package com.hxdi.nmjl.condition.portal;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ResourceCondition extends QueryCondition {

    /**
     * 栏目id
     */
    private String categoryId;
    private String categoryPath;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布状态：0-否，1-是
     */
    private Integer publishState;

    @ApiModelProperty(value="是否公开:0-否，1-是")
    private Integer publicIs;

    /**
     * 是否轮播展示：0-否，1-是
     */
    @ApiModelProperty(value="是否轮播展示：0-否，1-是")
    private Integer displayIs;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
