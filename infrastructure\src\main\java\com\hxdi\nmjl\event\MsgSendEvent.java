package com.hxdi.nmjl.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @program: nmjl-service
 * @description: 消息发送事件
 * @author: 王贝强
 * @create: 2025-07-08 19:51
 */
@Getter
public class MsgSendEvent extends ApplicationEvent {
    private final List<MsgPayload> payload;

    public MsgSendEvent(Object source, List<MsgPayload> payload) {
        super(source);
        this.payload = payload;
    }
}
