package com.hxdi.nmjl.domain.common;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 系统条码
 * @author: 王贝强
 * @create: 2025-08-20 14:02
 */
@Getter
@Setter
@Api(tags = "系统条码")
@TableName(value = "B_BAR_CODE")
public class BarCode implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField(value = "OPEN_ID")
    @ApiModelProperty(value = "二维码对应的OpenId，用于对外提供扫码支持")
    private String openId;

    @ApiModelProperty(value = "条码类型 见BarCodeEnum枚举")
    @TableField(value = "BAR_CODE_TYPE")
    private String barCodeType;

    @ApiModelProperty(value = "条码： base64图片信息")
    @TableField(value = "BAR_CODE")
    private String barCode;

    @ApiModelProperty(value = "业务ID")
    @TableField(value = "BUSINESS_ID")
    private String businessId;

    @ApiModelProperty(value = "业务类型")
    @TableField(value = "BUSINESS_TYPE")
    private String businessType;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    @ApiModelProperty(value = "租户id")
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

}
