<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ProductionOrderTraceMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.ProductionOrderTrace">
    <!--@mbg.generated-->
    <!--@Table B_PRODUCTION_ORDER_TRACE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="ORDER_ITEM_ID" jdbcType="VARCHAR" property="orderItemId" />
    <result column="REPORT_TIME" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="REPORTER" jdbcType="VARCHAR" property="reporter" />
    <result column="PRODUCT_DATE" jdbcType="TIMESTAMP" property="productDate" />
    <result column="TOTAL_PACK_QTY" jdbcType="INTEGER" property="totalPackQty" />
    <result column="TOTAL_QTY" jdbcType="DECIMAL" property="totalQty" />
    <result column="BATCH_STATE" jdbcType="INTEGER" property="batchState" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ORDER_ID, ORDER_ITEM_ID, REPORT_TIME, REPORTER, PRODUCT_DATE, TOTAL_PACK_QTY, 
    TOTAL_QTY, BATCH_STATE, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, 
    DATA_HIERARCHY_ID
  </sql>
</mapper>