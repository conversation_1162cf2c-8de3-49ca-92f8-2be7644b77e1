package com.hxdi.nmjl.condition.mobilization;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "动员商品信息查询条件")
@Getter
@Setter
public class MobilizedProductCondition extends QueryCondition {

    @ApiModelProperty(value = "动员企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "关键字")
    private String keywords;

    @ApiModelProperty(value = "质量等级：字典：YZLDJ/LZLDJ")
    private String grade;

    @ApiModelProperty(value = "状态：0-未确认，1-已确认")
    private Integer state;

}
