package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.BrandAuthorizing;
import com.hxdi.nmjl.condition.plan.BrandAuthorizingCondition;


import java.time.LocalDateTime;
import java.util.List;

/**
 * 品牌授权服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/7 10:30
 */
public interface BrandAuthorizingService extends IBaseService<BrandAuthorizing> {

    /**
     * 新增品牌授权
     * @param authorizing 品牌授权实体
     */
    void create(BrandAuthorizing authorizing);

    /**
     * 修改品牌授权
     * @param authorizing 品牌授权实体
     */
    void update(BrandAuthorizing authorizing);

    /**
     * 获取品牌授权详情
     * @param authorizeId 授权ID
     * @return 品牌授权实体
     */
    BrandAuthorizing getDetail(String authorizeId);

    /**
     * 分页查询品牌授权
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<BrandAuthorizing> pages(BrandAuthorizingCondition condition);

    /**
     * 列表查询品牌授权
     * @param condition 查询条件
     * @return 品牌授权列表
     */
    List<BrandAuthorizing> lists(BrandAuthorizingCondition condition);

    /**
     * 审批品牌授权（状态变更）
     * @param authorizeId 授权ID
     * @param approveStatus 审批状态
     * @param opinion 审批意见
     */
    void approve(String authorizeId, Integer approveStatus, String opinion);

    /**
     * 删除品牌授权
     * @param authorizeId 授权ID
     */
    void remove(String authorizeId);


    /**
     * 变更授权状态为无效
     * @param authorizeId 授权ID
     */
    void updateInvalidState(String authorizeId);

    /**
     * 定时检查授权状态并变更
     * @param today 当前时间
     */
    void checkAndUpdateExpiredState(LocalDateTime today);


    void submit(String authorizeId);
}