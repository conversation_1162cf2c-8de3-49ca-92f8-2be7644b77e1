package com.hxdi.nmjl.condition.emergency;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel(description="紧急任务项查询条件")
@Getter
@Setter
public class EmergencyTaskItemCondition extends QueryCondition {

    @ApiModelProperty(value="调度id")
    private String schedulieId;

    @ApiModelProperty(value="任务id")
    private String taskId;

    @ApiModelProperty(value="品种id")
    private String catalogId;

    @ApiModelProperty(value="规格")
    private String specification;

    @ApiModelProperty(value="质量等级")
    private String grade;

}
