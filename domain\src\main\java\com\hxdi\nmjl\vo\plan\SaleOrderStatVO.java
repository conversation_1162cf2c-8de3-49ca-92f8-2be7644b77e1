package com.hxdi.nmjl.vo.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: nmjl-service
 * @description: 销售订单统计
 * @author: 王贝强
 * @create: 2025-08-29 15:55
 */
@Setter
@Getter
@ApiModel(description = "销售订单统计VO")
public class SaleOrderStatVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单总数
     */
    @ApiModelProperty(value = "订单总数")
    private Integer orderCount;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal orderAmount;

    /**
     * 订单最多的来源
     */
    @ApiModelProperty(value = "订单最多的来源")
    private String popularSource;

    /**
     * 订单最多的品类
     */
    @ApiModelProperty(value = "订单最多的品类")
    private String popularCatalog;

}
