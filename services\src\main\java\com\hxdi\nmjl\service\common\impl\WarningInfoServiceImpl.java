package com.hxdi.nmjl.service.common.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.OrganizationCondition;
import com.hxdi.nmjl.condition.common.WarningInfoCondition;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.common.WarningInfo;
import com.hxdi.nmjl.mapper.common.WarningInfoMapper;
import com.hxdi.nmjl.service.base.OrganizationService;
import com.hxdi.nmjl.service.bigscreen.InfoAreaService;
import com.hxdi.nmjl.service.common.WarningInfoService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WarningInfoServiceImpl extends BaseServiceImpl<WarningInfoMapper, WarningInfo> implements WarningInfoService {

    @Resource
    @Lazy
    private InfoAreaService infoAreaService;

    @Resource
    @Lazy
    private OrganizationService organizationService;

    @Override
    public Page<WarningInfo> getPageByCondition(WarningInfoCondition condition) {
        return baseMapper.getPageByCondition(condition.newPage(), condition);
    }

    @Override
    public List<WarningInfo> getListByCondition(WarningInfoCondition condition) {
        return baseMapper.getListByCondition(condition);
    }

    @Override
    public Page<WarningInfo> getBigScreenPageByCondition(WarningInfoCondition condition) {
        if (condition.getStoreId() == null && CommonUtils.isNotEmpty(condition.getAreaCode())) {
            //先根据地区编码查询出对应的军供站id
            List<String> areaCodes = infoAreaService.getAllChildAreaCodes(condition.getAreaCode());
            areaCodes.add(condition.getAreaCode());
            OrganizationCondition organizationCondition = new OrganizationCondition();
            organizationCondition.setOrgType(2);
            organizationCondition.setAreaCode(String.join(",", areaCodes));
            List<Organization> organizationList = organizationService.lists(organizationCondition);
            List<String> storeIds = organizationList.stream().map(Organization::getId).collect(Collectors.toList());
            condition.setStoreId(String.join(",", storeIds));
        } else if (condition.getStoreId() == null && condition.getAreaCode() == null) {
            //错误的查询条件，直接返回空
            return new Page<>();
        }
        //根据军供站id查询出对应的告警信息
        return this.getPageByCondition(condition);
    }

    @Override
    public void handle(String id, String disposeDesc) {
        BaseUserDetails user = SecurityHelper.obtainUser();
        WarningInfo warningInfo = baseMapper.selectById(id);
        warningInfo.setDisposeState(1);
        warningInfo.setDisposeDesc(disposeDesc);
        warningInfo.setUserId(user.getUserId());
        warningInfo.setUserName(user.getNickName());
        warningInfo.setDisposeTime(new Date());

        baseMapper.updateById(warningInfo);
        //已读对应的消息
        //todo
    }


}
