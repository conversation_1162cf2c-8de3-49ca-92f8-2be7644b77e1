package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inventory.LocationCard;
import com.hxdi.nmjl.condition.inventory.LocationCardCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LocationCardMapper extends SuperMapper<LocationCard> {
    @DataPermission
    List<LocationCard> getList(@Param("condition") LocationCardCondition condition);
    @DataPermission
    Page<LocationCard> getPage(@Param("condition") LocationCardCondition condition,@Param("page") Page<LocationCard> page);
}