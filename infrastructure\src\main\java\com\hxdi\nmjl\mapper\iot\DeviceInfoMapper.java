package com.hxdi.nmjl.mapper.iot;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.iot.DeviceInfo;
import com.hxdi.nmjl.condition.iot.DeviceInfoCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceInfoMapper extends BaseMapper<DeviceInfo>, SuperMapper<DeviceInfo> {

    @DataPermission
    List<DeviceInfo> getList(@Param("condition") DeviceInfoCondition condition);

    @DataPermission
    Page<DeviceInfo> getPage(@Param("condition") DeviceInfoCondition condition,@Param("page") Page<DeviceInfo> page);
}