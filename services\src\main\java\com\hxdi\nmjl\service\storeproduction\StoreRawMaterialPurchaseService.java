package com.hxdi.nmjl.service.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.storeproduction.StoreRawMaterialPurchaseCondition;
import com.hxdi.nmjl.domain.storeproduction.StoreRawMaterialPurchase;


import java.util.List;

/**
 * 原料采购服务接口
 *
 * <AUTHOR>
 */
public interface StoreRawMaterialPurchaseService extends IBaseService<StoreRawMaterialPurchase> {

    /**
     * 创建原料采购记录
     *
     * @param purchase 原料采购实体
     */
    void create(StoreRawMaterialPurchase purchase);

    /**
     * 更新原料采购记录
     *
     * @param purchase 原料采购实体
     */
    void update(StoreRawMaterialPurchase purchase);

    /**
     * 获取原料采购详情
     *
     * @param id 采购记录ID
     * @return 原料采购实体
     */
    StoreRawMaterialPurchase getDetail(String id);

    /**
     * 分页查询原料采购记录
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<StoreRawMaterialPurchase> pages(StoreRawMaterialPurchaseCondition condition);

    /**
     * 列表查询原料采购记录
     *
     * @param condition 查询条件
     * @return 采购记录列表
     */
    List<StoreRawMaterialPurchase> lists(StoreRawMaterialPurchaseCondition condition);

    /**
     * 删除原料采购记录（逻辑删除）
     *
     * @param id 采购记录ID
     */
    void remove(String id);
}
