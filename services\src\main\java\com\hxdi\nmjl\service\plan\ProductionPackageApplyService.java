package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.plan.PackageApplyCondition;
import com.hxdi.nmjl.domain.plan.ProductionPackageApply;

import java.util.List;

public interface ProductionPackageApplyService extends IBaseService<ProductionPackageApply> {
    /**
     * 保存包装申请
     * @param apply 包装申请
     */
    void saveV1(ProductionPackageApply apply);

    /**
     * 更新包装申请
     * @param apply 包装申请
     */
    void updateV1(ProductionPackageApply apply);

    /**
     * 删除包装申请
     * @param id 包装申请ID
     */
    void removeV1(String id);

    /**
     * 审核包装申请
     * @param id 包装申请ID
     * @param approveStatus 审核状态
     * @param opinion 审核意见
     */
    void approveV1(String id, Integer approveStatus, String opinion);

    /**
     * 提交包装申请
     * @param id 包装申请ID
     */
    void submitV1(String id);

    /**
     * 获取包装申请列表
     * @param condition 查询条件
     * @return 包装申请列表
     */
    List<ProductionPackageApply> listV1(PackageApplyCondition condition);

    /**
     * 获取包装申请分页列表
     * @param condition 查询条件
     * @return 包装申请分页列表
     */
    Page<ProductionPackageApply> pageV1(PackageApplyCondition condition);


}
