<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.storeproduction.StoreRawMaterialPurchaseMapper">

    <sql id="Base_Column_List">
        ID, PLAN_NO, MATERIAL_NAME, SPECIFICATION, UNIT, qty, price,
        SUPPLIER_NAME, PURCHASE_DATE, STORE_ID, STORE_NAME, REMARK,
        ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID,
        TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.storeproduction.StoreRawMaterialPurchase">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo"/>
        <result column="MATERIAL_NAME" jdbcType="VARCHAR" property="materialName"/>
        <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification"/>
        <result column="UNIT" jdbcType="VARCHAR" property="unit"/>
        <result column="qty" jdbcType="DECIMAL" property="qty"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName"/>
        <result column="PURCHASE_DATE" jdbcType="DATE" property="purchaseDate"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_RAW_MATERIAL_PURCHASE
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.id)">
                AND ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planNo)">
                AND PLAN_NO LIKE CONCAT('%', #{condition.planNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.materialName)">
                AND MATERIAL_NAME LIKE CONCAT('%', #{condition.materialName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeName)">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_STORE_RAW_MATERIAL_PURCHASE
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.id)">
                AND ID = #{condition.id}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planNo)">
                AND PLAN_NO LIKE CONCAT('%', #{condition.planNo}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.materialName)">
                AND MATERIAL_NAME LIKE CONCAT('%', #{condition.materialName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeName)">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.tenantId)">
                AND TENANT_ID = #{condition.tenantId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                AND DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
