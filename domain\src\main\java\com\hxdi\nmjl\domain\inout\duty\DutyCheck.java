package com.hxdi.nmjl.domain.inout.duty;

import java.io.Serializable;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
*
* 值班查岗信息
 */
@ApiModel(description = "值班查岗信息")
@TableName("B_DUTY_CHECK")
@Getter
@Setter
public class DutyCheck implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("DUTY_PLAN_ID")
    @ApiModelProperty(value = "计划ID")
    private String dutyPlanId;

    @TableField("DUTY_NAME")
    @ApiModelProperty(value = "计划名称")
    private String dutyName;

    @TableField("STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @TableField("STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @TableField("START_TIME")
    @ApiModelProperty(value = "查看时间")
    private Date startTime;

    @TableField("CHECK_WAY")
    @ApiModelProperty(value = "查岗方式")
    private String checkWay;

    @TableField("AREA")
    @ApiModelProperty(value = "地点")
    private String area;

    @TableField("EMP_NAME")
    @ApiModelProperty(value = "人员姓名")
    private String empName;

    @TableField("REMARKS")
    @ApiModelProperty(value = "记录")
    private String remarks;

    @TableField("REQUIREMENT")
    @ApiModelProperty(value = "整改要求")
    private String requirement;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(value = "CREATE_ID",fill= FieldFill.INSERT)
    @ApiModelProperty(value = "创建ID")
    private String createId;

    @TableField(value = "UPDATE_ID",fill= FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新ID")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID",fill= FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    /**
     * --------------非数据库字段-------------
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "值班计划编号")
    private String dutyPlanCode;

}
