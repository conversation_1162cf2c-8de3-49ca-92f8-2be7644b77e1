<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryLogMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryLog">
    <!--@mbg.generated-->
    <!--@Table B_INVENTORY_LOG-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="INVENTORY_ID" jdbcType="VARCHAR" property="inventoryId" />
    <result column="MAIN_BIZ_ID" jdbcType="VARCHAR" property="mainBizId" />
    <result column="SUB_BIZ_ID" jdbcType="VARCHAR" property="subBizId" />
    <result column="BIZ_TYPE" jdbcType="VARCHAR" property="bizType" />
    <result column="INOUT_TYPE" jdbcType="VARCHAR" property="inoutType" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
    <result column="ST_ID" jdbcType="VARCHAR" property="stId" />
    <result column="LOC_ID" jdbcType="VARCHAR" property="locId" />
    <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId" />
    <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
    <result column="SPECIFICATIONS" jdbcType="VARCHAR" property="specifications" />
    <result column="GRADE" jdbcType="VARCHAR" property="grade" />
    <result column="RESERVE_LEVEL" jdbcType="INTEGER" property="reserveLevel" />
    <result column="PROVINCE" jdbcType="VARCHAR" property="province" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="COUNTY" jdbcType="VARCHAR" property="county" />
    <result column="PRODUCTION_DATE" jdbcType="TIMESTAMP" property="productionDate" />
    <result column="MANAGE_UNIT_ID" jdbcType="VARCHAR" property="manageUnitId" />
    <result column="CHANGE_QTY" jdbcType="DECIMAL" property="changeQty" />
    <result column="BEFORE_INVENTORY_QTY" jdbcType="DECIMAL" property="beforeInventoryQty" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INVENTORY_ID, MAIN_BIZ_ID, SUB_BIZ_ID, BIZ_TYPE, INOUT_TYPE, STORE_ID, ST_ID,
    LOC_ID, CATALOG_ID, "CATALOG_NAME", SPECIFICATIONS, GRADE, RESERVE_LEVEL, PROVINCE,
    CITY, COUNTY, PRODUCTION_DATE, MANAGE_UNIT_ID, CHANGE_QTY, BEFORE_INVENTORY_QTY,
    PRICE, ENABLED, CREATE_TIME, CREATE_ID, TENANT_ID, DATA_HIERARCHY_ID
  </sql>

</mapper>
