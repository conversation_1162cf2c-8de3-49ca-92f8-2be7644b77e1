package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.BrandAuthorizing;
import com.hxdi.nmjl.condition.plan.BrandAuthorizingCondition;
import org.apache.ibatis.annotations.Param;


import java.time.LocalDateTime;
import java.util.List;

public interface BrandAuthorizingMapper extends SuperMapper<BrandAuthorizing> {

    Page<BrandAuthorizing> selectPageV1(Page<BrandAuthorizing> page, @Param("condition") BrandAuthorizingCondition condition);

    List<BrandAuthorizing> selectListV1(@Param("condition") BrandAuthorizingCondition condition);

    void checkAndUpdateExpiredState(LocalDateTime date);
}
