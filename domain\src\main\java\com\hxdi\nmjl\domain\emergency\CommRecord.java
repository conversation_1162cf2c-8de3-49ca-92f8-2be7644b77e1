package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 《应急粮油供需信息沟通记录》主表
 */
@ApiModel(description = "应急粮油供需信息沟通记录表")
@TableName("B_COMM_RECORD")
@Getter
@Setter
public class CommRecord extends BModel implements Serializable {


    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 沟通主题
     */
    @TableField("TITLE")
    @ApiModelProperty(value = "沟通主题")
    private String title;

    /**
     * 沟通类型
     */
    @TableField("COMM_TYPE")
    @ApiModelProperty(value = "沟通类型")
    private String commType;

    /**
     * 关联方
     */
    @TableField("RELATED_PARTIES")
    @ApiModelProperty(value = "关联方")
    private String relatedParties;

    /**
     * 沟通日期
     */
    @TableField("RECORD_DATE")
    @ApiModelProperty(value = "沟通日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date recordDate;

    /**
     * 记录状态
     */
    @TableField("RECORD_STATUS")
    @ApiModelProperty(value = "记录状态")
    private Integer recordStatus;

    /**
     * 沟通内容
     */
    @TableField("CONTENT")
    @ApiModelProperty(value = "沟通内容")
    private String content;

    /**
     * 启用状态(1-启用，0-禁用)
     */
    @TableField("ENABLED")
    @ApiModelProperty(value = "启用状态(1-启用，0-禁用)")
    private Integer enabled;


}
