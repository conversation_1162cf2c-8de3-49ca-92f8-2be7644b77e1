<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.InspectionPlanMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.InspectionPlan">
        <!--@mbg.generated-->
        <!--@Table B_INSPECTION_PLAN-->
        <id column="ID" property="id" />
        <result column="PLAN_CODE" property="planCode" />
        <result column="PLAN_NAME" property="planName" />
        <result column="INSPECT_UNIT_ID" property="inspectUnitId" />
        <result column="INSPECT_UNIT_NAME" property="inspectUnitName" />
        <result column="PURPOSE" property="purpose" />
        <result column="INSPECTORS" property="inspectors" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="STATE" property="state" />
        <result column="ENABLED" property="enabled" />
        <result column="ATTACHMENTS" property="attachments" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
        <result column="INSPECT_TYPE" property="inspectType" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, PLAN_CODE, PLAN_NAME, INSPECT_UNIT_ID, INSPECT_UNIT_NAME, PURPOSE, INSPECTORS,
        START_TIME, END_TIME, "STATE", ENABLED, ATTACHMENTS, CREATE_TIME, UPDATE_TIME, CREATE_ID,
        UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, INSPECT_TYPE
    </sql>

</mapper>
