package com.hxdi.nmjl.service.emergency;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.emergency.EmergencyLoss;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStat;

import java.util.List;

/**
 * <应急损耗统计服务接口>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:10
 */
public interface EmergencyLossStatService extends IBaseService<EmergencyLossStat> {


    /**
     * <更新统计数据>
     * @param stat
     */
    void updateStat(EmergencyLossStat stat);

    /**
     * <变更状态: 已锁定>
     * @param statId
     */
    void lockState(String statId);

    /**
     * <变更状态: 待锁定>
     * @param statId
     */
    void unlockState(String statId);

    /**
     * <根据库点ID查询统计列表>
     * @param storeId
     * @return
     */
    List<EmergencyLossStat> getListByStoreId(String storeId);

    /**
     * <根据统计ID关联查询损耗明细列表>
     * @param statId
     * @return
     */
    List<EmergencyLoss> getLossDetail(String statId);
}
