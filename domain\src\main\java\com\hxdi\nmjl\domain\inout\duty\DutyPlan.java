package com.hxdi.nmjl.domain.inout.duty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
*
*
 */
@ApiModel(description = "值班计划")
@Getter
@Setter
@TableName("B_DUTY_PLAN")
public class DutyPlan implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("NAME")
    @ApiModelProperty(value = "计划名称")
    private String name;

    @TableField("STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @TableField("STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @TableField("PLAN_START_TIME")
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;

    @TableField("PLAN_END_TIME")
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;

    @TableField("FZR")
    @ApiModelProperty(value = "负责人")
    private String fzr;

    @TableField("MOBILE")
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    @TableField("REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableField("ATTACHEMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态：1-有效，0-删除")
    private Integer enabled;

    @TableField("APPROVE_STATUS")
    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回，3-已下发")
    private Integer approveStatus;

    @TableField("APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    @TableField("APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    @TableField("APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;

    @TableField("DUTY_PLAN_CODE")
    @ApiModelProperty(value = "值班计划编号")
    private String dutyPlanCode;



    /**
     * -----------------------------以下非实体属性
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "值班人员")
    private List<DutyEmp> dutyEmpList;


}
