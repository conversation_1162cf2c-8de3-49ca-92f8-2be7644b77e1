package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.base.TrademarkCondition;
import com.hxdi.nmjl.domain.base.Trademarks;
import com.hxdi.nmjl.service.base.TrademarksService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "品牌管理")
@RestController
@RequestMapping("/trademark")
public class TrademarksController extends BaseController<TrademarksService, Trademarks> {

    @ApiOperation("保存更新")
    @PostMapping("/saveOrUpdates")
    public ResultBody<Void> saveOrUpdates(@RequestBody Trademarks trademark) {
        bizService.saveOrUpdates(trademark);
        return ResultBody.OK();
    }

    @ApiOperation("删除")
    @DeleteMapping("/remove")
    public ResultBody<Void> remove(@RequestParam("id") String brandId) {
        bizService.remove(brandId);
        return ResultBody.OK();
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<Trademarks>> pages(TrademarkCondition condition) {
        return ResultBody.<Page<Trademarks>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<Trademarks>> lists(TrademarkCondition condition) {
        return ResultBody.<List<Trademarks>>OK().data(bizService.lists(condition));
    }
}
