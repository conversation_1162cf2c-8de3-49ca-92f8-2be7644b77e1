package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 应急预案物资项
 */
@ApiModel(description = "应急预案物资项信息")
@Getter
@Setter
@TableName("B_EMERGENCY_RESPONSE_ITEM") // 表名映射
public class EmergencyResponseItem implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("RESPONSE_ID")
    @ApiModelProperty(value = "预案ID")
    private String responseId;

    @TableField("CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    @TableField("CATALOG_NAME")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    @TableField("SPECIFICATION")
    @ApiModelProperty(value = "规格")
    private String specification;

    @TableField("GRADE")
    @ApiModelProperty(value = "质量等级")
    private String grade;

    @TableField("PLAN_QTY")
    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQty;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
