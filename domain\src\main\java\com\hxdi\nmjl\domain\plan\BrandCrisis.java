package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 品牌危机管理
 */
@Getter
@Setter
@TableName(value = "B_BRAND_CRISIS")
public class BrandCrisis implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 品牌id
     */
    @TableField(value = "BRAND_ID")
    private String brandId;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND_NAME")
    private String brandName;

    /**
     * 事件编号：YQYYYYMMDD0001
     */
    @TableField(value = "EVENT_NO")
    private String eventNo;

    /**
     * 事件标题
     */
    @TableField(value = "EVENT_TITLE")
    private String eventTitle;

    /**
     * 舆情类型：字典PPYQLX
     */
    @TableField(value = "EVNET_TYPE")
    private String evnetType;

    /**
     * 传播方式：字典PPCBFS
     */
    @TableField(value = "PROPAGATION_MODE")
    private String propagationMode;

    /**
     * 预警级别：字典PPYJJB
     */
    @TableField(value = "EVENT_LEVEL")
    private Integer eventLevel;

    /**
     * 舆情描述
     */
    @TableField(value = "CRISIS_DESC")
    private String crisisDesc;

    /**
     * 处置记录
     */
    @TableField(value = "DEAL_DESC")
    private String dealDesc;

    /**
     * 状态（1-有效 0删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    private String attachments;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}