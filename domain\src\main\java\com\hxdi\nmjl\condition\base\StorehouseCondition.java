package com.hxdi.nmjl.condition.base;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "仓房查询条件")
public class StorehouseCondition extends QueryCondition {

    @ApiModelProperty(value = "军供站ID ','分隔")
    private String storeId;

    @ApiModelProperty(value = "仓房类型")
    private String stType;

}
