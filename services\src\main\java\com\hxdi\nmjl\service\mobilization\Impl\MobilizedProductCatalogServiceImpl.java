package com.hxdi.nmjl.service.mobilization.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCatalogCondition;
import com.hxdi.nmjl.domain.mobilization.MobilizedProductCatalog;
import com.hxdi.nmjl.mapper.mobilization.MobilizedProductCatalogMapper;
import com.hxdi.nmjl.service.mobilization.MobilizedProductCatalogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MobilizedProductCatalogServiceImpl extends BaseServiceImpl<MobilizedProductCatalogMapper, MobilizedProductCatalog> implements MobilizedProductCatalogService {

    @Override
    public Page<MobilizedProductCatalog> pages(MobilizedProductCatalogCondition condition) {
        Page<MobilizedProductCatalog> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<MobilizedProductCatalog> lists(MobilizedProductCatalogCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void create(MobilizedProductCatalog catalog) {
        verify(catalog, 0);
        catalog.setState(0);
        baseMapper.insert(catalog);
    }

    @Override
    public void update(MobilizedProductCatalog catalog) {
        verify(catalog, 1);
        baseMapper.updateById(catalog);
    }

    private void verify(MobilizedProductCatalog catalog, int expectedCount) {
        long count = baseMapper.selectCount(Wrappers.<MobilizedProductCatalog>lambdaQuery()
                .eq(CommonUtils.isNotEmpty(catalog.getId()), MobilizedProductCatalog::getId, catalog.getId())
                .or()
                .eq(MobilizedProductCatalog::getCatalogCode, catalog.getCatalogCode()));
        if (count > expectedCount) {
            BizExp.pop("品种编码不能重复");
        }
    }

    @Override
    public void remove(String id) {
        MobilizedProductCatalog catalog = baseMapper.selectById(id);
        catalog.setEnabled(0);
        this.updateById(catalog);
    }

    @Override
    public MobilizedProductCatalog getDetail(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public void approve(String id) {
        MobilizedProductCatalog catalog = baseMapper.selectById(id);
        catalog.setState(1);
        this.updateById(catalog);
    }
}
