<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.plan.BidInfoMapper">
    <resultMap id="BidInfoResultMap" type="com.hxdi.nmjl.domain.plan.BidInfo">
        <id property="id" column="ID" />
        <result property="projectName" column="PROJECT_NAME" />
        <result property="year" column="YEAR" />
        <result property="planId" column="PLAN_ID" />
        <result property="planCode" column="PLAN_CODE" />
        <result property="unitId" column="UNIT_ID" />
        <result property="unitName" column="UNIT_NAME" />
        <result property="bidStartTime" column="BID_START_TIME" />
        <result property="bidEndTime" column="BID_END_TIME" />
        <result property="supplyStartTime" column="SUPPLY_START_TIME" />
        <result property="supplyEndTime" column="SUPPLY_END_TIME" />
        <result property="bidType" column="BID_TYPE" />
        <result property="clientId" column="CLIENT_ID" />
        <result property="clientName" column="CLIENT_NAME" />
        <result property="tradeTime" column="TRADE_TIME" />
        <result property="budgetAmount" column="BUDGET_AMOUNT" />
        <result property="tradeDeposit" column="TRADE_DEPOSIT" />
        <result property="performanceBondAmount" column="PERFORMANCE_BOND_AMOUNT" />
        <result property="state" column="STATE" />
        <result property="flowState" column="FLOW_STATE" />
        <result property="notes" column="NOTES" />
        <result property="bidDesc" column="BID_DESC" />
        <result property="judgeDesc" column="JUDGE_DESC" />
        <result property="zbgg" column="ZBGG" />
        <result property="zbwj" column="ZBWJ" />
        <result property="pbjl" column="PBJL" />
        <result property="attachments" column="ATTACHMENTS" />
        <result property="enabled" column="ENABLED" />
        <result property="approveStatus" column="APPROVE_STATUS" />
        <result property="approver" column="APPROVER" />
        <result property="approveTime" column="APPROVE_TIME" />
        <result property="approveOpinion" column="APPROVE_OPINION" />
        <result property="createTime" column="CREATE_TIME" />
        <result property="updateTime" column="UPDATE_TIME" />
        <result property="createId" column="CREATE_ID" />
        <result property="updateId" column="UPDATE_ID" />
        <result property="tenantId" column="TENANT_ID" />
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, PROJECT_NAME, YEAR, PLAN_ID, UNIT_ID, UNIT_NAME, BID_START_TIME, BID_END_TIME,
        SUPPLY_START_TIME, SUPPLY_END_TIME, BID_TYPE, CLIENT_ID, CLIENT_NAME, TRADE_TIME,
        BUDGET_AMOUNT, TRADE_DEPOSIT, PERFORMANCE_BOND_AMOUNT, STATE, NOTES, ZBGG, ZBWJ,
        PBJL, ATTACHMENTS, ENABLED, APPROVE_STATUS, APPROVER, APPROVE_TIME, APPROVE_OPINION,
        CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, PLAN_CODE
        ,FLOW_STATE, BID_DESC, JUDGE_DESC
    </sql>

    <select id="getPages" parameterType="com.hxdi.nmjl.condition.plan.BidInfoCondition" resultMap="BidInfoResultMap">
        select
        <include refid="Base_Column_List" />
        from B_BID_INFO
        <where>
            enabled = 1
            <if test="condition.planId != null and condition.planId != ''">
                and PLAN_ID=#{condition.planId}
            </if>
            <if test="condition.unitId != null and condition.unitId != ''">
                and UNIT_ID= #{condition.unitId}
            </if>
            <if test="condition.clientId != null and condition.clientId != ''">
                and CLIENT_ID= #{condition.clientId}
            </if>
            <if test="condition.state != null">
                and STATE = #{condition.state}
            </if>
            <if test="condition.flowState != null">
                and FLOW_STATE = #{condition.flowState}
            </if>
            <if test="condition.approveStatus != null and condition.approveStatus != ''">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="condition.bidType != null">
                and BID_TYPE = #{condition.bidType}
            </if>
            <if test="condition.search != null and condition.search != ''">
                and (PROJECT_NAME like concat('%', #{condition.search}, '%') or PLAN_CODE like concat('%', #{condition.search}, '%'))
            </if>
            <if test="condition.startTime != null">
                and BID_START_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and BID_END_TIME &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="getList" parameterType="com.hxdi.nmjl.condition.plan.BidInfoCondition" resultMap="BidInfoResultMap">
        select <include refid="Base_Column_List" />
        from B_BID_INFO
        <where>
            enabled = 1
            <if test="condition.planId != null and condition.planId != ''">
                and PLAN_ID=#{condition.planId}
            </if>
            <if test="condition.unitId != null and condition.unitId != ''">
                and UNIT_ID= #{condition.unitId}
            </if>
            <if test="condition.clientId != null and condition.clientId != ''">
                and CLIENT_ID= #{condition.clientId}
            </if>
            <if test="condition.state != null">
                and STATE = #{condition.state}
            </if>
            <if test="condition.flowState != null">
                and FLOW_STATE = #{condition.flowState}
            </if>
            <if test="condition.bidType != null">
                and BID_TYPE = #{condition.bidType}
            </if>
            <if test="condition.search != null and condition.search != ''">
                and (PROJECT_NAME like concat('%', #{condition.search}, '%') or PLAN_CODE like concat('%', #{condition.search}, '%'))
            </if>
            <if test="condition.approveStatus != null and condition.approveStatus != ''">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="condition.startTime != null">
                and BID_START_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and BID_END_TIME &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>