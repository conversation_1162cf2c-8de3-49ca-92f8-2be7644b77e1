package com.hxdi.nmjl.mapper.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.inout.FuelAnalysisCondition;
import com.hxdi.nmjl.domain.inout.delivery.FuelAnalysis;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【B_FUEL_ANALYSIS(油耗分析报表)】的数据库操作Mapper
 * @createDate 2025-08-05 15:12:43
 * @Entity com.hxdi.nmjl.domain.inout.delivery.BFuelAnalysis
 */
public interface FuelAnalysisMapper extends SuperMapper<FuelAnalysis> {
    /**
     * 分页查询燃料信息
     *
     * @param page      分页对象
     * @param condition 查询条件
     * @return 分页结果
     */
    @DataPermission
    Page<FuelAnalysis> selectPageV1(Page<FuelAnalysis> page, @Param("condition") FuelAnalysisCondition condition);

    /**
     * 列表查询燃料信息"
     *
     * @param condition 查询条件
     * @return 列表结果
     */
    @DataPermission
    List<FuelAnalysis> selectListV1(@Param("condition") FuelAnalysisCondition condition);

    Page<FuelAnalysis> selectPageV2(Page<FuelAnalysis> page, @Param("condition") FuelAnalysisCondition condition);


}




