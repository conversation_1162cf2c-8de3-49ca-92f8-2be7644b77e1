package com.hxdi.nmjl.service.specialproduct;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductApproval;


/**
 * 地方特色产品审批服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/16
 */
public interface SpecialProductApprovalService extends IBaseService<SpecialProductApproval> {

    /**
     * 一级审核处理
     * @param productId 产品ID
     * @param approveStatus 审核状态（0-未审核，1-已审核，2-驳回）
     * @param opinion 审核意见
     */
    void firstLevelApprove(String productId, Integer approveStatus, String opinion);

    /**
     * 二级审核处理
     * @param productId 产品ID
     * @param approveStatus 审核状态（0-未审核，1-已审核，2-驳回）
     * @param opinion 审核意见
     */
    void secondLevelApprove(String productId, Integer approveStatus, String opinion);

    /**
     * 三级审核处理
     * @param productId 产品ID
     * @param approveStatus 审核状态（0-未审核，1-已审核，2-驳回）
     * @param opinion 审核意见
     */
    void thirdLevelApprove(String productId, Integer approveStatus, String opinion);

    /**
     * 根据产品ID查询审批记录
     * @param productId 产品ID
     * @return 审批记录实体
     */
    SpecialProductApproval getByProductId(String productId);


    /**
     * 创建审批记录
     * @param productId 产品ID
     */
    void create(String productId);
}

