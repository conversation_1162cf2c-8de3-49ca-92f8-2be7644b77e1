package com.hxdi.nmjl.condition.base;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "机构信息查询条件")
public class OrganizationCondition extends QueryCondition {

    /**
     * 机构名称，信用代码
     */
    @ApiModelProperty(value = "模糊查询：机构名称、信用代码")
    private String keywords;

    /**
     * 机构类型：1-管理单位，2-军供站
     */
    @ApiModelProperty(value = "机构类型：1-管理单位，2-军供站")
    private Integer orgType;

    @ApiModelProperty(value = "上级机构ID")
    private String pid;

    @ApiModelProperty(value = "组织所在地区编码 ','分隔")
    private String areaCode;

}
