package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CachedBeanCopyUtils;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.base.StorehouseCondition;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.domain.base.StoreLocation;
import com.hxdi.nmjl.domain.bigscreen.BigScreenConfig;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inventory.InventoryBase;
import com.hxdi.nmjl.domain.inventory.LocationCard;
import com.hxdi.nmjl.dto.inventory.InventoryDTO;
import com.hxdi.nmjl.enums.BusinessType;
import com.hxdi.nmjl.enums.InoutBusinessType;
import com.hxdi.nmjl.mapper.inventory.InventoryMapper;
import com.hxdi.nmjl.service.base.StoreHouseService;
import com.hxdi.nmjl.service.bigscreen.InfoAreaService;
import com.hxdi.nmjl.service.inventory.InventoryBaseService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.service.inventory.LocationCardService;
import com.hxdi.nmjl.utils.RedisKeys;
import com.hxdi.nmjl.vo.bigscreen.InventorySumAndCapacityVO;
import com.hxdi.nmjl.vo.inventory.InventoryCoreDataVO;
import com.hxdi.nmjl.vo.inventory.InventoryOverviewItemVO;
import com.hxdi.nmjl.vo.inventory.InventoryOverviewVO;
import com.hxdi.nmjl.vo.inventory.LocationCardDetailResVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 库存服务实现
 *
 * @author: 王贝强
 * @create: 2025-03-10 15:12
 */
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class InventoryServiceImpl extends BaseServiceImpl<InventoryMapper, Inventory> implements InventoryService {

    /**
     * 库存变更锁
     * todo: 建议更换成分布式锁
     */
    private static final Lock lock = new ReentrantLock();

    @Resource
    private InventoryBaseService inventoryBaseService;

    @Resource
    private LocationCardService locationCardService;

    @Resource
    private StoreHouseService storeHouseService;

    @Resource
    private InfoAreaService infoAreaService;

    @Override
    public Page<Inventory> getPageWithGroupByCatalog(InventoryCondition condition) {
        Page<Inventory> page = condition.newPage();
        return baseMapper.selectCatalogInventoryByPage(page, condition);
    }

    @Override
    public List<Inventory> getListWithGroupByCatalog(InventoryCondition condition) {
        return baseMapper.selectCatalogInventoryList(condition);
    }

    @Override
    public List<Inventory> getListWithGroupByClassification(InventoryCondition condition) {
        return baseMapper.selectClassificationInventoryList(condition);
    }

    @Override
    public List<InventoryOverviewVO> getInventoryOverviewList(String storeId) {
        // 查询库存数据
        List<Inventory> inventoryList = getInventoryListByStoreId(storeId);
        if (CollectionUtils.isEmpty(inventoryList)) {
            return Collections.emptyList();
        }

        // 获取仓房仓容映射
        Map<String, BigDecimal> storeHouseCapacityMap = getStoreHouseCapacityMap(storeId);

        // 按仓房ID分组库存数据并转换为VO
        return inventoryList.stream()
                .collect(Collectors.groupingBy(Inventory::getStId))
                .entrySet().stream()
                .map(entry -> buildInventoryOverviewVO(entry.getKey(), entry.getValue(), storeHouseCapacityMap))
                .collect(Collectors.toList());
    }

    /**
     * 根据军供站ID查询库存列表
     */
    private List<Inventory> getInventoryListByStoreId(String storeId) {
        InventoryCondition condition = new InventoryCondition();
        condition.setStoreId(storeId);
        return baseMapper.selectInventoryDetailList(condition);
    }

    /**
     * 获取仓房仓容映射
     */
    private Map<String, BigDecimal> getStoreHouseCapacityMap(String storeId) {
        try {
            StorehouseCondition condition = new StorehouseCondition();
            condition.setStoreId(storeId);
            List<StoreHouse> storeHouses = storeHouseService.getList(condition);

            return storeHouses.stream()
                    .filter(storeHouse -> storeHouse.getCapacity() != null)
                    .collect(Collectors.toMap(
                            StoreHouse::getId,
                            StoreHouse::getCapacity,
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.warn("获取仓房仓容信息失败，storeId: {}", storeId, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 构建库存总览VO对象
     */
    private InventoryOverviewVO buildInventoryOverviewVO(String stId, List<Inventory> inventories,
                                                         Map<String, BigDecimal> capacityMap) {
        InventoryOverviewVO overviewVO = new InventoryOverviewVO();
        overviewVO.setStId(stId);

        // 设置仓房名称（从第一个库存记录中获取）
        if (!inventories.isEmpty()) {
            overviewVO.setStName(inventories.get(0).getStName());
        }

        // 计算库存总数量
        BigDecimal sumQty = inventories.stream()
                .map(Inventory::getInventoryQty)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        overviewVO.setUsed(sumQty);

        // 设置仓容（按仓房ID匹配）
        overviewVO.setCapacity(capacityMap.getOrDefault(stId, BigDecimal.ZERO));

        // 创建库存明细列表
        List<InventoryOverviewItemVO> itemVOList = inventories.stream()
                .map(this::buildInventoryOverviewItemVO)
                .collect(Collectors.toList());

        overviewVO.setInventoryOverviewItemVOList(itemVOList);
        return overviewVO;
    }

    /**
     * 构建库存总览明细VO对象
     */
    private InventoryOverviewItemVO buildInventoryOverviewItemVO(Inventory inventory) {
        InventoryOverviewItemVO itemVO = new InventoryOverviewItemVO();
        itemVO.setLocId(inventory.getLocId());
        itemVO.setLocName(inventory.getLocName());
        itemVO.setClassificationId(inventory.getClassificationId());
        itemVO.setCatalogId(inventory.getCatalogId());
        itemVO.setCatalogName(inventory.getCatalogName());
        itemVO.setGrade(inventory.getGrade());
        itemVO.setReserveLevel(inventory.getReserveLevel());
        itemVO.setUsed(inventory.getInventoryQty());
        return itemVO;
    }

    @Override
    public Page<Inventory> getBigScreenSummaryPage(InventoryCondition condition) {
        //根据地区、仓房、品类聚合查询
        Page<Inventory> page = condition.newPage();
        Page<Inventory> inventoryPage = baseMapper.selectInventorySummeryByPage(page, condition);
        //将地区名称设置到返回结果中
        inventoryPage.getRecords().forEach(inventory -> inventory.setCounty(infoAreaService.getFullAreaNameByCode(inventory.getCounty())));
        return inventoryPage;
    }

    @Override
    public Map<String, BigDecimal> getBigScreenSummary(List<String> areaCode, List<BigScreenConfig> configList) {
        //先按分类解析出配置中的品种信息
        Map<String, List<String>> classificationMap = configList.stream()
                .filter(config -> config.getCatalogId() != null && !config.getCatalogId().isEmpty())
                .collect(Collectors.toMap(BigScreenConfig::getValue, BigScreenConfig::getCatalogId));

        //再根据分类及地区查询
        Map<String, BigDecimal> result = new HashMap<>();
        //按照配置初始化返回结果
        configList.forEach(config -> result.put(config.getValue(), BigDecimal.ZERO));

        //查询并返回对应的结果
        classificationMap.forEach((classificationCode, catalogIdList) -> {
            BigDecimal sum = baseMapper.selectInventorySummery(areaCode, catalogIdList);
            if (sum == null) {
                sum = BigDecimal.ZERO;
            }
            //KG转换为T
            sum = sum.divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP);
            result.put(classificationCode, sum);
        });

        return result;
    }

    @Override
    public List<InventorySumAndCapacityVO> getInventorySumAndCapacity(String areaCode) {
        if (CommonUtils.isEmpty(areaCode)) {
            areaCode = "150000"; // 默认内蒙古自治区
        }

        // 查询直接下属的一级地区编码（用于最终分组统计）
        List<String> firstLevelAreaCodes = infoAreaService.getAreaCodesByParentId(areaCode);

        // 确定要统计的地区编码列表
        List<String> targetAreaCodes;
        if (firstLevelAreaCodes.isEmpty()) {
            // 没有下级地区，统计当前地区
            targetAreaCodes = Collections.singletonList(areaCode);
        } else {
            targetAreaCodes = firstLevelAreaCodes;
        }

        // 查询该地区下属的所有层级地区编码（用于查询军供站）
        List<String> allChildAreaCodes = infoAreaService.getAllChildAreaCodes(areaCode);
        if (allChildAreaCodes.isEmpty()) {
            // 没有下级地区，只查询当前地区
            allChildAreaCodes = Collections.singletonList(areaCode);
        }

        // 构建库存查询条件，查询所有相关地区的库存数据
        InventoryCondition inventoryCondition = new InventoryCondition();
        inventoryCondition.setAreaCode(String.join(",", allChildAreaCodes));

        // 查询库存汇总数据（按地区分组）
        List<InventorySumAndCapacityVO> inventoryList = baseMapper.getInventorySumAndCapacity(allChildAreaCodes);

        // 获取仓房容量数据
        Map<String, String> storehouseCapacityMap = storeHouseService.getStorehouseListByAreaCodeList(areaCode);

        // 构建地区编码到下一级地区编码的映射
        Map<String, String> areaCodeToFirstLevelMap = infoAreaService.buildAreaCodeToFirstLevelMap(areaCode, targetAreaCodes, allChildAreaCodes);

        // 按下一级地区分组统计（过滤无效数据）
        Map<String, List<InventorySumAndCapacityVO>> groupedInventory = inventoryList.stream()
                .filter(inventory -> {
                    String orgAreaCode = inventory.getAreaCode();
                    return areaCodeToFirstLevelMap.containsKey(orgAreaCode);
                })
                .collect(Collectors.groupingBy(inventory -> {
                    String orgAreaCode = inventory.getAreaCode();
                    return areaCodeToFirstLevelMap.get(orgAreaCode);
                }));

        // 构建结果列表
        List<InventorySumAndCapacityVO> resultList = new ArrayList<>();

        for (String targetAreaCode : targetAreaCodes) {
            InventorySumAndCapacityVO result = new InventorySumAndCapacityVO();
            result.setAreaCode(targetAreaCode);
            result.setAreaName(infoAreaService.getFullAreaNameByCode(targetAreaCode));

            // 统计库存数量（转换为吨）
            List<InventorySumAndCapacityVO> areaInventoryList = groupedInventory.getOrDefault(targetAreaCode, Collections.emptyList());
            BigDecimal totalInventoryQty = areaInventoryList.stream()
                    .map(InventorySumAndCapacityVO::getInventorySum)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP); // KG转T

            result.setInventorySum(totalInventoryQty);

            //获取仓容
            String capacityStr = storehouseCapacityMap.getOrDefault(targetAreaCode, "0");
            result.setCapacitySum(new BigDecimal(capacityStr));
            //计算使用率
            if (result.getCapacitySum().compareTo(BigDecimal.ZERO) > 0) {
                result.setUsedRate(result.getInventorySum().divide(result.getCapacitySum(), 3, RoundingMode.HALF_UP).floatValue());
            } else {
                result.setUsedRate(0);
            }

            resultList.add(result);
        }

        return resultList;
    }

    @Override
    public Page<InventorySumAndCapacityVO> getInventorySumAndCapacityPage(String areaCode, String storeId) {
        if (CommonUtils.isEmpty(areaCode)) {
            areaCode = "150000"; // 默认内蒙古自治区
        }

        // 查询该地区下属的所有层级地区编码（用于查询军供站）
        List<String> allChildAreaCodes = infoAreaService.getAllChildAreaCodes(areaCode);
        if (allChildAreaCodes.isEmpty()) {
            // 没有下级地区，只查询当前地区
            allChildAreaCodes = Collections.singletonList(areaCode);
        }

        // 构建库存查询条件，查询所有相关地区的库存数据
        InventoryCondition inventoryCondition = new InventoryCondition();
        inventoryCondition.setAreaCode(String.join(",", allChildAreaCodes));

        Page<InventorySumAndCapacityVO> page = inventoryCondition.newPage();
        Page<InventorySumAndCapacityVO> capacityPage = baseMapper.getInventorySumAndCapacityPage(page, allChildAreaCodes, storeId);
        //计算使用率
        capacityPage.getRecords().forEach(item -> {
            item.setAreaName(infoAreaService.getFullAreaNameByCode(item.getAreaCode()));
            if (item.getCapacitySum().compareTo(BigDecimal.ZERO) > 0) {
                item.setUsedRate(item.getInventorySum().divide(item.getCapacitySum(), 3, RoundingMode.HALF_UP).floatValue());
            } else {
                item.setUsedRate(0);
            }
        });
        return capacityPage;
    }

    @Override
    public String getQuantity(String locId) {
        return baseMapper.getQuantity(locId);
    }

    @Override
    public InventoryCoreDataVO getDetail(String locId) {
        InventoryCoreDataVO inventoryCoreDataVO = new InventoryCoreDataVO();
        LocationCard locationCard = locationCardService.getDetailByLocId(locId);
        if (locationCard != null) {
            LocationCardDetailResVO locationCardDetail = locationCardService.getDetail(locationCard.getId());
            BeanUtils.copyProperties(locationCardDetail, inventoryCoreDataVO);
        } else {
            //没有货位卡，则直接查询库点、仓房、货位信息
            StoreLocation storeLocation = CacheProvider.getCacheObject(RedisKeys.STORE_LOCATION.key(), locId);
            if (storeLocation == null) {
                throw new BaseException("货位不存在！");
            }
            inventoryCoreDataVO.setLocName(storeLocation.getName());
            inventoryCoreDataVO.setStName(CacheProvider.getValue(RedisKeys.STORE_LOCATION.key(), locId, StoreHouse::getName));
            inventoryCoreDataVO.setStoreName(CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), locId, Organization::getOrgName));
        }
        return inventoryCoreDataVO;
    }

    @Override
    public Page<Inventory> getPage(InventoryCondition condition) {
        Page<Inventory> page = condition.newPage();
        return baseMapper.selectInventoryDetailByPage(page, condition);
    }

    @Override
    public List<Inventory> getList(InventoryCondition condition) {
        return baseMapper.selectInventoryDetailList(condition);
    }

    @Override
    public List<Inventory> getListV1(InventoryCondition condition) {
        List<Inventory> list = baseMapper.selectInventoryDetailList(condition);

        // 过滤掉库存数量<=0的记录
        list = list.stream()
                .filter(inventory -> inventory.getInventoryQty().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        if (list.isEmpty()) {
            return list;
        }

        // 批量获取货位卡信息并设置批次号
        setBatchNumbers(list);

        // 按批次号合并库存
        return mergeInventoryByBatch(list);
    }

    /**
     * 批量设置批次号
     */
    private void setBatchNumbers(List<Inventory> inventoryList) {
        List<String> idList = inventoryList.stream()
                .map(Inventory::getId)
                .collect(Collectors.toList());

        List<LocationCard> cardList = locationCardService.getByInventoryIdList(idList);
        Map<String, String> batchMap = cardList.stream()
                .filter(card -> card.getBatchNum() != null)
                .collect(Collectors.toMap(LocationCard::getInventoryId, LocationCard::getBatchNum));

        inventoryList.forEach(inventory ->
        {
            inventory.setBatchNum(batchMap.get(inventory.getId()));
            //清除货位信息和库存id
            inventory.setLocId(null);
            inventory.setLocName(null);
            inventory.setId(null);
        });
    }

    /**
     * 按批次号合并库存
     */
    private List<Inventory> mergeInventoryByBatch(List<Inventory> inventoryList) {
        return inventoryList.stream()
                .filter(inventory -> inventory.getBatchNum() != null)
                .collect(Collectors.groupingBy(Inventory::getBatchNum))
                .values().stream()
                .map(this::mergeInventoryGroup)
                .collect(Collectors.toList());
    }

    /**
     * 合并同批次库存组
     */
    private Inventory mergeInventoryGroup(List<Inventory> inventoryGroup) {
        Inventory mergedInventory = inventoryGroup.get(0);
        BigDecimal totalQty = inventoryGroup.stream()
                .map(Inventory::getInventoryQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        mergedInventory.setInventoryQty(totalQty);
        return mergedInventory;
    }

    @Override
    public List<Inventory> getInventoryStorehouseList(String storeId) {
        return baseMapper.getInventoryStorehouseList(storeId);
    }

    @Override
    public List<InventoryDTO> updateBatch(List<InventoryDTO> dtoList) {
        lock.lock();
        try {
            dtoList.forEach(this::saveOrUpdate);
        } finally {
            lock.unlock();
        }

        return dtoList;
    }

    private void saveOrUpdate(InventoryDTO dto) {
        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<Inventory>()
                .eq(Inventory::getStId, dto.getStId())
                .eq(Inventory::getLocId, dto.getLocId())
                .eq(Inventory::getEnabled, StrPool.State.ENABLE);
        Inventory savedInventory = getOne(wrapper);

        if (Objects.equals(dto.getInoutType(), InoutBusinessType.IN.getCode())) {
            //入库
            if (savedInventory != null) {
                verify(savedInventory, dto);

                if (savedInventory.getInventoryQty().compareTo(BigDecimal.ZERO) == 0) {
                    try {
                        verify(savedInventory, dto);
                    } catch (BaseException e) {
                        log.info("当前库存数量为零，且入库信息与库存信息不一致，所以增加一条新库存记录");

                        removeById(savedInventory.getId());
                        addNewInventory(dto);
                        return;
                    }
                }

                dto.setCurrentInventoryQty(savedInventory.getInventoryQty());
                dto.setInventoryId(savedInventory.getId());

                savedInventory.setInventoryQty(savedInventory.getInventoryQty().add(dto.getChangeQty()));
                this.updateInventoryQty(savedInventory.getId(), savedInventory.getInventoryQty());
            } else {
                // 创建一条新库存记录
                addNewInventory(dto);
            }
        } else if (Objects.equals(dto.getInoutType(), InoutBusinessType.OUT.getCode())) {
            //出库
            if (savedInventory != null) {
                verify(savedInventory, dto);
            } else {
                throw new BaseException("未找到对应库存信息");
            }

            dto.setCurrentInventoryQty(savedInventory.getInventoryQty());
            dto.setInventoryId(savedInventory.getId());

            savedInventory.setInventoryQty(savedInventory.getInventoryQty().subtract(dto.getChangeQty()));
            this.updateInventoryQty(savedInventory.getId(), savedInventory.getInventoryQty());
        } else {
            throw new BaseException("更新库存业务类型错误!");
        }
    }

    /**
     * 创建新库存记录
     *
     * @param dto
     */
    private void addNewInventory(InventoryDTO dto) {
        //新增库存
        Inventory inventory = new Inventory();
        CachedBeanCopyUtils.copy(dto, inventory);

        inventory.setStName(CacheProvider.getValue(RedisKeys.STORE_HOUSE.key(), dto.getStId(), StoreHouse::getName));
        inventory.setLocName(CacheProvider.getValue(RedisKeys.STORE_LOCATION.key(), dto.getLocId(), StoreLocation::getName));

        Optional<Catalog> catalog = CacheProvider.optional(RedisKeys.CATALOG.key(), dto.getCatalogId());
        inventory.setClassificationId(CommonUtils.getOptionalValue(catalog, Catalog::getClassificationId));
        inventory.setCatalogName(CommonUtils.getOptionalValue(catalog, Catalog::getCatalogName));

        Optional<Organization> store = CacheProvider.optional(RedisKeys.ORGANIZATION.key(), dto.getStoreId());
        inventory.setStoreName(CommonUtils.getOptionalValue(store, Organization::getOrgName));
        inventory.setProvince(CommonUtils.getOptionalValue(store, Organization::getProvince));
        inventory.setCity(CommonUtils.getOptionalValue(store, Organization::getCity));
        inventory.setCounty(CommonUtils.getOptionalValue(store, Organization::getCounty));
        inventory.setManageUnitName(CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), dto.getManageUnitId(), Organization::getOrgName));

        // todo 锁定库存数量
        inventory.setLockQty(BigDecimal.ZERO);
        inventory.setInventoryQty(dto.getChangeQty());
        baseMapper.insert(inventory);

        dto.setCurrentInventoryQty(dto.getChangeQty());
        dto.setInventoryId(inventory.getId());

        // 保存库存基础信息，静态不变数据
        inventoryBaseService.saveBaseInfo(CachedBeanCopyUtils.copy(inventory, new InventoryBase()));
    }


    /**
     * 更新库存数量
     *
     * @param inventoryId
     * @param inventoryQty 当前库存数量
     */
    private void updateInventoryQty(String inventoryId, BigDecimal inventoryQty) {
        Inventory inventory = new Inventory();
        inventory.setId(inventoryId);
        inventory.setInventoryQty(inventoryQty);
        this.updateById(inventory);
    }

    /**
     * 校验库存信息
     *
     * @param savedInventory 原库存信息
     * @param dto            出入库更新信息
     */
    private void verify(final Inventory savedInventory, final InventoryDTO dto) {
        if (Objects.equals(dto.getInoutType(), InoutBusinessType.IN.getCode())) {
            // 入库
            if (savedInventory != null && !savedInventory.getCatalogId().equals(dto.getCatalogId())) {
                BizExp.pop(String.format("实际入库品种与库存品种（%s）不一致！", savedInventory.getCatalogName()));
            }
        } else if (Objects.equals(dto.getInoutType(), InoutBusinessType.OUT.getCode())) {
            //出库

            if (dto.getChangeQty().compareTo(savedInventory.getInventoryQty()) > 0) {
                BizExp.pop(String.format("%s货位的库存数量不足!", savedInventory.getLocName()));
            }

            if (!savedInventory.getCatalogId().equals(dto.getCatalogId())) {
                BizExp.pop(String.format("实际出库品种与库存品种（%s）不一致！", savedInventory.getCatalogName()));
            }
        }

        if (savedInventory != null) {
            if (!savedInventory.getReserveLevel().equals(dto.getReserveLevel())) {
                BizExp.pop(String.format("储备性质与当前%s货位不一致！", savedInventory.getLocName()));
            }

            if (!savedInventory.getGrade().equals(dto.getGrade())) {
                BizExp.pop(String.format("质量等级与当前%s货位不一致！", savedInventory.getLocName()));
            }
        }
    }


}
