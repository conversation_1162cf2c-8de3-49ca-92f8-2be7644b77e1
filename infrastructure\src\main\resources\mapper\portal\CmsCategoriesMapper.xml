<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.portal.CmsCategoriesMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.portal.CmsCategories">
    <!--@mbg.generated-->
    <!--@Table CMS_CATEGORIES-->
    <id column="ID" property="id" />
    <result column="MODULE" property="module" />
    <result column="MODULE_NAME" property="moduleName" />
    <result column="PID" property="pid" />
    <result column="NAME" property="name" />
    <result column="CATEGORY_PATH" property="categoryPath" />
    <result column="CATEGORY_PATH_NAME" property="categoryPathName" />
    <result column="LEAF_IS" property="leafIs" />
    <result column="SORTS" property="sorts" />
    <result column="ENABLED" property="enabled" />
    <result column="URI" property="uri" />
    <result column="SHOW_HEAD_IS" property="showHeadIs"/>
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, "MODULE", MODULE_NAME, PID, "NAME", CATEGORY_PATH, CATEGORY_PATH_NAME, LEAF_IS, SORTS, ENABLED, URI, SHOW_HEAD_IS,
    CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
  </sql>

  <select id="selectPageV1" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from CMS_CATEGORIES
    <where>
      enabled = 1
      <if test="@plugins.OGNL@isNotEmpty(condition.module)">
        AND MODULE = #{condition.module}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.pid)">
        AND PID = #{condition.pid}
      </if>
    </where>
    order by SORTS, ID
  </select>

  <select id="selectListV1" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from CMS_CATEGORIES
    <where>
      enabled = 1
      <if test="@plugins.OGNL@isNotEmpty(condition.module)">
        AND MODULE = #{condition.module}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.leafIs)">
        AND LEAF_IS = #{condition.leafIs}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.pid)">
        AND PID = #{condition.pid}
      </if>
    </where>
    order by SORTS, ID
  </select>
</mapper>
