package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <应急采购补库申请>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:01
 */
@Getter
@Setter
@TableName("B_EMERGENCY_STORE_APPLY")
public class EmergencyStoreApply extends BModel {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
    * 申请编号
    */
    @ApiModelProperty(value="申请编号")
    private String applyCode;

    /**
    * 管理单位ID
    */
    @ApiModelProperty(value="管理单位ID")
    private String orgId;

    /**
    * 管理单位名称
    */
    @ApiModelProperty(value="管理单位名称")
    private String orgName;

    /**
    * 库点ID
    */
    @ApiModelProperty(value="库点ID")
    private String storeId;

    /**
    * 库点名称
    */
    @ApiModelProperty(value="库点名称")
    private String storeName;

    /**
    * 品类ID
    */
    @ApiModelProperty(value="品类ID")
    private String classificationId;

    /**
    * 品类名称
    */
    @ApiModelProperty(value="品类名称")
    private String classificationName;

    /**
    * 损耗总量
    */
    @ApiModelProperty(value="损耗总量")
    private BigDecimal lossTotal;

    /**
    * 申请数量
    */
    @ApiModelProperty(value="申请数量")
    private BigDecimal applyQty;

    /**
    * 申请人
    */
    @ApiModelProperty(value="申请人")
    private String applicant;

    /**
    * 申请人电话
    */
    @ApiModelProperty(value="申请人电话")
    private String applicantTel;

    /**
    * 申请时间
    */
    @ApiModelProperty(value="申请时间")
    private Date applyTime;

    /**
    * 预期送货时间
    */
    @ApiModelProperty(value="预期送货时间")
    private Date estimatedTime;

    @ApiModelProperty(value="业务状态：0-待执行，1-执行中，2-已完成")
    private Integer state;

    /**
    * 审批人
    */
    @ApiModelProperty(value="审批人")
    private String approver;

    /**
    * 审批时间
    */
    @ApiModelProperty(value="审批时间")
    private Date approveTime;

    /**
    * 审批状态
    */
    @ApiModelProperty(value="审批状态: 0-未提交，1-待审核，2-已审核， 3-驳回")
    private Integer approveState;

    /**
    * 审批意见
    */
    @ApiModelProperty(value="审批意见")
    private String approveOpinion;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String notes;

    private Integer enabled;

    private String attachment;

    /**
    * 损耗统计ID
    */
    @ApiModelProperty(value="损耗统计ID")
    private String statId;

    /**
    * 结算状态：0-未结算，1-已结算
    */
    @ApiModelProperty(value="结算状态：0-未结算，1-已结算")
    private Integer accountState;

    /**
    * 结算金额
    */
    @ApiModelProperty(value="结算金额")
    private BigDecimal accountAmount;

    /**
    * 结算时间
    */
    @ApiModelProperty(value="结算时间")
    private Date accountTime;
}
