package com.hxdi.nmjl.service.store.Impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.store.StoreAssessmentCondition;
import com.hxdi.nmjl.domain.store.StoreAssessment;
import com.hxdi.nmjl.mapper.store.StoreAssessmentMapper;
import com.hxdi.nmjl.service.store.StoreAssessmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 门店评估服务实现类
 * <AUTHOR>
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class StoreAssessmentServiceImpl extends BaseServiceImpl<StoreAssessmentMapper, StoreAssessment> implements StoreAssessmentService {


    @Override
    public void create(StoreAssessment assessment) {

        this.save(assessment);
    }

    @Override
    public void update(StoreAssessment assessment) {
        // 查询原评估记录
        StoreAssessment savedAssessment = this.getById(assessment.getId());
        if (savedAssessment == null) {
            throw new BaseException("评估记录不存在");
        }

        this.updateById(assessment);
    }

    @Override
    public StoreAssessment getDetail(String id) {
        StoreAssessment assessment = baseMapper.selectById(id);
        if (assessment == null || assessment.getEnabled() == 0) {
            throw new BaseException("评估记录不存在或已删除");
        }
        return assessment;
    }

    @Override
    public Page<StoreAssessment> pages(StoreAssessmentCondition condition) {
        Page<StoreAssessment> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<StoreAssessment> lists(StoreAssessmentCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void approve(String id, Integer approveStatus, String opinion) {
        StoreAssessment assessment = baseMapper.selectById(id);
        if (assessment == null || assessment.getEnabled() == 0) {
            throw new BaseException("评估记录不存在或已删除");
        }


        BaseUserDetails user = SecurityHelper.obtainUser();
        assessment.setApproveStatus(approveStatus);
        assessment.setApprover(user.getNickName());
        assessment.setApproveTime(new Date());
        assessment.setApproveOpinion(opinion);

        this.updateById(assessment);
    }

    @Override
    public void remove(String id) {
        StoreAssessment assessment = baseMapper.selectById(id);
        if (assessment == null) {
            throw new BaseException("评估记录不存在");
        }

        // 逻辑删除
        assessment.setEnabled(0);
        this.updateById(assessment);
    }

    @Override
    public void submit(String id) {
        StoreAssessment storeAssessment = baseMapper.selectById(id);
        storeAssessment.setApproveStatus(0);
        this.updateById(storeAssessment);
    }
}

