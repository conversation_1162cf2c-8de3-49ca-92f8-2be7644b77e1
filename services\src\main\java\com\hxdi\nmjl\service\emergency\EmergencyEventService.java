package com.hxdi.nmjl.service.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.emergency.EmergencyEvent;
import com.hxdi.nmjl.condition.emergency.EmergencyEventCondition;

import java.util.List;

/**
 * 应急事件管理服务接口
 */
public interface EmergencyEventService extends IBaseService<EmergencyEvent> {

    /**
     * 查询应急事件详情
     *
     * @param id 应急事件ID
     * @return EmergencyEvent
     */
    EmergencyEvent getDetail(String id);

    /**
     * 分页查询应急事件信息
     *
     * @param condition 查询条件
     * @return Page<EmergencyEvent>
     */
    Page<EmergencyEvent> getPages(EmergencyEventCondition condition);

    /**
     * 列表查询应急事件信息
     *
     * @param condition 查询条件
     * @return List<EmergencyEvent>
     */
    List<EmergencyEvent> getList(EmergencyEventCondition condition);

    /**
     * 新增应急事件信息
     *
     * @param emergencyEvent 应急事件信息
     */
    void add(EmergencyEvent emergencyEvent);

    /**
     * 更新应急事件信息
     *
     * @param emergencyEvent 应急事件信息
     */
    void update(EmergencyEvent emergencyEvent);

    /**
     * 删除应急事件信息
     *
     * @param id 应急事件ID
     * @return boolean
     */
    boolean delete(String id);
}
