package com.hxdi.nmjl.service.specialproduct.Impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;

import com.hxdi.nmjl.domain.specialproduct.SpecialProductNotice;
import com.hxdi.nmjl.mapper.specialproduct.SpecialProductNoticeMapper;
import com.hxdi.nmjl.service.specialproduct.SpecialProductNoticeService;
import com.hxdi.nmjl.condition.specialproduct.SpecialProductNoticeCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 地方特色产品征集公告服务实现类
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SpecialProductNoticeServiceImpl extends BaseServiceImpl<SpecialProductNoticeMapper, SpecialProductNotice> implements SpecialProductNoticeService {


    @Override
    public void create(SpecialProductNotice notice) {
        BaseUserDetails user = SecurityHelper.obtainUser();
        notice.setPublisher(user.getNickName());
        notice.setOrgName(user.getOrganName());
        notice.setPublishState(0);
        this.save(notice);
    }

    @Override
    public void update(SpecialProductNotice notice) {
        // 校验公告是否存在
        SpecialProductNotice existingNotice = getById(notice.getId());
        if (Objects.isNull(existingNotice)) {
            throw new BaseException("公告不存在");
        }
        if (notice.getPublishState() == 1 ) {
            throw new BaseException("公告已发布，请先撤销发布");
        }
        notice.setPublishState(0);
        this.updateById(notice);
    }

    @Override
    public SpecialProductNotice getDetail(String noticeId) {
        SpecialProductNotice notice = getById(noticeId);
        if (Objects.isNull(notice)) {
            throw new BaseException("公告不存在");
        }
        return notice;
    }

    @Override
    public Page<SpecialProductNotice> pages(SpecialProductNoticeCondition condition) {
        Page<SpecialProductNotice> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<SpecialProductNotice> lists(SpecialProductNoticeCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void remove(String noticeId) {
        SpecialProductNotice notice = getById(noticeId);
        if (Objects.isNull(notice)) {
            throw new BaseException("公告不存在");
        }
        if (notice.getPublishState() == 1) {
            throw new BaseException("公告已发布，请先撤销发布");
        }
        this.removeById(notice);
    }

    @Override
    public void publish(String noticeId) {
        SpecialProductNotice notice = getById(noticeId);
        if (Objects.isNull(notice)) {
            throw new BaseException("公告不存在");
        }

        // 校验是否已发布
        if (notice.getPublishState() == 1) {
            throw new BaseException("公告已发布，请勿重复发布");
        }

        // 校验截止日期是否有效
        if (Objects.isNull(notice.getEndTime()) || !notice.getEndTime().after(new Date())) {
            throw new BaseException("截止日期无效，请设置未来的日期");
        }

        BaseUserDetails user = SecurityHelper.obtainUser();
        notice.setPublishTime(new Date());
        notice.setPublisher(user.getNickName());
        notice.setPublishState(1);

        this.updateById(notice);
    }

    @Override
    public void revoke(String noticeId) {
        SpecialProductNotice notice = getById(noticeId);
        if (Objects.isNull(notice)) {
            throw new BaseException("公告不存在");
        }

        // 校验是否已发布
        if (notice.getPublishState() == 0) {
            throw new BaseException("公告未发布，无需撤销");
        }

        notice.setPublishTime(null);
        notice.setPublisher(null);
        notice.setPublishState(0);
        this.updateById(notice);
    }

}