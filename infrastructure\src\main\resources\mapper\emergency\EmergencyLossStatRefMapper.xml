<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyLossStatRefMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyLossStatRef">
    <!--@mbg.generated-->
    <!--@Table B_EMERGENCY_LOSS_STAT_REF-->
    <id column="ID" property="id" />
    <result column="LOSS_ID" property="lossId" />
    <result column="STAT_ID" property="statId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LOSS_ID, STAT_ID
  </sql>

</mapper>
