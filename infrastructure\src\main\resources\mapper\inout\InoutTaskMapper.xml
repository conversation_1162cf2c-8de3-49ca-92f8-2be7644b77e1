<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.InoutTaskMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.InoutTask">
        <!--@mbg.generated-->
        <!--@Table B_INOUT_TASK-->
        <id column="ID" property="id" />
        <result column="TASK_ID" property="taskCode" />
        <result column="INOUT_TYPE" property="inoutType" />
        <result column="INOUT_BIZ_TYPE" property="inoutBizType" />
        <result column="PLAN_DATE" property="planDate" />
        <result column="CLIENT_ID" property="clientId" />
        <result column="CLIENT_NAME" property="clientName" />
        <result column="ORDER_ID" property="orderId" />
        <result column="ORDER_CODE" property="orderCode" />
        <result column="PLAN_ID" property="planId" />
        <result column="BATCH_NUM" property="batchNum" />
        <result column="STORE_ID" property="storeId" />
        <result column="STORE_NAME" property="storeName" />
        <result column="CATALOG_ID" property="catalogId" />
        <result column="CATALOG_NAME" property="catalogName" />
        <result column="BRAND" property="brand" />
        <result column="GRADE" property="grade" />
        <result column="SPECIFICATION" property="specification" />
        <result column="PRODUCT_DATE" property="productDate" />
        <result column="PLAN_QTY" property="planQty" />
        <result column="COMPLETED_QTY" property="completedQty" />
        <result column="ORG_ID" property="orgId" />
        <result column="ORG_NAME" property="orgName" />
        <result column="RESERVE_LEVEL" property="reserveLevel" />
        <result column="STATE" property="state" />
        <result column="ENABLED" property="enabled" />
        <result column="ATTACHEMENTS" property="attachments" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, TASK_ID, INOUT_TYPE, INOUT_BIZ_TYPE, PLAN_DATE, CLIENT_ID, CLIENT_NAME, ORDER_ID,
        PLAN_ID, BATCH_NUM, STORE_ID, STORE_NAME, CATALOG_ID, "CATALOG_NAME", BRAND, GRADE,
        SPECIFICATION, PRODUCT_DATE, PLAN_QTY, COMPLETED_QTY, ORG_ID, RESERVE_LEVEL, "STATE",
        ENABLED, ATTACHEMENTS, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID,
        DATA_HIERARCHY_ID, ORDER_CODE, ORG_NAME
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from B_INOUT_TASK
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.inoutType)">
                and INOUT_TYPE = #{condition.inoutType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.inoutBizType)">
                and INOUT_BIZ_TYPE = #{condition.inoutBizType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                and STATE in
                <foreach collection="condition.state" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                and CATALOG_ID = #{condition.catalogId}
            </if>
           <if test="@plugins.OGNL@isNotEmpty(condition.startTime)<EMAIL>@isNotEmpty(condition.endTime)">
                and CREATE_TIME between #{condition.startTime} and #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orderId)">
                and ORDER_ID = #{condition.orderId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planId)">
                and PLAN_ID = #{condition.planId}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from B_INOUT_TASK
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.inoutType)">
                and INOUT_TYPE = #{condition.inoutType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.inoutBizType)">
                and INOUT_BIZ_TYPE = #{condition.inoutBizType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                and STATE in
                <foreach collection="condition.state" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                and CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)<EMAIL>@isNotEmpty(condition.endTime)">
                and CREATE_TIME between #{condition.startTime} and #{condition.endTime}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
</mapper>
