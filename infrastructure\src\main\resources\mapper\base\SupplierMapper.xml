<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.SupplierMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.ProductionSupplier">
        <!--@mbg.generated-->
        <!--@Table B_PRODUCTION_SUPPLIER-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="SUPPLIER_CODE" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="SUPPLIER_TYPE" jdbcType="VARCHAR" property="supplierType"/>
        <result column="ABBR_NAME" jdbcType="VARCHAR" property="abbrName"/>
        <result column="COMPANY_TYPE" jdbcType="VARCHAR" property="companyType"/>
        <result column="PROVINCE_ID" jdbcType="VARCHAR" property="provinceId"/>
        <result column="PROVINCE" jdbcType="VARCHAR" property="province"/>
        <result column="CITY_ID" jdbcType="VARCHAR" property="cityId"/>
        <result column="CITY" jdbcType="VARCHAR" property="city"/>
        <result column="COUNTY_ID" jdbcType="VARCHAR" property="countyId"/>
        <result column="COUNTY" jdbcType="VARCHAR" property="county"/>
        <result column="DETAIL_ADDR" jdbcType="VARCHAR" property="detailAddr"/>
        <result column="CREDIT_CODE" jdbcType="VARCHAR" property="creditCode"/>
        <result column="ECONOMIC_TYPE" jdbcType="VARCHAR" property="economicType"/>
        <result column="REGISTER_TYPE" jdbcType="CHAR" property="registerType"/>
        <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo"/>
        <result column="LEGAL" jdbcType="VARCHAR" property="legal"/>
        <result column="PHONE" jdbcType="VARCHAR" property="phone"/>
        <result column="FAX" jdbcType="VARCHAR" property="fax"/>
        <result column="MAIL" jdbcType="VARCHAR" property="mail"/>
        <result column="WEBSITE" jdbcType="VARCHAR" property="website"/>
        <result column="POST_CODE" jdbcType="VARCHAR" property="postCode"/>
        <result column="LON" jdbcType="VARCHAR" property="lon"/>
        <result column="LAT" jdbcType="VARCHAR" property="lat"/>
        <result column="BANK" jdbcType="VARCHAR" property="bank"/>
        <result column="ACCOUNT" jdbcType="VARCHAR" property="account"/>
        <result column="BANK_CREDIT_LEVEL" jdbcType="CHAR" property="bankCreditLevel"/>
        <result column="FIXED_ASSETS" jdbcType="VARCHAR" property="fixedAssets"/>
        <result column="REGISTERED_CAPITAL" jdbcType="DECIMAL" property="registeredCapital"/>
        <result column="ASSETS" jdbcType="VARCHAR" property="assets"/>
        <result column="EMPLOYMENTS" jdbcType="INTEGER" property="employments"/>
        <result column="FOOD_PRODUCTION_LICENSE" jdbcType="VARCHAR" property="foodProductionLicense"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="GRADE" jdbcType="INTEGER" property="grade"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
        <result column="AVG_SCORE" jdbcType="DECIMAL" property="avgScore"/>
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus"/>
        <result column="APPROVER" jdbcType="VARCHAR" property="approver"/>
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime"/>
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        SUPPLIER_CODE,
        "NAME",
        SUPPLIER_TYPE,
        ABBR_NAME,
        COMPANY_TYPE,
        PROVINCE_ID,
        PROVINCE,
        CITY_ID,
        CITY,
        COUNTY_ID,
        COUNTY,
        DETAIL_ADDR,
        CREDIT_CODE,
        ECONOMIC_TYPE,
        REGISTER_TYPE,
        BUSINESS_NO,
        LEGAL,
        PHONE,
        FAX,
        MAIL,
        WEBSITE,
        POST_CODE,
        LON,
        LAT,
        BANK,
        "ACCOUNT",
        BANK_CREDIT_LEVEL,
        FIXED_ASSETS,
        REGISTERED_CAPITAL,
        ASSETS,
        EMPLOYMENTS,
        FOOD_PRODUCTION_LICENSE,
        REMARKS,
        GRADE,
        ENABLED,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID,
        "AVG_SCORE",
        APPROVE_STATUS,
        APPROVER,
        APPROVE_TIME,
        APPROVE_OPINION
    </sql>
    <update id="changeStatus" parameterType="com.hxdi.nmjl.domain.base.ProductionSupplier">
        update B_PRODUCTION_SUPPLIER
        set ENABLED = #{enabled}
        where id = #{id}
    </update>
    <select id="selectPageV1" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_PRODUCTION_SUPPLIER
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.name)">
                AND NAME LIKE CONCAT('%', #{condition.name}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startFilledDate) and @plugins.OGNL@isNotEmpty(condition.endFilledDate)">
                AND CREATE_TIME BETWEEN #{condition.startFilledDate} AND #{condition.endFilledDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.provinceId)">
                AND PROVINCE_ID = #{condition.provinceId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.cityId)">
                AND CITY_ID = #{condition.cityId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.countyId)">
                AND COUNTY_ID = #{condition.countyId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>
    <select id="selectListV1" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_PRODUCTION_SUPPLIER
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.name)">
                AND NAME LIKE CONCAT('%', #{condition.name}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startFilledDate) and @plugins.OGNL@isNotEmpty(condition.endFilledDate)">
                AND CREATE_TIME BETWEEN #{condition.startFilledDate} AND #{condition.endFilledDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.provinceId)">
                AND PROVINCE_ID = #{condition.provinceId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.cityId)">
                AND CITY_ID = #{condition.cityId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.countyId)">
                AND COUNTY_ID = #{condition.countyId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>
</mapper>
