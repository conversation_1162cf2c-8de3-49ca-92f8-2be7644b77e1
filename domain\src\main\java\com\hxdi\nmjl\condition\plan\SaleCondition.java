package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel(description = "销售订单信息查询条件")
public class SaleCondition extends QueryCondition {

    @ApiModelProperty(value = "模糊查询: 订单编号、合同编号")
    private String search;

    @ApiModelProperty(value = "军供站ID")
    private String storeId;
    
    @ApiModelProperty(value = "客户id")
    private String clientId;

    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "订单来源：1-门店，2-电商平台，3-企业订单")
    private String origin;

    @ApiModelProperty(value = "配送方式")
    private Integer deliveryMethod;

    //使用obj,方便后面做数据转换
    @ApiModelProperty(value = "订单状态：1-待确认，2-已确认，3-运输中，4-待支付，5-已支付，6-已完成，7-已关闭，8-退货，9-换货")
    private Object orderState;

    @ApiModelProperty(value = "审批状态：0-待审核，1-已通过，2-已驳回")
    private String approveStatus;

    @ApiModelProperty(value = "订单结算状态：0-未结算，2-已结算")
    private Integer settlementState;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

}
