package com.hxdi.nmjl.service.inout.opt.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inout.opt.ReceiptDetail;
import com.hxdi.nmjl.mapper.inout.receipt.ReceiptDetailMapper;
import com.hxdi.nmjl.service.inout.opt.ReceiptDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class ReceiptDetailServiceImpl extends BaseServiceImpl<ReceiptDetailMapper, ReceiptDetail> implements ReceiptDetailService {


    @Override
    public void delete(String id) {
        baseMapper.delete(new LambdaQueryWrapper<ReceiptDetail>().eq(ReceiptDetail::getReceiptId, id));
    }

    @Override
    public List<ReceiptDetail> getlists(String id) {
        return baseMapper.selectList(new LambdaQueryWrapper<ReceiptDetail>().eq(ReceiptDetail::getReceiptId, id));
    }
}
