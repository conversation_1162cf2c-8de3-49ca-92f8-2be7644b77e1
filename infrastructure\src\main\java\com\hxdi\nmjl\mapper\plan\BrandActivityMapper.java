package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.BrandActivity;
import com.hxdi.nmjl.condition.plan.BrandActivityCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BrandActivityMapper extends SuperMapper<BrandActivity> {
    Page<BrandActivity> selectPageV1(Page<BrandActivity> page, @Param("condition") BrandActivityCondition condition);

    List<BrandActivity> selectListV1(@Param("condition") BrandActivityCondition condition);
}
