package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.plan.ProductionOrderItem;
import com.hxdi.nmjl.mapper.plan.ProductionOrderItemMapper;
import com.hxdi.nmjl.service.plan.ProductionOrderItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class ProductionOrderItemServiceImpl extends BaseServiceImpl<ProductionOrderItemMapper, ProductionOrderItem> implements ProductionOrderItemService {

    @Override
    public List<ProductionOrderItem> listByOrderId(String orderId) {
        return baseMapper.selectList(Wrappers.<ProductionOrderItem>lambdaQuery().eq(ProductionOrderItem::getOrderId, orderId));
    }

    @Override
    public ProductionOrderItem listByCatalogIdAndGrade(String orderId, String catalogId, String grade) {
        return baseMapper.selectOne(Wrappers.<ProductionOrderItem>lambdaQuery()
                .eq(ProductionOrderItem::getOrderId, orderId)
                .eq(ProductionOrderItem::getCatalogId, catalogId)
                .eq(ProductionOrderItem::getGrade, grade));
    }

    @Override
    public List<ProductionOrderItem> listByIdList(List<String> IdList) {
        return baseMapper.selectList(Wrappers.<ProductionOrderItem>lambdaQuery().in(ProductionOrderItem::getOrderId, IdList));
    }

    @Override
    public void removeV1(String orderId) {
        baseMapper.delete(Wrappers.<ProductionOrderItem>lambdaQuery().eq(ProductionOrderItem::getOrderId, orderId));
    }
}
