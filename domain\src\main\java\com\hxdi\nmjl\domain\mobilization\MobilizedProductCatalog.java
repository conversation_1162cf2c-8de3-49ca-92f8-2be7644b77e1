package com.hxdi.nmjl.domain.mobilization;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


/**

 动员品种目录
 */
@Getter
@Setter
@TableName(value = "B_MOBILIZED_PRODUCT_CATALOG")
public class MobilizedProductCatalog implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     品类 id
     */
    @TableField (value = "CATEGORY_ID")
    private String categoryId;

    /**
     品种 编码
     */
    @TableField (value = "CATALOG_CODE")
    private String catalogCode;
    /**
     品种名称
     */
    @TableField (value = "CATALOG_NAME")
    private String catalogName;

    /**
     规格
     */
    @TableField (value = "SPECIFICATION")
    private String specification;

    /**
     计量单位
     */
    @TableField (value = "UNIT")
    private String unit;

    /**
     状态：1 - 有效，0 - 删除
     */
    @TableField (value = "ENABLED")
    private Integer enabled;

    /**
     * 状态：0 - 待确认，1 - 已确认
     */
    private Integer state;
    /**
     创建时间
     */
    @TableField (value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     更新时间
     */
    @TableField (value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     创建 id
     */
    @TableField (value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;
    /**
     更新 id
     */
    @TableField (value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;
    /**
     租户 id
     */
    @TableField (value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;
    /**
     组织
     */
    @TableField (value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
