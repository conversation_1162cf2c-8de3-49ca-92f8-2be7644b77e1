package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.OrganizationCondition;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.service.base.OrganizationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "单位信息管理")
@RestController
@RequestMapping("/unit")
public class OrganizationController extends BaseController<OrganizationService, Organization> {

    @Log(value = "保存或更新单位", saveReqParam = true)
    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody Organization org) {
        if (CommonUtils.isEmpty(org.getId())) {
            create(org);
        } else {
            update(org);
        }
        return ResultBody.OK();
    }

    @ApiOperation("新增")
    @PostMapping("/add")
    public ResultBody<Void> create(@RequestBody Organization org) {
        bizService.create(org);
        return ResultBody.OK();
    }

    @ApiOperation("更新")
    @PutMapping("/update")
    public ResultBody<Void> update(@RequestBody Organization org) {
        bizService.update(org);
        return ResultBody.OK();
    }

    @Log(value = "删除单位", saveReqParam = true)
    @ApiOperation("删除")
    @PostMapping("/remove")
    public ResultBody<Void> remove(String id) {
        this.changeState(id, 7);
        return ResultBody.OK();
    }

    @Log(value = "启用或禁用单位", saveReqParam = true)
    @ApiOperation("启用/禁用")
    @PostMapping("/change/state")
    public ResultBody<Void> changeState(@RequestBody String orgId, @RequestBody Integer state) {
        bizService.changeState(orgId, state);
        return ResultBody.OK();
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<Organization> getOrg(String uniqueKey) {
        return ResultBody.<Organization>OK().data(bizService.getByUniqueKey(uniqueKey));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<Organization>> pages(OrganizationCondition condition) {
        return ResultBody.<Page<Organization>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<Organization>> lists(OrganizationCondition condition) {
        return ResultBody.<List<Organization>>OK().data(bizService.lists(condition));
    }

    @ApiOperation("获取组织树")
    @GetMapping("/tree")
    public ResultBody<List<Organization>> tree(@RequestParam(required = false, value = "orgId") String orgId) {
        return ResultBody.<List<Organization>>OK().data(bizService.tree(orgId));
    }
}
