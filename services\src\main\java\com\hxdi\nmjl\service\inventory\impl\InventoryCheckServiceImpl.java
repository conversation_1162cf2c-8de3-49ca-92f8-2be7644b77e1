package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.inventory.InventoryCheckCondition;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inventory.InventoryCheck;
import com.hxdi.nmjl.domain.inventory.InventoryCheckConfig;
import com.hxdi.nmjl.domain.inventory.InventoryCheckDetail;
import com.hxdi.nmjl.mapper.inventory.InventoryCheckMapper;
import com.hxdi.nmjl.service.inventory.InventoryCheckConfigService;
import com.hxdi.nmjl.service.inventory.InventoryCheckDetailService;
import com.hxdi.nmjl.service.inventory.InventoryCheckService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 盘点记录服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryCheckServiceImpl extends BaseServiceImpl<InventoryCheckMapper, InventoryCheck> implements InventoryCheckService {

    @Resource
    private InventoryCheckConfigService inventoryCheckConfigService;

    @Resource
    private InventoryCheckDetailService inventoryCheckDetailService;

    @Resource
    private InventoryService inventoryService;

    @Override
    public void timedGenerate() {
        // 查询已启用且时间符合条件的盘点计划列表
        List<InventoryCheckConfig> checkConfigList = inventoryCheckConfigService.list().stream()
                .filter(checkConfig -> checkConfig.getEnabled() == 1 && this.isTimeToGenerating(checkConfig))
                .collect(Collectors.toList());

        // 生成盘点记录并更新计划的下次执行时间
        generateCheck(checkConfigList, true);
    }

    @Override
    public void generateNow(String checkConfigId) {
        // 查询盘点计划，立即生成盘点登记任务并更新计划下次执行时间
        InventoryCheckConfig checkConfig = inventoryCheckConfigService.getByUniqueKey(checkConfigId);
        boolean isExists = this.checkIsExists(checkConfig.getStoreId(), checkConfig.getStartTime());
        if (isExists) {
            log.warn("{}在{}已存在盘点记录，请勿重复生成", checkConfig.getStoreName(), checkConfig.getStartTime());
            BizExp.pop("盘点任务已存在，请勿重复生成！");
        }

        generateCheck(Lists.newArrayList(checkConfig), false);
    }

    @Override
    public void update(InventoryCheck inventoryCheck) {
        inventoryCheck.setCheckStatus(1);
        //更新时设置审核状态为未审核
        inventoryCheck.setApproveStatus(-1);
        updateById(inventoryCheck);

        // 保存盘点明细
        List<InventoryCheckDetail> datailList = inventoryCheck.getInventoryCheckDetailList();
        inventoryCheckDetailService.updateBatchById(datailList);
    }

    @Override
    public InventoryCheck getDetail(String id) {
        InventoryCheck inventoryCheck = super.getById(id);
        InventoryCheckConfig inventoryCheckConfig = inventoryCheckConfigService.getByUniqueKey(inventoryCheck.getStoreId());
        inventoryCheck.setPlanId(inventoryCheckConfig.getId());
        inventoryCheck.setPlanName(inventoryCheckConfig.getName());
        return inventoryCheck;
    }


    @Override
    public void delete(String id) {
        InventoryCheck existingCheck = getById(id);
        if (existingCheck.getApproveStatus() == 1) {
            throw new BaseException("该单据已审批，无法删除");
        }

        InventoryCheck updatingCheck = new InventoryCheck();
        updatingCheck.setId(id);
        updatingCheck.setEnabled(0);
        baseMapper.updateById(updatingCheck);

    }

    @Override
    public Page<InventoryCheck> selectPage(InventoryCheckCondition condition) {
        Page<InventoryCheck> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<InventoryCheck> selectList(InventoryCheckCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void submit(String id) {
        //已盘点，提交进入审批流程
        InventoryCheck check = getById(id);
        if (check.getCheckStatus().equals(0)) {
            throw new BaseException("请先完成盘点！");
        }
        changeApproveStatus(id, 0, null);
    }

    @Override
    public void approve(String id, String approveOpinion) {
        // 审批状态：0-未审批，1-已通过，2-已驳回
        changeApproveStatus(id, 1, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 2, approveOpinion);
    }

    @Override
    public Page<InventoryCheck> getApprovedPage(InventoryCheckCondition condition) {
        Page<InventoryCheck> page = condition.newPage();
        condition.setApproveStatus(1);
        baseMapper.selectPageV1(page, condition);
        page.getRecords().forEach(inventoryCheck -> inventoryCheck.setInventoryCheckDetailList(
                inventoryCheckDetailService.lists(inventoryCheck.getId()))
        );
        return page;
    }

    /**
     * 生成盘点记录的通用方法
     *
     * @param checkConfigs   盘点计划列表
     * @param updateNextTime 是否更新下次执行时间
     */
    private void generateCheck(List<InventoryCheckConfig> checkConfigs, boolean updateNextTime) {
        // 遍历需要生成盘点记录的计划
        for (InventoryCheckConfig checkConfig : checkConfigs) {
            String storeId = checkConfig.getStoreId();
            boolean isExists = this.checkIsExists(storeId, checkConfig.getStartTime());
            if (isExists) {
                log.warn("{}在{}已存在盘点记录, 未生成盘点任务！", checkConfig.getStoreName(), checkConfig.getStartTime());
            } else {
                // 查询该库点下存在库存的仓房列表
                List<Inventory> inventoryStorehouseList = inventoryService.getInventoryStorehouseList(storeId);

                List<InventoryCheck> inventoryChecks = new ArrayList<>();
                // 创建盘点记录
                inventoryStorehouseList.forEach(item -> {
                    InventoryCheck inventoryCheck = new InventoryCheck();
                    inventoryCheck.setStoreId(item.getStoreId());
                    inventoryCheck.setStoreName(item.getStoreName());
                    inventoryCheck.setStId(item.getStId());
                    inventoryCheck.setStName(item.getStName());
                    inventoryCheck.setPlanDate(checkConfig.getStartTime());
                    inventoryCheck.setApproveStatus(-1);
                    inventoryCheck.setDataHierarchyId(item.getDataHierarchyId());
                    inventoryChecks.add(inventoryCheck);
                });

                saveBatch(inventoryChecks);
            }

            if (updateNextTime) {
                inventoryCheckConfigService.updateToNextPlanTime(checkConfig.getId());
            }
        }
    }

    /**
     * 判断库点当前计划时间是否已生成过盘点记录
     *
     * @param storeId
     * @param startTime
     * @return
     */
    private boolean checkIsExists(String storeId, Date startTime) {
        long count = baseMapper.selectCount(Wrappers.<InventoryCheck>lambdaQuery()
                .eq(InventoryCheck::getStoreId, storeId)
                .eq(InventoryCheck::getPlanDate, startTime)
                .eq(InventoryCheck::getEnabled, 1));

        return count > 0;
    }

    /**
     * 判断当前是否到了生成盘点记录的时间
     * 根据盘点计划的开始时间(START_TIME)、周期(PERIODS)和时间单位(TIME_UNIT)计算
     *
     * @param checkConfig 盘点计划
     * @return 是否应生成盘点记录
     */
    private boolean isTimeToGenerating(InventoryCheckConfig checkConfig) {
        LocalDate now = LocalDate.now();
        LocalDate startTime = checkConfig.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        if (checkConfig.getTimeUnit() == 1) {
            long days = ChronoUnit.DAYS.between(startTime, now);
            return days == 0;
        } else if (checkConfig.getTimeUnit() == 2) {
            long months = ChronoUnit.MONTHS.between(startTime, now);
            return months == 0 && startTime.getDayOfMonth() == now.getDayOfMonth();
        }

        return false;
    }


    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        InventoryCheck inventoryCheck = new InventoryCheck();
        inventoryCheck.setId(id);
        inventoryCheck.setApproveStatus(approveStatus);
        if (approveStatus == 1 || approveStatus == 2) {
            inventoryCheck.setApprover(SecurityHelper.obtainUser().getNickName());
            inventoryCheck.setApproveTime(new Date());
            inventoryCheck.setApproveOpinion(approveOpinion);
            if(approveStatus == 2){
                //设置盘点状态为未盘点
                inventoryCheck.setCheckStatus(0);
            }
        }
        baseMapper.updateById(inventoryCheck);
    }

}
