package com.hxdi.nmjl.condition.emergency;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.model.QueryCondition;
import com.hxdi.common.core.mybatis.annotation.*;
import com.hxdi.common.core.mybatis.base.support.OrderItem;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.core.annotation.Order;

import java.util.Date;


@Getter
@Setter
public class CommRecordCondition extends QueryCondition {
    @ApiModelProperty(value = "上次更新时间")
    @Between(value = "RECORD_DATE")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private RangeBean<Date> updateTime;

    @ApiModelProperty(value = "沟通主题")
    @LIKE
    private String title;

    @ApiModelProperty(value = "沟通类型")
    @LIKE
    private String commType;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    @EQ
    private Integer enabled=1;

    @ORDER
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private OrderItem item = OrderItem.desc("create_time");

}
