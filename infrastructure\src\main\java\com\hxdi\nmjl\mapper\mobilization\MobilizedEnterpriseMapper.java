package com.hxdi.nmjl.mapper.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.mobilization.MobilizedEnterprise;
import com.hxdi.nmjl.condition.mobilization.MobilizedEnterpriseCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MobilizedEnterpriseMapper extends SuperMapper<MobilizedEnterprise> {

    Page<MobilizedEnterprise> selectPageV1(Page<MobilizedEnterprise> page, @Param("condition") MobilizedEnterpriseCondition condition);

    List<MobilizedEnterprise> selectListV1(@Param("condition") MobilizedEnterpriseCondition condition);
}
