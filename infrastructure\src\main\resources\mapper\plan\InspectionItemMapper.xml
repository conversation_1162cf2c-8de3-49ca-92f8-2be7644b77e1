<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.InspectionItemMapper">
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.InspectionItem">
        <!--@mbg.generated-->
        <!--@Table B_INSPECTION_ITEM-->
        <id column="ID" property="id" />
        <result column="MAIN_ID" property="mainId" />
        <result column="ITEM_TITLE" property="itemTitle" />
        <result column="REQUIREMENT" property="requirement" />
        <result column="DEAL_REQUIREMENT" property="dealRequirement" />
        <result column="DEAL_TIME" property="dealTime" />
        <result column="RESULT" property="result" />
        <result column="STATE" property="state" />
        <result column="NOTES" property="notes" />
        <result column="SORTS" property="sorts" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, MAIN_ID, ITEM_TITLE, REQUIREMENT, DEAL_REQUIREMENT, DEAL_TIME, "RESULT", "STATE",
        NOTES, SORTS, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>


</mapper>
