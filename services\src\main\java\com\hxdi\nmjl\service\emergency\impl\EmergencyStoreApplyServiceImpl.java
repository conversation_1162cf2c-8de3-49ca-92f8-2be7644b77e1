package com.hxdi.nmjl.service.emergency.impl;

import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStat;
import com.hxdi.nmjl.domain.emergency.EmergencyStoreApply;
import com.hxdi.nmjl.linker.ContextService;
import com.hxdi.nmjl.mapper.emergency.EmergencyStoreApplyMapper;
import com.hxdi.nmjl.service.emergency.EmergencyLossStatService;
import com.hxdi.nmjl.service.emergency.EmergencyStoreApplyService;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <应急补库申请服务实现>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:49
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class EmergencyStoreApplyServiceImpl extends BaseServiceImpl<EmergencyStoreApplyMapper, EmergencyStoreApply> implements EmergencyStoreApplyService {


    @Autowired
    private ContextService contextService;

    @Autowired
    private EmergencyLossStatService emergencyLossStatService;

    @Override
    public boolean saveOrUpdate(EmergencyStoreApply entity) {
        if (CommonUtils.isEmpty(entity.getId())) {
            create(entity);
        } else {
            update(entity);
        }
        return true;
    }

    public void create(EmergencyStoreApply entity) {
        emergencyLossStatService.lockState(entity.getStatId());

        Organization station = CacheProvider.getCacheObject(RedisKeys.ORGANIZATION.key(), entity.getStoreId());
        if (station == null || station.getOrgType() != 2) {
            BizExp.pop("库点不存在");
        }

        Organization parentOrg = CacheProvider.getCacheObject(RedisKeys.ORGANIZATION.key(), station.getPid());
        if (parentOrg != null) {
            entity.setOrgId(parentOrg.getId());
            entity.setOrgName(parentOrg.getOrgName());
        }

        entity.setStoreName(station.getOrgName());
        entity.setState(0);
        entity.setApproveState(0);
        entity.setAccountState(0);
        entity.setApplyCode((String) contextService.generateCode("emergency_store_apply_code"));
        baseMapper.insert(entity);
    }

    public void update(EmergencyStoreApply entity) {
        if (entity.getState() != 0) {
            BizExp.pop("当前状态不能进行修改操作");
        }

        Organization station = CacheProvider.getCacheObject(RedisKeys.ORGANIZATION.key(), entity.getStoreId());
        if (station == null || station.getOrgType() != 2) {
            BizExp.pop("库点不存在");
        }

        Organization parentOrg = CacheProvider.getCacheObject(RedisKeys.ORGANIZATION.key(), station.getPid());
        if (parentOrg != null) {
            entity.setOrgId(parentOrg.getId());
            entity.setOrgName(parentOrg.getOrgName());
        }

        entity.setApproveState(0);

        baseMapper.updateById(entity);
    }


    @Override
    public void approve(String id, String approveOpinion) {
        EmergencyStoreApply entity = getById(id);
        if (entity.getApproveState() == 0) {
            BizExp.pop("请先提交审核");
        }

        if (entity.getApproveState() == 1) {
            entity.setApproveState(2);
            entity.setApproveOpinion(approveOpinion);
            entity.setApproveTime(new Date());
            entity.setApprover(SecurityHelper.obtainUser().getNickName());

            entity.setState(1);
            baseMapper.updateById(entity);
        }
    }

    @Override
    public void reject(String id, String approveOpinion) {
        EmergencyStoreApply entity = getById(id);
        if (entity.getApproveState() == 0) {
            BizExp.pop("请先提交审核");
        }

        if (entity.getApproveState() != 1) {
            BizExp.pop("数据状态异常");
        }

        // 驳回后，将状态设置为待提交状态
        entity.setApproveState(3);
        entity.setApproveOpinion(approveOpinion);
        entity.setApproveTime(new Date());
        entity.setApprover(SecurityHelper.obtainUser().getNickName());
        baseMapper.updateById(entity);
    }

    @Override
    public void submit(String id) {
        EmergencyStoreApply entity = getById(id);
        if (entity.getApproveState() == 0) {
            entity.setApproveState(1);
            baseMapper.updateById(entity);
        }
    }

    @Override
    public void complete(String id) {
        EmergencyStoreApply entity = getById(id);
        if (entity.getState() != 1) {
           BizExp.pop("当前状态错误");
        }

        entity.setState(2);
        baseMapper.updateById(entity);
    }

    @Override
    public void accounted(EmergencyStoreApply updatingEntity) {
        EmergencyStoreApply savedEntity = getById(updatingEntity.getId());
        savedEntity.setAccountTime(updatingEntity.getAccountTime());
        savedEntity.setAccountAmount(updatingEntity.getAccountAmount());
        savedEntity.setAccountState(1);
        baseMapper.updateById(savedEntity);
    }

    @Override
    public void softRemoveById(Serializable id) {
        EmergencyStoreApply savedEntity = getById(id);
        emergencyLossStatService.unlockState(savedEntity.getStatId());

        super.softRemoveById(id);
    }

    @Override
    public List<EmergencyLossStat> queryLossStatList(String storeId) {
        return emergencyLossStatService.getListByStoreId(storeId);
    }
}
