package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "品牌危机管理查询条件")
@Getter
@Setter
public class BrandCrisisCondition extends QueryCondition {

    @ApiModelProperty(value = "品牌ID")
    private String brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "舆情类型：字典PPYQLX")
    private String evnetType;

    @ApiModelProperty(value = "传播方式：字典PPCBFS")
    private String propagationMode;

    @ApiModelProperty(value = "预警级别：字典PPYJJB")
    private Integer eventLevel;

    @ApiModelProperty(value = "创建时间（开始）")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间（结束）")
    private Date createTimeEnd;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}