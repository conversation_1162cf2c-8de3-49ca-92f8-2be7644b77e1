<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.LocationCardMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.LocationCard">
    <!--@mbg.generated-->
    <!--@Table B_LOCATION_CARD-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
    <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
    <result column="ST_ID" jdbcType="VARCHAR" property="stId" />
    <result column="ST_NAME" jdbcType="VARCHAR" property="stName" />
    <result column="LOC_ID" jdbcType="VARCHAR" property="locId" />
    <result column="LOC_NAME" jdbcType="VARCHAR" property="locName" />
    <result column="LOC_STATE" jdbcType="INTEGER" property="locState" />
    <result column="INVENTORY_ID" jdbcType="VARCHAR" property="inventoryId" />
    <result column="BATCH_NUM" jdbcType="VARCHAR" property="batchNum" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, STORE_ID, STORE_NAME, ST_ID, ST_NAME, LOC_ID, LOC_NAME, LOC_STATE, INVENTORY_ID,
    BATCH_NUM, ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
  </sql>

  <select id="getList" parameterType="com.hxdi.nmjl.condition.inventory.LocationCardCondition"
          resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_LOCATION_CARD
    <where>
      <if test="condition.locId != null and condition.locId != ''">
        and LOC_ID = #{condition.locId}
      </if>
      <if test="condition.storeId != null and condition.storeId != ''">
        and STORE_ID = #{condition.storeId}
      </if>
      <if test="condition.stId != null and condition.stId != ''">
        and ST_ID = #{condition.stId}
      </if>
      <if test="condition.locName != null and condition.locName != ''">
        and LOC_NAME = #{condition.locName}
      </if>
      <if test="condition.stName != null and condition.stName != ''">
        and ST_NAME = #{condition.stName}
      </if>
      <if test="condition.updateTimeStart != null">
        and UPDATE_TIME &gt;= #{condition.updateTimeStart}
      </if>
      <if test="condition.updateTimeEnd != null">
        and UPDATE_TIME &lt;= #{condition.updateTimeEnd}
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>

  <select id="getPage" parameterType="com.hxdi.nmjl.condition.inventory.LocationCardCondition"
          resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_LOCATION_CARD
    <where>
      <if test="condition.locId != null and condition.locId != ''">
        and LOC_ID = #{condition.locId}
      </if>
      <if test="condition.storeId != null and condition.storeId != ''">
        and STORE_ID = #{condition.storeId}
      </if>
      <if test="condition.stId != null and condition.stId != ''">
        and ST_ID = #{condition.stId}
      </if>
      <if test="condition.locName != null and condition.locName != ''">
        and LOC_NAME = #{condition.locName}
      </if>
      <if test="condition.stName != null and condition.stName != ''">
        and ST_NAME = #{condition.stName}
      </if>
      <if test="condition.updateTimeStart != null">
        and UPDATE_TIME &gt;= #{condition.updateTimeStart}
      </if>
      <if test="condition.updateTimeEnd != null">
        and UPDATE_TIME &lt;= #{condition.updateTimeEnd}
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>
