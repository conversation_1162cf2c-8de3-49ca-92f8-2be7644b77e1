package com.hxdi.nmjl.service.inout.duty.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.DutyCondition;
import com.hxdi.nmjl.domain.inout.duty.DutyEmp;
import com.hxdi.nmjl.domain.inout.duty.DutyPlan;
import com.hxdi.nmjl.domain.inout.duty.DutyRecord;
import com.hxdi.nmjl.mapper.inout.duty.DutyRecordMapper;
import com.hxdi.nmjl.service.inout.duty.DutyEmpService;
import com.hxdi.nmjl.service.inout.duty.DutyPlanService;
import com.hxdi.nmjl.service.inout.duty.DutyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class DutyRecordServiceImpl extends BaseServiceImpl<DutyRecordMapper, DutyRecord> implements DutyRecordService {


    @Resource
    private DutyEmpService dutyEmpService;

    @Resource
    private DutyPlanService dutyPlanService;

    @Override
    public void create(DutyRecord dutyRecord) {
        if (CommonUtils.isNotBlank(dutyRecord.getDutyPlanId())) {
            DutyPlan dutyPlan = dutyPlanService.getById(dutyRecord.getDutyPlanId());
            dutyRecord.setDutyName(dutyPlan.getName());
        }
        DutyEmp dutyEmp = dutyEmpService.getById(dutyRecord.getEmpId());
        dutyRecord.setEmpName(dutyEmp.getEmpName());
        dutyRecord.setMobile(dutyEmp.getMobile());
        this.save(dutyRecord);
    }

    @Override
    public void updating(DutyRecord dutyRecord) {
        this.updateById(dutyRecord);
    }

    @Override
    public DutyRecord detail(String dutyId) {
        return this.getById(dutyId);
    }

    @Override
    public void delete(String dutyId) {
        this.removeById(dutyId);
    }

    @Override
    public Page<DutyRecord> getPage(DutyCondition dutyCondition) {
        Page<DutyRecord> pages = dutyCondition.newPage();
        baseMapper.selectPageV1(pages, dutyCondition);
        List<DutyRecord> records = pages.getRecords();
        Map<DutyRecord, String> maps = records.stream().collect(Collectors.toMap(c -> c, DutyRecord::getDutyPlanId));
        Collection<String> ids = maps.values();
        List<DutyPlan> dutyPlans = dutyPlanService.listByIds(ids);

        Map<String, String> dutyPlanMap = dutyPlans.stream()
                .collect(Collectors.toMap(DutyPlan::getId, DutyPlan::getDutyPlanCode));
        records.forEach(c -> c.setDutyPlanCode(dutyPlanMap.get(c.getDutyPlanId())));
        return pages;
    }

    @Override
    public List<DutyRecord> getList(DutyCondition dutyCondition) {

        List<DutyRecord> dutyRecords = baseMapper.selectListV1(dutyCondition);
        List<String> collects = dutyRecords.stream()
                .map(DutyRecord::getDutyPlanId)
                .filter(CommonUtils::isNotEmpty)
                .collect(Collectors.toList());
        List<DutyPlan> dutyPlans = dutyPlanService.listByIds(collects);
        Map<String, String> dutyPlanMap = dutyPlans.stream()
                .collect(Collectors.toMap(DutyPlan::getId, DutyPlan::getDutyPlanCode));
        dutyRecords.forEach(c -> c.setDutyPlanCode(dutyPlanMap.get(c.getDutyPlanId())));
        return dutyRecords;
    }
}
