<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.quality.QualityInspectionDetailMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.quality.QualityInspectionDetail">
    <!--@mbg.generated-->
    <!--@Table B_QUALITY_INSPECTION_DETAIL-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="INSPECT_ID" jdbcType="VARCHAR" property="inspectId" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_SYMBOL" jdbcType="VARCHAR" property="itemSymbol" />
    <result column="ITEM_SIGN" jdbcType="VARCHAR" property="itemSign" />
    <result column="ITEM_SVAL" jdbcType="VARCHAR" property="itemSval" />
    <result column="ITEM_VAL" jdbcType="VARCHAR" property="itemVal" />
    <result column="ITEM_RESULT" jdbcType="INTEGER" property="itemResult" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INSPECT_ID, ITEM_NAME, ITEM_SYMBOL, ITEM_SIGN, ITEM_SVAL, ITEM_VAL, ITEM_RESULT, 
    TENANT_ID, DATA_HIERARCHY_ID
  </sql>
</mapper>