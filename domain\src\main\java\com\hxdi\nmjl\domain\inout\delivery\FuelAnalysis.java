package com.hxdi.nmjl.domain.inout.delivery;



import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 油耗分析报表
 *
 * @TableName B_FUEL_ANALYSIS
 */
@TableName(value = "B_FUEL_ANALYSIS")
@Data
public class FuelAnalysis {
    /**
     * 分析记录ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "油耗分析Id")
    private String id;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    /**
     * 车辆类型
     */
    @TableField(value = "VEHICLE_KIND")
    @ApiModelProperty(value = "车辆类型")
    private String vehicleKind;

    /**
     * 车牌号
     */
    @TableField(value = "VEHICLE_NO")
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    /**
     * 加油日期
     */
    @TableField(value = "REFUEL_DATE")
    @ApiModelProperty(value = "加油日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refuelDate;

    /**
     * 单次加油量(L)
     */
    @TableField(value = "REFUEL_QUANTITY")
    @ApiModelProperty(value = "单次加油量(L)")
    private BigDecimal refuelQuantity;

    /**
     * 当前油价(元/L)
     */
    @TableField(value = "CUR_FUEL_PRICE")
    @ApiModelProperty(value = "当前油价(元/L)")
    private BigDecimal curFuelPrice;

    /**
     * 油耗类型
     */
    @TableField(value = "FUEL_TYPE")
    @ApiModelProperty(value = "油耗类型")
    private String fuelType;

    /**
     * 加油后里程表读数(km)
     */
    @TableField(value = "MILEAGE_AFTER")
    @ApiModelProperty(value = "加油后里程表读数(km)")
    private Integer mileageAfter;

    /**
     * 单次行驶里程(km)
     */
    @TableField(value = "TOTAL_DISTANCE")
    @ApiModelProperty(value = "单次行驶里程(km)")
    private BigDecimal totalDistance;

    /**
     * 加油人
     */
    @TableField(value = "OPERATOR")
    @ApiModelProperty(value = "加油人")
    private String operator;

    /**
     * 加油地点
     */
    @TableField(value = "FUEL_PLACE")
    @ApiModelProperty(value = "加油地点")
    private String fuelPlace;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 附件路径
     */
    @TableField(value = "ATTACHMENT")
    @ApiModelProperty(value = "附件路径")
    private String attachment;

    /**
     * 预计百公里油耗(L/100km)
     */
    @TableField(value = "FUEL_CONSUMPTION")
    @ApiModelProperty(value = "预计百公里油耗(L/100km)")
    private BigDecimal fuelConsumption;

    /**
     * 实际百公里油耗(L/100km)
     */
    @TableField(value = "FUEL_CONSUMPTION_ACTUAL")
    @ApiModelProperty(value = "实际百公里油耗(L/100km)")
    private BigDecimal fuelConsumptionActual;

    /**
     * 单条记录异常标识
     */
    @TableField(value = "MARK")
    @ApiModelProperty(value = "单条记录异常标识")
    private String mark;

    /**
     * 异常确认结果
     */
    @TableField(value = "CONFIRM_RESULT")
    @ApiModelProperty(value = "异常确认结果")
    private String confirmResult;

    /**
     * 异常确认说明
     */
    @TableField(value = "CONFIRM_REMARK")
    @ApiModelProperty(value = "异常确认说明")
    private String confirmRemark;

    /**
     * 异常处理人
     */
    @TableField(value = "CONFIRM_USER")
    @ApiModelProperty(value = "异常处理人")
    private String confirmUser;

    /**
     * 异常处理时间
     */
    @TableField(value = "CONFIRM_TIME")
    @ApiModelProperty(value = "异常处理时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date confirmTime;

    /**
     * 统计时间
     */
    @TableField(value = "STAT_TIME")
    @ApiModelProperty(value = "统计时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statTime;

    /**
     * 统计周期内加油次数
     */
    @TableField(value = "REFUEL_COUNT")
    @ApiModelProperty(value = "统计周期内加油次数")
    private Integer refuelCount;

    /**
     * 统计周期内加油总量(L)
     */
    @TableField(value = "TOTAL_FUEL_AMOUNT")
    @ApiModelProperty(value = "统计周期内加油总量(L)")
    private BigDecimal totalFuelAmount;

    /**
     * 统计周期内加油总成本(元)
     */
    @TableField(value = "TOTAL_FUEL_COST")
    @ApiModelProperty(value = "统计周期内加油总成本(元)")
    private BigDecimal totalFuelCost;

    /**
     * 统计周期内行驶总里程(km)
     */
    @TableField(value = "TOTAL_DRIVE_DISTANCE")
    @ApiModelProperty(value = "统计周期内行驶总里程(km)")
    private BigDecimal totalDriveDistance;

    /**
     * 统计周期内异常标记次数
     */
    @TableField(value = "ALERT_COUNT")
    @ApiModelProperty(value = "统计周期内异常标记次数")
    private Integer alertCount;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATED_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "创建人ID")
    private String createId;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人ID")
    private String updateId;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 组织ID
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "组织ID")
    private String dataHierarchyId;

}