package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.domain.plan.BidObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 库容管理信息
 * @TableName B_STORAGE_CAPACITY
 */
@ApiModel(description = "库容管理信息表")
@TableName("B_STORAGE_CAPACITY")
@Getter
@Setter
public class StorageCapacity implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 军供站ID
     */
    @ApiModelProperty(value = "军供站ID")
    @TableField(value = "STORE_ID")
    private String storeId;

    /**
     * 军供站名称
     */
    @ApiModelProperty(value = "军供站名称")
    @TableField(value = "STORE_NAME")
    private String storeName;

    /**
     * 最大库容量
     */
    @ApiModelProperty(value = "最大库容量")
    @TableField(value = "MAX_CAP")
    private BigDecimal maxCap;

    /**
     * 已使用容量
     */
    @ApiModelProperty(value = "已使用容量")
    @TableField(value = "USED_CAP")
    private BigDecimal usedCap;

    /**
     * 可用容量
     */
    @ApiModelProperty(value = "可用容量")
    @TableField(value = "AVAILABLE_CAP")
    private BigDecimal availableCap;

    /**
     * 管理单位ID
     */
    @ApiModelProperty(value = "管理单位ID")
    @TableField(value = "ORG_ID")
    private String orgId;

    /**
     * 仓房ID
     */
    @ApiModelProperty(value = "仓房ID")
    @TableField(value = "ST_ID")
    private String stId;

    /**
     * 仓房名称
     */
    @ApiModelProperty(value = "仓房名称")
    @TableField(value = "ST_NAME")
    private String stName;

    /**
     * 管理单位名称
     */
    @ApiModelProperty(value = "管理单位名称")
    @TableField(value = "ORG_NAME")
    private String orgName;

    /**
     * 业务状态：1-闲置，2-租用，3-政策性占用
     */
    @ApiModelProperty(value = "业务状态：1-闲置，2-租用，3-政策性占用")
    @TableField(value = "STATE")
    private Integer state;

    /**
     * 状态:1-有效，0-删除
     */
    @ApiModelProperty(value = "状态：1-有效，0-删除")
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    @TableField(value = "ATTACHEMENTS")
    private String attachments;

    /**
     * 审批状态：0-未审核，1-已审核，2-驳回
     */
    @ApiModelProperty(value = "审批状态：0-未审核，1-已审核，2-驳回")
    @TableField(value = "APPROVE_STATUS")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @ApiModelProperty(value = "审批人")
    @TableField(value = "APPROVER")
    private String approver;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "APPROVE_TIME")
    private Date approveTime;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    @TableField(value = "APPROVE_OPINION")
    private String approveOpinion;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID")
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;

}

