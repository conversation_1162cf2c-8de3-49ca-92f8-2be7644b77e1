package com.hxdi.nmjl.domain.emergency;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 应急调度表
 */
@Getter
@Setter
@TableName(value = "B_EMERGENCY_SCHEDULE")
public class EmergencySchedule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调度编号
     */
    @TableField(value = "SCHEDULE_NO")
    private String scheduleNo;

    /**
     * 事件编号
     */
    @TableField(value = "EVENT_CODE")
    private String eventCode;

    /**
     * 事件名称
     */
    @TableField(value = "EVENT_NAME")
    private String eventName;

    /**
     * 调度日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "SCHEDULE_DATE")
    private Date scheduleDate;

    /**
     * 省
     */
    @TableField(value = "PROVINCE_CODE")
    private String provinceCode;

    /**
     * 市
     */
    @TableField(value = "CITY_CODE")
    private String cityCode;

    /**
     * 区县
     */
    @TableField(value = "COUNTY_CODE")
    private String countyCode;

    /**
     * 地址
     */
    @TableField(value = "AREA")
    private String area;

    /**
     * 详细地址
     */
    @TableField(value = "DETAIL_ADDR")
    private String detailAddr;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "START_TIME")
    private Date startTime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "END_TIME")
    private Date endTime;

    /**
     * 调出企业类型
     */
    @TableField(value = "UNIT_TYPE")
    private Integer unitType;

    /**
     * 调出企业id
     */
    @TableField(value = "UNIT_ID")
    private String unitId;

    /**
     * 调出企业名称
     */
    @TableField(value = "UNIT_NAME")
    private String unitName;

    /**
     * 调出企业联系人
     */
    @TableField(value = "UNIT_CONTACTS")
    private String unitContacts;

    /**
     * 调出企业联系电话
     */
    @TableField(value = "UNIT_CONTACTS_TEL")
    private String unitContactsTel;

    /**
     * 调出企业地址
     */
    @TableField(value = "UNIT_ADDR")
    private String unitAddr;

    /**
     * 调出企业详细地址
     */
    @TableField(value = "UNIT_DETAIL_ADDR")
    private String unitDetailAddr;

    /**
     * 接收单位id
     */
    @TableField(value = "RCV_ORG_ID")
    private String rcvOrgId;

    /**
     * 接收单位名称
     */
    @TableField(value = "RCV_ORG_NAME")
    private String rcvOrgName;

    /**
     * 负责人
     */
    @TableField(value = "PRINCIPAL")
    private String principal;

    /**
     * 联系电话
     */
    @TableField(value = "MOBILE")
    private String mobile;

    /**
     * 调度状态：1-进行中，2-已完成
     */
    @TableField(value = "SCHEDULE_STATE")
    private Integer scheduleState;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENT")
    private String attachment;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 应急调度明细
     */
    @TableField(exist = false)
    private List<EmergencyScheduleItem> emergencyScheduleItem;


}
