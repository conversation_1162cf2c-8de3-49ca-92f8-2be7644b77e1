package com.hxdi.nmjl.service.iot;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.iot.TemHumDayRecordCondition;
import com.hxdi.nmjl.domain.iot.TemHumDayRecord;

import java.util.List;

public interface TemHumDayRecordService extends IBaseService<TemHumDayRecord> {
    /**
     * 定时任务接口：生成日统计记录
     */
    void generateDayRecord();

    List<TemHumDayRecord> listV1(TemHumDayRecordCondition condition);

    Page<TemHumDayRecord> pageV1(TemHumDayRecordCondition condition);
}
