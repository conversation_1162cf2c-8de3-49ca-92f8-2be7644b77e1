package com.hxdi.nmjl.mapper.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.mobilization.MobilizeChefInfo;
import com.hxdi.nmjl.condition.mobilization.MobilizeChefCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MobilizeChefMapper extends SuperMapper<MobilizeChefInfo> {
    @DataPermission
    List<MobilizeChefInfo> getList(@Param("condition") MobilizeChefCondition condition);
    @DataPermission
    Page<MobilizeChefInfo> getPages(@Param("condition") MobilizeChefCondition condition, @Param("page") Page<MobilizeChefInfo> page);
}
