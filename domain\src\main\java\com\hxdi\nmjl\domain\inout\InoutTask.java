package com.hxdi.nmjl.domain.inout;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;


@ApiModel(description = "出入库任务表")
@TableName("B_INOUT_TASK")
@Getter
@Setter
public class InoutTask extends Entity<InoutTask> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "任务单号")
    @TableField(value = "TASK_ID")
    private String taskCode;

    @ApiModelProperty(value = "出入库类型")
    @TableField(value = "INOUT_TYPE")
    private String inoutType;

    @ApiModelProperty(value = "业务类型")
    @TableField(value = "INOUT_BIZ_TYPE")
    private String inoutBizType;

    @ApiModelProperty(value = "作业时间")
    @TableField(value = "PLAN_DATE")
    private LocalDate planDate;

    @ApiModelProperty(value = "客户ID")
    @TableField(value = "CLIENT_ID")
    private String clientId;

    @ApiModelProperty(value = "客户名称")
    @TableField(value = "CLIENT_NAME")
    private String clientName;

    @ApiModelProperty(value = "订单ID")
    @TableField(value = "ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "订单编号")
    @TableField(value = "ORDER_CODE")
    private String orderCode;

    @ApiModelProperty(value = "筹措计划ID")
    @TableField(value = "PLAN_ID")
    private String planId;

    @ApiModelProperty(value = "生产批次号")
    @TableField(value = "BATCH_NUM")
    private String batchNum;

    @ApiModelProperty(value = "库点ID")
    @TableField(value = "STORE_ID")
    private String storeId;

    @ApiModelProperty(value = "库点名称")
    @TableField(value = "STORE_NAME")
    private String storeName;

    @ApiModelProperty(value = "品种ID")
    @TableField(value = "CATALOG_ID")
    private String catalogId;

    @ApiModelProperty(value = "品种名称")
    @TableField(value = "CATALOG_NAME")
    private String catalogName;

    @ApiModelProperty(value = "品牌名称")
    @TableField(value = "BRAND")
    private String brand;

    @ApiModelProperty(value = "质量等级")
    @TableField(value = "GRADE")
    private String grade;

    @ApiModelProperty(value = "规格")
    @TableField(value = "SPECIFICATION")
    private String specification;

    @ApiModelProperty(value = "生产日期")
    @TableField(value = "PRODUCT_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productDate;

    @ApiModelProperty(value = "计划数量")
    @TableField(value = "PLAN_QTY")
    private BigDecimal planQty;

    @ApiModelProperty(value = "完成数量")
    @TableField(value = "COMPLETED_QTY")
    private BigDecimal completedQty;

    @ApiModelProperty(value = "单位ID")
    @TableField(value = "ORG_ID")
    private String orgId;

    @ApiModelProperty(value = "单位名称")
    @TableField(value = "ORG_NAME")
    private String orgName;


    @ApiModelProperty(value = "储备性质")
    @TableField(value = "RESERVE_LEVEL")
    private Integer reserveLevel;

    @ApiModelProperty(value = "业务状态: 0-待执行，1-执行中，2-已完成")
    @TableField(value = "STATE")
    private Integer state;

    @ApiModelProperty(value = "状态")
    @TableField(value = "ENABLED")
    private Integer enabled;

    @ApiModelProperty(value = "附件")
    @TableField(value = "ATTACHEMENTS")
    private String attachments;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty(value = "创建id")
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "更新id")
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "租户id")
    @TableField(value = "TENANT_ID")
    private String tenantId;

    @ApiModelProperty(value = "组织")
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;


    /**
     * ---------------------以下非实体字段---------------------
     */


    // 出入库明细
    @ApiModelProperty(value = "出入库明细")
    @TableField(exist = false)
    private List<InoutDetail> children;

}
