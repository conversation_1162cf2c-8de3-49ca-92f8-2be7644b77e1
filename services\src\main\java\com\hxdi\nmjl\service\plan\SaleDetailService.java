package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.SaleDetail;

import java.util.List;

public interface SaleDetailService extends IBaseService<SaleDetail> {

    /**
     * 根据订单ID查询订单详情
     * @param orderId
     * @return
     */
    List<SaleDetail> getList(String orderId);

    /**
     * 删除订单详情
     * @param orderId
     */
    void removeV1(String orderId);
}
