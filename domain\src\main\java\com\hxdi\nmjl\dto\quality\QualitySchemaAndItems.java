package com.hxdi.nmjl.dto.quality;

import com.hxdi.nmjl.domain.quality.QualityItem;
import com.hxdi.nmjl.domain.quality.QualitySchema;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 质检方案及对应质检标准项列表
 * @author: 王贝强
 * @create: 2025-04-16 14:33
 */
@Setter
@Getter
@NoArgsConstructor
@ApiModel(description = "质检方案及标准项列表")
public class QualitySchemaAndItems implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="质检方案Id")
    private String id;

    @ApiModelProperty(value="方案名称")
    private String schemaName;

    @ApiModelProperty(value="品类ID")
    private String classificationId;

    @ApiModelProperty(value="品类名称")
    private String classificationName;

    @ApiModelProperty(value="是否为该品类默认方案:0-否，1-是")
    private Integer isdefault;

    @ApiModelProperty(value="创建日期")
    private Date createTime;

    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    @ApiModelProperty(value="质检标准项列表")
    private List<QualityItem> items;

    /**
     * 根据质检方案和标准项列表构造
     * @param schema
     * @param items
     */
    public QualitySchemaAndItems(QualitySchema schema, List<QualityItem> items) {
        this.id = schema.getId();
        this.schemaName = schema.getSchemaName();
        this.classificationId = schema.getClassificationId();
        this.classificationName = schema.getClassificationName();
        this.isdefault = schema.getIsdefault();
        this.createTime = schema.getCreateTime();
        this.updateTime = schema.getUpdateTime();
        this.items = items;
    }

    public QualitySchema convertToQualitySchema(){
        QualitySchema schema=new QualitySchema();
        schema.setId(this.id);
        schema.setSchemaName(this.schemaName);
        schema.setClassificationId(this.classificationId);
        schema.setClassificationName(this.classificationName);
        schema.setIsdefault(this.isdefault);
        return schema;
    }
}
