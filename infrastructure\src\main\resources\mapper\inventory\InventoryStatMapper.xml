<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryStatMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryStat">
    <!--@mbg.generated-->
    <!--@Table B_INVENTORY_STAT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="INVENTORY_ID" jdbcType="VARCHAR" property="inventoryId" />
    <result column="REPORT_DATE" jdbcType="DATE" property="reportDate" />
    <result column="START_QTY" jdbcType="DECIMAL" property="startQty" />
    <result column="IN_QTY" jdbcType="DECIMAL" property="inQty" />
    <result column="OUT_QTY" jdbcType="DECIMAL" property="outQty" />
    <result column="FINAL_QTY" jdbcType="DECIMAL" property="finalQty" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INVENTORY_ID, REPORT_DATE, START_QTY, IN_QTY, OUT_QTY, FINAL_QTY, TENANT_ID,
    DATA_HIERARCHY_ID
  </sql>

  <select id="listV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryStatCondition" resultType="com.hxdi.nmjl.vo.inventory.InventoryStatResVO">
    select s.REPORT_DATE , s.START_QTY ,s.IN_QTY ,s.OUT_QTY, s.FINAL_QTY,
      i.STORE_ID ,i.STORE_NAME ,i.ST_ID , i.ST_NAME , i.LOC_ID , i.LOC_NAME , i.MANAGE_UNIT_ID , i.MANAGE_UNIT_NAME ,
      i.CATALOG_ID , i.CATALOG_NAME , i.GRADE , i.RESERVE_LEVEL , i.CREATE_TIME ,
    s.DATA_HIERARCHY_ID  -- 数据权限字段
    from B_INVENTORY_STAT s
    left join B_INVENTORY i on s.INVENTORY_ID = i.ID
    <where>
      <if test="condition.startTime != null">
        and s.REPORT_DATE &gt;= #{condition.startTime}
      </if>
      <if test="condition.endTime != null">
        and s.REPORT_DATE &lt;= #{condition.endTime}
      </if>
      <if test="condition.stId != null">
        and i.ST_ID = #{condition.stId}
      </if>
      <if test="condition.stName != null and condition.stName !='' ">
        and i.ST_NAME like concat('%',#{condition.stName},'%')
      </if>
      <if test="condition.catalogId != null">
        and i.CATALOG_ID = #{condition.catalogId}
      </if>
      <if test="condition.catalogName != null and condition.catalogName !='' ">
        and i.CATALOG_NAME like concat('%',#{condition.catalogName},'%')
      </if>
    </where>
    and i.ENABLED = 1
    ORDER BY s.CREATE_TIME DESC
  </select>

  <select id="pageV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryStatCondition" resultType="com.hxdi.nmjl.vo.inventory.InventoryStatResVO">
    select s.REPORT_DATE , s.START_QTY ,s.IN_QTY ,s.OUT_QTY, s.FINAL_QTY,
    i.STORE_ID ,i.STORE_NAME ,i.ST_ID , i.ST_NAME , i.LOC_ID , i.LOC_NAME , i.MANAGE_UNIT_ID , i.MANAGE_UNIT_NAME ,
    i.CATALOG_ID , i.CATALOG_NAME , i.GRADE , i.RESERVE_LEVEL , i.CREATE_TIME ,
    s.DATA_HIERARCHY_ID  -- 数据权限字段
    from B_INVENTORY_STAT s
    left join B_INVENTORY i on s.INVENTORY_ID = i.ID
    <where>
      <if test="condition.startTime != null">
        and s.REPORT_DATE &gt;= #{condition.startTime}
      </if>
      <if test="condition.endTime != null">
        and s.REPORT_DATE &lt;= #{condition.endTime}
      </if>
      <if test="condition.stId != null">
        and i.ST_ID = #{condition.stId}
      </if>
      <if test="condition.stName != null and condition.stName !='' ">
        and i.ST_NAME like concat('%',#{condition.stName},'%')
      </if>
      <if test="condition.catalogId != null">
        and i.CATALOG_ID = #{condition.catalogId}
      </if>
      <if test="condition.catalogName != null and condition.catalogName !='' ">
        and i.CATALOG_NAME like concat('%',#{condition.catalogName},'%')
      </if>
    </where>
    and i.ENABLED = 1
    ORDER BY s.REPORT_DATE DESC
  </select>

  <select id="detailListV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryStatCondition" resultType="com.hxdi.nmjl.vo.inventory.InventoryStatResVO">
    SELECT
    DATE_FORMAT(s.REPORT_DATE, '%Y-%m') AS REPORT_DATE,
    MIN(s.START_QTY) AS START_QTY,                      -- 月初的 START_QTY
    CAST(SUM(s.IN_QTY) AS DECIMAL(15, 3)) AS IN_QTY,	-- 当月的 IN_QTY 总和
    CAST(SUM(s.OUT_QTY) AS DECIMAL(15, 3)) AS OUT_QTY,  -- 当月的 OUT_QTY 总和
    MAX(s.FINAL_QTY) AS FINAL_QTY,                      -- 月末的 FINAL_QTY
    i.STORE_ID ,i.STORE_NAME ,i.ST_ID ,i.ST_NAME ,i.LOC_ID ,i.LOC_NAME ,
    i.MANAGE_UNIT_ID ,i.MANAGE_UNIT_NAME ,i.CATALOG_ID ,i.CATALOG_NAME ,
    i.GRADE ,i.RESERVE_LEVEL ,
    s.DATA_HIERARCHY_ID  -- 数据权限字段
    FROM
    B_INVENTORY_STAT s
    LEFT JOIN
    B_INVENTORY i ON s.INVENTORY_ID = i.ID
    WHERE
    i.ENABLED = 1
    <if test="condition.startTime != null">
      AND s.REPORT_DATE &gt;= #{condition.startTime}
    </if>
    <if test="condition.endTime != null">
      AND s.REPORT_DATE &lt;= #{condition.endTime}
    </if>
    <if test="condition.stId != null">
      AND i.ST_ID = #{condition.stId}
    </if>
    <if test="condition.stName != null and condition.stName != '' ">
      AND i.ST_NAME LIKE CONCAT('%', #{condition.stName}, '%')
    </if>
    <if test="condition.catalogId != null">
      AND i.CATALOG_ID = #{condition.catalogId}
    </if>
    <if test="condition.catalogName != null and condition.catalogName != '' ">
      AND i.CATALOG_NAME LIKE CONCAT('%', #{condition.catalogName}, '%')
    </if>
    GROUP BY
    DATE_FORMAT(s.REPORT_DATE, '%Y-%m'),i.STORE_ID,i.STORE_NAME,i.ST_ID,i.ST_NAME,i.LOC_ID,i.LOC_NAME,
    i.MANAGE_UNIT_ID,i.MANAGE_UNIT_NAME,i.CATALOG_ID,i.CATALOG_NAME,i.GRADE,i.RESERVE_LEVEL,
    s.DATA_HIERARCHY_ID  -- 数据权限字段
    ORDER BY
    s.REPORT_DATE;
  </select>

  <select id="detailPageV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryStatCondition" resultType="com.hxdi.nmjl.vo.inventory.InventoryStatResVO">
    SELECT
    DATE_FORMAT(s.REPORT_DATE, '%Y-%m') AS REPORT_DATE,
    MIN(s.START_QTY) AS START_QTY,                      -- 月初的 START_QTY
    CAST(SUM(s.IN_QTY) AS DECIMAL(15, 3)) AS IN_QTY,	-- 当月的 IN_QTY 总和
    CAST(SUM(s.OUT_QTY) AS DECIMAL(15, 3)) AS OUT_QTY,  -- 当月的 OUT_QTY 总和
    MAX(s.FINAL_QTY) AS FINAL_QTY,                      -- 月末的 FINAL_QTY
    i.STORE_ID ,i.STORE_NAME ,i.ST_ID ,i.ST_NAME ,i.LOC_ID ,i.LOC_NAME ,
    i.MANAGE_UNIT_ID ,i.MANAGE_UNIT_NAME ,i.CATALOG_ID ,i.CATALOG_NAME ,
    i.GRADE ,i.RESERVE_LEVEL ,
    s.DATA_HIERARCHY_ID  -- 数据权限字段
    FROM
    B_INVENTORY_STAT s
    LEFT JOIN
    B_INVENTORY i ON s.INVENTORY_ID = i.ID
    WHERE
    i.ENABLED = 1
    <if test="condition.startTime != null">
      AND s.REPORT_DATE &gt;= #{condition.startTime}
    </if>
    <if test="condition.endTime != null">
      AND s.REPORT_DATE &lt;= #{condition.endTime}
    </if>
    <if test="condition.stId != null">
      AND i.ST_ID = #{condition.stId}
    </if>
    <if test="condition.stName != null and condition.stName != '' ">
      AND i.ST_NAME LIKE CONCAT('%', #{condition.stName}, '%')
    </if>
    <if test="condition.catalogId != null">
      AND i.CATALOG_ID = #{condition.catalogId}
    </if>
    <if test="condition.catalogName != null and condition.catalogName != '' ">
      AND i.CATALOG_NAME LIKE CONCAT('%', #{condition.catalogName}, '%')
    </if>
    GROUP BY
    DATE_FORMAT(s.REPORT_DATE, '%Y-%m'),i.STORE_ID,i.STORE_NAME,i.ST_ID,i.ST_NAME,i.LOC_ID,i.LOC_NAME,
    i.MANAGE_UNIT_ID,i.MANAGE_UNIT_NAME,i.CATALOG_ID,i.CATALOG_NAME,i.GRADE,i.RESERVE_LEVEL,
    s.DATA_HIERARCHY_ID  -- 数据权限字段
    ORDER BY
    s.REPORT_DATE;
  </select>
</mapper>
