<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryStatMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryStat">
    <!--@mbg.generated-->
    <!--@Table B_INVENTORY_STAT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="INVENTORY_ID" jdbcType="VARCHAR" property="inventoryId" />
    <result column="REPORT_DATE" jdbcType="DATE" property="reportDate" />
    <result column="START_QTY" jdbcType="DECIMAL" property="startQty" />
    <result column="IN_QTY" jdbcType="DECIMAL" property="inQty" />
    <result column="OUT_QTY" jdbcType="DECIMAL" property="outQty" />
    <result column="FINAL_QTY" jdbcType="DECIMAL" property="finalQty" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INVENTORY_ID, REPORT_DATE, START_QTY, IN_QTY, OUT_QTY, FINAL_QTY, TENANT_ID,
    DATA_HIERARCHY_ID
  </sql>

  <select id="listV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryStatCondition" resultType="com.hxdi.nmjl.vo.inventory.InventoryStatResVO">
      SELECT
      tmp.REPORT_DATE,
      tmp.START_QTY,
      tmp.IN_QTY,
      tmp.OUT_QTY,
      tmp.FINAL_QTY,
      tmp.STORE_ID,
      tmp.STORE_NAME,
      tmp.ST_ID,
      tmp.ST_NAME,
      tmp.MANAGE_UNIT_ID,
      tmp.MANAGE_UNIT_NAME,
      tmp.CATALOG_ID,
      tmp.CATALOG_NAME,
      tmp.GRADE,
      tmp.RESERVE_LEVEL,
      tmp.CREATE_TIME,
      tmp.DATA_HIERARCHY_ID
      FROM (
      SELECT
      s.REPORT_DATE,
      -- 使用窗口函数获取当天第一条记录的START_QTY
      FIRST_VALUE(s.START_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ORDER BY s.REPORT_DATE
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS START_QTY,
      -- 当天入库出库总和
      SUM(s.IN_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ) AS IN_QTY,
      SUM(s.OUT_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ) AS OUT_QTY,
      -- 使用窗口函数获取当天最后一条记录的FINAL_QTY
      LAST_VALUE(s.FINAL_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ORDER BY s.REPORT_DATE
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS FINAL_QTY,
      i.STORE_ID,
      i.STORE_NAME,
      i.ST_ID,
      i.ST_NAME,
      i.MANAGE_UNIT_ID,
      i.MANAGE_UNIT_NAME,
      i.CATALOG_ID,
      i.CATALOG_NAME,
      i.GRADE,
      i.RESERVE_LEVEL,
      i.CREATE_TIME,
      s.DATA_HIERARCHY_ID,
      -- 添加行号用于去重
      ROW_NUMBER() OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ORDER BY s.REPORT_DATE DESC
      ) AS rn
      FROM B_INVENTORY_STAT s
      LEFT JOIN B_INVENTORY i ON s.INVENTORY_ID = i.ID
      WHERE i.ENABLED = 1
      <if test="condition.startTime != null">
          AND s.REPORT_DATE &gt;= #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
          AND s.REPORT_DATE &lt;= #{condition.endTime}
        </if>
        <if test="condition.stId != null">
          AND i.ST_ID = #{condition.stId}
        </if>
        <if test="condition.stName != null and condition.stName != '' ">
          AND i.ST_NAME LIKE CONCAT('%', #{condition.stName}, '%')
        </if>
        <if test="condition.catalogId != null">
          AND i.CATALOG_ID = #{condition.catalogId}
        </if>
        <if test="condition.catalogName != null and condition.catalogName != '' ">
          AND i.CATALOG_NAME LIKE CONCAT('%', #{condition.catalogName}, '%')
        </if>
    ) tmp
    WHERE tmp.rn = 1
    ORDER BY tmp.REPORT_DATE DESC,tmp.ST_ID
  </select>

  <select id="pageV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryStatCondition" resultType="com.hxdi.nmjl.vo.inventory.InventoryStatResVO">
      SELECT
      tmp.REPORT_DATE,
      tmp.START_QTY,
      tmp.IN_QTY,
      tmp.OUT_QTY,
      tmp.FINAL_QTY,
      tmp.STORE_ID,
      tmp.STORE_NAME,
      tmp.ST_ID,
      tmp.ST_NAME,
      tmp.MANAGE_UNIT_ID,
      tmp.MANAGE_UNIT_NAME,
      tmp.CATALOG_ID,
      tmp.CATALOG_NAME,
      tmp.GRADE,
      tmp.RESERVE_LEVEL,
      tmp.CREATE_TIME,
      tmp.DATA_HIERARCHY_ID
      FROM (
      SELECT
      s.REPORT_DATE,
      -- 使用窗口函数获取当天第一条记录的START_QTY
      FIRST_VALUE(s.START_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ORDER BY s.REPORT_DATE
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS START_QTY,
      -- 当天入库出库总和
      SUM(s.IN_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ) AS IN_QTY,
      SUM(s.OUT_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ) AS OUT_QTY,
      -- 使用窗口函数获取当天最后一条记录的FINAL_QTY
      LAST_VALUE(s.FINAL_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ORDER BY s.REPORT_DATE
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS FINAL_QTY,
      i.STORE_ID,
      i.STORE_NAME,
      i.ST_ID,
      i.ST_NAME,
      i.MANAGE_UNIT_ID,
      i.MANAGE_UNIT_NAME,
      i.CATALOG_ID,
      i.CATALOG_NAME,
      i.GRADE,
      i.RESERVE_LEVEL,
      i.CREATE_TIME,
      s.DATA_HIERARCHY_ID,
      -- 添加行号用于去重
      ROW_NUMBER() OVER (
      PARTITION BY s.INVENTORY_ID, s.REPORT_DATE, i.ST_ID, i.CATALOG_ID
      ORDER BY s.REPORT_DATE DESC
      ) AS rn
      FROM B_INVENTORY_STAT s
      LEFT JOIN B_INVENTORY i ON s.INVENTORY_ID = i.ID
      WHERE i.ENABLED = 1
      <if test="condition.startTime != null">
          AND s.REPORT_DATE &gt;= #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
          AND s.REPORT_DATE &lt;= #{condition.endTime}
        </if>
        <if test="condition.stId != null">
          AND i.ST_ID = #{condition.stId}
        </if>
        <if test="condition.stName != null and condition.stName != '' ">
          AND i.ST_NAME LIKE CONCAT('%', #{condition.stName}, '%')
        </if>
        <if test="condition.catalogId != null">
          AND i.CATALOG_ID = #{condition.catalogId}
        </if>
        <if test="condition.catalogName != null and condition.catalogName != '' ">
          AND i.CATALOG_NAME LIKE CONCAT('%', #{condition.catalogName}, '%')
        </if>
    ) tmp
    WHERE tmp.rn = 1
    ORDER BY tmp.REPORT_DATE DESC,tmp.ST_ID
  </select>

  <select id="detailListV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryStatCondition" resultType="com.hxdi.nmjl.vo.inventory.InventoryStatResVO">
      SELECT
      tmp.REPORT_DATE,
      tmp.START_QTY,
      tmp.IN_QTY,
      tmp.OUT_QTY,
      tmp.FINAL_QTY,
      tmp.STORE_ID,
      tmp.STORE_NAME,
      tmp.ST_ID,
      tmp.ST_NAME,
      tmp.LOC_ID,
      tmp.LOC_NAME,
      tmp.MANAGE_UNIT_ID,
      tmp.MANAGE_UNIT_NAME,
      tmp.CATALOG_ID,
      tmp.CATALOG_NAME,
      tmp.GRADE,
      tmp.RESERVE_LEVEL,
      tmp.DATA_HIERARCHY_ID
      FROM (
      SELECT
      DATE(CONCAT(DATE_FORMAT(s.REPORT_DATE, '%Y-%m'), '-01')) AS REPORT_DATE,
      -- 使用窗口函数获取月初第一天的START_QTY
      FIRST_VALUE(s.START_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m')
      ORDER BY s.REPORT_DATE
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS START_QTY,
      -- 当月入库出库总和
      SUM(s.IN_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m')
      ) AS IN_QTY,
      SUM(s.OUT_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m')
      ) AS OUT_QTY,
      -- 使用窗口函数获取月末最后一天的FINAL_QTY
      LAST_VALUE(s.FINAL_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m')
      ORDER BY s.REPORT_DATE
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS FINAL_QTY,
      i.STORE_ID,
      i.STORE_NAME,
      i.ST_ID,
      i.ST_NAME,
      i.LOC_ID,
      i.LOC_NAME,
      i.MANAGE_UNIT_ID,
      i.MANAGE_UNIT_NAME,
      i.CATALOG_ID,
      i.CATALOG_NAME,
      i.GRADE,
      i.RESERVE_LEVEL,
      s.DATA_HIERARCHY_ID,
      -- 添加行号用于去重
      ROW_NUMBER() OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m'),
      i.STORE_ID, i.ST_ID, i.LOC_ID, i.CATALOG_ID
      ORDER BY s.REPORT_DATE DESC
      ) AS rn
      FROM B_INVENTORY_STAT s
      LEFT JOIN B_INVENTORY i ON s.INVENTORY_ID = i.ID
      WHERE i.ENABLED = 1
      <if test="condition.startTime != null">
          AND s.REPORT_DATE &gt;= #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
          AND s.REPORT_DATE &lt;= #{condition.endTime}
        </if>
        <if test="condition.stId != null">
          AND i.ST_ID = #{condition.stId}
        </if>
        <if test="condition.stName != null and condition.stName != '' ">
          AND i.ST_NAME LIKE CONCAT('%', #{condition.stName}, '%')
        </if>
        <if test="condition.catalogId != null">
          AND i.CATALOG_ID = #{condition.catalogId}
        </if>
        <if test="condition.catalogName != null and condition.catalogName != '' ">
          AND i.CATALOG_NAME LIKE CONCAT('%', #{condition.catalogName}, '%')
        </if>
    ) tmp
    WHERE tmp.rn = 1
    ORDER BY tmp.REPORT_DATE DESC,tmp.ST_ID,tmp.LOC_ID
  </select>

  <select id="detailPageV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryStatCondition" resultType="com.hxdi.nmjl.vo.inventory.InventoryStatResVO">
      SELECT
      tmp.REPORT_DATE,
      tmp.START_QTY,
      tmp.IN_QTY,
      tmp.OUT_QTY,
      tmp.FINAL_QTY,
      tmp.STORE_ID,
      tmp.STORE_NAME,
      tmp.ST_ID,
      tmp.ST_NAME,
      tmp.LOC_ID,
      tmp.LOC_NAME,
      tmp.MANAGE_UNIT_ID,
      tmp.MANAGE_UNIT_NAME,
      tmp.CATALOG_ID,
      tmp.CATALOG_NAME,
      tmp.GRADE,
      tmp.RESERVE_LEVEL,
      tmp.DATA_HIERARCHY_ID
      FROM (
      SELECT
      DATE(CONCAT(DATE_FORMAT(s.REPORT_DATE, '%Y-%m'), '-01')) AS REPORT_DATE,
      -- 使用窗口函数获取月初第一天的START_QTY
      FIRST_VALUE(s.START_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m')
      ORDER BY s.REPORT_DATE
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS START_QTY,
      -- 当月入库出库总和
      SUM(s.IN_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m')
      ) AS IN_QTY,
      SUM(s.OUT_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m')
      ) AS OUT_QTY,
      -- 使用窗口函数获取月末最后一天的FINAL_QTY
      LAST_VALUE(s.FINAL_QTY) OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m')
      ORDER BY s.REPORT_DATE
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
      ) AS FINAL_QTY,
      i.STORE_ID,
      i.STORE_NAME,
      i.ST_ID,
      i.ST_NAME,
      i.LOC_ID,
      i.LOC_NAME,
      i.MANAGE_UNIT_ID,
      i.MANAGE_UNIT_NAME,
      i.CATALOG_ID,
      i.CATALOG_NAME,
      i.GRADE,
      i.RESERVE_LEVEL,
      s.DATA_HIERARCHY_ID,
      -- 添加行号用于去重
      ROW_NUMBER() OVER (
      PARTITION BY s.INVENTORY_ID, DATE_FORMAT(s.REPORT_DATE, '%Y-%m'),
      i.STORE_ID, i.ST_ID, i.LOC_ID, i.CATALOG_ID
      ORDER BY s.REPORT_DATE DESC
      ) AS rn
      FROM B_INVENTORY_STAT s
      LEFT JOIN B_INVENTORY i ON s.INVENTORY_ID = i.ID
      WHERE i.ENABLED = 1
      <if test="condition.startTime != null">
          AND s.REPORT_DATE &gt;= #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
          AND s.REPORT_DATE &lt;= #{condition.endTime}
        </if>
        <if test="condition.stId != null">
          AND i.ST_ID = #{condition.stId}
        </if>
        <if test="condition.stName != null and condition.stName != '' ">
          AND i.ST_NAME LIKE CONCAT('%', #{condition.stName}, '%')
        </if>
        <if test="condition.catalogId != null">
          AND i.CATALOG_ID = #{condition.catalogId}
        </if>
        <if test="condition.catalogName != null and condition.catalogName != '' ">
          AND i.CATALOG_NAME LIKE CONCAT('%', #{condition.catalogName}, '%')
        </if>
    ) tmp
    WHERE tmp.rn = 1
    ORDER BY tmp.REPORT_DATE DESC,tmp.ST_ID,tmp.LOC_ID
  </select>
</mapper>
