package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.plan.ContractDetail;
import com.hxdi.nmjl.mapper.plan.ContractDetailMapper;
import com.hxdi.nmjl.service.plan.ContractDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class ContractDetailServiceImpl extends BaseServiceImpl<ContractDetailMapper, ContractDetail> implements ContractDetailService {


    @Override
    public List<ContractDetail> getList(String contractId) {
        return baseMapper.selectList(Wrappers.<ContractDetail>lambdaQuery().eq(ContractDetail::getContractId, contractId));
    }

    @Override
    public List<ContractDetail> getListByContractIds(List<String> contractIds) {
        return baseMapper.selectList(Wrappers.<ContractDetail>lambdaQuery().in(ContractDetail::getContractId, contractIds));
    }

    @Override
    public void removeByContractId(String contractId) {
        this.remove(Wrappers.<ContractDetail>lambdaUpdate().eq(ContractDetail::getContractId, contractId));
    }

    @Override
    public ContractDetail selectByCatalogIdAndGrade(String contractId, String catalogId, String grade) {
        return baseMapper.selectOne(Wrappers.<ContractDetail>lambdaQuery()
                .eq(ContractDetail::getContractId, contractId)
                .eq(ContractDetail::getCatalogId, catalogId)
                .eq(ContractDetail::getGrade, grade));
    }
}
