package com.hxdi.nmjl.mapper.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.storeproduction.StoreRawMaterialPurchaseCondition;
import com.hxdi.nmjl.domain.storeproduction.StoreRawMaterialPurchase;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 原料采购Mapper接口
 */
public interface StoreRawMaterialPurchaseMapper extends SuperMapper<StoreRawMaterialPurchase> {

    Page<StoreRawMaterialPurchase> selectPageV1(Page<StoreRawMaterialPurchase> page, @Param("condition") StoreRawMaterialPurchaseCondition condition);

    List<StoreRawMaterialPurchase> selectListV1(@Param("condition") StoreRawMaterialPurchaseCondition condition);
}
