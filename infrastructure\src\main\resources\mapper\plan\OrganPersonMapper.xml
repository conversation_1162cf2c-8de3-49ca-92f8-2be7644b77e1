<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.OrganPersonMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.OrganPerson">
        <!--@mbg.generated-->
        <!--@Table B_ORGAN_PERSON-->
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="SEX" property="sex" />
        <result column="ORG_ID" property="orgId" />
        <result column="ORG_NAME" property="orgName" />
        <result column="MOBILE" property="mobile" />
        <result column="EMAIL" property="email" />
        <result column="EDU" property="edu" />
        <result column="JOB_TITLE" property="jobTitle" />
        <result column="JOB_DESC" property="jobDesc" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, "NAME", SEX, ORG_ID, ORG_NAME, MOBILE, EMAIL, EDU, JOB_TITLE, JOB_DESC, CREATE_TIME,
        UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>
</mapper>
