package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.base.ModelsCondition;
import com.hxdi.nmjl.domain.base.Models;
import com.hxdi.nmjl.service.base.ModelsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "规格管理")
@RestController
@RequestMapping("/model")
public class ModelsController extends BaseController<ModelsService, Models> {

    @ApiOperation("保存更新")
    @PostMapping("/saveOrUpdates")
    public ResultBody<Void> saveOrUpdates(@RequestBody Models model) {
        bizService.saveOrUpdates(model);
        return ResultBody.OK();
    }

    @ApiOperation("删除")
    @DeleteMapping("/remove")
    public ResultBody<Void> remove(@RequestParam("id") String modelId) {
        bizService.remove(modelId);
        return ResultBody.OK();
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<Models>> pages(ModelsCondition condition) {
        return ResultBody.<Page<Models>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<Models>> lists(ModelsCondition condition) {
        return ResultBody.<List<Models>>OK().data(bizService.lists(condition));
    }
}
