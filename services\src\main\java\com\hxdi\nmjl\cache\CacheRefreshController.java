package com.hxdi.nmjl.cache;

import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 缓存刷新控制器
 * 提供手动刷新缓存的接口
 */
@RestController
@RequestMapping("/api/cache")
@Slf4j
public class CacheRefreshController {

    @Resource
    private CacheManager cacheManager;

    @Resource
    private CachePreloadRunner cachePreloadRunner;

    private void securityCheck(String code) {

        if ("NMJL1qaz".equals(code)) {
            return;
        }

        throw new BaseException("access denied!");
    }

    /**
     * 清除指定缓存 e.g. localhost:{port}/noLogin/api/cache/redis/refresh/CATEGORY?code=NMJL1qaz
     *
     * @param code   附加权限码
     * @param prefix 指定缓存前缀{@link RedisKeys}
     * @return
     */
    @GetMapping(value = "/remove/{prefix}")
    public ResultBody<?> removeRedisCache(@RequestParam(value = "code") String code, @PathVariable String prefix) {
        securityCheck(code);

        if (CommonUtils.isNotEmpty(prefix)) {
            //清除特定缓存
            RedisKeys redisKeys = RedisKeys.match(prefix);
            if (redisKeys == RedisKeys.NOOP) {
                return ResultBody.failed().msg(String.format("[%s]键不匹配", prefix));
            }

            Objects.requireNonNull(cacheManager.getCache(redisKeys.key())).clear();

            log.info("手动触发缓存清除: {}", redisKeys.key());
            Map<String, Object> result = new HashMap<>();

            try {
                if (cacheManager.getCache(redisKeys.key()) != null) {
                    Objects.requireNonNull(cacheManager.getCache(redisKeys.key())).clear();
                    log.info("缓存清除成功...");
                    result.put("success", true);
                    result.put("message", "缓存 [" + redisKeys.key() + "] 清除成功");
                } else {
                    log.info("缓存 [{}] 不存在...", redisKeys.key());
                    result.put("success", false);
                    result.put("message", "缓存 [" + redisKeys.key() + "] 不存在");
                }

            } catch (Exception e) {
                log.error("缓存清除失败", e);
                result.put("success", false);
                result.put("message", "缓存清除失败: " + e.getMessage());
            }

            if (result.get("success").equals(true)) {
                return ResultBody.success().msg("缓存清除成功");
            } else {
                return ResultBody.failed().msg(result.get("message").toString());
            }

        }

        return ResultBody.failed().msg("请指定缓存前缀");
    }


    /**
     * 刷新所有缓存
     * e.g. localhost:{port}/noLogin/api/cache/refresh-all?code=NMJL1qaz
     *
     * @param code 附加权限码
     * @return 结果
     */
    @GetMapping("/refresh-all")
    public Map<String, Object> refreshAll(@RequestParam String code) {

        securityCheck(code);

        log.info("手动触发刷新所有缓存");
        Map<String, Object> result = new HashMap<>();

        try {
            // 清空所有缓存
            cacheManager.getCacheNames().forEach(cacheName -> {
                log.info("清空缓存: {}", cacheName);
                Objects.requireNonNull(cacheManager.getCache(cacheName)).clear();
            });

            // 重新预加载热点数据
            cachePreloadRunner.preloadAll();

            result.put("success", true);
            result.put("message", "所有缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新缓存失败", e);
            result.put("success", false);
            result.put("message", "缓存刷新失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 刷新指定类型的缓存数据
     * e.g. localhost:{port}/noLogin/api/cache/refresh?code=NMJL1qaz&type=catalog
     *
     * @param type 缓存类型，支持：catalog, classification, organization, store_house, store_location
     * @return 刷新结果
     */
    @GetMapping("/refresh")
    public ResultBody<?> refreshByType(@RequestParam String code, @RequestParam String type) {

        securityCheck(code);

        log.info("手动触发刷新指定类型缓存: {}", type);

        try {

            RedisKeys CacheKey = RedisKeys.match(type);

            if (CacheKey != RedisKeys.NOOP) {
                Objects.requireNonNull(cacheManager.getCache(CacheKey.key())).clear();
                cachePreloadRunner.LoadCache(type);
            } else {
                return ResultBody.failed().msg("不支持的缓存类型: " + type);
            }

            return ResultBody.ok().msg("缓存 [" + type + "] 刷新成功");
        } catch (Exception e) {
            log.error("刷新缓存失败", e);
            return ResultBody.failed().msg("缓存刷新失败: " + e.getMessage());
        }
    }
}
