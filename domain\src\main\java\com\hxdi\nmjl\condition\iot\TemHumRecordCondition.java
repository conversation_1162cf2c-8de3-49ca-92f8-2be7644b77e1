package com.hxdi.nmjl.condition.iot;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: nmjl-service
 * @description: 温湿度检测记录查询条件
 * @author: 王贝强
 * @create: 2025-04-17 12:55
 */
@Setter
@Getter
@ApiModel(description = "温湿度检测记录查询条件")
public class TemHumRecordCondition extends QueryCondition {

    @ApiModelProperty(value = "仓房ID")
    private String stId;

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "检测开始时间")
    private String startTime;

    @ApiModelProperty(value = "检测结束时间")
    private String endTime;

    @ApiModelProperty(value = "是否异常：0-否，1-是")
    private Integer errIs;

    @ApiModelProperty(value = "设备节点Id")
    private Integer nodeId;

    @ApiModelProperty(value = "安装位置")
    private String area;

}
