package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "品牌传播活动查询条件")
@Getter
@Setter
public class BrandActivityCondition extends QueryCondition {

    @ApiModelProperty(value = "品牌ID")
    private String brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "活动类型：字典PPHDLX")
    private String activityType;

    @ApiModelProperty(value = "渠道方式：字典PPQDFS")
    private String channel;

    @ApiModelProperty(value = "目标受众：字典PPMBSZ")
    private String targetUser;

    @ApiModelProperty(value = "办理单位ID")
    private String orgId;

    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "创建时间（起始）")
    private Date createStartTime;

    @ApiModelProperty(value = "创建时间（结束）")
    private Date createEndTime;
}