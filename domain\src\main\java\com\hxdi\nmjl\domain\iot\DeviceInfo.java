package com.hxdi.nmjl.domain.iot;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description="设备信息")
@TableName(value = "B_DEVICE_INFO")
public class DeviceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value="库点名称")
    private String storeName;

    /**
     * 设备名称
     */
    @TableField(value = "DEVICE_NAME")
    @ApiModelProperty(value="设备名称")
    private String deviceName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value="仓房ID")
    private String stId;

    /**
     * 仓房
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value="仓房")
    private String stName;

    /**
     * 设备状态：字典SBZT
     */
    @TableField(value = "DEVICE_STATE")
    @ApiModelProperty(value="设备状态：字典SBZT")
    private Integer deviceState;

    /**
     * 设备厂家
     */
    @TableField(value = "MAKER")
    @ApiModelProperty(value="设备厂家")
    private String maker;

    /**
     * 设备IP
     */
    @TableField(value = "IPA")
    @ApiModelProperty(value="设备IP")
    private String ipa;

    /**
     * 设备端口
     */
    @TableField(value = "PORTS")
    @ApiModelProperty(value="设备端口")
    private String ports;

    /**
     * 设备序列号：唯一
     */
    @TableField(value = "SERIAL")
    @ApiModelProperty(value="设备序列号：唯一")
    private String serial;

    /**
     * 经度
     */
    @TableField(value = "LON")
    @ApiModelProperty(value="经度")
    private String lon;

    /**
     * 纬度
     */
    @TableField(value = "LAT")
    @ApiModelProperty(value="纬度")
    private String lat;

    /**
     * 设备类型:字典SBLX
     */
    @TableField(value = "DEVICE_TYPE")
    @ApiModelProperty(value="设备类型:字典SBLX")
    private String deviceType;

    /**
     * 安装位置
     */
    @TableField(value = "AREA")
    @ApiModelProperty(value="安装位置")
    private String area;

    /**
     * 状态：0-删除，1-有效
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value="状态：0-删除，1-有效")
    private Integer enabled;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;

    /**
     * 节点数量
     */
    @TableField(value = "NODE_NUM")
    @ApiModelProperty(value="节点数量")
    private Integer nodeNum;
}