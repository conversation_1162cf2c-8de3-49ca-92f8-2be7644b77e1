<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyResponseMapper">

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, RESPONSE_CODE, RESPONSE_NAME, RESPONSE_NUMBER, RESP_LEVEL,
        RESP_TYPE, ORG_NAME, ORG_ID, PUBLISH_TIME, PUBLISH_ORG_ID,
        PUBLISH_ORG_NAME, PLAN_AMOUNT, GROUP_NO, GROUP_NAME, GROUP_TEL,
        LEADER_ID, LEADER, MOBILE, MEMBER_IDS, MEMBERS,
        CREATE_NAME, PUBLISH_STATE, APPROVE_TIME, APPROVE_STATUS,
        APPROVER, APPROVE_OPINION, ENABLED, ATTACHMENT,
        CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID,
        TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyResponse">
        <id column="ID" property="id"/>
        <result column="RESPONSE_CODE" property="responseCode"/>
        <result column="RESPONSE_NAME" property="responseName"/>
        <result column="RESPONSE_NUMBER" property="responseNumber"/>
        <result column="RESP_LEVEL" property="responseLevel"/>
        <result column="RESP_TYPE" property="responseType"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="ORG_ID" property="orgId"/>
        <result column="PUBLISH_TIME" property="publishTime"/>
        <result column="PUBLISH_ORG_ID" property="publishOrgId"/>
        <result column="PUBLISH_ORG_NAME" property="publishOrgName"/>
        <result column="PLAN_AMOUNT" property="planAmount"/>
        <result column="GROUP_NO" property="groupNo"/>
        <result column="GROUP_NAME" property="groupName"/>
        <result column="GROUP_TEL" property="groupTel"/>
        <result column="LEADER_ID" property="leaderId"/>
        <result column="LEADER" property="leader"/>
        <result column="MOBILE" property="mobile"/>
        <result column="MEMBER_IDS" property="memberIds"/>
        <result column="MEMBERS" property="members"/>
        <result column="CREATE_NAME" property="createName"/>
        <result column="PUBLISH_STATE" property="publishState"/>
        <result column="APPROVE_TIME" property="approveTime"/>
        <result column="APPROVE_STATUS" property="approveStatus"/>
        <result column="APPROVER" property="approver"/>
        <result column="APPROVE_OPINION" property="approveOpinion"/>
        <result column="ENABLED" property="enabled"/>
        <result column="ATTACHMENT" property="attachment"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
    </resultMap>

    <!-- 列表查询 -->
    <select id="getList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_EMERGENCY_RESPONSE
        <where>
            ENABLED = 1
            <!-- 预案编号 -->
            <if test="condition.responseCode != null and condition.responseCode != ''">
                AND RESPONSE_CODE = #{condition.responseCode}
            </if>
            <!-- 预案级别 -->
            <if test="condition.responseLevel != null">
                AND RESP_LEVEL = #{condition.responseLevel}
            </if>
            <!-- 发布状态 -->
            <if test="condition.publishState != null">
                AND PUBLISH_STATE = #{condition.publishState}
            </if>
            <!-- 审核状态 -->
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <!-- 审核时间范围 -->
            <if test="condition.startTime != null">
                AND APPROVE_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                AND APPROVE_TIME &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 分页查询 -->
    <select id="getPages" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_EMERGENCY_RESPONSE
        <where>
            ENABLED = 1
            <!-- 预案编号 -->
            <if test="condition.responseCode != null and condition.responseCode != ''">
                AND RESPONSE_CODE = #{condition.responseCode}
            </if>
            <!-- 预案级别 -->
            <if test="condition.responseLevel != null">
                AND RESP_LEVEL = #{condition.responseLevel}
            </if>
            <!-- 发布状态 -->
            <if test="condition.publishState != null">
                AND PUBLISH_STATE = #{condition.publishState}
            </if>
            <!-- 审核状态 -->
            <if test="condition.approveStatus != null">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <!-- 审核时间范围 -->
            <if test="condition.startTime != null">
                AND APPROVE_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                AND APPROVE_TIME &lt;= #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
