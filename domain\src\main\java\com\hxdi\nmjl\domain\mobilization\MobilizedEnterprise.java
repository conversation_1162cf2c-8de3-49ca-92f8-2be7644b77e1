package com.hxdi.nmjl.domain.mobilization;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 动员企业信息
 */
@Getter
@Setter
@TableName(value = "B_MOBILIZED_ENTERPRISE")
public class MobilizedEnterprise implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 动员企业编号
     */
    @TableField(value = "ENTERPRISE_CODE")
    private String enterpriseCode;

    /**
     * 管理单位id
     */
    @TableField(value = "UNIT_ID")
    private String unitId;

    /**
     * 管理单位名称
     */
    @TableField(value = "UNIT_NAME")
    private String unitName;

    /**
     * 动员企业名称
     */
    @TableField(value = "ENTERPRISE_NAME")
    private String enterpriseName;

    /**
     * 信用代码
     */
    @TableField(value = "CREDIT_CODE")
    private String creditCode;

    /**
     * 企业类型:字典DYQYLX
     */
    @TableField(value = "TYPE")
    private String type;

    /**
     * 所在区域编码
     */
    @TableField(value = "AREA_CODE")
    private String areaCode;

    /**
     * 地址
     */
    @TableField(value = "ADDR")
    private String addr;

    /**
     * 详细地址
     */
    @TableField(value = "DETAIL_ADDR")
    private String detailAddr;

    /**
     * 负责人
     */
    @TableField(value = "PRINCIPAL")
    private String principal;

    /**
     * 联系方式
     */
    @TableField(value = "PRINCIPAL_TEL")
    private String principalTel;

    /**
     * 备用联系人
     */
    @TableField(value = "ALTERNATE_CONTACT_PERSON")
    private String alternateContactPerson;

    /**
     * 备用联系电话
     */
    @TableField(value = "ALTERNATE_CONTACT_TEL")
    private String alternateContactTel;

    /**
     * 经度
     */
    @TableField(value = "LON")
    private String lon;

    /**
     * 纬度
     */
    @TableField(value = "LAT")
    private String lat;

    /**
     * 备案状态：1-正常，2-撤销，3-观察
     */
    @TableField(value = "STATE", fill = FieldFill.INSERT)
    private Integer state;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHEMENTS")
    private String attachements;

    @TableField(value = "APPROVE_STATUS")
    @ApiModelProperty(value = "0-未提交，1-待审核，2-已审核，3-驳回")
    private Integer approveStatus;

    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
