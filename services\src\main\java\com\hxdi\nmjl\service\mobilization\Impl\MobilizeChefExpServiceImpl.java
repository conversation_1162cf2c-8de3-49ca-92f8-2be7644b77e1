package com.hxdi.nmjl.service.mobilization.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.mobilization.MobilizeChefExp;
import com.hxdi.nmjl.mapper.mobilization.MobilizeChefExpMapper;
import com.hxdi.nmjl.service.mobilization.MobilizeChefExpService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class MobilizeChefExpServiceImpl extends BaseServiceImpl<MobilizeChefExpMapper, MobilizeChefExp> implements MobilizeChefExpService {
    @Override
    public MobilizeChefExp getList(String ChefId){
        return baseMapper.selectOne(Wrappers.<MobilizeChefExp>lambdaQuery().eq(MobilizeChefExp::getChefId, ChefId));
    }
}
