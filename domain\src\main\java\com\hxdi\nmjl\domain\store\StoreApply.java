package com.hxdi.nmjl.domain.store;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 店铺申请表
 */
@Getter
@Setter
@TableName(value = "B_STORE_APPLY")
public class StoreApply implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 申请编号
     */
    @TableField(value = "APPLY_NO")
    private String applyNo;

    /**
     * 企业名称
     */
    @TableField(value = "ENTERPRISE_NAME")
    private String enterpriseName;

    /**
     * 统一社会信用代码
     */
    @TableField(value = "CREDIT_CODE")
    private String creditCode;

    /**
     * 法人
     */
    @TableField(value = "FR")
    private String fr;

    /**
     * 法人身份证号
     */
    @TableField(value = "LEGAL_PERSON_ID_CARD")
    private String legalPersonIdCard;

    /**
     * 负责人
     */
    @TableField(value = "FZR")
    private String fzr;

    /**
     * 联系电话
     */
    @TableField(value = "MOBILE")
    private String mobile;


    /**
     * 申请类型（1-政策性；2-军民融合）
     */
    @TableField(value = "APPLY_TYPE")
    private Integer applyType;

    /**
     * 机构ID
     */
    @TableField(value = "ORG_ID")
    private String orgId;

    /**
     * 机构名称
     */
    @TableField(value = "ORG_NAME")
    private String orgName;


    /**
     * 省
     */
    @TableField(value = "PROVINCE_CODE")
    private String provinceCode;

    /**
     * 市
     */
    @TableField(value = "CITY_CODE")
    private String cityCode;

    /**
     * 区县
     */
    @TableField(value = "COUNTY_CODE")
    private String countyCode;

    /**
     * 区划地址
     */
    @TableField(value = "AREA")
    private String area;

    /**
     * 详细地址
     */
    @TableField(value = "DETAIL_ADDR")
    private String detailAddr;

    /**
     * 邮编
     */
    @TableField(value = "POST_CODE")
    private String postCode;

    /**
     * 成立时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "REGISTRE_DATE")
    private Date registreDate;



    /**
     * 企业邮箱
     */
    @TableField(value = "EMAIL")
    private String email;

    /**
     * 经度
     */
    @TableField(value = "LON")
    private String lon;

    /**
     * 纬度
     */
    @TableField(value = "LAT")
    private String lat;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENT")
    private String attachment;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @TableField(value = "APPROVE_STATUS")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    private String approveOpinion;

    /**
     * 状态（1-有效 0删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}