package com.hxdi.nmjl.mapper.quality;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.quality.QualityInspection;
import com.hxdi.nmjl.condition.quality.QualityInspectionCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QualityInspectionMapper extends BaseMapper<QualityInspection>, SuperMapper<QualityInspection> {
    @DataPermission
    List<QualityInspection> getList(@Param("condition") QualityInspectionCondition condition);

    @DataPermission
    Page<QualityInspection> getPage(@Param("condition")QualityInspectionCondition condition,@Param("page") Page<QualityInspection> page);
}