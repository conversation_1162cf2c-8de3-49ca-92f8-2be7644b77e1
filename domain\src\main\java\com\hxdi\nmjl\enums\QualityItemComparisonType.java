package com.hxdi.nmjl.enums;

import lombok.Getter;

/**
 * @program: nmjl-service
 * @description: 质检项比较符
 * @author: 王贝强
 * @create: 2025-04-16 15:03
 */
@Getter
public enum QualityItemComparisonType {
    EQUAL("等于", "="),
    NOT_EQUAL("不等于", "!="),
    GREATER_THAN("大于", ">"),
    LESS_THAN("小于", "<"),
    GREATER_THAN_OR_EQUAL_TO("大于等于", ">="),
    LESS_THAN_OR_EQUAL_TO("小于等于", "<=");

    private final String key;
    private final String value;

    QualityItemComparisonType(String key, String value) {
        this.key = key;
        this.value = value;
    }
}
