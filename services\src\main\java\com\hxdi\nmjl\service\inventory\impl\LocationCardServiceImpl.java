package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inventory.InventoryLog;
import com.hxdi.nmjl.domain.inventory.LocationCard;
import com.hxdi.nmjl.mapper.inventory.LocationCardMapper;
import com.hxdi.nmjl.service.inventory.InventoryLogService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.service.inventory.LocationCardService;
import com.hxdi.nmjl.utils.RedisKeys;
import com.hxdi.nmjl.condition.inventory.LocationCardCondition;
import com.hxdi.nmjl.vo.inventory.LocationCardDetailResVO;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
@Service
public class LocationCardServiceImpl extends BaseServiceImpl<LocationCardMapper, LocationCard> implements LocationCardService{

    @Resource
    @Lazy
    private InventoryService inventoryService;

    @Resource
    @Lazy
    private InventoryLogService logService;
    @Override
    public void createOrUpdate(LocationCard card) {
        if(CommonUtils.isEmpty(card.getStoreId())||CommonUtils.isEmpty(card.getStId())||CommonUtils.isEmpty(card.getLocId())){
            throw new BaseException("货位、仓房、库点Id不能为空");
        }
        LambdaQueryWrapper<LocationCard> wrapper = new LambdaQueryWrapper<LocationCard>()
                .eq(LocationCard::getStoreId, card.getStoreId())
                .eq(LocationCard::getStId, card.getStId())
                .eq(LocationCard::getLocId, card.getLocId())
                .eq(LocationCard::getEnabled, StrPool.State.ENABLE)
                .last(" limit 1");
        LocationCard old_card = getOne(wrapper);


        if(CommonUtils.isNotEmpty(old_card)){
            BeanUtils.copyProperties(card, old_card);
            updateById(old_card);
        }else {
            save(card);
        }

    }

    @Override
    public List<LocationCard> getList(LocationCardCondition condition) {
        return baseMapper.getList(condition);
    }

    @Override
    public Page<LocationCard> getPage(LocationCardCondition condition) {
        Page<LocationCard> page = condition.newPage();
        return baseMapper.getPage(condition,page);
    }


    @Override
    public LocationCard getDetailByLocId(String locId) {
        return baseMapper.selectOne(Wrappers.<LocationCard>lambdaQuery().eq(LocationCard::getLocId, locId));
    }

    @Override
    public List<LocationCard> getByInventoryIdList(List<String> inventoryId) {
        return baseMapper.selectList(Wrappers.<LocationCard>lambdaQuery().in(LocationCard::getInventoryId, inventoryId));
    }

    @Override
    public LocationCardDetailResVO getDetail(Serializable locationCardId) {
        LocationCardDetailResVO vo = new LocationCardDetailResVO();
        LocationCard locationCard = getById(locationCardId);
        if(CommonUtils.isNotEmpty(locationCard)){
            BeanUtils.copyProperties(locationCard, vo);
            //根据库存ID获取对应的库存信息
            Inventory inventory = inventoryService.getById(locationCard.getInventoryId());
            if(CommonUtils.isNotEmpty(inventory)){
                BeanUtils.copyProperties(inventory, vo);
                //当存在有效库存记录时，根据库存的品种Id获取品种信息
                Catalog catalog = CacheProvider.getCacheObject(RedisKeys.CATALOG.key(), inventory.getCatalogId());
                if(CommonUtils.isNotEmpty(catalog)){
                    vo.setBrand(catalog.getBrand());
                    vo.setSpecification(catalog.getFullSpecification());
                    vo.setMaxStorageTime(catalog.getMaxStorageTime());
                    vo.setGoodsName(catalog.getFullName());
                }
                //当存在有效库存记录时，根据库存Id获取对应的库存变更日志
                if(CommonUtils.isNotEmpty(locationCard.getInventoryId())){
                    List<InventoryLog> logList = logService.getList(locationCard.getInventoryId());
                    vo.setInventoryLogs(logList);
                }
            }
            //根据仓房ID获取仓库信息
            StoreHouse house = CacheProvider.getCacheObject(RedisKeys.STORE_HOUSE.key(), locationCard.getStId());
            if(CommonUtils.isNotEmpty(house)){
                vo.setCapacity(house.getCapacity());
                vo.setUnit(house.getUnit());
            }

        }
        return vo;
    }
}
