package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.emergency.EmergencyResponseCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyResponse;
import com.hxdi.nmjl.domain.emergency.EmergencyResponseItem;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.emergency.EmergencyResponseMapper;
import com.hxdi.nmjl.service.emergency.EmergencyResponseItemService;
import com.hxdi.nmjl.service.emergency.EmergencyResponseService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应急预案管理服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class EmergencyResponseServiceImpl extends BaseServiceImpl<EmergencyResponseMapper, EmergencyResponse> implements EmergencyResponseService {

    @Resource
    private EmergencyResponseMapper emergencyResponseMapper;

    @Resource
    private EmergencyResponseItemService emergencyResponseItemService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public Page<EmergencyResponse> getPages(EmergencyResponseCondition condition) {
        Page<EmergencyResponse> page = condition.newPage();
        return emergencyResponseMapper.getPages(condition, page);
    }

    @Override
    public List<EmergencyResponse> getList(EmergencyResponseCondition condition) {
        return emergencyResponseMapper.getList(condition);
    }

    @Override
    public EmergencyResponse getDetail(String id) {
        EmergencyResponse emergencyResponse = emergencyResponseMapper.selectById(id);
        emergencyResponse.setDetailList(emergencyResponseItemService.getList(Collections.singletonList(id)));
        return emergencyResponse;
    }

    @Override
    public void add(EmergencyResponse emergencyResponse) {
        // 校验预案编号是否重复
        if (CommonUtils.isNotEmpty(emergencyResponse.getResponseCode())) {
            Long count = emergencyResponseMapper.selectCount(Wrappers.<EmergencyResponse>lambdaQuery()
                    .eq(EmergencyResponse::getResponseCode, emergencyResponse.getResponseCode())
                    .eq(EmergencyResponse::getEnabled, StrPool.State.ENABLE));

            if (count > 0) {
                BizExp.pop("该应急预案编号已存在且处于启用状态");
            }
        }
        //生成预案编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("EMERGENCY_RESPONSE_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        emergencyResponse.setResponseCode((String) businessCode.getValue());
        // 设置基础信息
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        String userId = baseUserDetails.getUserId();
        String tenantId = baseUserDetails.getTenantId();
        String dataHierarchyId = baseUserDetails.getDataHierarchyId();
        emergencyResponse.setCreateId(userId);
        emergencyResponse.setUpdateId(userId);
        emergencyResponse.setTenantId(tenantId);
        emergencyResponse.setDataHierarchyId(dataHierarchyId);

        if (!this.save(emergencyResponse)) {
            BizExp.pop("应急预案信息保存失败");
        }
        List<EmergencyResponseItem> detailList = emergencyResponse.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            BizExp.pop("请填写应急预案明细!");
        }
        for (EmergencyResponseItem emergencyResponseItem : detailList) {
            emergencyResponseItem.setResponseId(emergencyResponse.getId());
        }
        if (!emergencyResponseItemService.saveBatch(detailList)) {
            BizExp.pop("应急预案明细保存失败");
        }
    }

    @Override
    public void update(EmergencyResponse emergencyResponse) {
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        emergencyResponse.setUpdateId(baseUserDetails.getUserId());
        if (!this.updateById(emergencyResponse)) {
            BizExp.pop("应急预案信息更新失败");
        }
        List<EmergencyResponseItem> detailList = emergencyResponse.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            BizExp.pop("请填写应急预案明细!");
        }
        //先删除旧明细
        emergencyResponseItemService.remove(Wrappers.<EmergencyResponseItem>lambdaQuery().eq(EmergencyResponseItem::getResponseId, emergencyResponse.getId()));
        for (EmergencyResponseItem emergencyResponseItem : detailList) {
            emergencyResponseItem.setResponseId(emergencyResponse.getId());
        }
        if (!emergencyResponseItemService.saveBatch(detailList)) {
            BizExp.pop("应急预案明细保存失败");
        }
    }

    @Override
    public boolean delete(String id) {
        EmergencyResponse emergencyResponse = this.getById(id);
        if (emergencyResponse == null) {
            BizExp.pop("应急预案信息不存在");
        }
        if (emergencyResponse.getApproveStatus() != null) {
            BizExp.pop("已提交数据无法删除!");
        }
        return this.update(Wrappers.<EmergencyResponse>lambdaUpdate().eq(EmergencyResponse::getId, id).set(EmergencyResponse::getEnabled, 0));
    }

    @Override
    public void approve(String id, String approveOpinion) {
        //审批通过
        changeApproveStatus(id, 1, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 2, approveOpinion);
    }

    @Override
    public void submit(String id) {
        // 审核状态：0-未审核，1-审核通过，2-驳回
        changeApproveStatus(id, 0, null);
    }

    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        EmergencyResponse emergencyResponse = baseMapper.selectById(id);
        emergencyResponse.setDetailList(emergencyResponseItemService.getList(Collections.singletonList(id)));
        if (emergencyResponse.getEnabled() == 0) {
            throw new BaseException("预案信息不存在！");
        }

        // 校验状态：已审核的不能重复审核
        if (approveStatus != 0 && emergencyResponse.getApproveStatus() != null && emergencyResponse.getApproveStatus() != 0) {
            throw new BaseException("该预案信息已审核，无法重复操作！");
        }

        emergencyResponse.setApproveStatus(approveStatus);

        //提交时，先根据主管部门id查询不能新增的品种，避免同主管部门下同一个品种被重复提交
        if (approveStatus == 1) {
            List<String> existingBidObject = getExistingEmergencyResponseItem(emergencyResponse.getOrgId());
            if (existingBidObject != null && !existingBidObject.isEmpty()) {
                List<EmergencyResponseItem> detailList = emergencyResponse.getDetailList();
                for (EmergencyResponseItem emergencyResponseItem : detailList) {
                    if (existingBidObject.contains(emergencyResponseItem.getCatalogId() + emergencyResponseItem.getGrade())) {

                        emergencyResponse.setApproveStatus(2);
                        emergencyResponse.setApproveOpinion("当前主管部门下已存在相同品种，请勿重复提交！");
                        baseMapper.updateById(emergencyResponse);

                        throw new BaseException("当前主管部门下已存在相同品种，请勿重复提交！");
                    }
                }
            }
        }
        if (approveStatus == 1 || approveStatus == 2) {
            emergencyResponse.setApprover(SecurityHelper.obtainUser().getNickName());
            emergencyResponse.setApproveTime(new Date());
            emergencyResponse.setApproveOpinion(approveOpinion);
        } else {
            //清除审核信息
            emergencyResponse.setApprover("");
            emergencyResponse.setApproveOpinion("");
        }
        baseMapper.updateById(emergencyResponse);
    }

    public List<String> getExistingEmergencyResponseItem(String orgId) {
        LambdaQueryWrapper<EmergencyResponse> wrapper = new LambdaQueryWrapper<EmergencyResponse>()
                .eq(EmergencyResponse::getOrgId, orgId)
                .eq(EmergencyResponse::getEnabled, StrPool.State.ENABLE)
                .eq(EmergencyResponse::getApproveStatus, 1);
        List<EmergencyResponse> emergencyResponseList = this.list(wrapper);

        List<String> resultList = new ArrayList<>();

        if (!emergencyResponseList.isEmpty()) {
            List<EmergencyResponseItem> emergencyResponseItemList = emergencyResponseItemService.getList(emergencyResponseList.stream().map(EmergencyResponse::getId).collect(Collectors.toList()));
            emergencyResponseItemList.forEach(emergencyResponseItem -> resultList.add(emergencyResponseItem.getCatalogId() + emergencyResponseItem.getGrade()));
        }
        return resultList;
    }
}
