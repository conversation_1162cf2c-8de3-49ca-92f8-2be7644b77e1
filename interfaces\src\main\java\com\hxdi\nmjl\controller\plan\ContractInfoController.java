package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.ContractCondition;
import com.hxdi.nmjl.domain.plan.ContractInfo;
import com.hxdi.nmjl.service.plan.ContractInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 合同管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/15 20:02
 */
@Api(tags = "合同信息")
@RestController
@RequestMapping("/contract")
public class ContractInfoController extends BaseController<ContractInfoService, ContractInfo> {


    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody ContractInfo contract) {
        if (CommonUtils.isEmpty(contract.getId())) {
            bizService.create(contract);
        } else {
            bizService.updating(contract);
        }
        return ResultBody.ok();
    }

    @ApiOperation("删除合同")
    @GetMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "审核通过")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @GetMapping("/reject")
    public ResultBody<Void> reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation("提交合同")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<ContractInfo> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<ContractInfo>> pages(ContractCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<ContractInfo>> list(ContractCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("列表查询V2(用于质量管理：下级库点查询时，显示上级单位及自身的合同；其他情况，走默认数据权限)")
    @GetMapping("/listV2")
    public ResultBody<List<ContractInfo>> listV2(ContractCondition condition) {
        return ResultBody.ok().data(bizService.listsV2(condition));
    }

    @ApiOperation("列表查询(包含结算状态字段)")
    @GetMapping("/listV3")
    public ResultBody<List<ContractInfo>> listV3(ContractCondition condition) {
        return ResultBody.ok().data(bizService.listsV3(condition));
    }

    @ApiOperation("查询合同及明细分页")
    @GetMapping("/uncompleted/page")
    public ResultBody<Page<ContractInfo>> getUncompletedPageList(ContractCondition condition) {
        return ResultBody.ok().data(bizService.getUncompletedPageList(condition));
    }

    @ApiOperation("查询所有有效合同中对应标的完成数量")
    @GetMapping("/getActiveContractTargetCompletion")
    public ResultBody<Map<String, BigDecimal>> getActiveContractTargetCompletion(String id) {
        return ResultBody.ok().data(bizService.getActiveContractTargetCompletion(id));
    }


    @ApiOperation("根据招标id查询关联计划中的库点信息")
    @GetMapping("/getStoreHouseList")
    public ResultBody<Map<String, String>> getStoreHouseList(String contractId) {
        return ResultBody.ok().data(bizService.getStoreHouseList(contractId));
    }
}
