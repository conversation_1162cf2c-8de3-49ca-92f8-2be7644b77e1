package com.hxdi.nmjl.controller.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inout.opt.AdditiveConsuming;
import com.hxdi.nmjl.service.inout.opt.AdditiveConsumingService;
import com.hxdi.nmjl.condition.inout.AdditiveConsumingCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 添加剂管理控制层
 *
 * <AUTHOR>
 * @since 2025-04-22 10:02:06
 */
@Api(tags = "添加剂管理")
@RestController
@RequestMapping("/additiveConsuming")
public class AdditiveConsumingController extends BaseController<AdditiveConsumingService, AdditiveConsuming> {

    @ApiOperation(value = "保存或更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody AdditiveConsuming additiveConsuming) {
        bizService.saveOrUpdateV1(additiveConsuming);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<AdditiveConsuming>> pages(AdditiveConsumingCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<AdditiveConsuming>> lists(AdditiveConsumingCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<AdditiveConsuming> getDetail(@RequestParam @NotNull(message = "id不能为空") String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

    @ApiOperation(value = "审核")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @PostMapping("/reject")
    public ResultBody reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "提交")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResultBody delete(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }
}
