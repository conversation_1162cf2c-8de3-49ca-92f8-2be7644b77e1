<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.DispatchTaskMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.DispatchTask">
    <!--@mbg.generated-->
    <!--@Table B_DISPATCH_TASK-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TASK_CODE" jdbcType="VARCHAR" property="taskCode" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
    <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
    <result column="ST_ID" jdbcType="VARCHAR" property="stId" />
    <result column="ST_NAME" jdbcType="VARCHAR" property="stName" />
    <result column="PLAN_DATE" jdbcType="TIMESTAMP" property="planDate" />
    <result column="INVENTORY_ID" jdbcType="VARCHAR" property="inventoryId" />
    <result column="PLAN_QTY" jdbcType="DECIMAL" property="planQty" />
    <result column="COMPLETED_QTY" jdbcType="DECIMAL" property="completedQty" />
    <result column="STATE" jdbcType="INTEGER" property="state" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="ATTACHEMENTS" jdbcType="VARCHAR" property="attachements" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TASK_CODE, STORE_ID, STORE_NAME, ST_ID, ST_NAME, PLAN_DATE, INVENTORY_ID, PLAN_QTY, 
    COMPLETED_QTY, "STATE", REMARKS, ATTACHEMENTS, ENABLED, CREATE_TIME, UPDATE_TIME, 
    CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,CATALOG_NAME
  </sql>

  <select id="listV1" parameterType="com.hxdi.nmjl.condition.inout.DispatchCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_DISPATCH_TASK
    <where>
      <if test="condition.status != null and condition.status != ''">
        and STATE = #{condition.status}
      </if>
      <if test="condition.startTime != null and condition.startTime != ''">
        and PLAN_DATE &gt;= #{condition.startTime}
      </if>
      <if test="condition.endTime != null and condition.endTime != ''">
        and PLAN_DATE &lt;= #{condition.endTime}
      </if>
      and enabled = 1
    </where>
    ORDER BY CREATE_TIME DESC
  </select>

  <select id="PageV1" parameterType="com.hxdi.nmjl.condition.inout.DispatchCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_DISPATCH_TASK
    <where>
      <if test="condition.status != null and condition.status != ''">
        and STATE = #{condition.status}
      </if>
      <if test="condition.startTime != null and condition.startTime != ''">
        and PLAN_DATE &gt;= #{condition.startTime}
      </if>
      <if test="condition.endTime != null and condition.endTime != ''">
        and PLAN_DATE &lt;= #{condition.endTime}
      </if>
      and enabled = 1
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>