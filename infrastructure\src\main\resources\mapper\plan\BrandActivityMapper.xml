<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.plan.BrandActivityMapper">

    <sql id="Base_Column_List">
        ID, BRAND_ID, BRAND_NAME, ACTIVITY_NAME, START_DATE, END_DATE,
ACTIVITY_TYPE, CHANNEL, TARGET_USER, BUDGET, CHAR<PERSON>_PERSON,
MO<PERSON>LE, ORG_ID, ORG_NAME, GOALS, COMPLETED_RATE, APPROVE_STATUS,
APPROVER, APPROVE_TIME, APPROVE_OPINION, ENABLED, ATTACHMENTS,
CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.BrandActivity">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="BRAND_ID" jdbcType="VARCHAR" property="brandId"/>
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
        <result column="ACTIVITY_NAME" jdbcType="VARCHAR" property="activityName"/>
        <result column="START_DATE" jdbcType="DATE" property="startDate"/>
        <result column="END_DATE" jdbcType="DATE" property="endDate"/>
        <result column="ACTIVITY_TYPE" jdbcType="VARCHAR" property="activityType"/>
        <result column="CHANNEL" jdbcType="VARCHAR" property="channel"/>
        <result column="TARGET_USER" jdbcType="VARCHAR" property="targetUser"/>
        <result column="BUDGET" jdbcType="DECIMAL" property="budget"/>
        <result column="CHARGE_PERSON" jdbcType="VARCHAR" property="chargePerson"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId"/>
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName"/>
        <result column="GOALS" jdbcType="VARCHAR" property="goals"/>
        <result column="COMPLETED_RATE" jdbcType="INTEGER" property="completedRate"/>
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus"/>
        <result column="APPROVER" jdbcType="VARCHAR" property="approver"/>
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime"/>
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="ATTACHMENTS" jdbcType="VARCHAR" property="attachments"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_BRAND_ACTIVITY
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.brandId)">
                AND BRAND_ID = #{condition.brandId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.brandName)">
                AND BRAND_NAME LIKE CONCAT('%', #{condition.brandName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.activityType)">
                AND ACTIVITY_TYPE = #{condition.activityType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.channel)">
                AND CHANNEL = #{condition.channel}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.targetUser)">
                AND TARGET_USER = #{condition.targetUser}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createStartTime)">
                AND CREATE_TIME >= #{condition.createStartTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createEndTime)">
                AND CREATE_TIME &lt;= #{condition.createEndTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.plan.BrandActivity">
        SELECT <include refid="Base_Column_List"/>
        FROM B_BRAND_ACTIVITY
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.brandId)">
                AND BRAND_ID = #{condition.brandId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.brandName)">
                AND BRAND_NAME LIKE CONCAT('%', #{condition.brandName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.activityType)">
                AND ACTIVITY_TYPE = #{condition.activityType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.channel)">
                AND CHANNEL = #{condition.channel}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.targetUser)">
                AND TARGET_USER = #{condition.targetUser}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createStartTime)">
                AND CREATE_TIME >= #{condition.createStartTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createEndTime)">
                AND CREATE_TIME &lt;= #{condition.createEndTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>