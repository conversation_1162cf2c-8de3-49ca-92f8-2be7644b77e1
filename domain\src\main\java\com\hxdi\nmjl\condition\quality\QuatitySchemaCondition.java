package com.hxdi.nmjl.condition.quality;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @program: nmjl-service
 * @description: 质检方案查询条件
 * @author: 王贝强
 * @create: 2025-04-16 12:45
 */
@Setter
@Getter
@NoArgsConstructor
@ApiModel(description = "质检方案查询条件")
public class QuatitySchemaCondition extends QueryCondition {

    @ApiModelProperty(value="方案名称(模糊查询)")
    private String schemaName;

    @ApiModelProperty(value="品种ID")
    private String catalogId;

    @ApiModelProperty(value="品种名称(模糊查询)")
    private String catalogName;

    @ApiModelProperty(value="品类ID")
    private String classificationId;

    @ApiModelProperty(value="品类名称(模糊查询)")
    private String classificationName;
}
