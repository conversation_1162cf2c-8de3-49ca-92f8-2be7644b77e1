package com.hxdi.nmjl.vo.bigscreen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: nmjl-service
 * @description: 大屏统计：库存数量与仓容统计VO
 * @author: 王贝强
 * @create: 2025-07-30 17:51
 */
@Setter
@Getter
@ApiModel(description = "库存数量与仓容VO")
public class InventorySumAndCapacityVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "地区编码")
    private String areaCode;

    @ApiModelProperty(value = "地区名称")
    private String areaName;

    @ApiModelProperty(value = "军供站Id 只在分页查询时返回")
    private String storeId;

    @ApiModelProperty(value = "军供站名称 只在分页查询时返回")
    private String storeName;

    @ApiModelProperty(value = "仓房Id  只在分页查询时返回")
    private String StoreHouseId;

    @ApiModelProperty(value = "仓房名称  只在分页查询时返回")
    private String StoreHouseName;

    @ApiModelProperty(value = "仓容")
    private BigDecimal capacitySum;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal inventorySum;

    @ApiModelProperty(value = "使用率")
    private float usedRate;
}
