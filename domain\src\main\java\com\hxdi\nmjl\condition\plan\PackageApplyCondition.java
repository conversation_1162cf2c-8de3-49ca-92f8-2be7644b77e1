package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 生产包装查询条件
 * @author: 王贝强
 * @create: 2025-07-17 16:16
 */
@Getter
@Setter
@ApiModel(description = "包装申请查询条件")
public class PackageApplyCondition extends QueryCondition {

    @ApiModelProperty(value = "模糊查询：订单编号、申请编号、申请企业、申请人")
    private String search;

    @ApiModelProperty(value = "申请开始日期")
    private Date startTime;

    @ApiModelProperty(value = "申请结束日期")
    private Date endTime;

    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;
}
