<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.plan.BrandAuthorizingMapper">
    <sql id="Base_Column_List">
        ID, BRAND_ID, BRAND_NAME, LICENSE_NO, CLIENT_ID, CLIENT_NAME,
    AUTH_TYPE, APPLY_TIME, APPLY_QTY, VALID_DATE, AUTH_STATE,
    MEMO, APPROVE_STATUS, APPROVER, APPROVE_TIME, APPROVE_OPINION,
    ENABLED, ATTACHMENTS, CREATE_TIME, UPDATE_TIME, CREATE_ID,
    UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.BrandAuthorizing">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="BRAND_ID" jdbcType="VARCHAR" property="brandId" />
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
        <result column="LICENSE_NO" jdbcType="VARCHAR" property="licenseNo" />
        <result column="CLIENT_ID" jdbcType="VARCHAR" property="clientId" />
        <result column="CLIENT_NAME" jdbcType="VARCHAR" property="clientName" />
        <result column="AUTH_TYPE" jdbcType="VARCHAR" property="authType" />
        <result column="APPLY_TIME" jdbcType="TIMESTAMP" property="applyTime" />
        <result column="APPLY_QTY" jdbcType="INTEGER" property="applyQty" />
        <result column="VALID_DATE" jdbcType="TIMESTAMP" property="validDate" />
        <result column="AUTH_STATE" jdbcType="INTEGER" property="authState" />
        <result column="MEMO" jdbcType="VARCHAR" property="memo" />
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus" />
        <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="ATTACHMENTS" jdbcType="VARCHAR" property="attachments" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>
    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_BRAND_AUTHORIZING
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.brandId)">
                and BRAND_ID = #{condition.brandId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.licenseNo)">
                and LICENSE_NO = #{condition.licenseNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
                and CLIENT_ID = #{condition.clientId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.authType)">
                and AUTH_TYPE = #{condition.authType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.authState)">
                and AUTH_STATE = #{condition.authState}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeStart)">
                and CREATE_TIME &gt;= #{condition.createTimeStart}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeEnd)">
                and CREATE_TIME &lt;= #{condition.createTimeEnd}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                and DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.plan.BrandAuthorizing">
        select <include refid="Base_Column_List"/>
        from B_BRAND_AUTHORIZING
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.brandId)">
                and BRAND_ID = #{condition.brandId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.licenseNo)">
                and LICENSE_NO = #{condition.licenseNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
                and CLIENT_ID = #{condition.clientId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.authType)">
                and AUTH_TYPE = #{condition.authType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.authState)">
                and AUTH_STATE = #{condition.authState}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeStart)">
                and CREATE_TIME &gt;= #{condition.createTimeStart}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createTimeEnd)">
                and CREATE_TIME &lt;= #{condition.createTimeEnd}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                and DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>

    <update id="checkAndUpdateExpiredState">
        update B_BRAND_AUTHORIZING
        set AUTH_STATE = 2
        where
            AUTH_STATE = 1
            and APPROVE_STATUS = 1
            and ENABLED = 1
            and VALID_DATE &lt; #{date, jdbcType=TIMESTAMP}
    </update>
</mapper>