<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.InoutDetailMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.InoutDetail">
        <!--@mbg.generated-->
        <!--@Table B_INOUT_DETAIL-->
        <id column="ID" property="id" />
        <result column="TASK_ID" property="taskCode" />
        <result column="INOUT_CODE" property="inoutCode" />
        <result column="INOUT_TYPE" property="inoutType" />
        <result column="INOUT_BIZ_TYPE" property="inoutBizType" />
        <result column="OPT_TIME" property="optTime" />
        <result column="CLIENT_ID" property="clientId" />
        <result column="CLIENT_NAME" property="clientName" />
        <result column="STORE_ID" property="storeId" />
        <result column="STORE_NAME" property="storeName" />
        <result column="ORG_ID" property="orgId" />
        <result column="RESERVE_LEVEL" property="reserveLevel" />
        <result column="BATCH_NUM" property="batchNum" />
        <result column="ENABLED" property="enabled" />
        <result column="OPT_FLG" property="optFlg"/>
        <result column="ATTACHEMENTS" property="attachments" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, TASK_ID, INOUT_CODE, INOUT_TYPE, INOUT_BIZ_TYPE, OPT_TIME, CLIENT_ID, CLIENT_NAME,
        STORE_ID, STORE_NAME, ORG_ID, RESERVE_LEVEL, BATCH_NUM, ENABLED, ATTACHEMENTS, CREATE_TIME,
        UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,OPT_FLG
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from B_INOUT_DETAIL
        <where>
            enabled  = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.inoutType)">
                and INOUT_TYPE = #{condition.inoutType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.inoutBizType)">
                and INOUT_BIZ_TYPE = #{condition.inoutBizType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enabled)">
                and ENABLED = #{condition.enabled}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)<EMAIL>@isNotEmpty(condition.endTime)">
                and CREATE_TIME between #{condition.startTime} and #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from B_INOUT_DETAIL
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.inoutType)">
                and INOUT_TYPE = #{condition.inoutType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.inoutBizType)">
                and INOUT_BIZ_TYPE = #{condition.inoutBizType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enabled)">
                and ENABLED = #{condition.enabled}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)<EMAIL>@isNotEmpty(condition.endTime)">
                and CREATE_TIME between #{condition.startTime} and #{condition.endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
