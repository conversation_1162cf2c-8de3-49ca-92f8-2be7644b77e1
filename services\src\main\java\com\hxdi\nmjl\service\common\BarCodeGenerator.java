package com.hxdi.nmjl.service.common;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.hxdi.common.core.properties.CommonProperties;
import com.hxdi.nmjl.domain.common.BarInfo;
import com.hxdi.nmjl.enums.BarCodeEnum;
import com.hxdi.nmjl.enums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * @program: nmjl-service
 * @description: 系统条码生成器
 * @author: 王贝强
 * @create: 2025-08-20 14:32
 */
@Slf4j
@Component
public class BarCodeGenerator {

    // 默认条码类型设置为二维码
    private static final String DEFAULT_BAR_CODE_TYPE = BarCodeEnum.QR_CODE.getCode();

    private static final int DEFAULT_BAR_PX_SIZE = 150;

    @Resource
    private CommonProperties commonProperties;

    /**
     * 生成默认的条码，使用二维码类型
     *
     * @param businessId   业务ID
     * @param businessType 业务类型
     * @return 生成的条码字符串
     */
    public static String generateDefault(String businessId, BusinessType businessType) {
        return generate(BarCodeEnum.QR_CODE, businessId, businessType);
    }

    /**
     * 生成指定类型的条码
     *
     * @param barCodeType  条码类型
     * @param businessId   业务ID
     * @param businessType 业务类型
     * @return 生成的条码字符串
     */
    public static String generate(BarCodeEnum barCodeType, String businessId, BusinessType businessType) {
        // 设置二维码大小
        QrConfig config = new QrConfig(DEFAULT_BAR_PX_SIZE, DEFAULT_BAR_PX_SIZE);
        // 设置容错级别 L (7%)
        config.setErrorCorrection(ErrorCorrectionLevel.L);
        BarInfo barInfo = new BarInfo(businessId, businessType, null);
        String json = barInfo.toJSONString();
        String result = "";
        if (Objects.equals(barCodeType.getCode(), DEFAULT_BAR_CODE_TYPE)) {
            // 生成二维码的Base64编码字符串(PNG格式)
            result = QrCodeUtil.generateAsBase64(json, config, ImgUtil.IMAGE_TYPE_PNG);
        }
        return result;
    }

    /**
     * 生成指定类型的条码( 用于外部链接 )
     *
     * @param barCodeType 条码类型
     * @param openId       条码对应的OpenId
     * @return 生成的条码字符串
     */
    public static String generateByUrl(BarCodeEnum barCodeType, String openId) {
        // 获取配置中的外部链接地址
        if (openId == null || openId.isEmpty()) {
            log.error("OpenId为空，无法生成条码");
            return "";
        }
        String Url = ConfigHolder.get().getOpenBarUrl() == null ? "http://10.13.4.38:8810/bar?p=" : ConfigHolder.get().getOpenBarUrl();
        // 设置二维码大小 200*200px
        QrConfig config = new QrConfig(DEFAULT_BAR_PX_SIZE, DEFAULT_BAR_PX_SIZE);
        // 设置容错级别 L (7%)
        config.setErrorCorrection(ErrorCorrectionLevel.L);
        String fullUrl = Url + openId;
        String result = "";
        if (Objects.equals(barCodeType.getCode(), DEFAULT_BAR_CODE_TYPE)) {
            result = QrCodeUtil.generateAsBase64(fullUrl, config, ImgUtil.IMAGE_TYPE_PNG);
        }
        return result;
    }

    /**
     * Bean初始化后设置静态配置引用
     */
    @PostConstruct
    private void initStaticConfig() {
        ConfigHolder.set(this.commonProperties);
    }

    /**
     * 静态配置持有者，用于在静态方法中访问注入的配置
     */
    private static class ConfigHolder {
        private static CommonProperties instance;

        public static CommonProperties get() {
            return instance;
        }

        private static void set(CommonProperties config) {
            instance = config;
        }
    }

}
