package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 粮油品类
 */
@Getter
@Setter
@TableName(value = "C_CLASSIFICATION")
public class Classification extends Entity<Classification> {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 分类代码
     */
    @TableField(value = "CLASSIFICATION_CODE")
    private String classificationCode;

    /**
     * 分类名称
     */
    @TableField(value = "CLASSIFICATION_NAME")
    private String classificationName;

    /**
     * 父级id
     */
    @TableField(value = "PARENT_ID")
    private String parentId;

    /**
     * 层级
     */
    @TableField(value = "\"LEVEL\"")
    private Integer level;

    /**
     * 排序
     */
    @TableField(value = "SEQ")
    private Integer seq;

    /**
     * 叶子结点 0否  1是
     */
    @TableField(value = "LEAF_IS")
    private Integer leafIs;

    /**
     * 类型：
     */
    @TableField(value = "CATEGORY")
    private Integer category;

    /**
     * 状态：0-禁用，1-启用，7-删除
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    @TableField(value = "PRE_IMG")
    private String preImg;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENT")
    private String attachment;

    /**
     * 存储时间/天
     */
    @TableField(value = "MAX_STORAGE_TIME")
    private Integer maxStorageTime;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 分类路径
     */
    @TableField(value = "CLASSIFICATION_PATH")
    private String classificationPath;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;


    /**
     * --------------------扩展
     */

    /**
     * 父级分类名称
     */
    @TableField(exist = false)
    private String parentClassificationName;
}
