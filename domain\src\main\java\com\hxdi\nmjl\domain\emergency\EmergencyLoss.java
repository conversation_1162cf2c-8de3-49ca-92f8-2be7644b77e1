package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <应急损耗记录>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 14:57
 */
@Getter
@Setter
@TableName(value = "B_EMERGENCY_LOSS")
public class EmergencyLoss extends BModel {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField(value = "LOSS_CODE")
    @ApiModelProperty(value = "损耗编号")
    private String lossCode;

    /**
     * 任务名称
     */
    @TableField(value = "TASK_NAME")
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @TableField(value = "TASK_CODE")
    @ApiModelProperty(value = "任务编号")
    private String taskCode;

    /**
     * 管理单位ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "管理单位ID")
    private String orgId;

    /**
     * 管理单位名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "管理单位名称")
    private String orgName;

    /**
     * 上报类型：1-军供站，2-动员企业
     */
    @TableField(value = "REPORT_TYPE")
    @ApiModelProperty(value = "上报类型：1-军供站，2-动员企业")
    private Integer reportType;

    /**
     * 库点ID
     */
    @TableField(value = "UNIT_ID")
    @ApiModelProperty(value = "单位ID")
    private String unitId;

    /**
     * 库点名称
     */
    @TableField(value = "UNIT_NAME")
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 省
     */
    @TableField(value = "PROVINCE")
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "CITY")
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区县
     */
    @TableField(value = "COUNTY")
    @ApiModelProperty(value = "区县")
    private String county;

    @TableField(value = "PROVINCE_CODE")
    private String provinceCode;

    @TableField(value = "CITY_CODE")
    private String cityCode;

    @TableField(value = "COUNTY_CODE")
    private String countyCode;

    /**
     * 详细地址
     */
    @TableField(value = "DETAIL_ADDR")
    @ApiModelProperty(value = "详细地址")
    private String detailAddr;

    /**
     * 指标ID
     */
    @TableField(value = "CLASSIFICATION_ID")
    @ApiModelProperty(value = "指标ID")
    private String classificationId;

    /**
     * 指标名称
     */
    @TableField(value = "CLASSIFICATION_NAME")
    @ApiModelProperty(value = "指标名称")
    private String classificationName;

    /**
     * 损耗数量
     */
    @TableField(value = "LOSS_QTY")
    @ApiModelProperty(value = "损耗数量")
    private BigDecimal lossQty;

    /**
     * 金额
     */
    @TableField(value = "AMOUNT")
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 统计员
     */
    @TableField(value = "STATISTICIAN")
    @ApiModelProperty(value = "统计员")
    private String statistician;

    /**
     * 统计员电话
     */
    @TableField(value = "STAT_MOBILE")
    @ApiModelProperty(value = "统计员电话")
    private String statMobile;

    /**
     * 单位负责人
     */
    @TableField(value = "FZR")
    @ApiModelProperty(value = "单位负责人")
    private String fzr;

    /**
     * 单位负责人电话
     */
    @TableField(value = "FZR_MOBILE")
    @ApiModelProperty(value = "单位负责人电话")
    private String fzrMobile;

    /**
     * 损耗原因
     */
    @TableField(value = "LOSS_DESC")
    @ApiModelProperty(value = "损耗原因")
    private String lossDesc;

    /**
     * 业务状态：0-待确认，1-已确认
     */
    @TableField(value = "STATE")
    @ApiModelProperty(value = "业务状态：0-待确认，1-已确认")
    private Integer state;

    /**
     * 状态：0-无效，1-有效
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态：0-无效，1-有效")
    private Integer enabled;

    @TableField(value = "ATTACHMENT")
    private String attachment;


}
