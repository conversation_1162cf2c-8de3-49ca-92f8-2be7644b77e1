package com.hxdi.nmjl.condition.bigscreen;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @program: nmjl-service
 * @description: 订单统计查询条件
 * @author: 王贝强
 * @create: 2025-07-28 15:27
 */
@Setter
@Getter
@ApiModel(description = "订单统计查询条件")
public class OrderMonthRecordCondition extends QueryCondition {

    @ApiModelProperty(value = "订单类型 1->采购订单 2->销售订单")
    private String orderType;

    @ApiModelProperty(value = "单位ID ','分割")
    private String orgIds;

    @ApiModelProperty(value = "军供站ID ','分割")
    private String storeIds;

    @ApiModelProperty(value = "客户ID ','分割")
    private String clientIds;

    @ApiModelProperty(value = "品种ID ','分割")
    private String catalogIds;

    @ApiModelProperty(value = "统计开始时间")
    private String startTime;

    @ApiModelProperty(value = "统计结束时间")
    private String endTime;

    @ApiModelProperty(value = "地区编码列表 ','分割 可同时查询多个地区数据（建议传入区域只包含同一个层级）")
    private String AreaCodes;

    @ApiModelProperty(value = "是否包含子区域数据 1->包含 0->不包含 (默认包含，但在分页查询时一定不包含)")
    private String isIncludeChild = "1";

    @ApiModelProperty(value = "后端查询字段，请勿使用")
    private List<String> Codes;
}
