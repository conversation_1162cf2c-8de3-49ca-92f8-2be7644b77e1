package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 销售订单明细
 */
@Getter
@Setter
@ApiModel(description = "销售订单明细")
@TableName(value = "B_SALE_ORDER_ITEM")
public class SaleOrderItem extends Entity<SaleOrderItem> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 订单ID
     */
    @TableField(value = "ORDER_ID")
    @ApiModelProperty(value = "订单ID")
    private String orderId;

    /**
     * 合同ID
     */
    @TableField(value = "CONTRACT_ID")
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND")
    @ApiModelProperty(value = "品牌名称")
    private String brand;

    /**
     * 质量等级：字典：YZLDJ/LZLDJ
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value = "质量等级：字典：YZLDJ/LZLDJ")
    private String grade;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    @ApiModelProperty(value = "规格")
    private String specification;

    /**
     * 生产日期
     */
    @TableField(value = "PRODUCT_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    /**
     * 订单数量
     */
    @TableField(value = "ORDER_QTY")
    @ApiModelProperty(value = "订单数量")
    private BigDecimal orderQty;

    /**
     * 完成数量
     */
    @TableField(value = "COMPLETED_QTY")
    @ApiModelProperty(value = "完成数量")
    private BigDecimal completedQty;

    /**
     * 订单包装数量
     */
    @TableField(value = "ORDER_PACK_QTY")
    @ApiModelProperty(value = "订单包装数量")
    private Integer orderPackQty;


    /**
     * 储备性质:字典CBXZ
     */
    @TableField(value = "RESERVE_LEVEL")
    @ApiModelProperty(value = "储备性质:字典CBXZ")
    private String reserveLevel;

    /**
     * 单价
     */
    @TableField(value = "PRICE")
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 生产批次号
     */
    @TableField(value = "BATCH_NO")
    @ApiModelProperty(value = "生产批次号")
    private String batchNo;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}