package com.hxdi.nmjl.service.clientrelated.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.clientrelated.SupplierCreditCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierCredit;
import com.hxdi.nmjl.mapper.clientrelated.SupplierCreditMapper;
import com.hxdi.nmjl.service.clientrelated.SupplierCreditService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 供应商信用管理实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SupplierCreditServiceImpl extends BaseServiceImpl<SupplierCreditMapper, SupplierCredit> implements SupplierCreditService {


    @Override
    public void create(SupplierCredit credit) {
        verifyCatalog(credit);
        baseMapper.insert(credit);
    }

    @Override
    public void update(SupplierCredit credit) {
        baseMapper.updateById(credit);
    }

    @Override
    public List<SupplierCredit> getDetail(String supplierId) {
        //根据主表id查询
        SupplierCreditCondition condition = new SupplierCreditCondition();
        condition.setSupplierId(supplierId);
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void changeState(String id, Integer enabled) {
        baseMapper.changeState(id, enabled);
    }

    @Override
    public SupplierCredit getByUniqueKey(String uniqueKey) {
        return baseMapper.selectById(uniqueKey);
    }

    @Override
    public Page<SupplierCredit> pages(SupplierCreditCondition condition) {
        Page<SupplierCredit> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);

    }

    @Override
    public List<SupplierCredit> lists(SupplierCreditCondition condition) {
        return baseMapper.selectListV1(condition);

    }

    @Override
    public void removeByMainId(String mainId) {
        baseMapper.delete(Wrappers.<SupplierCredit>lambdaQuery().eq(SupplierCredit::getSupplierId, mainId));
    }

    /**
     * 验证数据有效性
     * 名称可以重复
     *
     * @param credit 供应商信用信息
     */
    private void verifyCatalog(SupplierCredit credit) {
        long count = baseMapper.selectCount(Wrappers.<SupplierCredit>lambdaQuery()
                .eq(SupplierCredit::getSupplierId, credit.getSupplierId()));

        if (count > 0) {
            throw new BaseException("已存在相同的供应商信用信息，请检查录入数据");
        }
    }

}

