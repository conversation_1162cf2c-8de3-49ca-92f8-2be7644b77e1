<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.InspectionRecordMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.InspectionRecord">
        <!--@mbg.generated-->
        <!--@Table B_INSPECTION_RECORD-->
        <id column="ID" property="id" />
        <result column="INSPECT_CODE" property="inspectCode" />
        <result column="PLAN_CODE" property="planCode" />
        <result column="PLAN_NAME" property="planName" />
        <result column="INSPECT_UNIT_ID" property="inspectUnitId" />
        <result column="INSPECT_UNIT_NAME" property="inspectUnitName" />
        <result column="INSPECT_TYPE" property="inspectType" />
        <result column="PURPOSE" property="purpose" />
        <result column="INSPECTORS" property="inspectors" />
        <result column="RESULT_STATE" property="resultState" />
        <result column="DEAL_STATE" property="dealState" />
        <result column="ENABLED" property="enabled" />
        <result column="ATTACHMENTS" property="attachments" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
        <result column="INSPECT_TIME" property="inspectTime" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,INSPECT_CODE, PLAN_CODE, PLAN_NAME, INSPECT_UNIT_ID, INSPECT_UNIT_NAME, INSPECT_TYPE, PURPOSE,
        INSPECTORS, RESULT_STATE, DEAL_STATE, ENABLED, ATTACHMENTS, CREATE_TIME, UPDATE_TIME,
        CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, INSPECT_TIME
    </sql>
</mapper>
