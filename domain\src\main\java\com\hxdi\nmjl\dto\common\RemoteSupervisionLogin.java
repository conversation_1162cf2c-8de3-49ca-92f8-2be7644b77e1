package com.hxdi.nmjl.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class RemoteSupervisionLogin implements Serializable {

    private static final long serialVersionUID = -6305863298536125171L;

    @ApiModelProperty(value = "安全监管接入地址")
    private String loginUrl;

    @ApiModelProperty(value = "安全监管接入参数：ssoUrl")
    private String ssoUrl;

    @ApiModelProperty(value = "安全监管接入参数：ssoUserName")
    private String ssoUserName;

    @ApiModelProperty(value = "安全监管接入参数：ssoParam")
    private String ssoParam;

    /**
     * 转成自动登录url
     * @return
     */
    public String toUrl() {
        StringBuilder params = new StringBuilder();
        params.append("ssoUrl=").append(ssoUrl);
        params.append("&ssoUserName=").append(ssoUserName);
        params.append("&ssoParam=").append(ssoParam);
        return String.format("%s?%s", this.loginUrl, params);
    }
}
