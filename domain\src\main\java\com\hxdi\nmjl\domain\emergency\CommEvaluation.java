package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 《沟通效果评估信息表》
 * @TableName B_COMM_EVALUATION
 */
@TableName(value ="B_COMM_EVALUATION")
@Getter
@Setter
public class CommEvaluation extends BModel implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 评估标题
     */
    @TableField(value = "TITLE")
    @ApiModelProperty(value = "评估标题")
    private String title;

    /**
     * 评估对象
     */
    @TableField(value = "EVALUATION_OBJECT")
    @ApiModelProperty(value = "评估对象")
    private String evaluationObject;

    /**
     * 评估状态
     */
    @TableField(value = "EVALUATE_STATUS")
    @ApiModelProperty(value = "评估状态")
    private Integer evaluateStatus;

    /**
     * 评估日期
     */
    @TableField(value = "EVALUATION_TIME")
    @ApiModelProperty(value = "评估日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date evaluationTime;

    /**
     * 综合评分
     */
    @TableField(value = "SCORE")
    @ApiModelProperty(value = "综合评分")
    private Integer score;

    /**
     * 常见沟通问题
     */
    @TableField(value = "COMMON_ISSUES")
    @ApiModelProperty(value = "常见沟通问题")
    private String commonIssues;

    /**
     * 沟通改进措施
     */
    @TableField(value = "IMPROVEMENT_MEASURES")
    @ApiModelProperty(value = "沟通改进措施")
    private String improvementMeasures;

    /**
     * 启用状态(1-启用，0-禁用)
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "启用状态(1-启用，0-禁用)")
    private Integer enabled;

}
