<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.delivery.VehicleInfoMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.delivery.VehicleInfo">
        <!--@Table B_VEHICLE_INFO-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="ownerType" column="OWNER_TYPE" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="orgName" column="ORG_NAME" jdbcType="VARCHAR"/>
        <result property="vin" column="VIN" jdbcType="VARCHAR"/>
        <result property="vehicleNo" column="VEHICLE_NO" jdbcType="VARCHAR"/>
        <result property="vehicleType" column="VEHICLE_TYPE" jdbcType="VARCHAR"/>
        <result property="loadCap" column="LOAD_CAP" jdbcType="VARCHAR"/>
        <result property="vehicleState" column="VEHICLE_STATE" jdbcType="INTEGER"/>
        <result property="brand" column="BRAND" jdbcType="VARCHAR"/>
        <result property="models" column="MODELS" jdbcType="VARCHAR"/>
        <result property="purchaseDate" column="PURCHASE_DATE" jdbcType="DATE"/>
        <result property="owner" column="OWNER" jdbcType="VARCHAR"/>
        <result property="ownerPhone" column="OWNER_PHONE" jdbcType="VARCHAR"/>
        <result property="driver" column="DRIVER" jdbcType="VARCHAR"/>
        <result property="idCard" column="ID_CARD" jdbcType="VARCHAR"/>
        <result property="mobile" column="MOBILE" jdbcType="VARCHAR"/>
        <result property="enabled" column="ENABLED" jdbcType="INTEGER"/>
        <result property="attachments" column="ATTACHMENTS" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createId" column="CREATE_ID" jdbcType="VARCHAR"/>
        <result property="updateId" column="UPDATE_ID" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        OWNER_TYPE,
        ORG_ID,
        ORG_NAME,
        VIN,
        VEHICLE_NO,
        VEHICLE_TYPE,
        LOAD_CAP,
        VEHICLE_STATE,
        BRAND,
        MODELS,
        PURCHASE_DATE,
        "OWNER",
        OWNER_PHONE,
        DRIVER,
        ID_CARD,
        MOBILE,
        ENABLED,
        ATTACHMENTS,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID

    </sql>

    <!-- 分页查询 -->
    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_VEHICLE_INFO
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                AND ORG_NAME LIKE CONCAT('%', #{condition.orgName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleState)">
                AND VEHICLE_STATE = #{condition.vehicleState}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleType)">
                AND VEHICLE_TYPE = #{condition.vehicleType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.driver)">
                AND DRIVER = #{condition.driver}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 列表查询 -->
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_VEHICLE_INFO
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                AND ORG_NAME LIKE CONCAT('%', #{condition.orgName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleState)">
                AND VEHICLE_STATE = #{condition.vehicleState}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleType)">
                AND VEHICLE_TYPE = #{condition.vehicleType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.vehicleNo)">
                AND VEHICLE_NO = #{condition.vehicleNo}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.driver)">
                AND DRIVER = #{condition.driver}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>

