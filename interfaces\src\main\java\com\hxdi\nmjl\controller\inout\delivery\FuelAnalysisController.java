package com.hxdi.nmjl.controller.inout.delivery;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.inout.FuelAnalysisCondition;
import com.hxdi.nmjl.domain.inout.delivery.FuelAnalysis;
import com.hxdi.nmjl.service.inout.delivery.FuelAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 燃油分析控制器
 *
 * <AUTHOR>
 */
@Api(tags = "燃油分析管理")
@RestController
@RequestMapping("/fuelAnalysis")
public class FuelAnalysisController extends BaseController<FuelAnalysisService, FuelAnalysis> {


    /**
     * 获取燃油分析详情
     */
    @GetMapping("/get")
    public ResultBody<FuelAnalysis> get(@RequestParam String id) {
        FuelAnalysis fuelAnalysis = bizService.getById(id);
        return ResultBody.success().data(fuelAnalysis);
    }

    /**
     * 保存或更新燃油分析
     */
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody FuelAnalysis fuelAnalysis) {
        bizService.saveOrUpdateV1(fuelAnalysis);
        return ResultBody.ok();
    }

    /**
     * 删除燃油分析
     */
    @PostMapping("/delete")
    public ResultBody<Void> remove(@RequestParam String id) {
        bizService.removeById(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<FuelAnalysis>> pages(FuelAnalysisCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation(value = "分页监控查询")
    @GetMapping("/page1")
    public ResultBody<Page<FuelAnalysis>> pages1(FuelAnalysisCondition condition) {
        return ResultBody.ok().data(bizService.pages1(condition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<FuelAnalysis>> lists(FuelAnalysisCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation(value = "根据车辆类型获取车牌号")
    @GetMapping("/getByKind")
    public ResultBody<List<String>> getByKind(@RequestParam String kind) {
        return ResultBody.ok().data(bizService.getByKind(kind));
    }




}
