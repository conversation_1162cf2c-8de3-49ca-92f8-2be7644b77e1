package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.ContractInfo;
import com.hxdi.nmjl.condition.plan.ContractCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface ContractInfoMapper extends SuperMapper<ContractInfo> {

    /**
     * 分页查询
     * @param page
     * @param condition
     */
    @DataPermission
    Page<ContractInfo> selectPageV1(Page<ContractInfo> page, @Param("condition") ContractCondition condition);

    /**
     * 列表查询
     * @param condition
     */
    @DataPermission
    List<ContractInfo> selectListV1(@Param("condition") ContractCondition condition);

    /**
     * 查询合同下指定品种价格
     * @param contractCode
     * @param catalogId
     * @return
     */
    BigDecimal selectCatalogPrice(@Param("contractCode") String contractCode, @Param("catalogId") String catalogId);

    /**
     * 列表查询V2(用于质量管理：下级库点查询时，显示上级单位的合同，不走默认数据权限)
     * @param condition
     * @return
     */
    List<ContractInfo> selectListV2(@Param("condition") ContractCondition condition);
}
