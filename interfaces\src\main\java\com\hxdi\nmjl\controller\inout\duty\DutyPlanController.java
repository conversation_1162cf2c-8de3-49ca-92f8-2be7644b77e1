package com.hxdi.nmjl.controller.inout.duty;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.DutyCondition;
import com.hxdi.nmjl.domain.inout.duty.DutyPlan;
import com.hxdi.nmjl.service.inout.duty.DutyPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <值班计划接口>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21 14:19
 */

@RestController
@RequestMapping("/duty/plan")
@Api(tags = "值班计划接口")
@Validated
public class DutyPlanController extends BaseController<DutyPlanService, DutyPlan> {

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody DutyPlan dutyPlan) {
        if (CommonUtils.isEmpty(dutyPlan.getId())) {
            bizService.create(dutyPlan);
        } else {
            bizService.updating(dutyPlan);
        }
        return ResultBody.ok();
    }


    @ApiOperation(value = "查询")
    @GetMapping("/query")
    public ResultBody<DutyPlan> query(@RequestParam String dutyId) {
        return ResultBody.ok().data(bizService.detail(dutyId));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String dutyId) {
        bizService.delete(dutyId);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<DutyPlan>> page(DutyCondition dutyCondition) {
        return ResultBody.ok().data(bizService.getPage(dutyCondition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<DutyPlan>> list(DutyCondition dutyCondition) {
        return ResultBody.ok().data(bizService.getList(dutyCondition));
    }

    @ApiOperation(value = "审核")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String dutyId, @RequestParam String approveOpinion) {
        bizService.approve(dutyId, approveOpinion);
        return ResultBody.ok();
    }


    @ApiOperation(value = "驳回")
    @PostMapping("/reject")
    public ResultBody reject(@RequestParam String dutyId, @RequestParam String approveOpinion) {
        bizService.reject(dutyId, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "下发")
    @PostMapping("/agree")
    public ResultBody approved(@RequestParam String dutyId) {
        bizService.approved(dutyId);
        return ResultBody.ok();
    }

}
