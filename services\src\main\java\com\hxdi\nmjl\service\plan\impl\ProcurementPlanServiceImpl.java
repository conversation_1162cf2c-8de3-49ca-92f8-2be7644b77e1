package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.plan.ProcurementPlanCondition;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlan;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlanDetail;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.ProcurementPlanMapper;
import com.hxdi.nmjl.service.bigscreen.InfoAreaService;
import com.hxdi.nmjl.service.plan.ProcurementPlanDetailService;
import com.hxdi.nmjl.service.plan.ProcurementPlanService;
import com.hxdi.nmjl.utils.RedisKeys;
import com.hxdi.nmjl.vo.bigscreen.ProcurementPlanSummaryVO;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Year;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/26 15:30
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ProcurementPlanServiceImpl extends BaseServiceImpl<ProcurementPlanMapper, GrainProcurementPlan> implements ProcurementPlanService {

    @Resource
    public ProcurementPlanDetailService procurementPlanDetailService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private InfoAreaService infoAreaService;

    @Override
    public GrainProcurementPlan createV1(GrainProcurementPlan plan) {

        //生成计划编码
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("PROCUREMENT_PLAN_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        plan.setPlanCode((String) businessCode.getValue());

        //写入地区编码（库点的区县编码），委托采购的父计划中不存在
        String areaCode = CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), plan.getStoreId(), Organization::getCounty);
        plan.setAreaCode(areaCode);

        //保存计划信息
        this.save(plan);
        //保存详情
        if (plan.getDetailList() != null) {
            plan.getDetailList().forEach(detail -> detail.setPlanId(plan.getId()));
            procurementPlanDetailService.saveBatch(plan.getDetailList());
        }

        return plan;

    }

    @Override
    public void update(GrainProcurementPlan plan) {
        //查询原计划
        GrainProcurementPlan savedPlan = this.getById(plan.getId());

        //校验计划状态
        if (savedPlan.getState() == 0 && savedPlan.getApproveStatus() == null) {
            this.updateById(plan);
            procurementPlanDetailService.remove(plan.getId());
            plan.getDetailList().forEach(detail -> detail.setPlanId(plan.getId()));
            procurementPlanDetailService.saveBatch(plan.getDetailList());
        } else if (savedPlan.getState() == 1 && savedPlan.getParentFlag() == 1 && savedPlan.getApproveStatus() == null) {
            this.updateById(plan);
            procurementPlanDetailService.remove(plan.getId());
            plan.getDetailList().forEach(detail -> detail.setPlanId(plan.getId()));
            procurementPlanDetailService.saveBatch(plan.getDetailList());
        } else if (savedPlan.getApproveStatus() != null && savedPlan.getApproveStatus() == 2) {
            this.updateById(plan);
            procurementPlanDetailService.remove(plan.getId());
            plan.getDetailList().forEach(detail -> detail.setPlanId(plan.getId()));
            procurementPlanDetailService.saveBatch(plan.getDetailList());
        } else {
            BizExp.pop("当前状态不允许修改");
        }
    }

    @Override
    public GrainProcurementPlan getDetail(String planId) {
        GrainProcurementPlan plan = baseMapper.selectById(planId);
        plan.setDetailList(procurementPlanDetailService.getList(planId));
        return plan;
    }

    @Override
    public Page<GrainProcurementPlan> pages(ProcurementPlanCondition condition) {
        Page<GrainProcurementPlan> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<GrainProcurementPlan> lists(ProcurementPlanCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public List<GrainProcurementPlan> listAndDetail(ProcurementPlanCondition condition) {
//        BaseUserDetails userDetails = SecurityHelper.getUser();
//        Integer type = userDetails.getOrganType();
        //确保管理单位只能看到父计划
//        if (type != null && type == 1) {
//            condition.setParentFlag(1);
//        } else {
//            condition.setParentFlag(0);
//        }
        List<GrainProcurementPlan> planList = baseMapper.selectListV1(condition);
        planList.forEach(plan -> plan.setDetailList(procurementPlanDetailService.getList(plan.getId())));
        return planList;
    }

    @Override
    public void approve(String planId, Integer approveStatus, String opinion) {
        GrainProcurementPlan plan = this.getById(planId);
        if (plan == null) {
            throw new BaseException("计划不存在");
        }

        if (plan.getApproveStatus() == null || plan.getApproveStatus() == 1) {
            throw new BaseException("该计划未提交或已审核");
        }

        int year = Year.now().getValue();
        // 获取登录用户的数据权限
        String dataHierarchyId = SecurityHelper.getUser().getDataHierarchyId();

        LambdaQueryWrapper<GrainProcurementPlan> wrapper = Wrappers.<GrainProcurementPlan>lambdaQuery()
                .eq(GrainProcurementPlan::getYear, year)
                .eq(GrainProcurementPlan::getDataHierarchyId, dataHierarchyId)
                .notIn(GrainProcurementPlan::getState, 0, 4)
                .eq(GrainProcurementPlan::getEnabled, 1);

        if (baseMapper.selectCount(wrapper) > 0 && approveStatus == 0) {
            throw new BaseException("当前年份还有未完成的计划，无法审核！");
        }

        // 如果是委托采购的计划且审核通过，则为委托单位生成对应的父计划
        if (approveStatus == 1 && plan.getPlanType() == 1) {
            plan.setDetailList(procurementPlanDetailService.getList(planId));
            boolean result = createOrUpdateParentPlan(plan);
            if (!result) {
                plan.setApproveStatus(2);
                this.updateById(plan);
                throw new BaseException("父计划正在招标中，无法上报");
            }
        }

        // 设置审核状态
        updatePlanApprovalInfo(plan, approveStatus, opinion);

    }

    private void updatePlanApprovalInfo(GrainProcurementPlan plan, Integer approveStatus, String opinion) {
        plan.setApproveStatus(approveStatus);
        plan.setApproveOpinion(opinion);
        plan.setApprover(SecurityHelper.obtainUser().getNickName());
        plan.setApproveTime(new Date());

        if (approveStatus == 1) {
            // 审核通过后设置计划状态为已上报
            plan.setState(1);
        }
        this.updateById(plan);
    }

    /**
     * 为委托采购计划创建父计划
     * 如果委托单位已经生成过父计划，则直接将子计划关联到父计划下并更新父计划对应的信息
     *
     * @param plan 子计划
     */
    protected boolean createOrUpdateParentPlan(GrainProcurementPlan plan) {
        // 查询同一年份的父计划
        GrainProcurementPlan parentPlan = findExistingParentPlan(plan);

        // 如果父计划正在招标中，则不允许上报
        if (parentPlan != null) {
            if (parentPlan.getState() == 2) {
                return false;
            }

            // 更新现有父计划
            updateExistingParentPlan(plan, parentPlan);
        } else {
            // 创建新的父计划
            parentPlan = createNewParentPlan(plan);
        }

        // 将父计划Id与Code更新到子计划中
        plan.setPid(parentPlan.getId());
        plan.setPCode(parentPlan.getPlanCode());
        this.updateById(plan);

        return true;
    }

    /**
     * 查询同一年份的父计划
     *
     * @param plan
     * @return
     */
    private GrainProcurementPlan findExistingParentPlan(GrainProcurementPlan plan) {
        return baseMapper.selectOne(Wrappers.<GrainProcurementPlan>lambdaQuery()
                .eq(GrainProcurementPlan::getParentFlag, 1)
                .eq(GrainProcurementPlan::getEnabled, 1)
                .eq(GrainProcurementPlan::getManageUnitId, plan.getManageUnitId())
                .eq(GrainProcurementPlan::getYear, plan.getYear()));
    }

    /**
     * 更新现有的父计划
     *
     * @param childPlan  子计划
     * @param parentPlan 父计划
     */
    private void updateExistingParentPlan(GrainProcurementPlan childPlan, GrainProcurementPlan parentPlan) {
        List<GrainProcurementPlanDetail> parentDetails = procurementPlanDetailService.getList(parentPlan.getId());
        List<GrainProcurementPlanDetail> childDetails = childPlan.getDetailList();

        // 创建子计划详情的副本，避免直接修改原始列表
        List<GrainProcurementPlanDetail> remainingChildDetails = new ArrayList<>(childDetails);

        // 更新匹配的详情项
        for (GrainProcurementPlanDetail parentDetail : parentDetails) {
            Iterator<GrainProcurementPlanDetail> iterator = remainingChildDetails.iterator();
            while (iterator.hasNext()) {
                GrainProcurementPlanDetail childDetail = iterator.next();
                if (isSameClassificationAndGrade(parentDetail, childDetail)) {
                    // 更新上下限
                    parentDetail.setMaxLimit(parentDetail.getMaxLimit().add(childDetail.getMaxLimit()));
                    parentDetail.setMinLimit(parentDetail.getMinLimit().add(childDetail.getMinLimit()));
                    iterator.remove();
                    break;
                }
            }
        }

        // 添加剩余的子计划详情到父计划
        for (GrainProcurementPlanDetail detail : remainingChildDetails) {
            parentDetails.add(createParentPlanDetail(detail, parentPlan.getId()));
        }
        // 保存更新后的父计划详情
        procurementPlanDetailService.saveOrUpdateBatch(parentDetails);
    }

    /**
     * 判断两个计划详情是否属于同一品类和等级
     *
     * @param detail1
     * @param detail2
     * @return
     */
    private boolean isSameClassificationAndGrade(GrainProcurementPlanDetail detail1, GrainProcurementPlanDetail detail2) {
        return detail1.getClassificationId().equals(detail2.getClassificationId()) &&
                detail1.getGrade().equals(detail2.getGrade());
    }

    /**
     * 创建新的父计划
     *
     * @param childPlan 子计划
     * @return 父计划
     */
    private GrainProcurementPlan createNewParentPlan(GrainProcurementPlan childPlan) {
        GrainProcurementPlan parentPlan = new GrainProcurementPlan();

        // 复制子计划信息到父计划（排除不需要的字段）
        BeanUtils.copyProperties(childPlan, parentPlan,
                "id", "pid", "planCode", "approveStatus", "attachements", "rcvAddr", "rcvDetailAddr", "reporter", "reporterTime",
                "approver", "approveTime", "createTime", "updateTime", "approveOpinion", "notes", "detailList", "storeId", "storeName",
                "createId", "updateId", "tenantId", "dataHierarchyId");

        // 设置父计划信息
        BaseUserDetails user = SecurityHelper.obtainUser();
        if (CommonUtils.isNotEmpty(user)) {
            parentPlan.setManageUnitId(childPlan.getEntrustedUnitId());
            parentPlan.setManageName(childPlan.getEntrustedUnitName());
        }

        // 新增的父计划默认为已上报状态
        parentPlan.setState(1);
        parentPlan.setParentFlag(1);

        //将数据权限设置为委托单位
        parentPlan.setDataHierarchyId(childPlan.getEntrustedUnitId());

        // 保存父计划
        parentPlan = this.createV1(parentPlan);

        // 创建父计划详情
        GrainProcurementPlan finalParentPlan = parentPlan;
        List<GrainProcurementPlanDetail> parentDetails = childPlan.getDetailList().stream()
                .map(detail -> createParentPlanDetail(detail, finalParentPlan.getId()))
                .collect(Collectors.toList());

        // 保存父计划详情
        procurementPlanDetailService.saveBatch(parentDetails);

        return parentPlan;
    }

    /**
     * 创建父计划详情
     *
     * @param childDetail  子计划详情
     * @param parentPlanId 父计划ID
     * @return 父计划详情
     */
    private GrainProcurementPlanDetail createParentPlanDetail(GrainProcurementPlanDetail childDetail, String parentPlanId) {
        GrainProcurementPlanDetail parentDetail = new GrainProcurementPlanDetail();
        parentDetail.setPlanId(parentPlanId);
        parentDetail.setClassificationId(childDetail.getClassificationId());
        parentDetail.setClassificationName(childDetail.getClassificationName());
        parentDetail.setGrade(childDetail.getGrade());
        parentDetail.setMaxLimit(childDetail.getMaxLimit());
        parentDetail.setMinLimit(childDetail.getMinLimit());
        return parentDetail;
    }


    @Override
    public void remove(String planId) {

        // 查看计划是否存在
        GrainProcurementPlan plan = baseMapper.selectById(planId);
        if (plan == null) {
            throw new BaseException("计划不存在");
        }

        // 校验状态 - 只有未上报的计划才能删除
        Integer state = plan.getState();
        if (!state.equals(0)) {
            throw new BaseException("该计划已完成上报，无法删除！");
        }

        // 先删除计划详情，再删除主计划
        procurementPlanDetailService.remove(planId);
        plan.setEnabled(StrPool.State.DISABLE);
        this.updateById(plan);
    }

    @Override
    public void submit(String planId) {
        GrainProcurementPlan plan = baseMapper.selectById(planId);
        BaseUserDetails userDetails = SecurityHelper.getUser();
        if (plan == null) {
            return;
        }
        if (plan.getState() != 0 && plan.getParentFlag() == 0) {
            throw new BaseException("只有未上报的计划可以提交");
        }
        //提交时设置审核状态为未审核
        plan.setReporter(userDetails.getNickName());
        plan.setApproveStatus(0);
        this.updateById(plan);
    }

    @Override
    public List<String> getAvailableDelegatedUnits() {
        int currentYear = Year.now().getValue();

        //查询当前年份且已招标的计划
        LambdaQueryWrapper<GrainProcurementPlan> wrapper = new LambdaQueryWrapper<GrainProcurementPlan>()
                .isNotNull(GrainProcurementPlan::getPid)
                .eq(GrainProcurementPlan::getState, 2)
                .eq(GrainProcurementPlan::getYear, currentYear)
                .eq(GrainProcurementPlan::getEnabled, 1);
        List<GrainProcurementPlan> planList = this.list(wrapper);
        if (planList.isEmpty()) {
            return new ArrayList<>();
        }
        return planList.stream().map(GrainProcurementPlan::getManageUnitId).distinct().collect(Collectors.toList());
    }

    @Override
    public boolean CanBeCreated() {
        int currentYear = Year.now().getValue();

        //获取登录用户数据
        BaseUserDetails user = SecurityHelper.getUser();
        String dataHierarchyId = user.getDataHierarchyId();

        // 查询当前年份且未完成的计划
        LambdaQueryWrapper<GrainProcurementPlan> wrapper = new LambdaQueryWrapper<GrainProcurementPlan>()
                .eq(GrainProcurementPlan::getEnabled, 1)
                .eq(GrainProcurementPlan::getYear, currentYear)
                .eq(GrainProcurementPlan::getDataHierarchyId, dataHierarchyId)
                .notIn(GrainProcurementPlan::getState, 0, 4);
        long count = baseMapper.selectCount(wrapper);
        return count == 0;
    }

    public ProcurementPlanSummaryVO getPlanSummary(String areaCode) {
        List<String> areaCodes;
        //根据传入的地区编码，查询对应地区的所有下级地区
        areaCodes = infoAreaService.getAllChildAreaCodes(areaCode);
        ProcurementPlanSummaryVO planSummary = baseMapper.getPlanSummary(areaCodes);

        // 添加空值检查
        if (planSummary == null) {
            planSummary = new ProcurementPlanSummaryVO();
            planSummary.setPlanQty(BigDecimal.ZERO);
            planSummary.setCompletedQty(BigDecimal.ZERO);
            planSummary.setCompletedRate(0);
        }

        //根据传入的地区编码，查询对应地区的名称
        planSummary.setAreaName(infoAreaService.getAreaNameByCode(areaCode));
        planSummary.setAreaCode(areaCode);

        return planSummary;
    }

    @Override
    public Page<ProcurementPlanSummaryVO> getPlanSummaryPage(ProcurementPlanCondition condition) {
        List<String> areaCodes = infoAreaService.getAllChildAreaCodes(condition.getAreaCode());
        condition.setAreaCode(String.join(",", areaCodes));
        Page<ProcurementPlanSummaryVO> page = condition.newPage();
        Page<ProcurementPlanSummaryVO> summaryPage = baseMapper.getPlanSummaryPage(condition, page);
        summaryPage.getRecords().forEach(procurementPlanSummaryVO -> {
            procurementPlanSummaryVO.setAreaName(infoAreaService.getAreaNameByCode(procurementPlanSummaryVO.getAreaCode()));
            //计算完成率
            if (procurementPlanSummaryVO.getPlanQty().compareTo(BigDecimal.ZERO) > 0) {
                float rate = procurementPlanSummaryVO.getCompletedQty().divide(procurementPlanSummaryVO.getPlanQty(), 3, RoundingMode.HALF_UP).floatValue();
                //完成率最大为1
                if (rate > 1) {
                    rate = 1;
                }
                procurementPlanSummaryVO.setCompletedRate(rate);
            } else {
                procurementPlanSummaryVO.setCompletedRate(0);
            }
        });
        return summaryPage;
    }


    @Override
    public void updatePlanState(String planId, Integer state) {
        // 查询计划
        GrainProcurementPlan plan = baseMapper.selectById(planId);
        if (plan == null) {
            throw new BaseException("计划不存在!");
        }
        if (state < 2 || state > 4) {
            throw new BaseException("待更新的计划状态不合法!");
        }
        // 更新状态并保存
        plan.setState(state);
        // 检查计划下的所有品类是否存在招标信息，如果都已存在，则将计划状态更新为已完成
        if (state == 3) {
            boolean allDetailsHaveBids = procurementPlanDetailService.checkAllDetailsHaveBids(planId);
            if (allDetailsHaveBids) {
                state = 4;
                plan.setState(4);
            }
        }
        baseMapper.updateById(plan);
        //如果是父计划，则同时更新子计划状态
        if (plan.getParentFlag() == 1) {
            LambdaQueryWrapper<GrainProcurementPlan> wrapper = new LambdaQueryWrapper<GrainProcurementPlan>()
                    .eq(GrainProcurementPlan::getPid, planId)
                    .eq(GrainProcurementPlan::getEnabled, 1);
            List<GrainProcurementPlan> childPlanList = this.list(wrapper);
            for (GrainProcurementPlan childPlan : childPlanList) {
                childPlan.setState(state);
            }
            this.updateBatchById(childPlanList);
        }
    }

    @Override
    public void completePlan(String planId) {
        // 查询计划
        GrainProcurementPlan plan = baseMapper.selectById(planId);
        if (plan == null) {
            throw new BaseException("计划不存在!");
        }
        // 更新状态并保存
        plan.setState(4);
        baseMapper.updateById(plan);
        //如果是父计划，则同时更新子计划状态
        if (plan.getParentFlag() == 1) {
            LambdaQueryWrapper<GrainProcurementPlan> wrapper = new LambdaQueryWrapper<GrainProcurementPlan>()
                    .eq(GrainProcurementPlan::getPid, planId)
                    .eq(GrainProcurementPlan::getEnabled, 1);
            List<GrainProcurementPlan> childPlanList = this.list(wrapper);
            for (GrainProcurementPlan childPlan : childPlanList) {
                childPlan.setState(4);
            }
            this.updateBatchById(childPlanList);
        }
    }
}
