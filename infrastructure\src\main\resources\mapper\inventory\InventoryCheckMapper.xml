<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryCheckMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryCheck">
        <!--@Table B_INVENTORY_CHECK-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName"/>
        <result column="ST_ID" jdbcType="VARCHAR" property="stId"/>
        <result column="ST_NAME" jdbcType="VARCHAR" property="stName"/>
        <result column="PLAN_DATE" jdbcType="TIMESTAMP" property="planDate"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="PERSONS" jdbcType="VARCHAR" property="persons"/>
        <result column="FZR" jdbcType="VARCHAR" property="fzr"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="ATTACHMENTS" jdbcType="VARCHAR" property="attachments"/>
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus"/>
        <result column="APPROVER" jdbcType="VARCHAR" property="approver"/>
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion"/>
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime"/>
        <result column="CHECK_STATUS" jdbcType="INTEGER" property="checkStatus"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
        <!-- 盘点计划相关字段 -->
        <result column="PLAN_ID" property="planId"/>
        <result column="PLAN_NAME" property="planName"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, STORE_ID, STORE_NAME, ST_ID, ST_NAME, PLAN_DATE, START_TIME, PERSONS, FZR, REMARKS,
        ATTACHMENTS, APPROVE_STATUS, APPROVER, APPROVE_TIME, CHECK_STATUS, ENABLED, CREATE_TIME,
        UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,APPROVE_OPINION
    </sql>

    <!-- 包含盘点计划信息的字段列表 -->
    <sql id="With_Plan_Column_List">
        a.ID,  a.STORE_ID,  a.STORE_NAME,  a.ST_ID,  a.ST_NAME,  a.PLAN_DATE,  a.START_TIME,  a.PERSONS,  a.FZR,  a.REMARKS,
        a.ATTACHMENTS,  a.APPROVE_STATUS,  a.APPROVER,  a.APPROVE_TIME,  a.CHECK_STATUS,  a.ENABLED,  a.CREATE_TIME,
        a.UPDATE_TIME,  a.CREATE_ID,  a.UPDATE_ID,  a.TENANT_ID,  a.DATA_HIERARCHY_ID, a.APPROVE_OPINION,
        b.ID as PLAN_ID, b.NAME as PLAN_NAME
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT
        <include refid="With_Plan_Column_List"/>
        FROM B_INVENTORY_CHECK a
        LEFT JOIN B_INVENTORY_CHECK_CONFIG b ON a.STORE_ID = b.STORE_ID
        <where>
            a.ENABLED = 1
            AND b.ENABLED != 7
            <if test="@plugins.OGNL@isNotEmpty(condition.name)">
                AND b.NAME LIKE CONCAT('%', #{condition.name}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
                AND a.ST_ID = #{condition.stId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stName)">
                AND a.ST_NAME LIKE CONCAT('%', #{condition.stName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                AND b.START_TIME BETWEEN #{condition.planStartTime} AND #{condition.planEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime) and @plugins.OGNL@isNotEmpty(condition.endTime)">
                AND a.CHECK_DATE BETWEEN #{condition.startTime} AND #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.checkStatus)">
                AND a.CHECK_STATUS = #{condition.checkStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND a.APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY a.CREATE_TIME DESC
    </select>

    <!-- 列表查询 -->
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT
        <include refid="With_Plan_Column_List"/>
        FROM B_INVENTORY_CHECK a
        LEFT JOIN B_INVENTORY_CHECK_CONFIG b ON a.STORE_ID = b.STORE_ID
        <where>
            a.ENABLED = 1
            AND b.ENABLED != 7
            <if test="@plugins.OGNL@isNotEmpty(condition.name)">
                AND b.NAME LIKE CONCAT('%', #{condition.name}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
                AND a.ST_ID = #{condition.stId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stName)">
                AND a.ST_NAME LIKE CONCAT('%', #{condition.stName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                AND b.START_TIME BETWEEN #{condition.planStartTime} AND #{condition.planEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime) and @plugins.OGNL@isNotEmpty(condition.endTime)">
                AND a.CHECK_DATE BETWEEN #{condition.startTime} AND #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.checkStatus)">
                AND a.CHECK_STATUS = #{condition.checkStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND a.APPROVE_STATUS = #{condition.approveStatus}
            </if>
        </where>
        ORDER BY a.CREATE_TIME DESC
    </select>
</mapper>

