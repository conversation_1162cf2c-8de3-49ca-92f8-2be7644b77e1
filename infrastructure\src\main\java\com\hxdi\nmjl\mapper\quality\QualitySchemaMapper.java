package com.hxdi.nmjl.mapper.quality;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.quality.QualitySchema;
import com.hxdi.nmjl.condition.quality.QuatitySchemaCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

@Mapper
public interface QualitySchemaMapper extends SuperMapper<QualitySchema> {

    @DataPermission
    QualitySchema selectDefaultByClassificationId(@Param("classificationId") Serializable classificationId);

    void changeStatus(@Param("schemaId") Serializable schemaId, @Param("status") Integer status);

    @DataPermission(alias = "s")
    List<QualitySchema> selectListV1(@Param("condition") QuatitySchemaCondition condition);

    @DataPermission(alias = "s")
    Page<QualitySchema> selectPageV1(@Param("condition") QuatitySchemaCondition condition, @Param("page") Page<QualitySchema> page);
}
