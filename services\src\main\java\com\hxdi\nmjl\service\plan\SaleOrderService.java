package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.plan.SaleCondition;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.domain.plan.SaleOrder;

import java.math.BigDecimal;
import java.util.List;

public interface SaleOrderService extends IBaseService<SaleOrder> {

    /**
     * 查询详情
     *
     * @param orderId
     * @return
     */
    SaleOrder getDetail(String orderId);

    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    Page<SaleOrder> pages(SaleCondition condition);

    /**
     * 分页查询(销售订单->出库任务单->出库记录)
     *
     * @param condition
     * @return
     */
    Page<SaleOrder> PagesV1(SaleCondition condition);

    /**
     * 列表查询
     *
     * @param condition
     * @return
     */
    List<SaleOrder> lists(SaleCondition condition);

    /**
     * 列表查询(带明细)
     *
     * @param condition
     * @return
     */
    List<SaleOrder> listsV1(SaleCondition condition);


    /**
     * 创建订单
     *
     * @param saleOrder
     */
    void createV1(SaleOrder saleOrder);

    /**
     * 更新订单
     *
     * @param saleOrder
     */
    void updateV1(SaleOrder saleOrder);

    /**
     * 删除订单
     *
     * @param orderId
     */
    void removeV1(String orderId);

    /**
     * 提交订单
     *
     * @param orderId
     */
    void submitV1(String orderId);

    /**
     * 审核
     *
     * @param id             调整ID
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id             调整ID
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 更新订单状态
     * 注意：当订单状态处于完成时，会更新合同、标的、筹措计划的完成情况
     *
     * @param id
     * @param state
     */
    void updateState(String id, Integer state);

    /**
     * 更新订单结算状态
     *
     * @param orderIds
     */
    void updateSettlementStatus(String orderIds);

    /**
     * 更新订单完成数量
     *
     * @param orderId   订单ID
     * @param catalogId 品种ID
     * @param grade     质量等级
     * @param changeQty 变化数量
     */
    void updateProcess(String orderId, String catalogId, String grade, BigDecimal changeQty);

    /**
     * 生成销售订单统计数据
     *
     * @param inoutDetailList 出入库明细列表
     */
    void generateOrderStatistics(List<InoutDetail> inoutDetailList);
}
