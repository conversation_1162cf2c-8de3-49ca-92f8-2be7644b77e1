package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同明细
 */
@Getter
@Setter
@ApiModel(description="合同明细")
@TableName("B_CONTRACT_DETAIL")
public class ContractDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
    * 合同ID
    */
    @ApiModelProperty(value="合同ID")
    private String contractId;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String classificationId;

    /**
     * 品类名称
     */
    @ApiModelProperty(value = "品类名称")
    private String classificationName;

    /**
    * 品种ID
    */
    @ApiModelProperty(value="品种ID")
    private String catalogId;

    /**
    * 品种名称
    */
    @ApiModelProperty(value="品种名称")
    private String catalogName;

    /**
    * 品牌名称
    */
    @ApiModelProperty(value="品牌名称")
    private String brand;

    /**
    * 质量等级：字典：YZLDJ/LZLDJ
    */
    @ApiModelProperty(value="质量等级：字典：YZLDJ/LZLDJ")
    private String grade;

    /**
    * 规格
    */
    @ApiModelProperty(value="规格")
    private String specification;

    /**
     * 包装数量
     */
    @ApiModelProperty(value = "包装数量")
    private String contractPackQty;

    /**
     * 合同数量
     */
    @ApiModelProperty(value="合同数量")
    private BigDecimal contractQty;

    /**
     * 完成数量
     */
    @ApiModelProperty(value="完成数量")
    private BigDecimal completedQty;

    /**
    * 储备性质:字典CBXZ
    */
    @ApiModelProperty(value="储备性质:字典CBXZ")
    private String reserveLevel;

    /**
    * 单价
    */
    @ApiModelProperty(value="单价")
    private BigDecimal price;

    /**
     * 产地及年份要求
     */
    @ApiModelProperty(value="产地及年份")
    private String requirement;

    /**
     * 供货频次
     */
    @ApiModelProperty(value="供货频次")
    private String supplyFrequency;

    /**
     * 首次交货日期
     */
    @ApiModelProperty(value="首次交货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date firstDeliveryTime;

    /**
    * 租户id
    */
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
    * 组织
    */
    @ApiModelProperty(value="组织")
    @TableField(fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
