package com.hxdi.nmjl.service.inout.delivery.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.DeliveryOrderCondition;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryOrder;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryTrace;
import com.hxdi.nmjl.domain.inout.delivery.TransportRoute;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inout.delivery.DeliveryOrderMapper;
import com.hxdi.nmjl.service.inout.delivery.DeliveryOrderService;
import com.hxdi.nmjl.service.inout.delivery.DeliveryTraceService;
import com.hxdi.nmjl.service.inout.delivery.TransportRouteService;
import com.hxdi.nmjl.service.inout.delivery.VehicleInfoService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 调度配送单Service实现类
 *
 * <AUTHOR>
 * @since 2025/4/23 11:10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class DeliveryOrderServiceImpl extends BaseServiceImpl<DeliveryOrderMapper, DeliveryOrder> implements DeliveryOrderService {

    @Resource
    public VehicleInfoService vehicleInfoService;
    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;
    @Resource
    private DeliveryTraceService deliveryTraceService;
    @Resource
    private TransportRouteService transportRouteService;

    @Override
    public void saveOrUpdateV1(DeliveryOrder deliveryOrder) {
        if (CommonUtils.isEmpty(deliveryOrder.getId())) {
            verifyOrder(deliveryOrder);
            //添加配送单号
            BusinessCodeParams params = new BusinessCodeParams();
            params.setCode("DELIVERY_CODE");
            params.setDt(DataType.STRING);
            BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
            deliveryOrder.setDeliveryCode((String) businessCode.getValue());
            baseMapper.insert(deliveryOrder);
        } else {
            DeliveryOrder existingOrder = getById(deliveryOrder.getId());
            if (!existingOrder.getDeliveryState().equals(0)) {
                BizExp.pop("该配送单已在调度配送流程中，无法修改");
            }
            baseMapper.updateById(deliveryOrder);
        }
    }

    @Override
    public void remove(String id) {
        DeliveryOrder existingOrder = getById(id);
        if (!existingOrder.getDeliveryState().equals(0)) {
            BizExp.pop("该配送单已在调度配送流程中，无法删除");
        }
        DeliveryOrder updatingOrder = new DeliveryOrder();
        updatingOrder.setId(id);
        updatingOrder.setEnabled(StrPool.State.DISABLE);
        baseMapper.updateById(updatingOrder);
    }

    @Override
    public Page<DeliveryOrder> pages(DeliveryOrderCondition condition) {
        Page<DeliveryOrder> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<DeliveryOrder> lists(DeliveryOrderCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public DeliveryOrder getDetail(DeliveryOrderCondition condition) {
        DeliveryOrder deliveryOrder = baseMapper.selectOneV1(condition);
        if (deliveryOrder == null) {
            BizExp.pop("未找到符合条件的配送信息");
        }
        deliveryOrder.setDeliveryTraceList(deliveryTraceService.getListByPid(deliveryOrder.getId()));
        deliveryOrder.setTransportRouteList(transportRouteService.getListByPid(deliveryOrder.getId()));
        return deliveryOrder;
    }

    @Override
    public void updateState(String id, Integer state) {

        DeliveryOrder existingOrder = verifyState(id, state);

        DeliveryTrace deliveryTrace = new DeliveryTrace();
        deliveryTrace.setDeliveryId(id);
        deliveryTrace.setDeliveryState(state);
        deliveryTraceService.save(deliveryTrace);

        DeliveryOrder updatingOrder = new DeliveryOrder();
        updatingOrder.setId(id);
        updatingOrder.setDeliveryState(state);
        baseMapper.updateById(updatingOrder);

        // 车辆状态 1-空闲,2-在途,3-维修,4-停用
        if (state.equals(1) || state.equals(4)) {
            vehicleInfoService.changeState(state, existingOrder.getVehicleNo());
        }
    }

    @Override
    public void saveTrace(TransportRoute transportRoute) {
        if (CommonUtils.isEmpty(transportRoute.getId())) {
            verifyTrace(transportRoute);
            transportRouteService.save(transportRoute);
        } else {
            transportRouteService.updateById(transportRoute);
        }

    }

    /**
     * 验证配送轨迹有效性
     */
    private void verifyTrace(TransportRoute transportRoute) {
        long count = transportRouteService.count(Wrappers.<TransportRoute>lambdaQuery()
                .eq(TransportRoute::getLat, transportRoute.getLat())
                .eq(TransportRoute::getLon, transportRoute.getLon()));

        if (count > 0) {
            BizExp.pop("该配送单已存在相同位置信息，请勿重复添加");
        }
    }

    /**
     * 验证配送订单有效性
     */
    private void verifyOrder(DeliveryOrder deliveryOrder) {
        Long count = baseMapper.selectCount(Wrappers.<DeliveryOrder>lambdaQuery()
                .eq(DeliveryOrder::getOrderNo, deliveryOrder.getOrderNo())
                .eq(DeliveryOrder::getEnabled, 1));

        if (count > 0) {
            BizExp.pop(String.format("该业务单%s不能重复使用", deliveryOrder.getOrderNo()));
        }
    }

    /**
     * 验证配送状态有效性
     *
     * @param id
     * @return
     */
    private DeliveryOrder verifyState(String id, Integer targetState) {
        // 配送状态：0-未调度 1-已调度,2-已接单,3-运输中,4-已送达
        DeliveryOrder deliveryOrder = baseMapper.selectById(id);
        if (deliveryOrder.getDeliveryState().equals(targetState)) {
            BizExp.pop("该配送单已经是目标状态");
        }
        if (deliveryOrder.getDeliveryState().equals(4)) {
            BizExp.pop("该配送单已送达");
        }

        switch (deliveryOrder.getDeliveryState()) {
            case 0:
                if (!(targetState.equals(1))) {
                    BizExp.pop("未调度的配送单只能转为已调度状态");
                }
                break;
            case 1:
                if (!(targetState.equals(2))) {
                    BizExp.pop("已调度的配送单只能转为已接单状态");
                }
                break;
            case 2:
                if (!(targetState.equals(3))) {
                    BizExp.pop("已接单的配送单只能转为运输中状态");
                }
                break;
            case 3:
                if (!(targetState.equals(4))) {
                    BizExp.pop("运输中的配送单只能转为已送达状态");
                }
                break;
            default:
                BizExp.pop("无效的配送单状态");
        }
        return deliveryOrder;
    }


}
