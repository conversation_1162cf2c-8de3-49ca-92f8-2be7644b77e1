package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.BrandActivity;
import com.hxdi.nmjl.condition.plan.BrandActivityCondition;

import java.util.List;

/**
 * 品牌传播活动服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/7 14:30
 */
public interface BrandActivityService extends IBaseService<BrandActivity> {

    /**
     * 新增品牌传播活动
     * @param activity 品牌传播活动实体
     */
    void create(BrandActivity activity);

    /**
     * 修改品牌传播活动
     * @param activity 品牌传播活动实体
     */
    void update(BrandActivity activity);

    /**
     * 获取品牌传播活动详情
     * @param activityId 活动ID
     * @return 品牌传播活动实体
     */
    BrandActivity getDetail(String activityId);

    /**
     * 分页查询品牌传播活动
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<BrandActivity> pages(BrandActivityCondition condition);

    /**
     * 列表查询品牌传播活动
     * @param condition 查询条件
     * @return 品牌传播活动列表
     */
    List<BrandActivity> lists(BrandActivityCondition condition);

    /**
     * 审批品牌传播活动（状态变更）
     * @param activityId 活动ID
     * @param approveStatus 审批状态
     * @param opinion 审批意见
     */
    void approve(String activityId, Integer approveStatus, String opinion);

    /**
     * 删除品牌传播活动
     * @param activityId 活动ID
     */
    void remove(String activityId);


    /**
     * 更新活动目标达成率
     * @param activityId 活动ID
     * @param rate 达成率（百分比）
     */
    void updateCompletedRate(String activityId, Integer rate);

    /**
     * 提交品牌传播活动
     * @param activityId 活动ID
     */
    void submit(String activityId);
}