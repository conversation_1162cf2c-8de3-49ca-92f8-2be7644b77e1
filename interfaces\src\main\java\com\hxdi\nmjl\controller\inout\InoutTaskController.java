package com.hxdi.nmjl.controller.inout;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.InoutTaskCondition;
import com.hxdi.nmjl.domain.inout.InoutTask;
import com.hxdi.nmjl.service.inout.InoutTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/inout/task")
@Api(tags = "出入库任务接口")
@Validated
public class InoutTaskController extends BaseController<InoutTaskService, InoutTask> {


    @ApiOperation(value = "新增/编辑")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody InoutTask inoutTask) {
        if (CommonUtils.isEmpty(inoutTask.getId())) {
            bizService.create(inoutTask);
        } else {
            bizService.updating(inoutTask);
        }
        return ResultBody.ok();
    }


    @ApiOperation(value = "查询")
    @GetMapping("/query")
    public ResultBody<InoutTask> query(@RequestParam String taskId) {
        return ResultBody.ok().data(bizService.detail(taskId));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String taskId) {
        bizService.remove(taskId);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<InoutTask>> page(InoutTaskCondition taskCondition) {
        return ResultBody.ok().data(bizService.getPage(taskCondition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<InoutTask>> list(InoutTaskCondition taskCondition) {
        return ResultBody.ok().data(bizService.getList(taskCondition));
    }

    @ApiOperation(value = "获取当前用户选中的订单中已选择的品种")
    @GetMapping("/getCatalogIds")
    public ResultBody<List<String>> getCatalogIds(@RequestParam String orderId, @RequestParam String inoutType) {
        return ResultBody.ok().data(bizService.getCatalogIds(orderId, inoutType));
    }


}
