package com.hxdi.nmjl.controller.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inout.delivery.DeliveryOrder;
import com.hxdi.nmjl.domain.inout.delivery.TransportRoute;
import com.hxdi.nmjl.service.inout.delivery.DeliveryOrderService;
import com.hxdi.nmjl.condition.inout.DeliveryOrderCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 调度配送单控制层
 *
 * <AUTHOR>
 * @since 2025/4/23 11:28
 */
@Api(tags = "调度配送单")
@RestController
@RequestMapping("/deliveryOrder")
public class DeliveryOrderController extends BaseController<DeliveryOrderService, DeliveryOrder> {

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新")
    public ResultBody saveOrUpdate(@RequestBody DeliveryOrder deliveryOrder) {
        bizService.saveOrUpdateV1(deliveryOrder);
        return ResultBody.ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "查询详情")
    public ResultBody<DeliveryOrder> get(@RequestParam @NotNull(message = "id不能为空") String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

    @GetMapping("/page")
    @ApiOperation(value = "分页查询")
    public ResultBody<Page<DeliveryOrder>> pages(DeliveryOrderCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @GetMapping("/list")
    @ApiOperation(value = "列表查询")
    public ResultBody<List<DeliveryOrder>> lists(DeliveryOrderCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    public ResultBody delete(@RequestParam @NotNull(message = "id不能为空") String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }


    @GetMapping("/getDetail")
    @ApiOperation(value = "查询车辆行驶路线和配送状态")
    public ResultBody<DeliveryOrder> getDetail(DeliveryOrderCondition condition) {
        return ResultBody.ok().data(bizService.getDetail(condition));
    }

    @PostMapping("/updateState")
    @ApiOperation(value = "更改运输状态")
    public ResultBody updateState(@RequestParam @NotNull(message = "id不能为空") String id,
                                  @RequestParam @NotNull(message = "state不能为空") Integer state) {
        bizService.updateState(id, state);
        return ResultBody.ok();
    }

    @PostMapping("/saveTrace")
    @ApiOperation(value = "新增/更新运输轨迹")
    public ResultBody saveTrace(@RequestBody TransportRoute transportRoute) {
        bizService.saveTrace(transportRoute);
        return ResultBody.ok();
    }
}
