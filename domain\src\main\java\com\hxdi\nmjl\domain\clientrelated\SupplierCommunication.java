package com.hxdi.nmjl.domain.clientrelated;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 供应商沟通记录
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-11
 */
@Getter
@Setter
@TableName("B_SUPPLIER_COMMUNICATION")
@ApiModel(description = "供应商沟通记录")
public class SupplierCommunication extends Entity<SupplierCommunication> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 供应商ID
     */
    @TableField("SUPPLIER_ID")
    @ApiModelProperty(value = "供应商ID", required = true)
    @NotBlank(message = "供应商ID不能为空")
    private String supplierId;

    /**
     * 沟通类型：1-在线报价，2-即时交互，3-消息通知，4-文件传输，5-其他
     */
    @TableField("COMMUNICATION_TYPE")
    @ApiModelProperty(value = "沟通类型：1-在线报价，2-即时交互，3-消息通知，4-文件传输，5-其他", required = true)
    @NotNull(message = "沟通类型不能为空")
    private Integer communicationType;

    /**
     * 沟通标题
     */
    @TableField("TITLE")
    @ApiModelProperty(value = "沟通标题")
    @NotBlank(message = "沟通标题不能为空")
    private String title;

    /**
     * 沟通内容
     */
    @TableField("CONTENT")
    @ApiModelProperty(value = "沟通内容")
    private String content;

    /**
     * 优先级：1-低，2-中，3-高，4-紧急
     */
    @TableField("PRIORITY")
    @ApiModelProperty(value = "优先级：1-低，2-中，3-高，4-紧急")
    private Integer priority;

    /**
     * 文件附件路径（多个文件用逗号分隔）
     */
    @TableField("ATTACHMENTS")
    @ApiModelProperty(value = "文件附件路径（多个文件用逗号分隔）")
    private String ATTACHMENTS;

    @TableField("ORG_ID")
    @ApiModelProperty(value = "沟通发起方组织ID")
    private String orgId;

    @TableField("ORG_NAME")
    @ApiModelProperty(value = "沟通发起方组织名称")
    private String orgName;

    /**
     * 沟通发起方ID
     */
    @TableField("INITIATOR_ID")
    @ApiModelProperty(value = "沟通发起方ID")
    private String initiatorId;

    /**
     * 沟通发起方名称
     */
    @TableField("INITIATOR_NAME")
    @ApiModelProperty(value = "沟通发起方名称")
    private String initiatorName;

    /**
     * 沟通状态：1-待处理，2-处理中，3-已处理
     */
    @TableField("STATUS")
    @ApiModelProperty(value = "沟通状态：1-待处理，2-处理中，3-已处理")
    private Integer status;

    /**
     * 处理结果
     */
    @TableField("RESULT")
    @ApiModelProperty(value = "处理结果")
    private String result;

    /**
     * 处理人ID
     */
    @TableField("HANDLER_ID")
    @ApiModelProperty(value = "处理人ID")
    private String handlerId;

    /**
     * 处理人名称
     */
    @TableField("HANDLER_NAME")
    @ApiModelProperty(value = "处理人名称")
    private String handlerName;

    /**
     * 处理时间
     */
    @TableField("HANDLE_TIME")
    @ApiModelProperty(value = "处理时间")
    private Date handleTime;

    /**
     * 供应商名称
     */
    @TableField("SUPPLIER_NAME")
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 备注
     */
    @TableField("REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 状态（0禁用，1有效，7删除）
     */
    @TableField("ENABLED")
    @ApiModelProperty(value = "状态（0禁用，1有效，7删除）")
    private Integer enabled;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人ID")
    private String createId;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人ID")
    private String updateId;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 数据权限字段
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "数据权限字段")
    private String dataHierarchyId;
}
