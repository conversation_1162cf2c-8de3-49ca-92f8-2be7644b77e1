package com.hxdi.nmjl.domain.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hxdi.nmjl.enums.BusinessType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

/**
 * @program: nmjl-service
 * @description: 条码中包含的固定对象信息
 * @author: 王贝强
 * @create: 2025-08-21 09:27
 */
@Getter
@Setter
public class BarInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务ID
     */
    @JsonProperty("BID")
    private String bid;

    /**
     * 业务类型
     */
    @JsonProperty("BTP")
    private String btp;

    /**
     * 扩展JSON字段
     */
    @JsonProperty("EXT")
    private String ext;

    /**
     * 条码版本
     */
    @JsonProperty("VER")
    private String ver = "1.0";

    public BarInfo(String bid, BusinessType btp, String ext) {
        this.bid = bid;
        this.btp = btp.getCode();
        this.ext = ext;
    }

    public BarInfo(String bid, String btp, String ext, String ver) {
        this.bid = bid;
        this.btp = btp;
        this.ext = ext;
        this.ver = ver;
    }

    public BarInfo() {
    }

    @Override
    public String toString() {
        return "BarInfo{" +
                "bid='" + bid + '\'' +
                ", btp='" + btp + '\'' +
                ", ext='" + ext + '\'' +
                ", ver='" + ver + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof BarInfo)) return false;
        BarInfo barInfo = (BarInfo) o;
        return Objects.equals(getBid(), barInfo.getBid()) && Objects.equals(getBtp(), barInfo.getBtp()) && Objects.equals(getExt(), barInfo.getExt()) && Objects.equals(getVer(), barInfo.getVer());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getBid(), getBtp(), getExt(), getVer());
    }

    public String toJSONString() {
        return "{\"BID\":\"" + bid + "\",\"BTP\":\"" + btp + "\",\"EXT\":\"" + ext + "\",\"VER\":\"" + ver + "\"}";
    }
}
