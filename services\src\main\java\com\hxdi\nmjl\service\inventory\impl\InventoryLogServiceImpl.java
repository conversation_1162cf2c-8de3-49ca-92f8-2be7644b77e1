package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxdi.common.core.constants.StrPool;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.nmjl.mapper.inventory.InventoryLogMapper;
import com.hxdi.nmjl.domain.inventory.InventoryLog;
import com.hxdi.nmjl.service.inventory.InventoryLogService;

import java.util.List;

@Service
public class InventoryLogServiceImpl extends ServiceImpl<InventoryLogMapper, InventoryLog> implements InventoryLogService{

    @Override
    public List<InventoryLog> getList(String inventoryId) {
        //根据库存id获取对应的有效的库存变更日志
        LambdaQueryWrapper<InventoryLog> wrapper=new LambdaQueryWrapper<InventoryLog>()
                .eq(InventoryLog::getInventoryId,inventoryId)
                .eq(InventoryLog::getEnabled, StrPool.State.ENABLE)
                .orderByDesc(InventoryLog::getCreateTime);
        return baseMapper.selectList(wrapper);
    }
}
