package com.hxdi.nmjl.controller.storeproduction;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlan;
import com.hxdi.nmjl.service.storeproduction.StoreProductionPlanService;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionPlanCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 生产计划管理
 <AUTHOR>
 @version 1.0
 @since 2025/8/5
 */
@Api(tags = "生产计划管理")
@RestController
@RequestMapping("/storeProductionPlan")
public class StoreProductionPlanController extends BaseController<StoreProductionPlanService, StoreProductionPlan> {

    @ApiOperation("分页查询计划")
    @GetMapping("/page")
    public ResultBody<Page<StoreProductionPlan>> page(StoreProductionPlanCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }


    @ApiOperation("列表查询计划")
    @GetMapping("/list")
    public ResultBody<List<StoreProductionPlan>> list(StoreProductionPlanCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("列表查询已审核计划")
    @GetMapping("/approvedList")
    public ResultBody<List<StoreProductionPlan>> approvedList() {
        return ResultBody.ok().data(bizService.approvedLists());
    }

    @ApiOperation("保存/修改计划")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody StoreProductionPlan plan) {
        if(CommonUtils.isEmpty(plan.getId())) {
            bizService.create(plan);
        } else {
            bizService.update(plan);
        }
        return ResultBody.ok();
    }


    @ApiOperation("查看计划详情")
    @GetMapping("/getDetail")
    public ResultBody<StoreProductionPlan> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }


    @ApiOperation("审批计划")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id,
                              @RequestParam Integer approveStatus,
                              @RequestParam(required = false) String opinion) {
        bizService.approve(id, approveStatus, opinion);
        return ResultBody.ok();
    }

    @ApiOperation("提交生产计划")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }

    @ApiOperation("删除计划")
    @DeleteMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }
}