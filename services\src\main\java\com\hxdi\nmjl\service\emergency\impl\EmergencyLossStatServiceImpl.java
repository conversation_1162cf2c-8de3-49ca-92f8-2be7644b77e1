package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CalculationUtil;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.emergency.EmergencyLoss;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStat;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStatRef;
import com.hxdi.nmjl.domain.emergency.EmergencyStoreApply;
import com.hxdi.nmjl.linker.ContextService;
import com.hxdi.nmjl.mapper.emergency.EmergencyLossMapper;
import com.hxdi.nmjl.mapper.emergency.EmergencyLossStatMapper;
import com.hxdi.nmjl.service.emergency.EmergencyLossService;
import com.hxdi.nmjl.service.emergency.EmergencyLossStatRefService;
import com.hxdi.nmjl.service.emergency.EmergencyLossStatService;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <应急损耗统计服务实现>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:49
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class EmergencyLossStatServiceImpl extends BaseServiceImpl<EmergencyLossStatMapper, EmergencyLossStat> implements EmergencyLossStatService {

    @Autowired
    private EmergencyLossStatRefService emergencyLossStatRefService;

    @Override
    public void updateStat(EmergencyLossStat entity) {
        String statId = null;
        EmergencyLossStat savedStat = baseMapper.selectOne(Wrappers.<EmergencyLossStat>lambdaQuery()
                        .eq(EmergencyLossStat::getTaskCode, entity.getTaskCode())
                        .eq(EmergencyLossStat::getStoreId, entity.getStoreId())
                        .eq(EmergencyLossStat::getClassificationId, entity.getClassificationId()));

        if (savedStat == null) {
            entity.setState(0);
            entity.setLossTotal(Optional.ofNullable(entity.getCurrentLossQty()).orElse(BigDecimal.ZERO));
            entity.setAmount(Optional.ofNullable(entity.getAmount()).orElse(BigDecimal.ZERO));
            baseMapper.insert(entity);

            statId = entity.getId();
        } else {
            savedStat.setLossTotal(CalculationUtil.add(savedStat.getLossTotal(), entity.getCurrentLossQty()));
            savedStat.setAmount(CalculationUtil.add(savedStat.getAmount(), entity.getAmount()));
            baseMapper.updateById(savedStat);

            statId = savedStat.getId();
        }

        // 保存关联关系
        emergencyLossStatRefService.save(statId, entity.getLossId());
    }

    @Override
    public void lockState(String statId) {
        EmergencyLossStat stat = baseMapper.selectById(statId);
        if (stat.getState() == 1) {
            BizExp.pop("请不要重复申请！");
        }

        stat.setState(1);
        baseMapper.updateById(stat);
    }

    @Override
    public void unlockState(String statId) {
        EmergencyLossStat stat = new EmergencyLossStat();
        stat.setId(statId);
        stat.setState(0);
        baseMapper.updateById(stat);
    }

    @Override
    public List<EmergencyLossStat> getListByStoreId(String storeId) {
        return baseMapper.selectList(Wrappers.<EmergencyLossStat>lambdaQuery().eq(EmergencyLossStat::getStoreId, storeId).eq(EmergencyLossStat::getState, 0));
    }

    @Override
    public List<EmergencyLoss> getLossDetail(String statId) {
        return Collections.emptyList();
    }
}
