package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("整体经营计划查询条件")
public class OperatingPlanCondition extends QueryCondition {

    @ApiModelProperty(value = "计划状态：1未生效，2已生效，3已完成")
    private Integer state;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "计划编号")
    private String planCode;

    @ApiModelProperty(value = "审核状态")
    private Integer approveStatus;

    //传入pid为0，筛选整体计划,传入为1时筛选子计划
    @ApiModelProperty(value = "筛选子计划")
    private String pid;

}
