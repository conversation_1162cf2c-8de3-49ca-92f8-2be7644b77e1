package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 招标(标的物)明细
 */
@ApiModel(description = "招标对象")
@Getter
@Setter
@TableName("B_BID_OBJECT")
public class BidObject implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @TableField(value = "BID_ID")
    @ApiModelProperty(value = "招标ID")
    private String bidId;

    @TableField(value = "CLASSIFICATION_ID")
    @ApiModelProperty(value = "品类")
    private String classificationId;

    @TableField(value = "CLASSIFICATION_NAME")
    @ApiModelProperty(value = "品类名称")
    private String classificationName;

    @TableField(value = "SPECIFICATIONS")
    @ApiModelProperty(value = "规格")
    private String specifications;

    @TableField(value = "GRADE")
    @ApiModelProperty(value = "质量等级")
    private String grade;

    @TableField(value = "MAX_LIMIT")
    @ApiModelProperty(value = "需求上限")
    private BigDecimal maxLimit;

    @TableField(value = "MIN_LIMIT")
    @ApiModelProperty(value = "需求下限")
    private BigDecimal minLimit;

    @TableField(value = "REQUIREMENT")
    @ApiModelProperty(value = "产地及年份要求")
    private String requirement;

    @TableField(value = "SUPPLY_FREQUENCY")
    @ApiModelProperty(value = "供货频次:1次/月")
    private String supplyFrequency;

    @TableField(value = "FIRST_DELIVERY_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "首次交货日期")
    private Date firstDeliveryTime;

    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建ID")
    private String createId;

    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新ID")
    private String updateId;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
