package com.hxdi.nmjl.vo.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 库存保管总账返回VO
 * @author: 王贝强
 * @create: 2025-04-21 17:01
 */
@Setter
@Getter
@ApiModel(description="库存保管账（总账、明细账）返回VO")
public class InventoryStatResVO {

    /**
     * —————————————————库存统计表————————————————————
     */
    @ApiModelProperty(value="统计月份")
    private Date reportDate;

    @ApiModelProperty(value="期初数量kg")
    private BigDecimal startQty;

    @ApiModelProperty(value="入库数量kg")
    private BigDecimal inQty;

    @ApiModelProperty(value="出库数量kg")
    private BigDecimal outQty;

    @ApiModelProperty(value="结存数量kg")
    private BigDecimal finalQty;

    /**
     * —————————————————库存表————————————————————
     */
    @ApiModelProperty(value="库点ID")
    private String storeId;

    @ApiModelProperty(value="库点")
    private String storeName;

    @ApiModelProperty(value="仓房ID")
    private String stId;

    @ApiModelProperty(value="仓房")
    private String stName;

    @ApiModelProperty(value="货位ID")
    private String locId;

    @ApiModelProperty(value="货位名称")
    private String locName;

    @ApiModelProperty(value="粮权单位id")
    private String manageUnitId;

    @ApiModelProperty(value="粮权单位名称")
    private String manageUnitName;

    @ApiModelProperty(value="品种ID")
    private String catalogId;

    @ApiModelProperty(value="品种名称")
    private String catalogName;

    @ApiModelProperty(value="质量等级")
    private String grade;

    @ApiModelProperty(value="储备性质")
    private Integer reserveLevel;

    @ApiModelProperty(value="入库日期")
    private Date createTime;

    /**
     * —————————————————品种表————————————————————
     */
    @ApiModelProperty(value="规格")
    private String specification;

    @ApiModelProperty(value="品牌")
    private String brand;

    @ApiModelProperty(value="商品名称")
    private String fullName;

    @ApiModelProperty(value="保质期")
    private Integer maxStorageTime;

}
