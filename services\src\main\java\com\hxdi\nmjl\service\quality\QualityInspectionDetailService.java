package com.hxdi.nmjl.service.quality;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.quality.QualityInspectionDetail;

import java.io.Serializable;
import java.util.List;

public interface QualityInspectionDetailService extends IBaseService<QualityInspectionDetail> {
    /**
     * 根据检验ID获取检验详情
     * @param InspectionId 检验ID
     * @return
     */
    List<QualityInspectionDetail> getDetail(Serializable InspectionId);
}
