<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.mobilization.MobilizedContractMapper">
    <sql id="Base_Column_List">
        ID, ENTERPRISE_ID, ENTERPRISE_NAME, CONTRACT_CODE, ORIGIN_CODE, NAME,
    BIZ_TYPE, ORG_ID, ORG_NAME, QTY, STATE, TOTAL_AMOUNT, STORE_ID, STORE_NAME,
    NOTES, SIGNER, SID_CARD, SIGNED_DATE, START_DATE, END_DATE, APPROVE_TIME,
    APPROVE_STATUS, APPROVER, APPROVE_OPINION, ENABLED, CREATE_TIME, UPDATE_TIME,
    CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, ATTACHMENT
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.mobilization.MobilizedContract">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="ENTERPRISE_ID" jdbcType="VARCHAR" property="enterpriseId" />
        <result column="ENTERPRISE_NAME" jdbcType="VARCHAR" property="enterpriseName" />
        <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contractCode" />
        <result column="ORIGIN_CODE" jdbcType="VARCHAR" property="originCode" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="BIZ_TYPE" jdbcType="VARCHAR" property="bizType" />
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="STATE" jdbcType="INTEGER" property="state" />
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
        <result column="NOTES" jdbcType="VARCHAR" property="notes" />
        <result column="SIGNER" jdbcType="VARCHAR" property="signer" />
        <result column="SID_CARD" jdbcType="VARCHAR" property="sidCard" />
        <result column="SIGNED_DATE" jdbcType="DATE" property="signedDate" />
        <result column="START_DATE" jdbcType="DATE" property="startDate" />
        <result column="END_DATE" jdbcType="DATE" property="endDate" />
        <result column="APPROVE_TIME" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="APPROVE_STATUS" jdbcType="INTEGER" property="approveStatus" />
        <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
        <result column="APPROVE_OPINION" jdbcType="VARCHAR" property="approveOpinion" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
        <result column="ATTACHMENT" jdbcType="VARCHAR" property="attachment" />
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_MOBILIZED_CONTRACT
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.bizType)">
                and BIZ_TYPE = #{condition.bizType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseName)">
                and ENTERPRISE_NAME like concat('%',#{condition.enterpriseName},'%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                and ORG_NAME like concat('%',#{condition.orgName},'%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                and STATE = #{condition.state}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                and STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createStartTime)">
                and CREATE_TIME between #{condition.createStartTime} and #{condition.createEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                and DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_MOBILIZED_CONTRACT
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.bizType)">
                and BIZ_TYPE = #{condition.bizType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseName)">
                and ENTERPRISE_NAME like concat('%',#{condition.enterpriseName},'%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                and ORG_NAME like concat('%',#{condition.orgName},'%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                and STATE = #{condition.state}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                and STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                and APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createStartTime)">
                and CREATE_TIME between #{condition.createStartTime} and #{condition.createEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.dataHierarchyId)">
                and DATA_HIERARCHY_ID = #{condition.dataHierarchyId}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
</mapper>