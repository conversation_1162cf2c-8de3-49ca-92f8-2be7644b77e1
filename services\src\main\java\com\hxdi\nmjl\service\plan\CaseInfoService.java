package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.CaseInfo;
import com.hxdi.nmjl.condition.plan.CaseInfoCondition;

import java.util.List;

/**
 * 案件管理服务接口
 */
public interface CaseInfoService extends IBaseService<CaseInfo> {

    /**
     * 查询案件详情
     *
     * @param id 案件ID
     * @return CaseInfo
     */
    CaseInfo getDetail(String id);

    /**
     * 分页查询案件信息
     *
     * @param condition 查询条件
     * @return Page<CaseInfo>
     */
    Page<CaseInfo> getPages(CaseInfoCondition condition);

    /**
     * 列表查询案件信息
     *
     * @param condition 查询条件
     * @return List<CaseInfo>
     */
    List<CaseInfo> getList(CaseInfoCondition condition);

    /**
     * 新增案件信息
     *
     * @param caseInfo 案件信息
     * @return boolean
     */
    void add(CaseInfo caseInfo);

    /**
     * 更新案件信息
     *
     * @param caseInfo 案件信息
     * @return boolean
     */
    void update(CaseInfo caseInfo);

    /**
     * 删除案件信息
     *
     * @param id 案件ID
     * @return boolean
     */
    boolean delete(String id);
}
