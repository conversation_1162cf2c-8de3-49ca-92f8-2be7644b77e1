package com.hxdi.nmjl.service.inout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.linker.InventoryLinker;
import com.hxdi.nmjl.condition.inout.DispatchCondition;
import org.springframework.stereotype.Service;
import com.hxdi.nmjl.domain.inout.DispatchInfo;
import com.hxdi.nmjl.mapper.inout.DispatchInfoMapper;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class DispatchInfoServiceImpl extends BaseServiceImpl<DispatchInfoMapper, DispatchInfo> implements DispatchInfoService{

    @Resource
    InventoryLinker linker;

    @Override
    public void create(List<DispatchInfo> dispatchInfoList) {
        if(!dispatchInfoList.isEmpty()){
           this.saveBatch(dispatchInfoList);
        }
    }

    @Override
    public void removeByIdList(List<String> dispatchIdList) {
        if(dispatchIdList.isEmpty()){
            return;
        }
        List<DispatchInfo> list = this.listByIds(dispatchIdList);
        if(!list.isEmpty()){
            int canDel=0;
            for (DispatchInfo dispatchInfo : list) {
                if(dispatchInfo.getStatus() == 1 || dispatchInfo.getStatus() == 2) {
                    canDel = 1;
                    break;
                }
                //设置删除状态
                dispatchInfo.setEnabled(0);
            }
            if(canDel == 0){
                this.updateBatchById(list);
            }else{
                throw new BaseException("存在执行中或已执行的记录，无法执行删除操作！");
            }
        }
    }

    @Override
    public void removeByTaskId(String taskId) {
        LambdaQueryWrapper<DispatchInfo> wrapper=new LambdaQueryWrapper<DispatchInfo>()
                .eq(DispatchInfo::getTaskId, taskId)
                .eq(DispatchInfo::getEnabled, StrPool.State.ENABLE);
        List<String> infoList = this.list(wrapper).stream().map(DispatchInfo::getId).collect(Collectors.toList());
        this.removeByIdList(infoList);
    }

    public void doSubmit(List<String> dispatchIdList) {
        List<DispatchInfo> list = this.listByIds(dispatchIdList);
        if(!list.isEmpty()){
            //校验是否属于同一个拆分调度计划
            if(list.stream().map(DispatchInfo::getTaskId).distinct().count() > 1){
                throw new BaseException("选中记录项来源于多个拆分调度计划，请重新选择");
            }

            //设置执行状态——已提交
            list.forEach(dispatchInfo->dispatchInfo.setStatus(1));
            this.updateBatchById(list);
            // 执行库存拆分，完成后更新执行状态——执行完成
            linker.onChange(list);
        }
    }


    @Override
    public List<DispatchInfo> listV1(DispatchCondition condition) {
        return baseMapper.listV1(condition);
    }

    @Override
    public Page<DispatchInfo> PageV1(DispatchCondition condition) {
        Page<DispatchInfo> page = condition.newPage();
        return baseMapper.pageV1(condition,page);
    }
}
