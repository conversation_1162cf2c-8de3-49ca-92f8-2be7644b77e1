package com.hxdi.nmjl.service.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.emergency.EmergencyPerson;
import com.hxdi.nmjl.condition.emergency.EmergencyPersonCondition;

import java.util.List;

/**
 * 应急人员管理服务接口
 */
public interface EmergencyPersonService extends IBaseService<EmergencyPerson> {

    /**
     * 查询应急人员详情
     *
     * @param id 应急人员ID
     * @return EmergencyPerson
     */
    EmergencyPerson getDetail(String id);

    /**
     * 分页查询应急人员信息
     *
     * @param condition 查询条件
     * @return Page<EmergencyPerson>
     */
    Page<EmergencyPerson> getPages(EmergencyPersonCondition condition);

    /**
     * 列表查询应急人员信息
     *
     * @param condition 查询条件
     * @return List<EmergencyPerson>
     */
    List<EmergencyPerson> getList(EmergencyPersonCondition condition);

    /**
     * 新增应急人员信息
     *
     * @param emergencyPerson 应急人员信息
     */
    void add(EmergencyPerson emergencyPerson);

    /**
     * 更新应急人员信息
     *
     * @param emergencyPerson 应急人员信息
     */
    void update(EmergencyPerson emergencyPerson);

    /**
     * 删除应急人员信息
     *
     * @param id 应急人员ID
     * @return boolean
     */
    boolean delete(String id);
}
