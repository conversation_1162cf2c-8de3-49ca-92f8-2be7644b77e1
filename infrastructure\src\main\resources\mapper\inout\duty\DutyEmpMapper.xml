<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.duty.DutyEmpMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.duty.DutyEmp">
        <id column="ID" property="id" />
        <result column="DUTY_PLAN_ID" property="dutyPlanId" />
        <result column="PHASE" property="phase" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="JOB_TITLE" property="jobTitle" />
        <result column="EMP_NAME" property="empName" />
        <result column="MOBILE" property="mobile" />
        <result column="REMARKS" property="remarks" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,DUTY_PLAN_ID,PHASE,START_TIME,END_TIME,JOB_TITLE,
        EMP_NAME,MOBILE,REMARKS,CREATE_TIME,UPDATE_TIME,
        TENANT_ID,DATA_HIERARCHY_ID
    </sql>


</mapper>
