package com.hxdi.nmjl.domain.storeproduction;


import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 原料采购表
 */
@Getter
@Setter
@TableName(value = "B_STORE_RAW_MATERIAL_PURCHASE")
public class StoreRawMaterialPurchase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 计划编号
     */
    @TableField(value = "PLAN_NO")
    private String planNo;

    /**
     * 原料名称
     */
    @TableField(value = "MATERIAL_NAME")
    private String materialName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    private String specification;

    /**
     * 计量单位
     */
    @TableField(value = "UNIT")
    private String unit;

    /**
     * 进货数量
     */
    @TableField(value = "qty")
    private BigDecimal qty;

    /**
     * 进货单价
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 供应商名称
     */
    @TableField(value = "SUPPLIER_NAME")
    private String supplierName;

    /**
     * 进货日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "PURCHASE_DATE")
    private Date purchaseDate;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    private String storeName;

    /**
     * 备注信息
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 状态（1-有效 0-删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}

