#\u5E94\u7528\u4FE1\u606F
app.port=8244
profile.name=release
config.server-addr=10.6.35.9:8848
discovery.server-addr=10.6.35.9:8848
# ????
#config.namespace=b90c5508-2bfd-4b67-8b3d-502310e29d94
# ????
config.namespace=051354fa-3c32-43ac-84e7-4d2146ca3ce1
config.group=DEFAULT_GROUP
auth.username=nacos
auth.password=nmjl@nacos

#\u7CFB\u7EDF\u65E5\u5FD7\u914D\u7F6E
application.name=${artifactId}
log.path=logs/${artifactId}
# \u9ED8\u8BA4\u7EA7\u522Binfo\uFF0C\u53EF\u901A\u8FC7\u7CFB\u7EDF\u53C2\u6570\u6307\u5B9A-Dlog.level=debug \u67E5\u770B\u66F4\u8BE6\u7EC6\u65E5\u5FD7
log.level=info

api.debug=false


