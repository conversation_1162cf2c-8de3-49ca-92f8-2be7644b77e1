package com.hxdi.nmjl.controller.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionResourceCondition;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionResource;
import com.hxdi.nmjl.service.storeproduction.StoreProductionResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**

 生产资源管理
 <AUTHOR>
 @version 1.0
 @since 2025/8/5
 */
@Api (tags = "生产资源管理")
@RestController
@RequestMapping ("/storeProductionResource")
public class StoreProductionResourceController extends BaseController<StoreProductionResourceService, StoreProductionResource> {
    @ApiOperation ("分页查询资源")
    @GetMapping ("/page")
    public ResultBody<Page<StoreProductionResource>> page(StoreProductionResourceCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }
    @ApiOperation ("列表查询资源")
    @GetMapping ("/list")
    public ResultBody<List<StoreProductionResource>> list(StoreProductionResourceCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }
    @ApiOperation ("保存/修改资源")
    @PostMapping ("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody StoreProductionResource resource) {
        if (CommonUtils.isEmpty(resource.getId ())) {
            bizService.create(resource);
        } else {
            bizService.update(resource);
        }
        return ResultBody.ok();
    }
    @ApiOperation ("查看资源详情")
    @GetMapping ("/getDetail")
    public ResultBody<StoreProductionResource> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }
    @ApiOperation ("删除资源")
    @PostMapping ("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }
}