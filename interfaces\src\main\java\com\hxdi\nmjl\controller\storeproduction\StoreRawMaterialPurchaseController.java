package com.hxdi.nmjl.controller.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.storeproduction.StoreRawMaterialPurchaseCondition;
import com.hxdi.nmjl.domain.storeproduction.StoreRawMaterialPurchase;
import com.hxdi.nmjl.service.storeproduction.StoreRawMaterialPurchaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**

 原料采购管理
 <AUTHOR>
 @version 1.0
 @since 2025/8/5
 */
@Api (tags = "原料采购管理")
@RestController
@RequestMapping ("/rawMaterialPurchase")
public class StoreRawMaterialPurchaseController extends BaseController<StoreRawMaterialPurchaseService, StoreRawMaterialPurchase> {
    @ApiOperation ("分页查询采购记录")
    @GetMapping ("/page")
    public ResultBody<Page<StoreRawMaterialPurchase>> page(StoreRawMaterialPurchaseCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }
    @ApiOperation ("列表查询采购记录")
    @GetMapping ("/list")
    public ResultBody<List<StoreRawMaterialPurchase>> list(StoreRawMaterialPurchaseCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }
    @ApiOperation ("保存/修改采购记录")
    @PostMapping ("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody StoreRawMaterialPurchase purchase) {
        if (CommonUtils.isEmpty(purchase.getId ())) {
            bizService.create(purchase);
        } else {
            bizService.update(purchase);
        }
        return ResultBody.ok();
    }
    @ApiOperation ("查看采购详情")
    @GetMapping ("/getDetail")
    public ResultBody<StoreRawMaterialPurchase> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }
    @ApiOperation ("删除采购记录")
    @PostMapping ("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }
}