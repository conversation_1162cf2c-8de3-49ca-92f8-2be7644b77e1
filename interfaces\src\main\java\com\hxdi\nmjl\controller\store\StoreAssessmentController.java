package com.hxdi.nmjl.controller.store;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.store.StoreAssessment;
import com.hxdi.nmjl.service.store.StoreAssessmentService;
import com.hxdi.nmjl.condition.store.StoreAssessmentCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 军供站评估管理
 <AUTHOR> 1.0
 @since 2025/8/11
 */
@Api(tags = " 军供站评估管理")
@RestController
@RequestMapping("/storeAssessment")
public class StoreAssessmentController extends BaseController<StoreAssessmentService, StoreAssessment> {

    @ApiOperation("分页查询评估记录")
    @GetMapping("/page")
    public ResultBody<Page<StoreAssessment>> page(StoreAssessmentCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }


    @ApiOperation("列表查询评估记录")
    @GetMapping("/list")
    public ResultBody<List<StoreAssessment>> list(StoreAssessmentCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }


    @ApiOperation("保存/修改评估记录")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody StoreAssessment assessment) {
        if(CommonUtils.isEmpty(assessment.getId())) {
            bizService.create(assessment);
        } else {
            bizService.update(assessment);
        }
        return ResultBody.ok();
    }


    @ApiOperation("查看评估详情")
    @GetMapping("/getDetail")
    public ResultBody<StoreAssessment> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }


    @ApiOperation("审批评估记录")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id,
                              @RequestParam Integer approveStatus,
                              @RequestParam(required = false) String opinion) {
        bizService.approve(id, approveStatus, opinion);
        return ResultBody.ok();
    }

    @ApiOperation("提交审核")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }


    @ApiOperation("删除评估记录")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }
}
