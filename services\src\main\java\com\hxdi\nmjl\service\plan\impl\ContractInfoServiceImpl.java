package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.plan.ContractCondition;
import com.hxdi.nmjl.domain.plan.*;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.ContractInfoMapper;
import com.hxdi.nmjl.service.plan.*;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同信息服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/15 22:23
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ContractInfoServiceImpl extends BaseServiceImpl<ContractInfoMapper, ContractInfo> implements ContractInfoService {

    @Resource
    private ContractDetailService contractDetailService;

    @Resource
    private BidInfoService bidInfoService;

    @Resource
    private ProcurementPlanService planService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private ProductionOrderService productionOrderService;

    @Resource
    private SaleOrderService saleOrderService;

    @Resource
    private ProductionOrderItemService productionOrderItemService;

    @Resource
    @Lazy
    private ContractSettlementService contractSettlementService;

    @Override
    public void create(ContractInfo contract) {

        String codeKey;
        //根据不同的业务类型生成不同的合同编号
        if (contract.getBizType().equals("1")) {
            codeKey = "PROD_CONTRACT_CODE";
        } else {
            codeKey = "SALE_CONTRACT_CODE";
        }
        //生成编码
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode(codeKey);
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        contract.setContractCode((String) businessCode.getValue());
        contract.setState(0);

        this.save(contract);

        //保存合同明细
        if (contract.getDetailList() != null) {
            contract.getDetailList().forEach(detail -> detail.setContractId(contract.getId()));
            contractDetailService.saveBatch(contract.getDetailList());
        }
    }

    @Override
    public void updateProcessV1(List<SaleOrderItem> itemList) {
        if (itemList == null || itemList.isEmpty()) {
            return;
        }
        String contractId = itemList.get(0).getContractId();
        if (contractId == null) {
            return;
        }
        //按品种ID分组并累加数量
        Map<String, BigDecimal> catalogQtyMap = itemList.stream()
                .collect(Collectors.groupingBy(SaleOrderItem::getCatalogId,
                        Collectors.reducing(BigDecimal.ZERO, SaleOrderItem::getOrderQty, BigDecimal::add)));

        //查询合同明细并更新完成数量
        List<ContractDetail> detailList = contractDetailService.getList(contractId);
        detailList.forEach(detail -> {
            BigDecimal completedQty = catalogQtyMap.get(detail.getCatalogId());
            if (completedQty != null) {
                detail.setCompletedQty(detail.getCompletedQty().add(completedQty));
            }
        });

        //批量更新明细
        contractDetailService.updateBatchById(detailList);

        //检查合同是否完成，更新合同状态
        updateContractStateIfCompleted(contractId, detailList);
    }

    @Override
    public void updateProcess(List<ProductionOrderTrace> traceList) {
        if (traceList == null || traceList.isEmpty()) {
            return;
        }

        // 获取订单ID
        String orderId = traceList.get(0).getOrderId();

        // 查询生产订单获取合同ID
        ProductionOrder order = productionOrderService.getById(orderId);
        if (order == null || order.getContractId() == null) {
            return;
        }

        String contractId = order.getContractId();

        // 获取订单明细信息，用于获取品种ID及等级
        List<ProductionOrderItem> orderItems = productionOrderItemService.listByOrderId(orderId);
        Map<String, String> itemCatalogMap = orderItems.stream()
                .collect(Collectors.toMap(
                        ProductionOrderItem::getId,
                        ProductionOrderItem::getCatalogId
                ));
        Map<String, String> itemGradeMap = orderItems.stream()
                .collect(Collectors.toMap(
                        ProductionOrderItem::getId,
                        ProductionOrderItem::getGrade
                ));

        // 按品种ID及等级分组并累加数量
        Map<String, BigDecimal> catalogGradeQtyMap = traceList.stream()
                .filter(trace -> itemCatalogMap.containsKey(trace.getOrderItemId()))
                .collect(Collectors.groupingBy(
                        trace -> itemCatalogMap.get(trace.getOrderItemId()) + "_" + itemGradeMap.get(trace.getOrderItemId()),
                        Collectors.reducing(BigDecimal.ZERO,
                                ProductionOrderTrace::getTotalQty,
                                BigDecimal::add)
                ));

        // 查询合同明细并更新完成数量
        List<ContractDetail> detailList = contractDetailService.getList(contractId);
        detailList.forEach(detail -> {
            String key = detail.getCatalogId() + "_" + detail.getGrade();
            BigDecimal completedQty = catalogGradeQtyMap.get(key);
            if (completedQty != null) {
                detail.setCompletedQty(detail.getCompletedQty().add(completedQty));
            }
        });

        // 更新合同总数量
        ContractInfo contract = this.getById(contractId);
        contract.setCompletedQty(contract.getCompletedQty().add(BigDecimal.valueOf(catalogGradeQtyMap.values().stream().mapToDouble(BigDecimal::doubleValue).sum())));
        this.updateById(contract);

        // 批量更新明细
        contractDetailService.updateBatchById(detailList);

        // 检查合同是否完成，更新合同状态
        updateContractStateIfCompleted(contractId, detailList);
    }

    /**
     * 检查合同是否完成，如果完成则更新状态
     */
    private void updateContractStateIfCompleted(String contractId, List<ContractDetail> detailList) {
        boolean allCompleted = detailList.stream()
                .allMatch(detail -> detail.getCompletedQty().compareTo(detail.getContractQty()) >= 0);

        if (allCompleted) {
            ContractInfo contract = this.getById(contractId);
            if (contract != null && contract.getState() == 1) { // 已提交状态
                contract.setState(2); // 设置为已完成
                this.updateById(contract);
            }
        }
    }

    @Override
    public void updating(ContractInfo contract) {
        ContractInfo contractInfo = this.getById(contract.getId());
        if (contractInfo == null) {
            throw new BaseException("合同不存在！");
        }
        if (contractInfo.getState() != 0) {
            throw new BaseException("合同已提交，无法修改");
        }
        //更新合同信息
        this.updateById(contract);

        //更新合同明细
        contractDetailService.removeByContractId(contract.getId());
        if (contract.getDetailList() != null) {
            contract.getDetailList().forEach(detail -> detail.setContractId(contract.getId()));
            contractDetailService.saveBatch(contract.getDetailList());
        }
    }

    @Override
    public void remove(String id) {
        ContractInfo contractInfo = this.getById(id);
        if (contractInfo == null) {
            throw new BaseException("合同不存在！");
        }
        //只有未提交的合同才能删除
        if (contractInfo.getState() == 0) {
            contractInfo.setEnabled(0);
            baseMapper.updateById(contractInfo);
        } else {
            throw new BaseException("合同已提交，不能删除！");
        }
    }

    @Override
    public void submit(String id) {
        ContractInfo contractInfo = this.getById(id);
        if (contractInfo == null) {
            throw new BaseException("合同不存在！");
        }
        if (contractInfo.getState() != 0) {
            throw new BaseException("合同已提交！");
        }
        //提交时设置审核状态为待审核
        contractInfo.setApproveStatus(0);

        this.updateById(contractInfo);
    }

    @Override
    public void approve(String id, String approveOpinion) {
        changeApproveStatus(id, 1, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 2, approveOpinion);
    }


    /**
     * 更新审核状态
     *
     * @param id
     * @param approveStatus
     * @param approveOpinion
     */
    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        ContractInfo contractInfo = this.getById(id);
        contractInfo.setDetailList(contractDetailService.getList(id));
        if (contractInfo.getEnabled() == 0) {
            throw new BaseException("合同信息不存在！");
        }

        // 校验状态：已审核的不能重复审核
        if (approveStatus != 0 && contractInfo.getApproveStatus() != null && contractInfo.getApproveStatus() != 0) {
            throw new BaseException("该合同信息已审核，无法重复操作！");
        }

        contractInfo.setApproveStatus(approveStatus);


        if (approveStatus == 1 || approveStatus == 2) {
            contractInfo.setApprover(SecurityHelper.obtainUser().getNickName());
            contractInfo.setApproveTime(new Date());
            contractInfo.setApproveOpinion(approveOpinion);

            if(approveStatus == 1){
                //审核通过后，将合同状态改为执行中
                contractInfo.setState(1);
            }
        } else {
            //清除审核信息
            contractInfo.setApprover("");
            contractInfo.setApproveOpinion("");
        }

        baseMapper.updateById(contractInfo);
    }

    @Override
    public ContractInfo getDetail(String contractId) {
        ContractInfo contractInfo = baseMapper.selectById(contractId);
        contractInfo.setDetailList(contractDetailService.getList(contractId));
        return contractInfo;
    }

    @Override
    public Page<ContractInfo> pages(ContractCondition condition) {
        Page<ContractInfo> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<ContractInfo> lists(ContractCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public List<ContractInfo> listsV2(ContractCondition condition) {
        BaseUserDetails user = SecurityHelper.getUser();
        if (user.getOrganType() != null && user.getOrganType() == 2) {
            //库点查询上级单位及自身的合同
            List<String> dataHierarchyId = Lists.newArrayList(user.getPid(), user.getOrganId());
            condition.setDataHierarchyId(dataHierarchyId);
            return baseMapper.selectListV2(condition);
        } else {
            return baseMapper.selectListV1(condition);
        }
    }

    @Override
    public List<ContractInfo> listsV3(ContractCondition condition) {
        List<ContractInfo> contractInfoList = baseMapper.selectListV1(condition);
        contractInfoList.forEach(contractInfo -> {
            ContractSettlement settlement = contractSettlementService.get(contractInfo.getId());
            if(settlement != null){
                contractInfo.setSettlementStatus(settlement.getSettlementStatus());
            }else{
                contractInfo.setSettlementStatus(1);
            }
        });
        return contractInfoList;
    }

    @Override
    public Page<ContractInfo> getUncompletedPageList(ContractCondition condition) {
        Page<ContractInfo> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);

        //批量查询明细
        List<ContractInfo> records = page.getRecords();
        if (!records.isEmpty()) {
            List<String> contractIds = records.stream()
                    .map(ContractInfo::getId)
                    .collect(Collectors.toList());

            List<ContractDetail> allDetails = contractDetailService.getListByContractIds(contractIds);
            Map<String, List<ContractDetail>> detailMap = allDetails.stream()
                    .collect(Collectors.groupingBy(ContractDetail::getContractId));

            records.forEach(contract ->
                    contract.setDetailList(detailMap.getOrDefault(contract.getId(), Lists.newArrayList()))
            );
        }

        return page;
    }

    @Override
    public Map<String, BigDecimal> getActiveContractTargetCompletion(String bidId) {

        //根据标的查询现有的有效合同数据
        LambdaQueryWrapper<ContractInfo> infoWrapper = new LambdaQueryWrapper<ContractInfo>()
                .in(ContractInfo::getState, 1)
                .eq(ContractInfo::getBidId, bidId)
                .eq(ContractInfo::getEnabled, 1);
        List<ContractInfo> infoList = this.list(infoWrapper);

        if (!infoList.isEmpty()) {
            //查询合同的详情数据
            LambdaQueryWrapper<ContractDetail> detailWrapper = new LambdaQueryWrapper<ContractDetail>()
                    .in(ContractDetail::getContractId, infoList);
            List<ContractDetail> detailList = contractDetailService.list(detailWrapper);

            if (!detailList.isEmpty()) {
                //根据品类分组，并统计每个品类的数量
                return detailList.stream()
                        .collect(Collectors.groupingBy(ContractDetail::getClassificationId,
                                Collectors.reducing(BigDecimal.ZERO, ContractDetail::getContractQty, BigDecimal::add)));
            }
        }

        return null;
    }


    public Map<String, String> getStoreHouseList(String contractId) {
        //先查询合同信息
        ContractInfo contractInfo = this.getById(contractId);
        if (contractInfo == null) {
            throw new BaseException("合同信息不存在!");
        }

        //再查询对应的招标信息
        LambdaQueryWrapper<BidInfo> bidWrapper = new LambdaQueryWrapper<BidInfo>()
                .eq(BidInfo::getId, contractInfo.getBidId());
        BidInfo bidInfo = bidInfoService.getOne(bidWrapper);
        if (bidInfo == null) {
            throw new BaseException("招标信息不存在!");
        }
        //然后查询关联的计划
        //如果是父计划，则查询对应的子计划
        LambdaQueryWrapper<GrainProcurementPlan> pWrapper = new LambdaQueryWrapper<GrainProcurementPlan>()
                .eq(GrainProcurementPlan::getId, bidInfo.getPlanId());
        GrainProcurementPlan plan = planService.getOne(pWrapper);

        if (plan == null) {
            throw new BaseException("招标信息对应筹措计划不存在!");
        }

        if (plan.getParentFlag() == 1) {
            LambdaQueryWrapper<GrainProcurementPlan> cWrapper = new LambdaQueryWrapper<GrainProcurementPlan>()
                    .eq(GrainProcurementPlan::getPid, plan.getId());
            List<GrainProcurementPlan> childPlanList = planService.list(cWrapper);

            return childPlanList.stream()
                    .filter(childPlan -> childPlan.getStoreId() != null && childPlan.getStoreName() != null)
                    .collect(Collectors.toMap(GrainProcurementPlan::getStoreId, GrainProcurementPlan::getStoreName));
        } else {
            return Collections.singletonMap(plan.getStoreId(), plan.getStoreName());
        }
    }


    @Override
    public BigDecimal findPrice(int type, String orderId, String catalogId, String Grade) {
        if (type == 1) {
            //生产订单
            ProductionOrder order = productionOrderService.getById(orderId);
            if (order == null) {
                return BigDecimal.ZERO;
            }
            ContractDetail contractDetail = contractDetailService.selectByCatalogIdAndGrade(order.getContractId(), catalogId, Grade);
            if (contractDetail == null) {
                return BigDecimal.ZERO;
            }
            return contractDetail.getPrice();
        } else if (type == 2) {
            //销售订单
            SaleOrder order = saleOrderService.getById(orderId);
            if (order == null) {
                return BigDecimal.ZERO;
            }
            ContractDetail contractDetail = contractDetailService.selectByCatalogIdAndGrade(order.getContractId(), catalogId, Grade);
            if (contractDetail == null) {
                return BigDecimal.ZERO;
            }
            return contractDetail.getPrice();
        } else {
            return BigDecimal.ZERO;
        }
    }
}
