package com.hxdi.nmjl.condition.store;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "评估查询条件")
@Getter
@Setter
public class StoreAssessmentCondition extends QueryCondition {

    @ApiModelProperty(value = "申请编号")
    private String applyNo;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "评价体系类型：1-政策性；2-军民融合")
    private Integer applyType;

    @ApiModelProperty(value = "评价等级：1-优秀；2-良好；3-合格；4-不合格")
    private String grade;

    @ApiModelProperty(value = "审核状态：0-未审核；1-审核通过；2-审核驳回")
    private Integer approveStatus;

}
