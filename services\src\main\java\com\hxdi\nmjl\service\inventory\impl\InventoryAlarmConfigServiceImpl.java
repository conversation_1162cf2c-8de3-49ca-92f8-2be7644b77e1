package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.domain.common.WarningInfo;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inventory.InventoryAlarmConfig;
import com.hxdi.nmjl.enums.MsgTemplateCode;
import com.hxdi.nmjl.event.MsgPayload;
import com.hxdi.nmjl.event.MsgSendEvent;
import com.hxdi.nmjl.mapper.inventory.InventoryAlarmConfigMapper;
import com.hxdi.nmjl.service.common.WarningInfoService;
import com.hxdi.nmjl.service.inventory.InventoryAlarmConfigService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryAlarmConfigServiceImpl extends BaseServiceImpl<InventoryAlarmConfigMapper, InventoryAlarmConfig> implements InventoryAlarmConfigService {

    @Resource
    private InventoryService inventoryService;

    @Resource
    private WarningInfoService warningInfoService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    public List<InventoryAlarmConfig> listV1(InventoryAlarmConfig condition) {
        return baseMapper.listV1(condition);
    }

    @Override
    public Page<InventoryAlarmConfig> pageV1(InventoryAlarmConfig condition, Page<InventoryAlarmConfig> page) {
        return baseMapper.pageV1(condition, page);
    }

    @Override
    public void generateWarningMsg() {
        List<InventoryAlarmConfig> alarmConfigList = this.list();
        if (alarmConfigList.isEmpty()) {
            return;
        }

        // 按库点ID和品种ID分组配置
        Map<String, Map<String, InventoryAlarmConfig>> alarmConfigMap = alarmConfigList.stream()
                .collect(Collectors.groupingBy(InventoryAlarmConfig::getStoreId,
                        Collectors.toMap(InventoryAlarmConfig::getClassificationId,
                                item -> item,
                                (existing, replacement) -> replacement)));
        if (alarmConfigMap.isEmpty()) {
            return;
        }

        // 获取所有需要检查的库点ID
        List<String> storeIdList = new ArrayList<>(alarmConfigMap.keySet());

        // 处理每个库点的预警
        for (String storeId : storeIdList) {
            processStoreWarnings(storeId, alarmConfigMap.get(storeId));
        }
    }

    private void processStoreWarnings(String storeId, Map<String, InventoryAlarmConfig> configMap) {
        // 查询库点下的按品类聚合的库存数据
        InventoryCondition condition = new InventoryCondition();
        if (storeId.isEmpty()) {
            return;
        }
        condition.setStoreId(storeId);
        List<Inventory> inventoryList = inventoryService.getListWithGroupByClassification(condition);

        if (inventoryList.isEmpty()) {
            return;
        }

        List<MsgPayload> payloadList = Lists.newArrayList();
        List<WarningInfo> warningInfoList = new ArrayList<>();

        // 检查每个库存项
        for (Inventory inventory : inventoryList) {
            InventoryAlarmConfig config = configMap.get(inventory.getClassificationId());
            if (config == null || inventory.getClassificationId() == null) {
                continue;
            }

            checkInventoryLimits(inventory, config, warningInfoList, payloadList);
        }

        // 批量保存预警信息
        if (!warningInfoList.isEmpty()) {
            warningInfoService.saveBatch(warningInfoList);

            //保存预警信息后，将预警信息对应的id设置到对应的消息中
            warningInfoList.forEach(warningInfo -> {
                MsgPayload payload = payloadList.get(warningInfoList.indexOf(warningInfo));
                if (payload != null) {
                    payload.setRefId(warningInfo.getId());
                }
            });
        }

        // 发布消息事件
        if (!payloadList.isEmpty()) {
            eventPublisher.publishEvent(new MsgSendEvent(this, payloadList));
        }
    }

    private void checkInventoryLimits(Inventory inventory, InventoryAlarmConfig config,
                                      List<WarningInfo> warningInfoList, List<MsgPayload> payloadList) {
        // 检查上限
        if (config.getMaxLimit() != null && inventory.getInventoryQty().compareTo(config.getMaxLimit()) > 0) {
            createWarning(inventory, config, warningInfoList, payloadList, true);
        }
        // 检查下限
        else if (config.getMinLimit() != null && inventory.getInventoryQty().compareTo(config.getMinLimit()) < 0) {
            createWarning(inventory, config, warningInfoList, payloadList, false);
        }
    }

    private void createWarning(Inventory inventory, InventoryAlarmConfig config,
                               List<WarningInfo> warningInfoList, List<MsgPayload> payloadList, boolean isOverLimit) {
        // 创建预警信息
        WarningInfo warningInfo = new WarningInfo();
        warningInfo.setWarningType("1");
        warningInfo.setTriMode(1);
        warningInfo.setWarningTime(new Date());
        warningInfo.setStoreId(config.getStoreId());
        warningInfo.setStoreName(config.getStoreName());

        String limitType = isOverLimit ? "超过预警设定的上限" : "低于预警设定的下限";
        String limitValue = isOverLimit ? config.getMaxLimit().toString() : config.getMinLimit().toString();

        warningInfo.setMsg(String.format("品类 '%s' 的库存数量已%s '%s' KG，当前库存数量为 '%s' KG",
                config.getClassificationName(), limitType, limitValue, inventory.getInventoryQty()));
        //此处暂不添加关联记录ID
        //warningInfo.setRefId(inventory.getClassificationId());
        warningInfo.setDisposeState(0);
        //保持预警信息与库存数据权限一致，便于后面进行查询
        warningInfo.setCreateId(inventory.getCreateId());
        warningInfo.setTenantId(inventory.getTenantId());
        warningInfo.setDataHierarchyId(warningInfo.getStoreId());
        warningInfoList.add(warningInfo);

        // 创建消息
        MsgPayload payload = new MsgPayload();
        payload.setMsgCode(MsgTemplateCode.INVENTORY_ALARM.getCode());
        payload.setReceiver(config.getReceiver());

        Map<String, String> jsonParams = Maps.newHashMap();
        jsonParams.put("storeName", config.getStoreName());
        jsonParams.put("stName", inventory.getStName());
        jsonParams.put("classificationName", config.getClassificationName());
        jsonParams.put("limitType", limitType);
        jsonParams.put("limitNum", limitValue);
        jsonParams.put("inventoryQty", inventory.getInventoryQty().toString());
        payload.setJsonParams(jsonParams);

        payloadList.add(payload);
    }
}
