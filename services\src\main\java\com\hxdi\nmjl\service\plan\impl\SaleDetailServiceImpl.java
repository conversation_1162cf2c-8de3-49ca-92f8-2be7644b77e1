package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.plan.SaleDetail;
import com.hxdi.nmjl.mapper.plan.SaleDetailMapper;
import com.hxdi.nmjl.service.plan.SaleDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SaleDetailServiceImpl extends BaseServiceImpl<SaleDetailMapper, SaleDetail> implements SaleDetailService {


    @Override
    public List<SaleDetail> getList(String orderId) {
        return baseMapper.selectList(Wrappers.<SaleDetail>lambdaQuery().eq(SaleDetail::getOrderId, orderId));
    }

    @Override
    public void removeV1(String orderId) {
        baseMapper.delete(Wrappers.<SaleDetail>lambdaQuery().eq(SaleDetail::getOrderId, orderId));
    }
}
