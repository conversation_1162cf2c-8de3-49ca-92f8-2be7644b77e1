package com.hxdi.nmjl.service.mobilization.Impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.mobilization.MobilizedEnterpriseCondition;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCondition;
import com.hxdi.nmjl.domain.mobilization.MobilizedEnterprise;
import com.hxdi.nmjl.domain.mobilization.MobilizedProduct;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.mobilization.MobilizedEnterpriseMapper;
import com.hxdi.nmjl.service.mobilization.MobilizedEnterpriseService;
import com.hxdi.nmjl.service.mobilization.MobilizedProductService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MobilizedEnterpriseServiceImpl extends BaseServiceImpl<MobilizedEnterpriseMapper, MobilizedEnterprise> implements MobilizedEnterpriseService {

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private MobilizedProductService mobilizedProductService;

    @Override
    public Page<MobilizedEnterprise> pages(MobilizedEnterpriseCondition condition) {
        Page<MobilizedEnterprise> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<MobilizedEnterprise> lists(MobilizedEnterpriseCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void update(MobilizedEnterprise mobilizedEnterprise) {
        this.updateById(mobilizedEnterprise);
    }

    @Override
    public void create(MobilizedEnterprise mobilizedEnterprise) {
        // 生成企业编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("MOBILIZED_ENTERPRISE_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        mobilizedEnterprise.setEnterpriseCode((String) businessCode.getValue());
        mobilizedEnterprise.setApproveStatus(0);
        this.save(mobilizedEnterprise);
    }

    @Override
    public void remove(String enterpriseId) {
        MobilizedProductCondition mobilizedProductCondition = new MobilizedProductCondition();
        mobilizedProductCondition.setEnterpriseId(enterpriseId);
        List<MobilizedProduct> lists = mobilizedProductService.lists(mobilizedProductCondition);

        if (!lists.isEmpty()) {
            throw new BaseException("请先删除该企业下的动员商品信息！");
        }
        this.removeById(enterpriseId);
    }

    @Override
    public void changeState(String enterpriseId, Integer state) {
        MobilizedEnterprise mobilizedEnterprise = baseMapper.selectById(enterpriseId);
        //更新状态并保存
        mobilizedEnterprise.setState(state);
        this.updateById(mobilizedEnterprise);
    }


    @Override
    public void approve(String id, String approveOpinion) {
        MobilizedEnterprise enterprise = getById(id);
        if (enterprise.getApproveStatus() == 0) {
            BizExp.pop("请先提交审核");
        }

        if (enterprise.getApproveStatus() == 1) {
            enterprise.setApproveStatus(2);
            enterprise.setApproveOpinion(approveOpinion);
            enterprise.setApproveTime(new Date());
            enterprise.setApprover(SecurityHelper.obtainUser().getNickName());
            baseMapper.updateById(enterprise);
        }
    }

    @Override
    public void reject(String id, String approveOpinion) {
        MobilizedEnterprise enterprise = getById(id);
        if (enterprise.getApproveStatus() == 0) {
            BizExp.pop("请先提交审核");
        }

        if (enterprise.getApproveStatus() != 1) {
            BizExp.pop("数据状态异常");
        }

        // 驳回后，将状态设置为待提交状态
        enterprise.setApproveStatus(3);
        enterprise.setApproveOpinion(approveOpinion);
        enterprise.setApproveTime(new Date());
        enterprise.setApprover(SecurityHelper.obtainUser().getNickName());
        baseMapper.updateById(enterprise);
    }

    @Override
    public void submit(String id) {
        MobilizedEnterprise enterprise = getById(id);
        if (enterprise.getApproveStatus() == 0) {
            enterprise.setApproveStatus(1);
            baseMapper.updateById(enterprise);
        }
    }
}
