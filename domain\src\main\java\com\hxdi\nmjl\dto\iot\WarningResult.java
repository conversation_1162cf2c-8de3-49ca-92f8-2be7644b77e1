package com.hxdi.nmjl.dto.iot;

import lombok.Getter;

/**
 * 预警结果
 */
@Getter
public class WarningResult {
    private final boolean isWarning;
    private final boolean isBelowLimit;
    private final String limitValue;
    private final String currentValue;

    public WarningResult(boolean isWarning, boolean isBelowLimit, String limitValue, String currentValue) {
        this.isWarning = isWarning;
        this.isBelowLimit = isBelowLimit;
        this.limitValue = limitValue;
        this.currentValue = currentValue;
    }

    public String getLimitType() {
        return isBelowLimit ? "低于预警设定的下限值" : "超过预警设定的上限值";
    }
}
