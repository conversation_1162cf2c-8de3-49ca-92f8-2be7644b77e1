package com.hxdi.nmjl.vo.base;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.hxdi.nmjl.vo.inventory.InventoryCoreDataVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @program: nmjl-service
 * @description: 扫码基础信息返回VO
 * @author: 王贝强
 * @create: 2025-08-21 18:48
 */
@Getter
@Setter
public class BaseQRDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "库存信息")
    private InventoryCoreDataVO inventory;
}
