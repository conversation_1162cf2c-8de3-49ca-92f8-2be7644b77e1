<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.ClassificationMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.Classification">
        <!--@mbg.generated-->
        <!--@Table C_CLASSIFICATION-->
        <id column="ID" property="id" />
        <result column="CLASSIFICATION_CODE" property="classificationCode" />
        <result column="CLASSIFICATION_NAME" property="classificationName" />
        <result column="PARENT_ID" property="parentId" />
        <result column="LEVEL" property="level" />
        <result column="SEQ" property="seq" />
        <result column="LEAF_IS" property="leafIs" />
        <result column="CATEGORY" property="category" />
        <result column="ENABLED" property="enabled" />
        <result column="PRE_IMG" property="preImg" />
        <result column="ATTACHMENT" property="attachment" />
        <result column="MAX_STORAGE_TIME" property="maxStorageTime" />
        <result column="REMARK" property="remark" />
        <result column="CLASSIFICATION_PATH" property="classificationPath" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="TENANT_ID" property="tenantId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, CLASSIFICATION_CODE, CLASSIFICATION_NAME, PARENT_ID, "LEVEL", SEQ, LEAF_IS, CATEGORY,
        ENABLED, PRE_IMG, ATTACHMENT, MAX_STORAGE_TIME, REMARK, CLASSIFICATION_PATH, DATA_HIERARCHY_ID,
        CREATE_TIME, UPDATE_TIME, TENANT_ID
    </sql>

    <select id="selectMaxSeqNumber" resultType="java.lang.Integer">
        SELECT MAX(seq) FROM c_classification WHERE parent_id= #{parentId}
    </select>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from c_classification
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                AND classification_name  LIKE CONCAT('%', #{condition.keywords}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.parentId)">
                AND parent_id = #{condition.parentId}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from c_classification
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                AND classification_name  LIKE CONCAT('%', #{condition.keywords}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.parentId)">
                AND parent_id = #{condition.parentId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.leafIs)">
                AND leaf_is = #{condition.leafIs}
            </if>
            order by CREATE_TIME DESC
        </where>
    </select>

    <select id="selectAll" resultType="com.hxdi.nmjl.domain.base.Classification">
        select * from c_classification
        order by CREATE_TIME DESC
    </select>

    <delete id="softDeleteById">
        UPDATE c_classification SET enabled = 0 WHERE id = #{id}
    </delete>
</mapper>
