package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.BrandInfo;
import com.hxdi.nmjl.condition.plan.BrandInfoCondition;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface BrandInfoService extends IBaseService<BrandInfo> {

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return
     */
    List<BrandInfo> lists(BrandInfoCondition condition);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return
     */
    Page<BrandInfo> pages(BrandInfoCondition condition);

    /**
     * 详情查询
     *
     * @param id
     * @return
     */
    BrandInfo getDetail(String id);

    /**
     * 新增
     *
     * @param brandInfo
     */
    void create(BrandInfo brandInfo);

    /**
     * 修改
     *
     * @param brandInfo
     */
    void update(BrandInfo brandInfo);

    void remove(String id);
}
