package com.hxdi.nmjl.domain.common;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@ApiModel(description = "告警信息")
@TableName(value = "B_WARNING_INFO")
public class WarningInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 告警类型
     */
    @TableField(value = "WARNING_TYPE")
    @ApiModelProperty(value = "告警类型：1-库存预警，2-设备预警，3-其他预警")
    private String warningType;

    /**
     * 告警时间
     */
    @TableField(value = "WARNING_TIME")
    @ApiModelProperty(value = "告警时间")
    private Date warningTime;

    /**
     * 触发方式: 1-自动，2-手动
     */
    @TableField(value = "TRI_MODE")
    @ApiModelProperty(value = "触发方式: 1-自动，2-手动")
    private Integer triMode;

    /**
     * 消息
     */
    @TableField(value = "MSG")
    @ApiModelProperty(value = "消息")
    private String msg;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value = "仓房ID")
    private String stId;

    /**
     * 仓房名称
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value = "仓房名称")
    private String stName;

    /**
     * 货位ID
     */
    @TableField(value = "LOC_ID")
    @ApiModelProperty(value = "货位ID")
    private String locId;

    /**
     * 货位名称
     */
    @TableField(value = "LOC_NAME")
    @ApiModelProperty(value = "货位名称")
    private String locName;

    /**
     * 处理用户ID
     */
    @TableField(value = "USER_ID")
    @ApiModelProperty(value = "处理用户ID")
    private String userId;

    /**
     * 处理人
     */
    @TableField(value = "USER_NAME")
    @ApiModelProperty(value = "处理人")
    private String userName;

    /**
     * 处理时间
     */
    @TableField(value = "DISPOSE_TIME")
    @ApiModelProperty(value = "处理时间")
    private Date disposeTime;

    /**
     * 处理状态:0-未处理，1-已处理
     */
    @TableField(value = "DISPOSE_STATE")
    @ApiModelProperty(value = "处理状态:0-未处理，1-已处理")
    private Integer disposeState;

    /**
     * 处理描述
     */
    @TableField(value = "DISPOSE_DESC")
    @ApiModelProperty(value = "处理描述")
    private String disposeDesc;

    /**
     * 关联记录ID
     */
    @TableField(value = "REF_ID")
    @ApiModelProperty(value = "关联记录ID")
    private String refId;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
