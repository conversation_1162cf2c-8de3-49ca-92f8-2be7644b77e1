package com.hxdi.nmjl.controller.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inout.delivery.VehicleInfo;
import com.hxdi.nmjl.service.inout.delivery.VehicleInfoService;
import com.hxdi.nmjl.condition.inout.VehicleInfoCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 车辆管理控制层
 *
 * <AUTHOR>
 * @since 2025-04-22 14:52:10
 */
@Api(tags = "车辆管理")
@RestController
@RequestMapping("/vehicleInfo")
public class VehicleInfoController extends BaseController<VehicleInfoService, VehicleInfo> {

    @ApiOperation(value = "保存或更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody VehicleInfo vehicleInfo) {
        bizService.saveOrUpdateV1(vehicleInfo);
        return ResultBody.ok();
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<VehicleInfo> getDetail(@RequestParam @NotNull(message = "id不能为空") String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResultBody delete(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<VehicleInfo>> pages(VehicleInfoCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<VehicleInfo>> lists(VehicleInfoCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }
}
