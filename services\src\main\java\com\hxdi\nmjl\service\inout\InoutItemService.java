package com.hxdi.nmjl.service.inout;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.InoutItem;

import java.util.List;


public interface InoutItemService extends IBaseService<InoutItem> {

    /**
     * 查询统计信息
     *
     * @param mainId
     * @return
     */
    InoutItem getStatInfo(String mainId);

    /**
     * 根据主表ID查询
     *
     * @param mainId
     * @return
     */
    List<InoutItem> getListByMainId(String mainId);

    List<InoutItem> getListByMainIds(List<String> detailIds);
}
