package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataSyncMessage;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.JsonConverter;
import com.hxdi.common.core.utils.Strcat;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.base.OrganizationCondition;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.event.DataChangeEvent;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.handler.OrganizationSyncPayload;
import com.hxdi.nmjl.mapper.base.OrganizationMapper;
import com.hxdi.nmjl.service.base.OrganizationService;
import com.hxdi.nmjl.utils.RedisKeys;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <单位机构管理实现>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/12 14:01
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class OrganizationServiceImpl extends BaseServiceImpl<OrganizationMapper, Organization> implements OrganizationService {

    public static final String ROOT_PARENT_ID = "0";

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    @Cacheable(value = RedisKeys.Prefix.ORGANIZATION, key = "#uniqueKey", unless = "#result == null")
    public Organization getByUniqueKey(String uniqueKey) {
        Organization organization = baseMapper.selectById(uniqueKey);
        if (organization == null) {
            organization = baseMapper.selectOne(Wrappers.<Organization>lambdaQuery().eq(Organization::getOrgCode, uniqueKey));
        }
        return organization;
    }

    @Override
    public Page<Organization> pages(OrganizationCondition condition) {
        Page<Organization> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        page.getRecords().forEach(item -> item.setParentOrgName(CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), item.getId(), Organization::getOrgName)));
        return page;
    }

    @Override
    public List<Organization> lists(OrganizationCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public Map<Integer, Integer> getOrgNum(OrganizationCondition condition) {
        if (CommonUtils.isEmpty(condition.getAreaCode())) {
            return new HashMap<>();
        }
        List<Organization> organizationList = baseMapper.selectListV1(condition);
        //根据组织的orgType进行分类并返回
        return organizationList.stream()
                .filter(org -> org.getFunctional() != null)
                .collect(Collectors.groupingBy(Organization::getFunctional, Collectors.summingInt(value -> 1)));
    }

    @Override
    @CachePut(value = RedisKeys.Prefix.ORGANIZATION, key = "#result.id", unless = "#result == null")
    public Organization create(Organization org) {
        return createInternal(org, false);
    }


    private Organization createInternal(Organization org, Boolean isMessage) {
        if (CommonUtils.isNotEmpty(org.getCreditCode())) {
            org.setOrgCode(org.getCreditCode());
        } else {
            org.setOrgCode(IdWorker.getIdStr(new Object()));
        }

        verifyOrganization(org, 0);

        if (!isMessage) {
            //生成组织编号
            BusinessCodeParams params = new BusinessCodeParams();
            params.setCode(CommonConstants.SEQ_ORG_ID);
            params.setDt(DataType.STRING);
            BusinessCode idSeq = systemNumberRuleClient.getNumber(params).getData();
            org.setId(idSeq.getValue().toString());
        }

        String paths = org.getId();
        if (CommonUtils.notEquals(org.getPid(), ROOT_PARENT_ID)) {
            Organization parentOrg = getByUniqueKey(org.getPid());
            paths = Strcat.joinWithDelimiter(".", parentOrg.getPaths(), org.getId()).toString();
        } else {
            org.setPid(ROOT_PARENT_ID);
        }

        org.setPaths(paths);
        Integer seq = baseMapper.selectMaxSeqNumber(org.getPid());
        org.setSorts(seq == null ? 0 : seq + 1);

        org.setDataHierarchyId(org.getId());
        baseMapper.insert(org);

        Organization newOrg = getByUniqueKey(org.getId());

        if (!isMessage) {
            // 发布数据变更（新增）事件
            publishEventAfterCommit(newOrg.getId(), "CREATE",
                    () -> {
                        OrganizationSyncPayload syncPayload = OrganizationSyncPayload.fromOrganization(newOrg);
                        return JsonConverter.convertToMap(syncPayload);
                    },
                    newOrg.getUpdateTime()
            );
        }

        return newOrg;
    }

    @Override
    @CachePut(value = RedisKeys.Prefix.ORGANIZATION, key = "#org.id", unless = "#result == null")
    public Organization update(Organization org) {
        return updateInternal(org, false);
    }

    private Organization updateInternal(Organization org, Boolean isMessage) {
        verifyOrganization(org, 1);

        int updatedRows = baseMapper.updateById(org);

        Organization organization = getByUniqueKey(org.getId());

        if (updatedRows > 0 && !isMessage) {
            // 发布数据变更（更新）事件
            //提交后获取完整更新的实体，以确保数据一致性
            publishEventAfterCommit(organization.getId(), "UPDATE",
                    () -> {
                        OrganizationSyncPayload syncPayload = OrganizationSyncPayload.fromOrganization(organization);
                        return JsonConverter.convertToMap(syncPayload);
                    },
                    organization.getUpdateTime()
            );
        }

        return organization;
    }

    @Override
    @CacheEvict(value = RedisKeys.Prefix.ORGANIZATION, key = "#orgId", condition = "#state == 7 OR #state == 0")
    @CachePut(value = RedisKeys.Prefix.ORGANIZATION, key = "#orgId", condition = "#state == 1")
    public Organization changeState(String orgId, Integer state) {
        return changeStateInternal(orgId, state, false);
    }

    private Organization changeStateInternal(String orgId, Integer state, Boolean isMessage) {
        Organization organization_old = getByUniqueKey(orgId);
        if (StrPool.State.DELETE == organization_old.getEnabled().intValue()) {
            return null;
        }

        Organization updatingOrganization = new Organization();
        updatingOrganization.setId(orgId);
        updatingOrganization.setEnabled(state);
        int updatedRows = baseMapper.updateById(updatingOrganization);

        if (updatedRows > 0 && !isMessage) {
            Organization organization_new = new Organization();
            organization_new.setId(orgId);
            organization_new.setEnabled(state);
            // 发布数据变更（状态变更）事件
            //状态变更时不需要携带实体数据，仅携带id和操作类型及状态字段
            publishEventAfterCommit(orgId, "CHANGE",
                    () -> {
                        OrganizationSyncPayload syncPayload = OrganizationSyncPayload.fromOrganization(organization_new);
                        return JsonConverter.convertToMap(syncPayload);
                    },
                    new Date()
            );
        }

        if (Objects.equals(StrPool.State.ENABLE, state)) {
            return getByUniqueKey(orgId);
        } else {
            return null;
        }
    }


    @Override
    @CacheEvict(value = RedisKeys.Prefix.ORGANIZATION, key = "#message.entityId", condition = "#result == null")
    @CachePut(value = RedisKeys.Prefix.ORGANIZATION, key = "#message.entityId", condition = "#message.entityId != null and  #result != null")
    public Organization handleMessage(DataSyncMessage message) {
        String operation = message.getOperation();
        String entityId = message.getEntityId();
        Map<String, Object> data = message.getData();
        Date remoteVersion = message.getVersion();

        Organization localOrg = getByUniqueKey(entityId);

        // 校验消息体（不处理缺少ID字段的消息）
        if (CommonUtils.isEmpty(entityId) || data.get("id") == null) {
            return null;
        }

        // 消息体转换
        OrganizationSyncPayload SyncPayload = JsonConverter.convertFromMap(data, OrganizationSyncPayload.class); // Map 转实体

        //返回值，用于处理缓存刷新
        Organization resultOrg = null;

        switch (operation) {
            case "CREATE":
                if (localOrg == null) {
                    log.info("正在处理Organization的创建操作: {}", entityId);
                    Organization newOrg = SyncPayload.toOrganization();
                    resultOrg = this.createInternal(newOrg, true);
                } else {
                    log.warn("已存在相同ID的Organization: {},跳过创建操作.", entityId);
                }
                break;

            case "UPDATE":
                if (localOrg != null) {
                    // 版本检查，防止旧数据覆盖新数据(两个数据更新时间之差应当大于2秒)
                    if (remoteVersion != null && (Math.abs(localOrg.getUpdateTime().getTime() - remoteVersion.getTime()) > 2000)) {
                        log.info("正在处理Organization的更新操作: {}, RemoteVersion={}, LocalVersion={}",
                                entityId, remoteVersion, localOrg.getUpdateTime());
                        SyncPayload.mergeIntoOrganization(localOrg);
                        resultOrg = this.updateInternal(localOrg, true);
                    } else {
                        log.warn("由于当前数据版本较高或相同,跳过对Organization的更新操作: {}  RemoteVersion={}, LocalVersion={}",
                                entityId, remoteVersion, localOrg.getUpdateTime());
                    }
                } else {
                    log.warn("收到一个不存在的Organization的更新，将会执行创建操作: {}", entityId);
                    // 如果当前没有则创建一个新的
                    Organization newOrg = SyncPayload.toOrganization();
                    resultOrg = createInternal(newOrg, true);
                }
                break;

            case "CHANGE":
                if (localOrg != null) {
                    log.info("正在处理Organization的状态变更操作: {}", entityId);
                    resultOrg = changeStateInternal(entityId, SyncPayload.getStatus(), true);
                } else {
                    log.warn("需要变更状态的Organization不存在，跳过处理: {}", entityId);
                }
                break;

            default:
                log.warn("未知的操作类型 '{}' for EntityId: {}", operation, entityId);
        }

        return resultOrg;
    }

    @Override
    public List<Organization> tree(String orgId) {
        // 1. 获取所有组织数据（无权限限制）
        List<Organization> allOrganizations = getAllOrgWithoutPermission();

        // 2. 根据orgId筛选需要构建树的组织数据
        List<Organization> filteredOrganizations = filterOrganizationsByOrgId(allOrganizations, orgId);

        // 3. 构建树形结构
        return buildOrganizationTree(filteredOrganizations, orgId);
    }

    /**
     * 获取所有组织数据（无权限限制）
     */
    private List<Organization> getAllOrgWithoutPermission() {
        // 直接从数据库查询所有启用的组织，不使用权限控制的lists方法
        OrganizationCondition allCondition = new OrganizationCondition();
        List<Organization> allOrg = baseMapper.selectListV2(allCondition);

        // 补充缺失的父组织名称
        allOrg.forEach(org -> {
            if (CommonUtils.isNotEmpty(org.getPid()) && !ROOT_PARENT_ID.equals(org.getPid())) {
                String parentOrgName = CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), org.getPid(), Organization::getOrgName);
                org.setParentOrgName(parentOrgName);
            }
        });

        return allOrg;
    }

    /**
     * 根据orgId筛选需要构建树的组织数据
     */
    private List<Organization> filterOrganizationsByOrgId(List<Organization> organizations, String orgId) {
        if (organizations.isEmpty()) {
            return organizations;
        }

        // 如果orgId为null，返回所有组织构建全体组织树
        if (CommonUtils.isEmpty(orgId)) {
            return organizations;
        }

        // 创建ID到组织的映射，便于查找
        Map<String, Organization> orgMap = organizations.stream()
                .collect(Collectors.toMap(Organization::getId, org -> org));

        // 查找指定的根组织
        Organization rootOrg = orgMap.get(orgId);
        if (rootOrg == null) {
            log.warn("未找到指定的组织ID: {}", orgId);
            return new ArrayList<>();
        }

        // 收集指定组织及其所有下级组织
        Set<String> targetOrgIds = new HashSet<>();
        collectOrgAndChildren(rootOrg, orgMap, targetOrgIds);

        // 筛选出目标组织
        return organizations.stream()
                .filter(org -> targetOrgIds.contains(org.getId()))
                .collect(Collectors.toList());
    }

    /**
     * 递归收集组织及其所有下级组织的ID
     */
    private void collectOrgAndChildren(Organization org, Map<String, Organization> orgMap, Set<String> targetOrgIds) {
        if (org == null || targetOrgIds.contains(org.getId())) {
            return;
        }

        targetOrgIds.add(org.getId());

        // 查找所有以当前组织为父级的子组织
        orgMap.values().stream()
                .filter(child -> org.getId().equals(child.getPid()))
                .forEach(child -> collectOrgAndChildren(child, orgMap, targetOrgIds));
    }

    /**
     * 构建组织树形结构
     */
    private List<Organization> buildOrganizationTree(List<Organization> organizations, String orgId) {
        if (organizations.isEmpty()) {
            return new ArrayList<>();
        }

        // 创建ID到组织的映射
        Map<String, Organization> orgMap = organizations.stream()
                .collect(Collectors.toMap(Organization::getId, org -> org));

        // 初始化所有组织的children列表
        organizations.forEach(org -> org.setChildren(new ArrayList<>()));

        // 构建父子关系
        List<Organization> rootNodes = new ArrayList<>();

        for (Organization org : organizations) {
            String parentId = org.getPid();

            // 如果指定了orgId，则以该组织为根节点
            if (CommonUtils.isNotEmpty(orgId) && orgId.equals(org.getId())) {
                rootNodes.add(org);
            } else if (CommonUtils.isEmpty(orgId) && (ROOT_PARENT_ID.equals(parentId) || CommonUtils.isEmpty(parentId))) {
                // 如果没有指定orgId，则以系统根节点为根
                rootNodes.add(org);
            } else {
                // 子节点，添加到父节点的children中
                Organization parent = orgMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(org);
                } else if (CommonUtils.isEmpty(orgId)) {
                    // 父节点不在筛选结果中且没有指定orgId时，作为根节点处理
                    rootNodes.add(org);
                }
            }
        }

        // 对所有节点的children按sorts排序
        sortOrganizationTree(rootNodes);

        return rootNodes;
    }

    /**
     * 递归排序组织树
     */
    private void sortOrganizationTree(List<Organization> organizations) {
        if (organizations == null || organizations.isEmpty()) {
            return;
        }

        // 按sorts字段排序，如果sorts为null则放到最后
        organizations.sort((o1, o2) -> {
            Integer sorts1 = o1.getSorts();
            Integer sorts2 = o2.getSorts();

            if (sorts1 == null && sorts2 == null) {
                return 0;
            }
            if (sorts1 == null) {
                return 1;
            }
            if (sorts2 == null) {
                return -1;
            }

            return sorts1.compareTo(sorts2);
        });

        // 递归排序子节点
        for (Organization org : organizations) {
            if (org.getChildren() != null && !org.getChildren().isEmpty()) {
                sortOrganizationTree(org.getChildren());
            }
        }
    }


    /**
     * 校验机构信息有效性
     *
     * @param org         单位实体
     * @param expectCount 预期存在的条目
     */
    private void verifyOrganization(Organization org, int expectCount) {
        long count = baseMapper.selectCount(Wrappers.<Organization>lambdaQuery()
                .eq(CommonUtils.isNotEmpty(org.getId()), Organization::getId, org.getId())
                .or()
                .eq(Organization::getOrgCode, org.getOrgCode())
                .or()
                .eq(CommonUtils.isNotEmpty(org.getCreditCode()), Organization::getCreditCode, org.getCreditCode()));

        if (count > expectCount) {
            throw new BaseException("该单位机构信息已存在，系统中已存在相同机构编号或统一社会信用代码！");
        }
    }


    /**
     * 在当前事务成功提交后发布DataChangeEvent
     * 使用提交后获取的数据和版本，确保一致性
     *
     * @param entityId     实体的ID
     * @param operation    操作类型 ("CREATE", "UPDATE", "CHANGE")
     * @param dataSupplier Supplier function,以map形式获取实体数据 (提交后执行)
     */
    private void publishEventAfterCommit(String entityId, String operation,
                                         Supplier<Map<String, Object>> dataSupplier, Date version) {

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {

                    Map<String, Object> data = dataSupplier.get();


                    if (("CREATE".equals(operation) || "UPDATE".equals(operation) || "CHANGE".equals(operation)) && (data == null || data.isEmpty())) {
                        log.warn("实体[organization],数据变更类型[{}],值为null或空,实体ID:{},事件停止发布", operation, entityId);
                        return;
                    }


                    DataChangeEvent event = new DataChangeEvent(
                            OrganizationServiceImpl.this, //事件源
                            "organization",
                            entityId,
                            operation,
                            data,
                            version
                    );

                    eventPublisher.publishEvent(event);

                } catch (Exception e) {
                    // 提交后事件创建/发布阶段出现错误
                    log.error("事件发布失败: 实体类型[{}],值:{},ID:{},错误消息:{}",
                            "organization", operation, entityId, e.getMessage(), e);
                }
            }
        });
    }

}
