<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.mobilization.MobilizeChefExpMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.mobilization.MobilizeChefExp">
        <id property="id" column="ID"/>
        <result property="chefId" column="CHEF_ID"/>

        <!-- 工作经历1 -->
        <result property="workUnit1" column="WORK_UNIT1"/>
        <result property="jobTitle1" column="JOB_TITLE1"/>
        <result property="jobDesc1" column="JOB_DESC1"/>
        <result property="startTime1" column="START_TIME1"/>
        <result property="endTime1" column="END_TIME1"/>

        <!-- 工作经历2 -->
        <result property="workUnit2" column="WORK_UNIT2"/>
        <result property="jobTitle2" column="JOB_TITLE2"/>
        <result property="jobDesc2" column="JOB_DESC2"/>
        <result property="startTime2" column="START_TIME2"/>
        <result property="endTime2" column="END_TIME2"/>

        <!-- 工作经历3 -->
        <result property="workUnit3" column="WORK_UNIT3"/>
        <result property="jobTitle3" column="JOB_TITLE3"/>
        <result property="jobDesc3" column="JOB_DESC3"/>
        <result property="startTime3" column="START_TIME3"/>
        <result property="endTime3" column="END_TIME3"/>
    </resultMap>

    <sql id="base_column_list">
        ID,
        CHEF_ID,
        WORK_UNIT1,
        JOB_TITLE1,
        JOB_DESC1,
        START_TIME1,
        END_TIME1,
        WORK_UNIT2,
        JOB_TITLE2,
        JOB_DESC2,
        START_TIME2,
        END_TIME2,
        WORK_UNIT3,
        JOB_TITLE3,
        JOB_DESC3,
        START_TIME3,
        END_TIME3
    </sql>

</mapper>