package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 添加剂管理查询条件
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Getter
@Setter
@ApiModel(description = "添加剂管理查询条件")
public class AdditiveConsumingCondition extends QueryCondition {

    @ApiModelProperty(value = "备案开始日期")
    private String recordStartTime;

    @ApiModelProperty(value = "备案结束日期")
    private String recordEndTime;

    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    @ApiModelProperty(value = "审核状态：0-未审核，1-待审核，2-已审核，3-已驳回")
    private Integer approveStatus;
}
