package com.hxdi.nmjl.condition.inventory;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 货位卡查询条件
 * @author: 王贝强
 * @create: 2025-04-18 10:45
 */
@Getter
@Setter
public class LocationCardCondition extends QueryCondition {

    @ApiModelProperty(value="军供站ID")
    private String storeId;

    @ApiModelProperty(value="仓库")
    private String storeName;

    @ApiModelProperty(value="仓房ID")
    private String stId;

    @ApiModelProperty(value="仓房")
    private String stName;

    @ApiModelProperty(value="货位ID")
    private String locId;

    @ApiModelProperty(value="货位名称")
    private String locName;

    @ApiModelProperty(value="更新时间区间开始")
    private Date updateTimeStart;

    @ApiModelProperty(value="更新时间区间结束")
    private Date updateTimeEnd;
}
