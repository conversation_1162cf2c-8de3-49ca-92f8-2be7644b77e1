package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import com.hxdi.nmjl.domain.inout.InoutTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 销售订单
 */
@Getter
@Setter
@ApiModel(description = "销售订单")
@TableName(value = "B_SALE_ORDER")
public class SaleOrder extends Entity<SaleOrder> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 订单号
     */
    @TableField(value = "ORDER_CODE")
    @ApiModelProperty(value = "订单号")
    private String orderCode;

    /**
     * 父订单
     */
    @TableField(value = "PID")
    @ApiModelProperty(value = "父订单")
    private String pid;

    /**
     * 订单来源：1-门店，2-电商平台，3-企业订单
     */
    @TableField(value = "ORIGIN")
    @ApiModelProperty(value = "订单来源：1-门店，2-电商平台，3-企业订单")
    private String origin;

    /**
     * 合同ID
     */
    @TableField(value = "CONTRACT_ID")
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 合同编号
     */
    @TableField(value = "CONTRACT_CODE")
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 订单日期
     */
    @TableField(value = "ORDER_TIME")
    @ApiModelProperty(value = "订单日期")
    private Date orderTime;

    /**
     * 单位ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "单位ID")
    private String orgId;

    /**
     * 单位名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "单位名称")
    private String orgName;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "军供站ID")
    private String storeId;

    /**
     * 军供站名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "军供站名称")
    private String storeName;

    /**
     * 客户ID
     */
    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    /**
     * 客户名称
     */
    @TableField(value = "CLIENT_NAME")
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    /**
     * 支付账期：天
     */
    @TableField(value = "PAYMENT_PERIOD")
    @ApiModelProperty(value = "支付账期：天")
    private Integer paymentPeriod;

    /**
     * 支付截止时间
     */
    @TableField(value = "PAYMENT_END_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "支付截止时间")
    private Date paymentEndTime;

    /**
     * 订单金额
     */
    @TableField(value = "TOTAL_AMOUNT")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal totalAmount;

    /**
     * 折扣率
     */
    @TableField(value = "DISCOUNT_RATE")
    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountRate;

    /**
     * 折扣金额
     */
    @TableField(value = "DISCOUNT_AMOUNT")
    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountAmount;

    /**
     * 配送方式:1-物流，2-自提
     */
    @TableField(value = "DELIVERY_METHOD")
    @ApiModelProperty(value = "配送方式:1-物流，2-自提")
    private Integer deliveryMethod;

    /**
     * 业务状态：1-待确认，2-已确认，3-运输中，4-待支付，5-已支付，6-已完成，7-已关闭，8-退货，9-换货
     */
    @TableField(value = "STATE")
    @ApiModelProperty(value = "业务状态：1-待确认，2-已确认，3-运输中，4-待支付，5-已支付，6-已完成，7-已关闭，8-退货，9-换货")
    private Integer state;

    /**
     * 结算状态：0-待结算，1-已结算
     */
    @TableField(value = "SETTLEMENT_STATUS")
    @ApiModelProperty(value = "结算状态：0-待结算，1-已结算")
    private Integer settlementStatus;

    /**
     * 提货码
     */
    @TableField(value = "PICKUP_CODE")
    @ApiModelProperty(value = "提货码")
    private String pickupCode;

    /**
     * 收货地址
     */
    @TableField(value = "RCV_ADDR")
    @ApiModelProperty(value = "收货地址")
    private String rcvAddr;

    /**
     * 收货地址详情
     */
    @TableField(value = "RCV_DETAIL_ADDR")
    @ApiModelProperty(value = "收货地址详情")
    private String rcvDetailAddr;

    /**
     * 备注
     */
    @TableField(value = "NOTES")
    @ApiModelProperty(value = "备注")
    private String notes;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHEMENTS")
    @ApiModelProperty(value = "附件")
    private String attachements;

    @TableField(value = "APPROVE_STATUS")
    @ApiModelProperty(value = "0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;


    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 销售明细
     */
    @TableField(exist = false)
    private List<SaleOrderItem> detailList;

    /**
     * 出入库任务单（这里只查出库）
     */
    @TableField(exist = false)
    private List<InoutTask> children;
}