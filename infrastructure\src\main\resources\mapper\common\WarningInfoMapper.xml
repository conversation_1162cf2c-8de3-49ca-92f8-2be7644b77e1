<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.common.WarningInfoMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.common.WarningInfo">
        <!--@mbg.generated-->
        <!--@Table B_WARNING_INFO-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="WARNING_TYPE" jdbcType="VARCHAR" property="warningType"/>
        <result column="WARNING_TIME" jdbcType="TIMESTAMP" property="warningTime"/>
        <result column="TRI_MODE" jdbcType="INTEGER" property="triMode"/>
        <result column="MSG" jdbcType="VARCHAR" property="msg"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName"/>
        <result column="ST_ID" jdbcType="VARCHAR" property="stId"/>
        <result column="ST_NAME" jdbcType="VARCHAR" property="stName"/>
        <result column="LOC_ID" jdbcType="VARCHAR" property="locId"/>
        <result column="LOC_NAME" jdbcType="VARCHAR" property="locName"/>
        <result column="USER_ID" jdbcType="VARCHAR" property="userId"/>
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName"/>
        <result column="DISPOSE_TIME" jdbcType="TIMESTAMP" property="disposeTime"/>
        <result column="DISPOSE_STATE" jdbcType="INTEGER" property="disposeState"/>
        <result column="DISPOSE_DESC" jdbcType="VARCHAR" property="disposeDesc"/>
        <result column="REF_ID" jdbcType="VARCHAR" property="refId"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        WARNING_TYPE,
        WARNING_TIME,
        TRI_MODE,
        MSG,
        STORE_ID,
        STORE_NAME,
        ST_ID,
        ST_NAME,
        LOC_ID,
        LOC_NAME,
        USER_ID,
        USER_NAME,
        DISPOSE_TIME,
        DISPOSE_STATE,
        DISPOSE_DESC,
        REF_ID,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>

    <select id="getPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_WARNING_INFO
        <where>
            <if test="condition.id != null and condition.id != ''">
                and ID = #{condition.id}
            </if>
            <if test="condition.warningType != null and condition.warningType != ''">
                and WARNING_TYPE = #{condition.warningType}
            </if>
            <if test="condition.storeId != null and condition.storeId != ''">
                and STORE_ID in
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                and ST_ID = #{condition.stId}
            </if>
            <if test="condition.locId != null and condition.locId != ''">
                and LOC_ID = #{condition.locId}
            </if>
            <if test="condition.disposeState != null and condition.disposeState != ''">
                and DISPOSE_STATE = #{condition.disposeState}
            </if>
            <if test="condition.userId != null and condition.userId != ''">
                and USER_ID = #{condition.userId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="getListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_WARNING_INFO
        <where>
            <if test="condition.id != null and condition.id != ''">
                and ID = #{condition.id}
            </if>
            <if test="condition.warningType != null and condition.warningType != ''">
                and WARNING_TYPE = #{condition.warningType}
            </if>
            <if test="condition.storeId != null and condition.storeId != ''">
                and STORE_ID in
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                and ST_ID = #{condition.stId}
            </if>
            <if test="condition.locId != null and condition.locId != ''">
                and LOC_ID = #{condition.locId}
            </if>
            <if test="condition.disposeState != null and condition.disposeState != ''">
                and DISPOSE_STATE = #{condition.disposeState}
            </if>
            <if test="condition.userId != null and condition.userId != ''">
                and USER_ID = #{condition.userId}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
