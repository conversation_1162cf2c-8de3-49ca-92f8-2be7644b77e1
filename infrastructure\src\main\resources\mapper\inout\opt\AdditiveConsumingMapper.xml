<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.opt.AdditiveConsumingMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.opt.AdditiveConsuming">
        <!--@Table B_ADDITIVE_CONSUMING-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="consumingCode" column="CONSUMING_CODE" jdbcType="VARCHAR"/>
        <result property="storeId" column="STORE_ID" jdbcType="VARCHAR"/>
        <result property="storeName" column="STORE_NAME" jdbcType="VARCHAR"/>
        <result property="applicant" column="APPLICANT" jdbcType="VARCHAR"/>
        <result property="enabled" column="ENABLED" jdbcType="INTEGER"/>
        <result property="attachments" column="ATTACHMENTS" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createId" column="CREATE_ID" jdbcType="VARCHAR"/>
        <result property="updateId" column="UPDATE_ID" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
        <result property="catalogId" column="CATALOG_ID" jdbcType="VARCHAR"/>
        <result property="catalogName" column="CATALOG_NAME" jdbcType="VARCHAR"/>
        <result property="specifications" column="SPECIFICATIONS" jdbcType="VARCHAR"/>
        <result property="usageTime" column="USAGE_TIME" jdbcType="DATE"/>
        <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>
        <result property="approveStatus" column="APPROVE_STATUS" jdbcType="INTEGER"/>
        <result property="approver" column="APPROVER" jdbcType="VARCHAR"/>
        <result property="approveTime" column="APPROVE_TIME" jdbcType="TIMESTAMP"/>
        <result property="approveOpinion" column="APPROVE_OPINION" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        CONSUMING_CODE,
        STORE_ID,
        STORE_NAME,
        APPLICANT,
        ENABLED,
        ATTACHMENTS,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID,
        CATALOG_ID,
        "CATALOG_NAME",
        SPECIFICATIONS,
        USAGE_TIME,
        REMARKS,
        APPROVE_STATUS,
        APPROVER,
        APPROVE_TIME,
        APPROVE_OPINION
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_ADDITIVE_CONSUMING
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeName)">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogName)">
                AND "CATALOG_NAME" LIKE CONCAT('%', #{condition.catalogName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.recordStartTime) and @plugins.OGNL@isNotEmpty(condition.recordEndTime)">
                AND USAGE_TIME BETWEEN #{condition.recordStartTime} AND #{condition.recordEndTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 列表查询 -->
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_ADDITIVE_CONSUMING
        <where>
            AND ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeName)">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogName)">
                AND "CATALOG_NAME" LIKE CONCAT('%', #{condition.catalogName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
                AND APPROVE_STATUS = #{condition.approveStatus}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.recordStartTime) and @plugins.OGNL@isNotEmpty(condition.recordEndTime)">
                AND USAGE_TIME BETWEEN #{condition.recordStartTime} AND #{condition.recordEndTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
