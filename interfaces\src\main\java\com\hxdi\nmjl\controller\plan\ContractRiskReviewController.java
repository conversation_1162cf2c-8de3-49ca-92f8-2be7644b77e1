package com.hxdi.nmjl.controller.plan;

import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.plan.ContractRiskReviewCondition;
import com.hxdi.nmjl.domain.plan.ContractRiskReview;
import com.hxdi.nmjl.service.plan.impl.ContractRiskReviewService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 合同风险审查表控制层
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "合同风险审查表")
@RequestMapping("/contractRiskReview")
public class ContractRiskReviewController extends CommonApiController<ContractRiskReviewService, ContractRiskReview, ContractRiskReviewCondition> {

}
