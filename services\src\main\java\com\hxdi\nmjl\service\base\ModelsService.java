package com.hxdi.nmjl.service.base;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.Models;
import com.hxdi.nmjl.condition.base.ModelsCondition;

import java.util.List;

public interface ModelsService extends IBaseService<Models> {

    /**
     * 保存更新
     * @param model
     */
    void saveOrUpdates(Models model);

    /**
     * 删除
     * @param modelId
     */
    void remove(String modelId);

    /**
     * 分页
     * @param condition
     * @return
     */
    Page<Models> pages(ModelsCondition condition);

    /**
     * 列表
     * @param condition
     * @return
     */
    List<Models> lists(ModelsCondition condition);
}
