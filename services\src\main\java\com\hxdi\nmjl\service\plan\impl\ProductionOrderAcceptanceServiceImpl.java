package com.hxdi.nmjl.service.plan.impl;

import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.quality.QualityInspectionCondition;
import com.hxdi.nmjl.domain.plan.ProductionOrder;
import com.hxdi.nmjl.domain.plan.ProductionOrderAcceptance;
import com.hxdi.nmjl.domain.plan.ProductionOrderItem;
import com.hxdi.nmjl.domain.quality.QualityInspection;
import com.hxdi.nmjl.linker.ContextService;
import com.hxdi.nmjl.mapper.plan.ProductionOrderAcceptanceMapper;
import com.hxdi.nmjl.service.plan.ProductionOrderItemService;
import com.hxdi.nmjl.service.quality.QualityInspectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ProductionOrderAcceptanceServiceImpl extends BaseServiceImpl<ProductionOrderAcceptanceMapper, ProductionOrderAcceptance> implements ProductionOrderAcceptanceService {

    @Resource
    private ContextService contextService;

    @Resource
    private ProductionOrderItemService productionOrderItemService;

    @Resource
    private QualityInspectionService qualityInspectionService;

    @Override
    public boolean saveOrUpdate(ProductionOrderAcceptance entity) {
        if (CommonUtils.isEmpty(entity.getId())) {
            throw new BaseException("更新数据时请指定ID");
        } else {
            update(entity);
        }
        return true;
    }

    public void update(ProductionOrderAcceptance entity) {
        if (entity.getState() == 2) {
            BizExp.pop("当前状态不能进行修改操作");
        }

        if (entity.getAcceptanceResult() == 1 && entity.getHandleStatus() == 2) {
            entity.setState(2); // 验收通过且已处理，状态设为已完成
        } else if (entity.getAcceptanceResult() == 2) {
            entity.setState(0); // 验收不通过，状态设为验收中
        }
        baseMapper.updateById(entity);
    }

    @Override
    public void create(ProductionOrder order, List<ProductionOrderItem> detailList) {
        log.info("开始生成验收单------");
        ProductionOrderAcceptance acceptance = new ProductionOrderAcceptance();

        // 生成验收编号
        acceptance.setAcceptanceNo((String) contextService.generateCode("order_acceptance_code"));

        // 设置订单相关信息
        acceptance.setStoreId(order.getStoreId());
        acceptance.setStoreName(order.getStoreName());
        acceptance.setOrgId(order.getOrgId());
        acceptance.setOrgName(order.getOrgName());
        acceptance.setClientId(order.getClientId());
        acceptance.setClientName(order.getClientName());
        acceptance.setOrderId(order.getId());
        acceptance.setOrderCode(order.getOrderCode());
        acceptance.setBatchNo(order.getBatchNo());
        acceptance.setContractId(order.getContractId());
        acceptance.setContractCode(order.getContractCode());

        String catalogId;
        String catalogName;
        if (detailList != null && !detailList.isEmpty()) {
            // ','分隔ids
            catalogId = detailList.stream().map(ProductionOrderItem::getCatalogId).collect(Collectors.joining(","));
            catalogName = detailList.stream().map(ProductionOrderItem::getCatalogName).collect(Collectors.joining(","));
            acceptance.setCatalogId(catalogId);
            acceptance.setCatalogName(catalogName);
        }

        // 查询并设置质检相关信息
        QualityInspectionCondition condition = new QualityInspectionCondition();
        condition.setOrderId(order.getId());
        List<QualityInspection> qualityInspectionList = qualityInspectionService.getList(condition);

        if (qualityInspectionList != null && !qualityInspectionList.isEmpty()) {
            acceptance.setQualityInspectionId(qualityInspectionList.get(0).getId());
            acceptance.setQualityInspectionNo(qualityInspectionList.get(0).getInspectionNo());
        }

        // 设置验收状态为待验收
        acceptance.setState(0);

        // 设置数据权限
        acceptance.setDataHierarchyId(order.getDataHierarchyId());

        baseMapper.insert(acceptance);
        log.info("验收单生成完成------");
    }
}
