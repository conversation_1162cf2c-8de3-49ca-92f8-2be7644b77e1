package com.hxdi.nmjl.controller.inout.duty;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.DutyCondition;
import com.hxdi.nmjl.domain.inout.duty.DutyRecord;
import com.hxdi.nmjl.service.inout.duty.DutyRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/duty/record")
@Api(tags = "值班记录接口")
@Validated
public class DutyRecordController extends BaseController<DutyRecordService, DutyRecord> {

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody DutyRecord dutyRecord) {
        if (CommonUtils.isEmpty(dutyRecord.getId())) {
            bizService.create(dutyRecord);
        } else {
            bizService.updating(dutyRecord);
        }
        return ResultBody.ok();
    }


    @ApiOperation(value = "查询")
    @GetMapping("/query")
    public ResultBody<DutyRecord> query(@RequestParam String dutyId) {
        return ResultBody.ok().data(bizService.detail(dutyId));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String dutyId) {
        bizService.delete(dutyId);
        return ResultBody.ok();
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<DutyRecord>> page(DutyCondition dutyCondition) {
        return ResultBody.ok().data(bizService.getPage(dutyCondition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<DutyRecord>> list(DutyCondition dutyCondition) {
        return ResultBody.ok().data(bizService.getList(dutyCondition));
    }
}
