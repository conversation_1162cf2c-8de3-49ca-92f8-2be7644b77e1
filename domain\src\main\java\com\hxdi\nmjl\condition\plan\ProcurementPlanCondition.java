package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * <AUTHOR>
 */
@ApiModel(description="粮食筹措计划查询条件")
@Getter
@Setter
public class ProcurementPlanCondition extends QueryCondition {

    @ApiModelProperty(value="年份：YYYY")
    private Integer year;

    @ApiModelProperty(value="计划类型：1-委托采购，2-自行采购")
    private Integer planTypes;

    @ApiModelProperty(value="计划状态：0-未上报，1-已上报、2-招标中，3-已采购，4-已完成")
    private Integer states;

    @ApiModelProperty(value="审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    @ApiModelProperty(value="管理单位ID")
    private String manageUnitId;

    @ApiModelProperty(value="军供站ID")
    private String storeId;

    @ApiModelProperty(value = "是否为父计划")
    private Integer parentFlag;

    @ApiModelProperty(value = "父计划ID")
    private String pid;

    @ApiModelProperty(value = "父计划编号")
    private String pCode;

    @ApiModelProperty(value="上报开始时间")
    private Date reportStartTime;

    @ApiModelProperty(value="上报结束时间")
    private Date reportEndTime;

    @ApiModelProperty(value="模糊查询(管理单位、计划编号)")
    private String search;

    //----------大屏接口参数------------

    @ApiModelProperty(value = "地区编码")
    private String areaCode;

    @ApiModelProperty(value = "品类")
    private String ClassificationId;
}