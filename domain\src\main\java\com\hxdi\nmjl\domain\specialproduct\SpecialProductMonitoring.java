package com.hxdi.nmjl.domain.specialproduct;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 优特产品分析报告
 */
@Getter
@Setter
@TableName (value = "B_SPECIAL_PRODUCT_MONITORING")
public class SpecialProductMonitoring implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**

     产品 ID
     */
    @TableField (value = "PRODUCT_ID")
    private String productId;

    /**

     产品名称
     */
    @TableField (value = "PRODUCT_NAME")
    private String productName;

    /**

     标题
     */
    @TableField (value = "TITLE")
    private String title;

    /**

     报告日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "REPORT_DATE")
    private Date reportDate;

    /**

     报告人
     */
    @TableField (value = "REPORTER")
    private String reporter;

    /**

     销售额
     */
    @TableField (value = "SALES_VOLUMN")
    private BigDecimal salesVolumn;

    /**

     满意度评分
     */
    @TableField (value = "CUSTOMER_SCORE")
    private BigDecimal customerScore;

    /**

     销售趋势描述
     */
    @TableField (value = "SALES_TREND")
    private String salesTrend;

    /**

     影响因素
     */
    @TableField (value = "INFLUENCE_FACTORS")
    private String influenceFactors;

    /**

     其它备注
     */
    @TableField (value = "NOTES")
    private String notes;

    /**

     市场份额
     */
    @TableField (value = "MARKEY_SHARE")
    private BigDecimal marketShare;

    /**

     状态（1 - 有效 0 删除）
     */
    @TableField (value = "ENABLED")
    private Integer enabled;

    /**

     创建日期
     */
    @TableField (value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**

     更新时间
     */
    @TableField (value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**

     创建 id
     */
    @TableField (value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**

     更新 id
     */
    @TableField (value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**

     租户 id
     */
    @TableField (value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**

     组织
     */
    @TableField (value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;



}