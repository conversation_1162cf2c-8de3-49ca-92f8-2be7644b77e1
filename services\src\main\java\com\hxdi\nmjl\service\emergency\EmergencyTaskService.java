package com.hxdi.nmjl.service.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.emergency.EmergencyTaskCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyTask;

import java.util.List;

/**
 * 应急任务服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/21
 */
public interface EmergencyTaskService extends IBaseService<EmergencyTask> {

    /**
     * 创建应急任务
     * @param task 应急任务实体
     */
    void create(EmergencyTask task);

    /**
     * 更新应急任务
     * @param task 应急任务实体
     */
    void update(EmergencyTask task);

    /**
     * 获取应急任务详情
     * @param taskId 任务ID
     * @return 应急任务实体
     */
    EmergencyTask getDetail(String taskId);

    /**
     * 分页查询应急任务
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<EmergencyTask> pages(EmergencyTaskCondition condition);

    /**
     * 列表查询应急任务
     * @param condition 查询条件
     * @return 任务列表
     */
    List<EmergencyTask> lists(EmergencyTaskCondition condition);

    /**
     * 更新应急任务状态
     * @param taskId 任务ID
     */
    void updateState(String taskId);

    /**
     * 删除应急任务（逻辑删除）
     * @param taskId 任务ID
     */
    void remove(String taskId);


}