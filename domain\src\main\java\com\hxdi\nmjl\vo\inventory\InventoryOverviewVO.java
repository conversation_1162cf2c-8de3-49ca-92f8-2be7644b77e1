package com.hxdi.nmjl.vo.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 库存总览VO
 * @author: 王贝强
 * @create: 2025-08-13 16:47
 */
@Getter
@Setter
@ApiModel(description = "库存总览返回VO")
public class InventoryOverviewVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓房ID")
    private String stId;

    @ApiModelProperty(value = "仓房名称")
    private String stName;

    @ApiModelProperty(value="库存总数量")
    private BigDecimal used;

    @ApiModelProperty(value="仓容")
    private BigDecimal capacity;

    @ApiModelProperty(value="库存总览明细")
    List<InventoryOverviewItemVO> inventoryOverviewItemVOList;
}
