package com.hxdi.nmjl.service.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.portal.PortalCondition;
import com.hxdi.nmjl.condition.portal.ResourceCondition;
import com.hxdi.nmjl.domain.portal.CmsCategories;
import com.hxdi.nmjl.domain.portal.CmsResources;


/**
 * 门户资源管理接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/18 16:52
 */
public interface CmsResourcesService extends IBaseService<CmsResources> {

    /**
     * 新增
     * @param resources
     */
    void create(CmsResources resources);

    /**
     * 修改
     * @param resources
     */
    void update(CmsResources resources);

    /**
     * 更新发布状态：0-未发布，1-已发布
     * @param resourceId
     * @param state
     */
    void changeState(String resourceId, Integer state);

    /**
     * 删除
     * @param id
     */
    void remove(String id);

    /**
     * 根据栏目id删除-标记删除
     * @param categoryId
     */
    void removeByCategoryId(String categoryId);

    /**
     * 查询详情
     * @param resourceId
     * @return
     */
    CmsResources getDetail(String resourceId);

    /**
     * 门户后台管理-分页查询
     * @param condition
     * @return
     */
    Page<CmsResources> pages(ResourceCondition condition);

    /**
     * 门户前台页面-分页查询
     * @param condition
     * @return
     */
    Page<CmsResources> pageForPortal(PortalCondition condition);
}
