package com.hxdi.nmjl.controller.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.clientrelated.SupplierCreditCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierCredit;
import com.hxdi.nmjl.service.clientrelated.SupplierCreditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商信用管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@RestController
@RequestMapping("/credit")
@Api(tags = "供应商信用管理")
public class SupplierCreditController extends BaseController<SupplierCreditService, SupplierCredit> {

    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody SupplierCredit credit) {
        if (CommonUtils.isEmpty(credit.getId())) {
            create(credit);
        } else {
            update(credit);
        }
        return ResultBody.OK();
    }

    @ApiOperation("新增")
    @PostMapping("/add")
    public ResultBody<Void> create(@RequestBody SupplierCredit credit) {
        bizService.create(credit);
        return ResultBody.OK();
    }

    @ApiOperation("更新")
    @PostMapping("/update")
    public ResultBody<Void> update(@RequestBody SupplierCredit credit) {
        bizService.update(credit);
        return ResultBody.OK();
    }

    /**
     * 删除
     */
    @ApiOperation(value = "删除")
    @DeleteMapping("/remove")
    public ResultBody<Void> remove(@RequestParam("id") String id) {
        bizService.changeState(id, 0);
        return ResultBody.OK();
    }

    /**
     * 查询详情
     */
    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<SupplierCredit> getDetail(String uniqueKey) {
        return ResultBody.<SupplierCredit>OK().data(bizService.getByUniqueKey(uniqueKey));
    }

    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<SupplierCredit>> pages(SupplierCreditCondition condition) {
        return ResultBody.<Page<SupplierCredit>>OK().data(bizService.pages(condition));
    }

    /**
     * 列表查询
     */
    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<SupplierCredit>> lists(SupplierCreditCondition condition) {
        return ResultBody.<List<SupplierCredit>>OK().data(bizService.lists(condition));
    }
}


