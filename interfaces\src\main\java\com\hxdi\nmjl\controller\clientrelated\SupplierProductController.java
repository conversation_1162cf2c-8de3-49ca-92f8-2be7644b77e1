package com.hxdi.nmjl.controller.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.clientrelated.SupplierProductCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierProduct;
import com.hxdi.nmjl.service.clientrelated.SupplierProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商产品管理接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@RestController
@RequestMapping("/product")
@Api(tags = "供应商产品管理")
public class SupplierProductController extends BaseController<SupplierProductService, SupplierProduct> {

    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody SupplierProduct product) {
        if (CommonUtils.isEmpty(product.getId())) {
            create(product);
        } else {
            update(product);
        }
        return ResultBody.OK();
    }

    @ApiOperation("新增")
    @PostMapping("/add")
    public ResultBody<Void> create(@RequestBody SupplierProduct product) {
        bizService.create(product);
        return ResultBody.OK();
    }

    @ApiOperation("更新")
    @PostMapping("/update")
    public ResultBody<Void> update(@RequestBody SupplierProduct product) {
        bizService.update(product);
        return ResultBody.OK();
    }

    /**
     * 删除
     */
    @ApiOperation(value = "删除")
    @DeleteMapping("/remove")
    public ResultBody<Void> remove(@RequestParam("id") String id) {
        bizService.changeState(id, 0);
        return ResultBody.OK();
    }

    /**
     * 查询详情
     */
    @ApiOperation(value = "查询详情")
    @GetMapping("/get")
    public ResultBody<SupplierProduct> getProduct(String id) {
        return ResultBody.<SupplierProduct>OK().data(bizService.getByUniqueKey(id));
    }


    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<SupplierProduct>> pages(SupplierProductCondition condition) {
        return ResultBody.<Page<SupplierProduct>>OK().data(bizService.pages(condition));
    }


    @ApiOperation(value = "查询列表")
    @GetMapping("/list")
    public ResultBody<List<SupplierProduct>> lists(SupplierProductCondition condition) {
        return ResultBody.<List<SupplierProduct>>OK().data(bizService.lists(condition));
    }
}


