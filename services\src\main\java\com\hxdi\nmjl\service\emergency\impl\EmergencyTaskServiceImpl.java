package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.emergency.EmergencyTaskCondition;
import com.hxdi.nmjl.condition.emergency.EmergencyTaskItemCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.emergency.EmergencySchedule;
import com.hxdi.nmjl.domain.emergency.EmergencyTask;
import com.hxdi.nmjl.domain.emergency.EmergencyTaskItem;
import com.hxdi.nmjl.mapper.emergency.EmergencyTaskMapper;
import com.hxdi.nmjl.service.base.CatalogService;
import com.hxdi.nmjl.service.emergency.EmergencyScheduleService;
import com.hxdi.nmjl.service.emergency.EmergencyTaskItemService;
import com.hxdi.nmjl.service.emergency.EmergencyTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应急任务服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class EmergencyTaskServiceImpl extends BaseServiceImpl<EmergencyTaskMapper, EmergencyTask> implements EmergencyTaskService {

    @Resource
    private EmergencyTaskItemService emergencyTaskItemService;

    @Resource
    private EmergencyScheduleService emergencyScheduleService;

    @Resource
    private CatalogService catalogService;

    @Override
    public void create(EmergencyTask task) {
        task.setState(1);
        this.save(task);
        //查询调度信息
        EmergencySchedule schedule = emergencyScheduleService.getOne(
                new LambdaQueryWrapper<EmergencySchedule>()
                        .eq(EmergencySchedule::getScheduleNo, task.getScheduleNo())
        );
        List<EmergencyTaskItem> emergencyTaskItem = task.getEmergencyTaskItem();
        if (emergencyTaskItem != null && !emergencyTaskItem.isEmpty()) {
            emergencyTaskItem.forEach(item -> {
                item.setTaskId(task.getId());
                item.setScheduleId(schedule.getId());
                Catalog catalog = catalogService.getById(item.getCatalogId());
                item.setCatalogName(catalog.getCatalogName());
            });
            emergencyTaskItemService.saveBatch(task.getEmergencyTaskItem());
        }
    }

    @Override
    public void update(EmergencyTask task) {
        // 查询原任务
        EmergencyTask savedTask = this.getById(task.getId());
        if (savedTask == null) {
            throw new BaseException("应急任务不存在");
        }

        // 校验状态：只有进行中的任务可修改
        if (savedTask.getState() != 1) {
            throw new BaseException("只有进行中的任务允许修改");
        }
        this.updateById(task);
        //查询调度信息
        EmergencySchedule schedule = emergencyScheduleService.getOne(
                new LambdaQueryWrapper<EmergencySchedule>()
                        .eq(EmergencySchedule::getScheduleNo, task.getScheduleNo())
        );
        List<EmergencyTaskItem> emergencyTaskItem = task.getEmergencyTaskItem();
        if (emergencyTaskItem != null && !emergencyTaskItem.isEmpty()) {
            emergencyTaskItem.forEach(item -> {
                item.setTaskId(task.getId());
                item.setScheduleId(schedule.getId());
                Catalog catalog = catalogService.getById(item.getCatalogId());
                item.setCatalogName(catalog.getCatalogName());
            });
            emergencyTaskItemService.removeBatch(task.getId());
            emergencyTaskItemService.saveBatch(task.getEmergencyTaskItem());
        }

    }

    @Override
    public EmergencyTask getDetail(String taskId) {
        EmergencyTask emergencyTask = baseMapper.selectById(taskId);
        EmergencyTaskItemCondition emergencyTaskItemCondition = new EmergencyTaskItemCondition();
        emergencyTaskItemCondition.setTaskId(taskId);
        emergencyTask.setEmergencyTaskItem(emergencyTaskItemService.lists(emergencyTaskItemCondition));
        return emergencyTask;
    }

    @Override
    public Page<EmergencyTask> pages(EmergencyTaskCondition condition) {
        Page<EmergencyTask> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<EmergencyTask> lists(EmergencyTaskCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void updateState(String taskId) {
        EmergencyTask task = baseMapper.selectById(taskId);
        if (task == null) {
            throw new BaseException("应急任务不存在");
        }
        // 校验状态流转合法性
        if (task.getState() != 1) {
            throw new BaseException("只有进行中的任务可标记为已送达");
        }
        task.setState(2);
        this.updateById(task);
    }

    @Override
    public void remove(String taskId) {
        EmergencyTask task = baseMapper.selectById(taskId);
        if (task == null) {
            throw new BaseException("应急任务不存在");
        }

        // 校验状态：已送达的任务不可删除
        if (task.getState() == 2) {
            BizExp.pop("已送达的任务不能删除");
        }

        // 逻辑删除
        task.setEnabled(0);
        this.updateById(task);
    }

}