package com.hxdi.nmjl.condition.clientrelated;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "案件查询参数")
@Setter
@Getter
public class ClientSaleConfigCondition extends QueryCondition {

    @ApiModelProperty(value = "发生时间：开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "发生时间：结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;
}
