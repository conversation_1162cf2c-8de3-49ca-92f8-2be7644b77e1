package com.hxdi.nmjl.service.inout.delivery.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inout.delivery.TransportRoute;
import com.hxdi.nmjl.mapper.inout.delivery.TransportRouteMapper;
import com.hxdi.nmjl.service.inout.delivery.TransportRouteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 运输轨迹表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-23 11:18:48
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class TransportRouteServiceImpl extends BaseServiceImpl<TransportRouteMapper, TransportRoute> implements TransportRouteService {
    @Override
    public List<TransportRoute> getListByPid(String deliveryId) {
        return baseMapper.selectList(Wrappers.<TransportRoute>lambdaQuery()
                .eq(TransportRoute::getDeliveryId, deliveryId));
    }


}

