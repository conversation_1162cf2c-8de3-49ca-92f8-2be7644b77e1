package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <追溯信息实体>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/16 23:56
 */
@Setter
@Getter
@TableName("B_TRACE_INFO")
public class TraceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 生产批次号
     */
    @ApiModelProperty(value = "生产批次号")
    @TableField(value = "BATCH_NUM")
    private String batchNum;

    /**
     * 库存ID
     */
    @ApiModelProperty(value = "库存ID")
    @TableField(value = "INVENTORY_ID")
    private String inventoryId;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
