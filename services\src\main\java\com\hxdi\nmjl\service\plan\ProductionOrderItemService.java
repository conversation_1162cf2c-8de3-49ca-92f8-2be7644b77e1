package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.ProductionOrderItem;

import java.util.List;

public interface ProductionOrderItemService extends IBaseService<ProductionOrderItem> {

    /**
     * 根据订单ID查询订单详情
     *
     * @param orderId
     * @return
     */
    List<ProductionOrderItem> listByOrderId(String orderId);

    /**
     * 根据订单ID、品种ID、质量等级查询订单详情
     *
     * @param orderId
     * @param catalogId
     * @param grade
     * @return
     */
    ProductionOrderItem listByCatalogIdAndGrade(String orderId, String catalogId, String grade);

    /**
     * 根据订单ID列表批量查询订单详情
     *
     * @param IdList
     * @return
     */
    List<ProductionOrderItem> listByIdList(List<String> IdList);


    void removeV1(String orderId);
}
