<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.specialproduct.SpecialProductNoticeMapper">



    <sql id="Base_Column_List">
        ID, TITLE, CONTENT, ORG_NAME, PUBLISHER, PUBLISH_TIME,
        MOBILE, END_TIME, CREATE_TIME, UPDATE_TIME, CREATE_ID,
        UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, PUBLISH_STATE
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.specialproduct.SpecialProductNotice">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="CONTENT" jdbcType="VARCHAR" property="content"/>
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName"/>
        <result column="PUBLISHER" jdbcType="VARCHAR" property="publisher"/>
        <result column="PUBLISH_TIME" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="END_TIME" jdbcType="DATE" property="endTime"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
        <result column="PUBLISH_STATE" jdbcType="INTEGER" property="publishState"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_SPECIAL_PRODUCT_NOTICE
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.title)">
                and TITLE like concat('%', #{condition.title}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                and ORG_NAME = #{condition.orgName}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.publisher)">
                and PUBLISHER = #{condition.publisher}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createStartTime)">
                and CREATE_TIME >= #{condition.createStartTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createEndTime)">
                and CREATE_TIME &lt;= #{condition.createEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.publishState)">
                and PUBLISH_STATE = #{condition.publishState}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_SPECIAL_PRODUCT_NOTICE
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.title)">
                and TITLE like concat('%', #{condition.title}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                and ORG_NAME = #{condition.orgName}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.publisher)">
                and PUBLISHER = #{condition.publisher}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createStartTime)">
                and CREATE_TIME >= #{condition.createStartTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.createEndTime)">
                and CREATE_TIME &lt;= #{condition.createEndTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.publishState)">
                and PUBLISH_STATE = #{condition.publishState}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
</mapper>
