package com.hxdi.nmjl.domain.quality;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(description="质检结果检测项")
@TableName(value = "B_QUALITY_INSPECTION_DETAIL")
public class QualityInspectionDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 检测ID
     */
    @TableField(value = "INSPECT_ID")
    @ApiModelProperty(value="检测ID")
    private String inspectId;

    /**
     * 质检项名称
     */
    @TableField(value = "ITEM_NAME")
    @ApiModelProperty(value="质检项名称")
    private String itemName;

    /**
     * 比较符
     */
    @TableField(value = "ITEM_SYMBOL")
    @ApiModelProperty(value="比较符")
    private String itemSymbol;

    /**
     * 单位符号
     */
    @TableField(value = "ITEM_SIGN")
    @ApiModelProperty(value="单位符号")
    private String itemSign;

    /**
     * 标准值
     */
    @TableField(value = "ITEM_SVAL")
    @ApiModelProperty(value="标准值")
    private String itemSval;

    /**
     * 检测值
     */
    @TableField(value = "ITEM_VAL")
    @ApiModelProperty(value="检测值")
    private String itemVal;

    /**
     * 单项判定结果：0-不合格，1-合格
     */
    @TableField(value = "ITEM_RESULT")
    @ApiModelProperty(value="单项判定结果：0-不合格，1-合格")
    private Integer itemResult;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}