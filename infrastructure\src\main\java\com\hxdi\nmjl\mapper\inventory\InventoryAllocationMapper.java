package com.hxdi.nmjl.mapper.inventory;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.inventory.InventoryAllocationCondition;
import com.hxdi.nmjl.domain.inventory.InventoryAllocation;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_INVENTORY_ALLOCATION(库存调配管理)】的数据库操作Mapper
* @createDate 2025-07-31 09:45:14
* @Entity generator.domain.BInventoryAllocation
*/
public interface InventoryAllocationMapper extends SuperMapper<InventoryAllocation> {
    @DataPermission
    List<InventoryAllocation> listV1(@Param("condition") InventoryAllocationCondition condition);
    @DataPermission
    Page<InventoryAllocation> PageV1(@Param("condition") InventoryAllocationCondition condition, @Param("page") Page<InventoryAllocation> page);
}




