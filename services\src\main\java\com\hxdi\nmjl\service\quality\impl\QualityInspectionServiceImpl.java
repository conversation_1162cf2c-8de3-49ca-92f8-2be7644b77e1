package com.hxdi.nmjl.service.quality.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.quality.QualityInspectionCondition;
import com.hxdi.nmjl.domain.quality.QualityInspection;
import com.hxdi.nmjl.domain.quality.QualityInspectionDetail;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.quality.QualityInspectionMapper;
import com.hxdi.nmjl.service.quality.QualityInspectionDetailService;
import com.hxdi.nmjl.service.quality.QualityInspectionService;
import com.hxdi.nmjl.vo.quality.QualityInspectionVO;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

@Service
public class QualityInspectionServiceImpl extends BaseServiceImpl<QualityInspectionMapper, QualityInspection> implements QualityInspectionService {

    @Resource
    private QualityInspectionDetailService detailService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public void SaveOrUpdate(QualityInspectionVO result) {
        QualityInspection inspection = new QualityInspection();
        List<QualityInspectionDetail> detailList;
        if (CommonUtils.isNotEmpty(result.getId())) {
            BeanUtils.copyProperties(result, inspection);
            baseMapper.updateById(inspection);
            // 更新详情
            if (CommonUtils.isNotEmpty(result.getDetailList())) {
                detailList = result.getDetailList();
                detailList.forEach(detail -> detail.setInspectId(inspection.getId()));
                detailService.saveOrUpdateBatch(detailList);
            }
        } else {
            // 生成质检单编号
            BusinessCodeParams params = new BusinessCodeParams();
            params.setCode("QUALITY_INSPECTION_CODE");
            params.setDt(DataType.STRING);
            BeanUtils.copyProperties(result, inspection);
            BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
            inspection.setInspectionNo((String) businessCode.getValue());
            baseMapper.insert(inspection);
            // 保存详情
            if (CommonUtils.isNotEmpty(result.getDetailList())) {
                detailList = result.getDetailList();
                detailList.forEach(detail -> detail.setInspectId(inspection.getId()));
                detailService.saveOrUpdateBatch(detailList);
            }

        }
    }

    @Override
    public QualityInspectionVO getDetail(Serializable id) {
        QualityInspectionVO vo = new QualityInspectionVO();
        QualityInspection inspection = getById(id);
        List<QualityInspectionDetail> detail = detailService.getDetail(id);
        if (CommonUtils.isNotEmpty(inspection)) {
            BeanUtils.copyProperties(inspection, vo);
            if (CommonUtils.isNotEmpty(detail)) {
                vo.setDetailList(detail);
            }
            return vo;
        }
        return null;
    }

    @Override
    public List<QualityInspection> getList(QualityInspectionCondition condition) {
        return baseMapper.getList(condition);
    }

    @Override
    public Page<QualityInspection> getPage(QualityInspectionCondition condition) {
        Page<QualityInspection> page = condition.newPage();
        return baseMapper.getPage(condition, page);
    }

    @Override
    public void removeid(String id) {
        // 删除质检结果
        QualityInspection inspection = new QualityInspection();
        inspection.setId(id);
        inspection.setEnabled(StrPool.State.DISABLE);
        this.updateById(inspection);
        //删除质检结果详情
        detailService.remove(new LambdaQueryWrapper<QualityInspectionDetail>().eq(QualityInspectionDetail::getInspectId, id));
    }
}
