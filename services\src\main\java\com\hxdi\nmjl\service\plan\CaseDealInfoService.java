package com.hxdi.nmjl.service.plan;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.CaseDealInfo;

import java.util.List;

/**
 * 案件处理服务接口
 */
public interface CaseDealInfoService extends IBaseService<CaseDealInfo> {


    /**
     * 根据案件ID查询案件处理信息列表
     * @param caseId
     * @return
     */
    List<CaseDealInfo> getList(String caseId);

    /**
     * 更新案件处理信息列表
     * @param caseId
     * @param caseDealList
     */
    void updateList(String caseId, List<CaseDealInfo> caseDealList);
}
