package com.hxdi.nmjl.vo.inventory;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: nmjl-service
 * @description: 库存总览明细VO
 * @author: 王贝强
 * @create: 2025-08-13 17:00
 */
@Getter
@Setter
@ApiModel(description = "库存总览明细VO")
public class InventoryOverviewItemVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 货位ID
     */
    @ApiModelProperty(value="货位ID")
    private String locId;

    /**
     * 货位名称
     */
    @ApiModelProperty(value="货位名称")
    private String locName;

    /**
     * 品类ID
     */
    @ApiModelProperty(value="品类ID")
    private String classificationId;

    /**
     * 品种ID
     */
    @ApiModelProperty(value="品种ID")
    private String catalogId;


    @ApiModelProperty(value="品种名称")
    private String catalogName;


    @ApiModelProperty(value="质量等级")
    private String grade;


    @ApiModelProperty(value="储备性质")
    private Integer reserveLevel;


    @ApiModelProperty(value="库存数量")
    private BigDecimal used;
}
