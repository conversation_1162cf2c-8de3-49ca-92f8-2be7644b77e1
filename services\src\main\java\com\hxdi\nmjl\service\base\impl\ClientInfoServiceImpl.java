package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.ClientInfoCondition;
import com.hxdi.nmjl.domain.base.ClientInfo;
import com.hxdi.nmjl.domain.clientrelated.ClientRelation;
import com.hxdi.nmjl.mapper.base.ClientInfoMapper;
import com.hxdi.nmjl.mapper.base.ClientRelationMapper;
import com.hxdi.nmjl.service.base.ClientInfoService;
import com.hxdi.nmjl.utils.RedisKeys;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Transactional(rollbackFor = Exception.class)
public class ClientInfoServiceImpl extends BaseServiceImpl<ClientInfoMapper, ClientInfo> implements ClientInfoService {

    @Resource
    private ClientRelationMapper clientRelationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CachePut(value = RedisKeys.Prefix.CLIENT_INFO, key = "#client.id", unless = "#result == null")
    public ClientInfo updateV1(ClientInfo client, Boolean isSupplier) {
        if (!isSupplier) {
            if (CommonUtils.isNotEmpty(client) && Objects.equals(client.getClientType(), "1")) {
                throw new BaseException("更新失败，请通过供应商管理功能进行操作！");
            }
            baseMapper.updateById(client);
            return client;
        } else {
            //先查出原来的供应商信息
            LambdaQueryWrapper<ClientInfo> wrapper = new LambdaQueryWrapper<ClientInfo>()
                    .eq(ClientInfo::getRefId, client.getRefId());
            ClientInfo oldClient = baseMapper.selectOne(wrapper);
            if (CommonUtils.isNotEmpty(oldClient)) {
                client.setId(oldClient.getId());
                baseMapper.updateById(client);
                return client;
            } else {
                this.createV1(client);
                return null;
            }
        }

    }

    @Override
    @CachePut(value = RedisKeys.Prefix.CLIENT_INFO, key = "#client.id", unless = "#result == null")
    public ClientInfo createV1(ClientInfo client) {
        //新增时，先查询当前表中是否已存在相同客户，存在则执行更新操作
        LambdaQueryWrapper<ClientInfo> wrapper = new LambdaQueryWrapper<ClientInfo>()
                .eq(CommonUtils.isNotEmpty(client.getName()), ClientInfo::getName, client.getName())
                .eq(CommonUtils.isNotEmpty(client.getClientType()), ClientInfo::getClientType, client.getClientType())
                .eq(CommonUtils.isNotEmpty(client.getCompanyType()), ClientInfo::getCompanyType, client.getCompanyType())
                .eq(CommonUtils.isNotEmpty(client.getCreditCode()), ClientInfo::getCreditCode, client.getCreditCode())
                .eq(ClientInfo::getEnabled, StrPool.State.ENABLE);
        List<ClientInfo> clientInfos = baseMapper.selectList(wrapper);
        //最终需要插入到关系表中的Id
        String clientId;
        if (!clientInfos.isEmpty()) {
            //不为空，默认取第一个进行更新操作
            ClientInfo currClient = clientInfos.get(0);
            BeanUtils.copyProperties(client, currClient, "id");
            baseMapper.updateById(currClient);
            //获取当前客户Id
            clientId = currClient.getId();

            client = currClient;
        } else {
            //为空，执行新增操作
            baseMapper.insert(client);
            clientId = client.getId();
        }
        //同时，向客户——组织关系表插入一条数据
        ClientRelation clientRelation = new ClientRelation();
        clientRelation.setClientId(clientId);
        if (SecurityHelper.isStationUser()) {
            clientRelation.setDataHierarchyId(SecurityHelper.obtainUser().getPid());
        } else {
            clientRelation.setDataHierarchyId(SecurityHelper.obtainUser().getOrganId());
        }
        clientRelationMapper.insert(clientRelation);

        return client;
    }

    @Override
    public void remove(String clientId) {
        if (SecurityHelper.isStationUser()) {
            clientRelationMapper.deleteByClientIdAndOrgId(clientId, SecurityHelper.obtainUser().getPid());
        } else if (SecurityHelper.isSuperUser()) {
            //超管直接删除
            clientRelationMapper.deleteAll(clientId);
            this.deleteSoft(clientId);
            return;
        } else {
            clientRelationMapper.deleteByClientIdAndOrgId(clientId, SecurityHelper.obtainUser().getOrganId());
        }
        List<ClientRelation> relations = clientRelationMapper.selectList(Wrappers.<ClientRelation>lambdaQuery().eq(ClientRelation::getClientId, clientId));
        if (relations.isEmpty()) {
            this.deleteSoft(clientId);
        }
    }

    @Override
    @CacheEvict(value = RedisKeys.Prefix.CLIENT_INFO, key = "#result", condition = "#result != null")
    public String removeByRefId(String refId, Integer state) {
        ClientInfo clientInfo = baseMapper.selectOne(new LambdaQueryWrapper<ClientInfo>().eq(ClientInfo::getRefId, refId));
        if (clientInfo != null) {
            baseMapper.deleteSoft(clientInfo.getId(), SecurityHelper.getUserId());

            if (state == 7) {
                //删除该客户对应的所有组织关系
                clientRelationMapper.deleteAll(clientInfo.getId());
                return clientInfo.getId();
            } else {
                return null;
            }
        }
        return null;
    }

    /**
     * 逻辑删除客户信息
     *
     * @param clientId
     */
    @CacheEvict(value = RedisKeys.Prefix.CLIENT_INFO, key = "#clientId", condition = "#result == true")
    public boolean deleteSoft(String clientId) {
        if (!SecurityHelper.isStationUser()) {
            ClientInfo clientInfo = baseMapper.selectById(clientId);
            if (Objects.equals(clientInfo.getClientType(), "1")) {
                BizExp.pop("无法通过客户管理删除供应商信息");
                return false;
            } else {
                baseMapper.deleteSoft(clientId, SecurityHelper.getUserId());
                return true;
            }
        }
        return false;
    }

    @Override
    @Cacheable(value = RedisKeys.Prefix.CLIENT_INFO, key = "#id", unless = "#result == null")
    public ClientInfo selectOne(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Page<ClientInfo> getPage(ClientInfoCondition condition) {
        Page<ClientInfo> page = condition.newPage();
        //如果是超管，直接查询所有数据
        if (SecurityHelper.isAdminUser(SecurityHelper.getUser())) {
            return baseMapper.selectPageV2(page, condition);
        } else {
            return baseMapper.selectPageV1(page, condition);
        }
    }

    @Override
    public List<ClientInfo> getList(ClientInfoCondition condition) {
        //如果是超管，直接查询所有数据
        if (SecurityHelper.isAdminUser(SecurityHelper.getUser())) {
            return baseMapper.selectListV2(condition);
        } else {
            if (CommonUtils.equals("1", condition.getClientType()) || CommonUtils.equals("2", condition.getClientType())) {
                return baseMapper.selectListV2(condition);
            } else {
                return baseMapper.selectListV1(condition);
            }
        }
    }

    @Override
    public List<ClientInfo> lists() {
        return baseMapper.selectList(Wrappers.<ClientInfo>lambdaQuery().eq(ClientInfo::getEnabled, StrPool.State.ENABLE));
    }

    @Override
    public List<ClientInfo> searchList(ClientInfoCondition condition) {
        return baseMapper.searchList(condition);
    }
}
