package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.ProductionOrder;
import com.hxdi.nmjl.condition.plan.ProductionOrderCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductionOrderMapper extends SuperMapper<ProductionOrder> {

    @DataPermission
    List<ProductionOrder> selectListV1(@Param("condition") ProductionOrderCondition condition);

    List<ProductionOrder> selectListV2(@Param("condition") ProductionOrderCondition condition);

    @DataPermission
    Page<ProductionOrder> selectPageV1(@Param("page") Page<ProductionOrder> page,@Param("condition") ProductionOrderCondition condition);
}