package com.hxdi.nmjl.domain.store;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 评估表
 */
@Getter
@Setter
@TableName(value = "B_STORE_ASSESSMENT")
public class StoreAssessment implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 申请编号
     */
    @TableField(value = "APPLY_NO")
    private String applyNo;

    /**
     * 企业名称
     */
    @TableField(value = "ENTERPRISE_NAME")
    private String enterpriseName;

    /**
     * 统一社会信用代码
     */
    @TableField(value = "CREDIT_CODE")
    private String creditCode;

    /**
     * 机构ID
     */
    @TableField(value = "ORG_ID")
    private String orgId;

    /**
     * 机构名称
     */
    @TableField(value = "ORG_NAME")
    private String orgName;

    /**
     * 申请类型：1-政策性；2-军民融合
     */
    @TableField(value = "APPLY_TYPE")
    private Integer applyType;

    /**
     * 评价描述
     */
    @TableField(value = "ASSES_DESC")
    private String assesDesc;

    /**
     * 评价等级：1-优秀；2-良好；3-合格；4-不合格（字典项）
     */
    @TableField(value = "GRADE")
    private String grade;

    /**
     * 审核状态：0-未审核；1-已通过；2-驳回
     */
    @TableField(value = "APPROVE_STATUS", fill = FieldFill.INSERT)
    private Integer approveStatus;

    /**
     * 审核人ID
     */
    @TableField(value = "APPROVER")
    private String approver;

    /**
     * 审核时间
     */
    @TableField(value = "APPROVE_TIME")
    private Date approveTime;

    /**
     * 审核意见
     */
    @TableField(value = "APPROVE_OPINION")
    private String approveOpinion;

    /**
     * 状态（1-有效 0删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}

