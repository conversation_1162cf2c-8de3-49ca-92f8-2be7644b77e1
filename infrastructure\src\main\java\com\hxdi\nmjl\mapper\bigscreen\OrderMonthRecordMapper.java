package com.hxdi.nmjl.mapper.bigscreen;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.bigscreen.OrderMonthRecordCondition;
import com.hxdi.nmjl.domain.bigscreen.OrderMonthRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: nmjl-service
 * @description: 订单统计Mapper
 * @author: 王贝强
 * @create: 2025-07-28 14:52
 */
@Mapper
public interface OrderMonthRecordMapper extends SuperMapper<OrderMonthRecord> {

    @DataPermission
    List<OrderMonthRecord> selectListV1(@Param("condition") OrderMonthRecordCondition condition);

    @DataPermission
    Page<OrderMonthRecord> selectPageV1(Page<OrderMonthRecord> page,@Param("condition") OrderMonthRecordCondition condition);
}
