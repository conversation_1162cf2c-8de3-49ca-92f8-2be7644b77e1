package com.hxdi.nmjl.domain.inout;

import java.io.Serializable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.enums.InoutBusinessType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
* 出入库记录
* @TableName B_INOUT_DETAIL
*/
@ApiModel(description = "出入库记录表")
@TableName("B_INOUT_DETAIL")
@Getter
@Setter
public class InoutDetail implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "任务编号")
    @TableField(value = "TASK_ID")
    private String taskCode;

    @ApiModelProperty(value = "出入库编号")
    @TableField(value = "INOUT_CODE")
    private String inoutCode;

    @ApiModelProperty(value = "出入库类型: 01-入库，02-出库")
    @TableField(value = "INOUT_TYPE")
    private String inoutType;

    @ApiModelProperty(value = "业务类型：0101-采购计划, 0201-销售订单")
    @TableField(value = "INOUT_BIZ_TYPE")
    private String inoutBizType;

    @ApiModelProperty(value = "业务时间")
    @TableField(value = "OPT_TIME")
    private LocalDateTime optTime;

    @ApiModelProperty(value = "客户ID")
    @TableField(value = "CLIENT_ID")
    private String clientId;

    @ApiModelProperty(value = "客户名称")
    @TableField(value = "CLIENT_NAME")
    private String clientName;

    @ApiModelProperty(value = "库点ID")
    @TableField(value = "STORE_ID")
    private String storeId;

    @ApiModelProperty(value = "库点名称")
    @TableField(value = "STORE_NAME")
    private String storeName;

    @ApiModelProperty(value = "单位ID")
    @TableField(value = "ORG_ID")
    private String orgId;

    @ApiModelProperty(value = "储备性质")
    @TableField(value = "RESERVE_LEVEL")
    private Integer reserveLevel;

    @ApiModelProperty(value = "生产批次号")
    @TableField(value = "BATCH_NUM")
    private String batchNum;

    @ApiModelProperty(value = "状态: 0-删除，1-正常")
    @TableField(value = "ENABLED")
    private Integer enabled;

    @ApiModelProperty(value = "状态: 0-暂存，1-提交")
    @TableField(value = "OPT_FLG")
    private Integer optFlg;

    @ApiModelProperty(value = "附件")
    @TableField(value = "ATTACHEMENTS")
    private String attachments;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty(value = "创建id")
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "更新id")
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "组织")
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    private String dataHierarchyId;


    /**
     * -----------以下非实体字段------------
     */

    @ApiModelProperty(value = "品种名称")
    @TableField(exist = false)
    private String catalogName;

    @ApiModelProperty(value = "质量等级")
    @TableField(exist = false)
    private String grade;

    @ApiModelProperty(value = "规格")
    @TableField(exist = false)
    private String specification;

    @ApiModelProperty(value = "数量")
    @TableField(exist = false)
    private BigDecimal totalQty;

    @ApiModelProperty(value = "订单编码")
    @TableField(exist = false)
    private String orderCode;

    @ApiModelProperty(value = "区域编码")
    @TableField(exist = false)
    private String areaCode;

    @TableField(exist = false)
    private List<InoutItem> detailList;


    /**
     * 是否入库类型
     * @return
     */
    public boolean isInType() {
        return InoutBusinessType.IN.getCode().equals(inoutType);
    }
}
