package com.hxdi.nmjl.mapper.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionInfo;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionInfoCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存生产信息Mapper接口
 */
public interface StoreProductionInfoMapper extends SuperMapper<StoreProductionInfo> {

    Page<StoreProductionInfo> selectPageV1(Page<StoreProductionInfo> page, @Param("condition") StoreProductionInfoCondition condition);

    List<StoreProductionInfo> selectListV1(@Param("condition") StoreProductionInfoCondition condition);
}