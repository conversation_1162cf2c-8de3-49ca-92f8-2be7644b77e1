<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.delivery.TransportRouteMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.delivery.TransportRoute">
        <!--@Table B_TRANSPORT_ROUTE-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="deliveryId" column="DELIVERY_ID" jdbcType="VARCHAR"/>
        <result property="lon" column="LON" jdbcType="VARCHAR"/>
        <result property="lat" column="LAT" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        DELIVERY_ID,
        LON,
        LAT,
        CREATE_TIME,
        TENANT_ID
    </sql>


</mapper>

