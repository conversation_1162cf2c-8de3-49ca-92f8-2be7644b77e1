package com.hxdi.nmjl.service.storeproduction;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlan;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionPlanCondition;

import java.util.List;

/**
 * 生产计划服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/5
 */
public interface StoreProductionPlanService extends IBaseService<StoreProductionPlan> {

    /**
     * 新增生产计划
     * @param plan 生产计划实体
     */
    void create(StoreProductionPlan plan);

    /**
     * 修改生产计划
     * @param plan 生产计划实体
     */
    void update(StoreProductionPlan plan);

    /**
     * 获取计划详情
     * @param planId 计划ID
     * @return 生产计划实体
     */
    StoreProductionPlan getDetail(String planId);

    /**
     * 分页查询计划
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<StoreProductionPlan> pages(StoreProductionPlanCondition condition);

    /**
     * 列表查询计划
     * @param condition 查询条件
     * @return 计划列表
     */
    List<StoreProductionPlan> lists(StoreProductionPlanCondition condition);

    /**
     * 审批计划（状态变更）
     * @param planId 计划ID
     * @param approveStatus 审批状态
     * @param opinion 审批意见
     */
    void approve(String planId, Integer approveStatus, String opinion);

    /**
     * 删除计划
     * @param planId 计划ID
     */
    void remove(String planId);

    /**
     * 提交计划
     * @param id 计划ID
     */
    void submit(String id);

    /**
     * 获取已审核计划列表
     * @return 已审核计划列表
     */
    List<StoreProductionPlan> approvedLists();
}
