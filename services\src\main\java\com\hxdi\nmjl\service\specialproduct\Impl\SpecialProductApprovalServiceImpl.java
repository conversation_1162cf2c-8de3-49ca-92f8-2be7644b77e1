package com.hxdi.nmjl.service.specialproduct.Impl;

import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductApproval;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo;
import com.hxdi.nmjl.mapper.specialproduct.SpecialProductApprovalMapper;
import com.hxdi.nmjl.service.specialproduct.SpecialProductApprovalService;
import com.hxdi.nmjl.service.specialproduct.SpecialProductInfoService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 地方特色产品审批服务实现
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SpecialProductApprovalServiceImpl extends BaseServiceImpl<SpecialProductApprovalMapper, SpecialProductApproval> implements SpecialProductApprovalService {

    @Resource
    private SpecialProductInfoService specialProductInfoService;

    @Override
    public void firstLevelApprove(String productId, Integer approveStatus, String opinion) {
        // 校验参数
        validateApproveParams(productId, approveStatus);

        // 获取产品审批信息
        SpecialProductApproval approval = baseMapper.selectById(productId);

        // 校验当前状态
        if (approval.getApproveStatus1() != null && approval.getApproveStatus1() != 0) {
            throw new BaseException("一级审核已完成，不能重复操作");
        }

        // 设置审核信息
        approval.setApproveStatus1(approveStatus);
        approval.setApprover1(SecurityHelper.obtainUser().getNickName());
        approval.setApproveTime1(new Date());
        approval.setApproveOpinion1(opinion);

        // 更新产品主表状态
        updateProductApproveStatus(productId, approveStatus);

        // 保存审批信息
        this.saveOrUpdate(approval);
    }

    @Override
    public void secondLevelApprove(String productId, Integer approveStatus, String opinion) {
        validateApproveParams(productId, approveStatus);

        // 获取产品审批信息
        SpecialProductApproval approval = getApprovalByProductId(productId);

        // 校验前置条件
        if (approval.getApproveStatus1() == null || approval.getApproveStatus1() != 1) {
            throw new BaseException("一级审核未通过，不能进行二级审核");
        }

        if (approval.getApproveStatus2() != null && approval.getApproveStatus2() != 0) {
            throw new BaseException("二级审核已完成，不能重复操作");
        }

        // 设置审核信息
        approval.setApproveStatus2(approveStatus);
        approval.setApprover2(SecurityHelper.obtainUser().getNickName());
        approval.setApproveTime2(new Date());
        approval.setApproveOpinion2(opinion);

        // 更新产品主表状态
        updateProductApproveStatus(productId, approveStatus);

        this.saveOrUpdate(approval);
    }

    @Override
    public void thirdLevelApprove(String productId, Integer approveStatus, String opinion) {
        validateApproveParams(productId, approveStatus);

        // 获取产品审批信息
        SpecialProductApproval approval = getApprovalByProductId(productId);

        // 校验前置条件
        if (approval.getApproveStatus2() == null || approval.getApproveStatus2() != 1) {
            throw new BaseException("二级审核未通过，不能进行三级审核");
        }

        if (approval.getApproveStatus3() != null && approval.getApproveStatus3() != 0) {
            throw new BaseException("三级审核已完成，不能重复操作");
        }

        // 设置审核信息
        approval.setApproveStatus3(approveStatus);
        approval.setApprover3(SecurityHelper.obtainUser().getNickName());
        approval.setApproveTime3(new Date());
        approval.setApproveOpinion3(opinion);

        // 更新产品主表状态（最终审核状态）
        updateFinalApproveStatus(productId, approveStatus);

        this.saveOrUpdate(approval);
    }

    @Override
    public SpecialProductApproval getByProductId(String productId) {
        return getApprovalByProductId(productId);
    }

    /**
     * 创建产品审批信息
     */
    @Override
    public void create(String productId) {
        SpecialProductApproval savedApproval = this.getById(productId);
        if (savedApproval != null) {
            throw new BaseException("该产品已存在审批信息，请勿重复创建");
        }
        // 创建
        SpecialProductApproval approval = new SpecialProductApproval();
        approval.setProductId(productId);
        approval.setApproveStatus1(0);
        approval.setApproveStatus2(0);
        approval.setApproveStatus3(0);
        // 保存
        this.save(approval);
    }

    /**
     * 校验审核参数
     */
    private void validateApproveParams(String productId, Integer approveStatus) {
        if (productId == null || productId.trim().isEmpty()) {
            throw new BaseException("产品ID不能为空");
        }

        if (approveStatus == null || (approveStatus != 0 && approveStatus != 1 && approveStatus != 2)) {
            throw new BaseException("审核状态无效，必须为0-未审核，1-已审核，2-驳回");
        }
    }


    /**
     * 获取产品审批信息，不存在则抛出异常
     */
    @NotNull
    private SpecialProductApproval getApprovalByProductId(String productId) {
        SpecialProductApproval approval = baseMapper.selectById(productId);

        if (approval == null) {
            throw new BaseException("产品审批信息不存在，产品ID：" + productId);
        }

        return approval;
    }

    /**
     * 更新产品主表的审核状态（中间状态）
     */
    private void updateProductApproveStatus(String productId, Integer approveStatus) {
        SpecialProductInfo productInfo = new SpecialProductInfo();
        productInfo.setId(productId);

        // 如果是驳回状态，直接更新
        if (approveStatus == 2) {
            productInfo.setApproveStatus(2);
        }
        // 否则保持原状态或设为审核中
        else {
            productInfo.setApproveStatus(0);
        }

        specialProductInfoService.updateById(productInfo);
    }

    /**
     * 更新产品主表的最终审核状态
     */
    private void updateFinalApproveStatus(String productId, Integer approveStatus) {
        SpecialProductInfo productInfo = new SpecialProductInfo();
        productInfo.setId(productId);

        // 最终审核状态：通过或驳回
        productInfo.setApproveStatus(approveStatus);

        // 如果审核通过，自动发布
        if (approveStatus == 1) {
            productInfo.setPublishState(1);
        }

        specialProductInfoService.updateById(productInfo);
    }
}
