package com.hxdi.nmjl.mapper.specialproduct;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductNotice;
import com.hxdi.nmjl.condition.specialproduct.SpecialProductNoticeCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地方特色产品征集公告Mapper接口
 */
public interface SpecialProductNoticeMapper extends SuperMapper<SpecialProductNotice> {

    Page<SpecialProductNotice> selectPageV1(Page<SpecialProductNotice> page, @Param("condition") SpecialProductNoticeCondition condition);

    List<SpecialProductNotice> selectListV1(@Param("condition") SpecialProductNoticeCondition condition);
}