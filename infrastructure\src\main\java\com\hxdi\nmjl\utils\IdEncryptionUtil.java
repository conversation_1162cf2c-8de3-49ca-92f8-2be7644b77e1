package com.hxdi.nmjl.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * ID加密工具类
 * 将19位ID加密为8位固定长度的随机字符串
 * 冲突概率约为 1/(62^8) ≈ 0.000000045%
 */
public class IdEncryptionUtil {

    private static final String BASE62_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int ENCODED_LENGTH = 8; // 固定输出长度
    private static final MessageDigest md;

    static {
        try {
            md = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }

    /**
     * 加密19位ID为8位固定长度字符串
     *
     * @param id 19位ID字符串
     * @return 8位加密后的字符串
     */
    public static String encrypt(String id) {
        if (id == null || id.length() != 19) {
            throw new IllegalArgumentException("ID must be exactly 19 characters long");
        }

        // 使用SHA-256生成哈希
        byte[] hashBytes;
        synchronized (md) {
            md.reset();
            hashBytes = md.digest(id.getBytes(StandardCharsets.UTF_8));
        }

        // 取前8字节转换为Base62
        return toBase62(hashBytes, ENCODED_LENGTH);
    }

    /**
     * 将字节数组转换为Base62编码的固定长度字符串
     */
    private static String toBase62(byte[] bytes, int length) {
        StringBuilder result = new StringBuilder();

        // 使用字节数组生成固定长度的Base62字符串
        for (int i = 0; i < length; i++) {
            // 使用多个字节组合来增加随机性
            int index = Math.abs(
                    (bytes[i % bytes.length] & 0xFF) ^
                            (bytes[(i + 8) % bytes.length] & 0xFF) ^
                            (bytes[(i + 16) % bytes.length] & 0xFF)
            ) % 62;
            result.append(BASE62_CHARS.charAt(index));
        }

        return result.toString();
    }

    /**
     * 验证加密后的字符串格式是否正确
     */
    public static boolean isValidEncryptedId(String encryptedId) {
        if (encryptedId == null || encryptedId.length() != ENCODED_LENGTH) {
            return false;
        }

        for (char c : encryptedId.toCharArray()) {
            if (BASE62_CHARS.indexOf(c) == -1) {
                return false;
            }
        }

        return true;
    }
}