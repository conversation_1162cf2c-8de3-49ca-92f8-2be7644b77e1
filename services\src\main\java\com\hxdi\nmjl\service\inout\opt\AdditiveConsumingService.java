package com.hxdi.nmjl.service.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.opt.AdditiveConsuming;
import com.hxdi.nmjl.condition.inout.AdditiveConsumingCondition;

import java.util.List;

/**
 * 添加剂管理服务接口
 *
 * <AUTHOR>
 * @since 2025-04-22 10:02:10
 */
public interface AdditiveConsumingService extends IBaseService<AdditiveConsuming> {
    /**
     * 保存或更新
     *
     * @param additiveConsuming 添加剂管理信息
     */
    void saveOrUpdateV1(AdditiveConsuming additiveConsuming);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AdditiveConsuming> pages(AdditiveConsumingCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AdditiveConsuming> lists(AdditiveConsumingCondition condition);

    /**
     * 审核
     *
     * @param id   添加剂管理id
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id   添加剂管理id
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     *
     * @param id 添加剂管理id
     */
    void submit(String id);

    /**
     * 删除
     *
     * @param id 添加剂管理id
     */
    void remove(String id);
}
