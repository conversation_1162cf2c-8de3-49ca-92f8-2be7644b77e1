package com.hxdi.nmjl.controller.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.mobilization.MobilizeChefCondition;
import com.hxdi.nmjl.domain.mobilization.MobilizeChefInfo;
import com.hxdi.nmjl.service.mobilization.MobilizeChefService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <厨师动员信息管理接口>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "厨师动员信息管理")
@RestController
@RequestMapping("/chef")
public class MobilizeChefController extends BaseController<MobilizeChefService, MobilizeChefInfo> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<MobilizeChefInfo>> getPages(MobilizeChefCondition condition) {
        return ResultBody.ok().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<MobilizeChefInfo>> getList(MobilizeChefCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<MobilizeChefInfo> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除厨师动员信息")
    @DeleteMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.ok().data(bizService.delete(id));
    }

    @ApiOperation(value = "审核")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @PostMapping("/reject")
    public ResultBody reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "提交")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody MobilizeChefInfo cook) {
        if (CommonUtils.isEmpty(cook.getId())) {
            bizService.add(cook);
        } else {
            bizService.update(cook);
        }
        return ResultBody.ok();
    }
}
