package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.BrandCrisis;
import com.hxdi.nmjl.condition.plan.BrandCrisisCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BrandCrisisMapper extends SuperMapper<BrandCrisis> {

    Page<BrandCrisis> selectPageV1(Page<BrandCrisis> page,@Param("condition") BrandCrisisCondition condition);

    List<BrandCrisis> selectListV1(@Param("condition") BrandCrisisCondition condition);
}
