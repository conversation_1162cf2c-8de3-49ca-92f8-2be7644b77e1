package com.hxdi.nmjl.condition.mobilization;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MobilizedProductCatalogCondition extends QueryCondition {

    @ApiModelProperty(value = "关键字")
    private String keywords;

    @ApiModelProperty(value = "状态：0-未确认，1-已确认")
    private Integer state;

    @ApiModelProperty(value = "品种ID")
    private String categoryId;

}
