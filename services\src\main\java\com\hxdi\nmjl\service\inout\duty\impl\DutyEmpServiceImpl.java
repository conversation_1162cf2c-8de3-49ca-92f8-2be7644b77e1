package com.hxdi.nmjl.service.inout.duty.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.inout.duty.DutyEmp;
import com.hxdi.nmjl.mapper.inout.duty.DutyEmpMapper;
import com.hxdi.nmjl.service.inout.duty.DutyEmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class DutyEmpServiceImpl extends BaseServiceImpl<DutyEmpMapper, DutyEmp> implements DutyEmpService {

    @Override
    public void remove(String dutyId) {
        baseMapper.delete(Wrappers.<DutyEmp>lambdaQuery().eq(DutyEmp::getDutyPlanId, dutyId));
    }

    @Override
    public List<DutyEmp> getList(String dutyId) {
        return baseMapper.selectList(Wrappers.<DutyEmp>lambdaQuery().eq(DutyEmp::getDutyPlanId , dutyId));
    }
}
