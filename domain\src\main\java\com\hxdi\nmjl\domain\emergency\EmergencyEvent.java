package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 应急事件信息
 */
@ApiModel(description = "应急事件信息")
@Getter
@Setter
@TableName("B_EMERGENCY_EVENT") // 表名映射
public class EmergencyEvent implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("EVENT_CODE")
    @ApiModelProperty(value = "事件代码")
    private String eventCode;

    @TableField("EVENT_NAME")
    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @TableField("EVENT_TYPE")
    @ApiModelProperty(value = "事件类型")
    private Integer eventType;

    @TableField("EVENT_LEVEL")
    @ApiModelProperty(value = "事件级别")
    private Integer eventLevel;

    @TableField("RESP_CODE")
    @ApiModelProperty(value = "预案编号")
    private String respCode;

    @TableField("RESP_NAME")
    @ApiModelProperty(value = "预案名称")
    private String respName;

    @TableField("EVENT_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "发生时间")
    private Date eventTime;

    @TableField("PROVINCE_ID")
    @ApiModelProperty(value = "省")
    private String provinceId;

    @TableField("CITY_ID")
    @ApiModelProperty(value = "市")
    private String cityId;

    @TableField("COUNTY_ID")
    @ApiModelProperty(value = "区县")
    private String countyId;

    @TableField("AREA")
    @ApiModelProperty(value = "地址")
    private String area;

    @TableField("LON")
    @ApiModelProperty(value = "经度")
    private String lon;

    @TableField("LAT")
    @ApiModelProperty(value = "纬度")
    private String lat;

    @TableField("DESCS")
    @ApiModelProperty(value = "情况描述")
    private String descs;

    @TableField("END_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @TableField("STATE")
    @ApiModelProperty(value = "事件状态：1-进行中，2-已结束")
    private Integer state;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除", example = "1")
    private Integer enabled;
//attention
    @TableField("ATTACHMENT")
    @ApiModelProperty(value = "附件")
    private String attachment;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
