package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlanDetail;
import com.hxdi.nmjl.condition.plan.ProcurementPlanDetailCondition;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ProcurementPlanDetailMapper extends SuperMapper<GrainProcurementPlanDetail> {

    Page<GrainProcurementPlanDetail> selectPageV1(Page<GrainProcurementPlanDetail> page, ProcurementPlanDetailCondition condition);

    void saveBatch(List<GrainProcurementPlanDetail> planDetail);

}
