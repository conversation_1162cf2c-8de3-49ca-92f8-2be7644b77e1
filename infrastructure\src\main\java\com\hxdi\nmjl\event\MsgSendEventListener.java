package com.hxdi.nmjl.event;

import com.hxdi.common.core.utils.JsonConverter;
import com.hxdi.msg.client.model.vo.MsgTempateSend;
import com.hxdi.nmjl.feign.MsgSeviceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 消息发送事件监听器
 * @author: 王贝强
 * @create: 2025-07-08 19:54
 */
@Component
@Slf4j
public class MsgSendEventListener {

    @Resource
    MsgSeviceClient msgSeviceClient;

    @EventListener
    public void handleMsgSendEvent(MsgSendEvent event){
        try {
            if(event.getPayload().isEmpty()){
                return;
            }
            log.info("收到消息发送事件: 事件来源->{},消息模版编码->{}，消息数量->{}", event.getSource().toString(), event.getPayload().get(0).getMsgCode(),event.getPayload().size());
            MsgTempateSend send = convert(event);
            msgSeviceClient.sendMessageByTemplate(send);
            log.info("消息发送完成");
        }catch (Exception e){
            log.error("消息发送失败",e);
        }

    }

    protected MsgTempateSend convert(MsgSendEvent event){
        List<MsgPayload> payload = event.getPayload();
        MsgTempateSend send = new MsgTempateSend();

        List<String> msgCodes = new ArrayList<>(payload.size());
        List<String> refIds = new ArrayList<>(payload.size());
        List<String> jsonParamsList = new ArrayList<>(payload.size());
        List<String> receivers = new ArrayList<>(payload.size());
        for(MsgPayload msgPayload : payload){
            refIds.add(msgPayload.getRefId());
            msgCodes.add(msgPayload.getMsgCode());
            String json = JsonConverter.toJsonWithPretty(msgPayload.getJsonParams());
            jsonParamsList.add(json);
            receivers.add(msgPayload.getReceiver());
        }

        send.setMsgCodes(msgCodes);
        send.setRefIds(refIds);
        send.setJsonParamsList(jsonParamsList);
        send.setReceivers(receivers);
        return send;
    }
}
