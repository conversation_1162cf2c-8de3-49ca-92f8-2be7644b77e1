package com.hxdi.nmjl.service.iot.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.iot.DeviceInfoCondition;
import com.hxdi.nmjl.domain.iot.DeviceInfo;
import com.hxdi.nmjl.mapper.iot.DeviceInfoMapper;
import com.hxdi.nmjl.service.iot.DeviceInfoService;
import com.hxdi.nmjl.utils.RedisKeys;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

@Service
public class DeviceInfoServiceImpl extends BaseServiceImpl<DeviceInfoMapper, DeviceInfo> implements DeviceInfoService {

    @Override
    @Cacheable(value = RedisKeys.Prefix.TEM_HUM_DEVICE, key = "#serial", condition = "#result !=null")
    public DeviceInfo findBySerial(String serial) {
        if (CommonUtils.isEmpty(serial)) return null;
        LambdaQueryWrapper<DeviceInfo> wrapper = new LambdaQueryWrapper<DeviceInfo>()
                .eq(DeviceInfo::getSerial, serial);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeys.Prefix.TEM_HUM_DEVICE, key = "#deviceInfo.serial")
    public void insertOrUpdate(DeviceInfo deviceInfo) {
        //非库点用户新增时，设置数据权限为库点
        if (!SecurityHelper.isStationUser()) {
            deviceInfo.setDataHierarchyId(deviceInfo.getStoreId());
        }
        //检查设备序列号是否存在
        LambdaQueryWrapper<DeviceInfo> wrapper = new LambdaQueryWrapper<DeviceInfo>()
                .eq(DeviceInfo::getSerial, deviceInfo.getSerial())
                .eq(DeviceInfo::getEnabled, 1);
        if (CommonUtils.isNotEmpty(deviceInfo.getId())) {
            wrapper.ne(DeviceInfo::getId, deviceInfo.getId());
        }
        Long count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BaseException("该设备序列号:["+deviceInfo.getSerial()+"]已存在!");
        }
        saveOrUpdate(deviceInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeys.Prefix.TEM_HUM_DEVICE, key = "#serial")
    public void changeDeviceState(Serializable id, String serial, Integer state) {
        DeviceInfo device = getById(id);
        device.setDeviceState(state);
        updateById(device);
    }

    @Override
    public List<DeviceInfo> getList(DeviceInfoCondition condition) {
        return baseMapper.getList(condition);
    }

    @Override
    public Page<DeviceInfo> getPage(DeviceInfoCondition condition) {
        Page<DeviceInfo> page = condition.newPage();
        return baseMapper.getPage(condition, page);
    }
}
