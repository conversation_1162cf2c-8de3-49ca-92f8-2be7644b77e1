<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.portal.CmsResourcesMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.portal.CmsResources">
    <!--@mbg.generated-->
    <!--@Table CMS_RESOURCES-->
    <id column="ID" property="id" />
    <result column="CATEGORY_ID" property="categoryId" />
    <result column="MODULE" property="module" />
    <result column="TITLE" property="title" />
    <result column="IMG" property="img" />
    <result column="CONTENT" property="content" />
    <result column="SOURCE" property="source" />
    <result column="AUTHOR" property="author" />
    <result column="REF_URL" property="refUrl" />
    <result column="PUBLIC_IS" property="publicIs" />
    <result column="DISPLAY_IS" property="displayIs" />
    <result column="PUBLISH_TIME" property="publishTime" />
    <result column="SORTS" property="sorts" />
    <result column="HITS" property="hits" />
    <result column="PUBLISH_STATE" property="publishState" />
    <result column="ENABLED" property="enabled" />
    <result column="ATTACHMENT" property="attachment" />
    <result column="CATEGORY_PATH" property="categoryPath" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CATEGORY_ID, MODULE, TITLE, IMG, CONTENT, "SOURCE", AUTHOR, REF_URL, PUBLIC_IS, DISPLAY_IS,
    PUBLISH_TIME, SORTS, HITS, PUBLISH_STATE, ENABLED, ATTACHMENT, CREATE_TIME, UPDATE_TIME,
    CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,CATEGORY_PATH
  </sql>

  <select id="selectPageV1" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from CMS_RESOURCES
    <where>
      ENABLED = 1
      <if test="@plugins.OGNL@isNotEmpty(condition.categoryPath)">
        and CATEGORY_PATH like concat(#{condition.categoryPath},'%')
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.title)">
        and TITLE like concat('%',#{condition.title},'%')
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.publicIs)">
        and PUBLIC_IS = #{condition.publicIs}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.displayIs)">
        and DISPLAY_IS = #{condition.displayIs}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.publishState)">
        and PUBLISH_STATE = #{condition.publishState}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
        and PUBLISH_TIME between #{condition.startTime} and #{condition.endTime}
      </if>
    </where>
    order by SORTS,  CREATE_TIME desc
  </select>

  <select id="selectPageForPortal" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from CMS_RESOURCES
    <where>
        PUBLISH_STATE = 1 and ENABLED = 1
      <if test="@plugins.OGNL@isNotEmpty(condition.categoryId)">
        and CATEGORY_ID = #{condition.categoryId}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.module)">
        and MODULE = #{condition.module}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.title)">
        and TITLE like concat('%',#{condition.title},'%')
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.publicIs)">
        and PUBLIC_IS = #{condition.publicIs}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.displayIs)">
        and DISPLAY_IS = #{condition.displayIs}
      </if>
    </where>
    order by SORTS,  PUBLISH_TIME desc
  </select>
</mapper>
