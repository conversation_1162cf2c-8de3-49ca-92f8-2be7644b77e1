package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.plan.BrandAuthorizing;
import com.hxdi.nmjl.service.plan.BrandAuthorizingService;
import com.hxdi.nmjl.condition.plan.BrandAuthorizingCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 品牌授权管理
 <AUTHOR>
 @version 1.0
 @since 2025/7/7
 */
@Api(tags = "品牌授权管理")
@RestController
@RequestMapping("/brand/authorizing")
public class BrandAuthorizingController extends BaseController<BrandAuthorizingService, BrandAuthorizing> {

    @ApiOperation("分页查询授权记录")
    @GetMapping("/page")
    public ResultBody<Page<BrandAuthorizing>> page(BrandAuthorizingCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }


    @ApiOperation("列表查询授权记录")
    @GetMapping("/list")
    public ResultBody<List<BrandAuthorizing>> list(BrandAuthorizingCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }


    @ApiOperation("保存/修改授权记录")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody BrandAuthorizing authorizing) {
        if(CommonUtils.isEmpty(authorizing.getId())) {
            bizService.create(authorizing);
        } else {
            bizService.update(authorizing);
        }
        return ResultBody.ok();
    }


    @ApiOperation("查看授权详情")
    @GetMapping("/getDetail")
    public ResultBody<BrandAuthorizing> getDetail(@RequestParam String authorizeId) {
        return ResultBody.ok().data(bizService.getDetail(authorizeId));
    }


    @ApiOperation("审批授权")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String authorizeId,
                              @RequestParam Integer approveStatus,
                              @RequestParam(required = false) String opinion) {
        bizService.approve(authorizeId, approveStatus, opinion);
        return ResultBody.ok();
    }

    @ApiOperation("提交")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String authorizeId) {
        bizService.submit(authorizeId);
        return ResultBody.ok();
    }


    @ApiOperation("删除授权记录")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }



    @ApiOperation("变更授权状态为无效")
    @PutMapping("/updateInvalidState")
    public ResultBody updateInvalidState(@RequestParam String authorizeId) {
        bizService.updateInvalidState(authorizeId);
        return ResultBody.ok();
    }


    @ApiOperation("检查并更新授权状态")
    @PostMapping("/checkAndUpdateExpiredState")
    public void checkAndUpdateExpiredState() {
        LocalDateTime today = LocalDate.now()
                .atTime(0, 0, 0);
        // 定时任务
        bizService.checkAndUpdateExpiredState(today);
    }
}