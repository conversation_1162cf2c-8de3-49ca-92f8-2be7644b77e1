package com.hxdi.nmjl.service.inout.opt.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.inout.opt.ProcessRecord;
import com.hxdi.nmjl.mapper.inout.opt.ProcessRecordMapper;
import com.hxdi.nmjl.service.inout.opt.ProcessRecordService;
import com.hxdi.nmjl.condition.inout.ProcessRecordCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 加工工艺备案Service实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ProcessRecordServiceImpl extends BaseServiceImpl<ProcessRecordMapper, ProcessRecord> implements ProcessRecordService {
    @Override
    public void saveOrUpdateV1(ProcessRecord processRecord) {
        if (CommonUtils.isEmpty(processRecord.getId())) {
            verifyRecord(processRecord);
            baseMapper.insert(processRecord);
        } else {
            ProcessRecord existing = this.getById(processRecord.getId());
            if (existing.getApproveStatus() == 1 || existing.getApproveStatus() == 2) {
                throw new BaseException("该单据正在审核中，无法更新");
            }
            baseMapper.updateById(processRecord);
        }
    }

    @Override
    public Page<ProcessRecord> pages(ProcessRecordCondition condition) {
        Page<ProcessRecord> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<ProcessRecord> lists(ProcessRecordCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void approve(String id, String approveOpinion) {
        // 审核状态：0-未审核，1-待审核，2-已审核，3-驳回
        changeApproveStatus(id, 2, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 3, approveOpinion);
    }

    @Override
    public void submit(String id) {
        changeApproveStatus(id, 1, null);
    }

    /**
     * 更新审核状态
     *
     * @param id            加工工艺备案ID
     * @param approveStatus 审核状态
     * @param approveOpinion          审核意见
     */
    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        ProcessRecord processRecord = new ProcessRecord();
        processRecord.setId(id);
        processRecord.setApproveStatus(approveStatus);
        if (approveStatus == 2 || approveStatus == 3) {
            processRecord.setApprover(SecurityHelper.obtainUser().getNickName());
            processRecord.setApproveTime(new Date());
            processRecord.setApproveOpinion(approveOpinion);
        }
        baseMapper.updateById(processRecord);
    }

    /**
     * 验证数据有效性
     *
     * @param processRecord 加工工艺备案
     */
    private void verifyRecord(ProcessRecord processRecord) {
        long count = baseMapper.selectCount(Wrappers.<ProcessRecord>lambdaQuery()
                .eq(ProcessRecord::getStoreId, processRecord.getStoreId())
                .eq(ProcessRecord::getCatalogId, processRecord.getCatalogId()));

        if (count > 0) {
            throw new BaseException("该库点已存在该品种的加工工艺备案，请勿重复创建");
        }
    }
}
