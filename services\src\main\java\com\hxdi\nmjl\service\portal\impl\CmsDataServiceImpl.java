package com.hxdi.nmjl.service.portal.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.portal.CmsData;
import com.hxdi.nmjl.mapper.portal.CmsDataMapper;
import com.hxdi.nmjl.service.portal.CmsDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 门户-数据集服务
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/21 11:33
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class CmsDataServiceImpl extends BaseServiceImpl<CmsDataMapper, CmsData> implements CmsDataService {

    @Override
    public CmsData getData(String dataId) {
        return baseMapper.selectById(dataId);
    }
}
