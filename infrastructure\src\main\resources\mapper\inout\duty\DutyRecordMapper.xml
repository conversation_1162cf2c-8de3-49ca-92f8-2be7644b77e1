<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.duty.DutyRecordMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.duty.DutyRecord">
        <id column="ID" property="id" />
        <result column="DUTY_PLAN_ID" property="dutyPlanId" />
        <result column="DUTY_NAME" property="dutyName" />
        <result column="STORE_ID" property="storeId" />
        <result column="STORE_NAME" property="storeName" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="DUTY_WAY" property="dutyWay" />
        <result column="AREA" property="area" />
        <result column="EMP_ID" property="empId" />
        <result column="EMP_NAME" property="empName" />
        <result column="MOBILE" property="mobile" />
        <result column="FZR" property="fzr" />
        <result column="REMARKS" property="remarks" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,DUTY_PLAN_ID,DUTY_NAME,STORE_ID,STORE_NAME,START_TIME,
        END_TIME,DUTY_WAY,AREA,EMP_ID,EMP_NAME,
        MOBILE,FZR,REMARKS,CREATE_TIME,UPDATE_TIME,
        CREATE_ID,UPDATE_ID,TENANT_ID,DATA_HIERARCHY_ID
    </sql>

    <select id="selectPageV1" resultType="com.hxdi.nmjl.domain.inout.duty.DutyRecord">
        SELECT <include refid="Base_Column_List"/> from B_DUTY_RECORD
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.inout.duty.DutyRecord">
        SELECT <include refid="Base_Column_List"/> from B_DUTY_RECORD
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>


</mapper>
