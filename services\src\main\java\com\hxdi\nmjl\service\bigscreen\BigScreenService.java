package com.hxdi.nmjl.service.bigscreen;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.nmjl.condition.bigscreen.OrderMonthRecordCondition;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.condition.common.WarningInfoCondition;
import com.hxdi.nmjl.condition.plan.ProcurementPlanCondition;
import com.hxdi.nmjl.domain.bigscreen.BigScreenConfig;
import com.hxdi.nmjl.domain.bigscreen.OrgPoint;
import com.hxdi.nmjl.domain.common.WarningInfo;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.vo.bigscreen.AreaOrderStatVO;
import com.hxdi.nmjl.vo.bigscreen.InventorySumAndCapacityVO;
import com.hxdi.nmjl.vo.bigscreen.ProcurementPlanSummaryVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @program: nmjl-service
 * @description: 大屏服务接口
 * @author: 王贝强
 * @create: 2025-07-28 15:20
 */
public interface BigScreenService {
    //-----------------大屏配置--------------------

    /**
     * 根据配置Key获取对应的大屏配置
     *
     * @param ConfigKey 配置Key
     * @return
     */
    List<BigScreenConfig> getScreenConfig(String ConfigKey);

    //------------------订单及供销------------------

    /**
     * 获取区域每月订单汇总统计数据(默认统计内蒙古自治区)
     *
     * @param condition 查询条件
     * @return 每月汇总统计数据列表
     */
    List<AreaOrderStatVO> getAreaMonthlyOrderSummary(OrderMonthRecordCondition condition);

    /**
     * 获取区域每月按品种分类的订单汇总统计数据(默认统计内蒙古自治区)
     *
     * @param condition 查询条件
     * @return 每月汇总统计数据列表
     */
    List<AreaOrderStatVO> getAreaMonthlyCategoryOrderSummary(OrderMonthRecordCondition condition);

    //----------------机构信息------------------


    /**
     * 获取该区域下所有机构坐标
     *
     * @param areaCode
     * @return
     */
    List<OrgPoint> getOrgPoint(String areaCode);

    /**
     * 获取该区域下各类机构的数量(默认内蒙古自治区)
     *
     * @param areaCode
     * @return
     */
    Map<Integer, Integer> getOrgNum(String areaCode);

    //----------------预警信息------------------

    /**
     * 获取预警数据
     *
     * @param condition
     * @return
     */
    Page<WarningInfo> getWarningInfoPage(WarningInfoCondition condition);

    //----------------库存总览------------------

    /**
     * 获取库存总览数据(按配置分类返回的对应品类的数量(吨))
     *
     * @param areaCode 区域代码 ','分隔
     * @return 每个配置品类对应的库存数量
     */
    Map<String, BigDecimal> getInventorySummary(String areaCode);

    /**
     * 获取库存总览分页数据(吨)
     *
     * @param condition
     * @return
     */
    Page<Inventory> getInventorySummaryPage(InventoryCondition condition);

    //----------------仓储能力------------------

    /**
     * 大屏统计仓储能力总览： 根据军供站ID列表查询库存数量与仓容(吨)
     * 根据传入的地区编码，查询下属所有军供站的库存数量与仓容，并按下一级地区进行分组汇总
     *
     * @param areaCode
     * @return
     */
    List<InventorySumAndCapacityVO> getInventorySumAndCapacity(String areaCode);

    /**
     * 大屏统计仓储能力分页查询： 根据军供站ID列表查询库存数量与仓容(吨)
     * 根据传入的地区编码，查询下属所有军供站的库存数量与仓容，并以仓房为单位进行统计
     *
     * @param areaCode
     * @param storeId
     * @return
     */
    Page<InventorySumAndCapacityVO> getInventorySumAndCapacityPage(String areaCode, String storeId);

    //----------------粮源筹措------------------


    /**
     * 大屏筹措计划总览：获取指定城市的筹措计划统计
     * 传入当前用户地区编码，获取该地区的筹措计划统计
     *
     * @param areaCode 当前用户地区编码
     * @return
     */
    ProcurementPlanSummaryVO getPlanSummary(String areaCode);

    /**
     * 大屏筹措计划分页查询：获取指定地区下的各个军供站的各个品类的筹措计划
     *
     * @param condition 查询条件
     * @return
     */
    Page<ProcurementPlanSummaryVO> getPlanSummaryPage(ProcurementPlanCondition condition);

}
