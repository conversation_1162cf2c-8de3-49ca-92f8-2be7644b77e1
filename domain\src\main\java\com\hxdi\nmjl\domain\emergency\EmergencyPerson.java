package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 应急人员信息
 */
@ApiModel(description = "应急人员信息")
@Getter
@Setter
@TableName("B_EMERGNECY_PERSON") // 表名映射
public class EmergencyPerson {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("ORG_ID")
    @ApiModelProperty(value = "主管单位ID")
    private String orgId;

    @TableField("ORG_NAME")
    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    @TableField("NAME")
    @ApiModelProperty(value = "姓名")
    private String name;

    @TableField("SEX")
    @ApiModelProperty(value = "性别")
    private Integer sex;

    @TableField("UNIT_NAME")
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @TableField("JOB_TITLE")
    @ApiModelProperty(value = "职务")
    private String jobTitle;

    @TableField("BUSINESS")
    @ApiModelProperty(value = "主管业务")
    private String business;

    @TableField("OFFICE_TEL")
    @ApiModelProperty(value = "办公电话")
    private String officeTel;

    @TableField("MOBILE")
    @ApiModelProperty(value = "手机号")
    private String mobile;

    @TableField("EMAIL")
    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @TableField("NOTES")
    @ApiModelProperty(value = "备注")
    private String notes;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
