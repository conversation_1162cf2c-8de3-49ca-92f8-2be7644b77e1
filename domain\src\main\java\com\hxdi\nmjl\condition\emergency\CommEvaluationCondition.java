package com.hxdi.nmjl.condition.emergency;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import com.hxdi.common.core.mybatis.annotation.Between;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


@Getter
@Setter
public class CommEvaluationCondition extends QueryCondition {
    @ApiModelProperty(value = "上次更新时间")
    @Between(value = "EVALUATION_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private RangeBean<Date> updateTime;

    @ApiModelProperty(value = "评估标题")
    @LIKE
    private String title;

    @ApiModelProperty(value = "评估对象")
    @LIKE
    private String evaluationObject;

    @ApiModelProperty(value = "评估状态")
    @EQ
    private Integer evaluateStatus;
}