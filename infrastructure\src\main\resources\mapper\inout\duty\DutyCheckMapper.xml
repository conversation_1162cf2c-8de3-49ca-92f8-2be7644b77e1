<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.duty.DutyCheckMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.duty.DutyCheck">
        <id column="ID" property="id" />
        <result column="DUTY_PLAN_ID" property="dutyPlanId" />
        <result column="DUTY_NAME" property="dutyName" />
        <result column="STORE_ID" property="storeId" />
        <result column="STORE_NAME" property="storeName" />
        <result column="START_TIME" property="startTime" />
        <result column="CHECK_WAY" property="checkWay" />
        <result column="AREA" property="area" />
        <result column="EMP_NAME" property="empName" />
        <result column="REMARKS" property="remarks" />
        <result column="REQUIREMENT" property="requirement" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,DUTY_PLAN_ID,DUTY_NAME,STORE_ID,STORE_NAME,START_TIME,
        CHECK_WAY,AREA,EMP_NAME,REMARKS,REQUIREMENT,
        CREATE_TIME,UPDATE_TIME,CREATE_ID,UPDATE_ID,TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from B_DUTY_CHECK
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> from B_DUTY_CHECK
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.planStartTime) and @plugins.OGNL@isNotEmpty(condition.planEndTime)">
                and CREATE_TIME between #{condition.planStartTime} and #{condition.planEndTime}
            </if>
        </where>
    </select>

</mapper>
