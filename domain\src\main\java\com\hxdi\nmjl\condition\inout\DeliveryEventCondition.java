package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 配送事件记录查询条件
 *
 * <AUTHOR>
 * @since 2025/4/23 15:55
 */
@Getter
@Setter
@ApiOperation(value = "配送事件记录查询条件")
public class DeliveryEventCondition extends QueryCondition {

    @ApiModelProperty(value = "司机手机号")
    private String mobile;

    @ApiModelProperty(value = "事件开始时间")
    private Date startTime;

    @ApiModelProperty(value = "事件结束时间")
    private Date endTime;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
}
