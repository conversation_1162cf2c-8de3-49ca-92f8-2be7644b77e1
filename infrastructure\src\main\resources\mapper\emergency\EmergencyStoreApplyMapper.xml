<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyStoreApplyMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyStoreApply">
    <!--@mbg.generated-->
    <!--@Table B_EMERGENCY_STORE_APPLY-->
    <id column="ID" property="id" />
    <result column="APPLY_CODE" property="applyCode" />
    <result column="ORG_ID" property="orgId" />
    <result column="ORG_NAME" property="orgName" />
    <result column="STORE_ID" property="storeId" />
    <result column="STORE_NAME" property="storeName" />
    <result column="CLASSIFICATION_ID" property="classificationId" />
    <result column="CLASSIFICATION_NAME" property="classificationName" />
    <result column="LOSS_TOTAL" property="lossTotal" />
    <result column="APPLY_QTY" property="applyQty" />
    <result column="APPLICANT" property="applicant" />
    <result column="APPLICANT_TEL" property="applicantTel" />
    <result column="APPLY_TIME" property="applyTime" />
    <result column="ESTIMATED_TIME" property="estimatedTime" />
    <result column="STATE" property="state" />
    <result column="APPROVER" property="approver" />
    <result column="APPROVE_TIME" property="approveTime" />
    <result column="APPROVE_STATE" property="approveState" />
    <result column="APPROVE_OPINION" property="approveOpinion" />
    <result column="NOTES" property="notes" />
    <result column="ENABLED" property="enabled" />
    <result column="ATTACHMENT" property="attachment" />
    <result column="STAT_ID" property="statId" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    <result column="ACCOUNT_STATE" property="accountState" />
    <result column="ACCOUNT_AMOUNT" property="accountAmount" />
    <result column="ACCOUNT_TIME" property="accountTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, APPLY_CODE, ORG_ID, ORG_NAME, STORE_ID, STORE_NAME, CLASSIFICATION_ID, CLASSIFICATION_NAME,
    LOSS_TOTAL, APPLY_QTY, APPLICANT, APPLICANT_TEL, APPLY_TIME, ESTIMATED_TIME, "STATE",
    APPROVER, APPROVE_TIME, APPROVE_STATE, APPROVE_OPINION, NOTES, ENABLED, ATTACHMENT,
    STAT_ID, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,
    ACCOUNT_STATE, ACCOUNT_AMOUNT, ACCOUNT_TIME
  </sql>

</mapper>
