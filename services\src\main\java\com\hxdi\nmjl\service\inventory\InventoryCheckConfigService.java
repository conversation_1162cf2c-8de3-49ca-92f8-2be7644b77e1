package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.InventoryCheckConfig;
import com.hxdi.nmjl.condition.inventory.InventoryCheckConfigCondition;

import java.util.List;

/**
 * 盘点任务管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
public interface InventoryCheckConfigService extends IBaseService<InventoryCheckConfig> {

    /**
     * 新增盘点计划
     *
     * @param config 盘点计划实体
     */
    void create(InventoryCheckConfig config);

    /**
     * 更新盘点计划
     *
     * @param config 盘点计划实体
     */
    void update(InventoryCheckConfig config);

    /**
     * 根据ID查询盘点计划详情
     *
     * @param uniqueKey 盘点计划ID或库点ID
     * @return 盘点计划实体
     */
    InventoryCheckConfig getByUniqueKey(String uniqueKey);

    /**
     * 分页查询盘点计划
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<InventoryCheckConfig> pages(InventoryCheckConfigCondition condition);

    /**
     * 列表查询盘点计划
     *
     * @param condition 查询条件
     * @return 列表结果
     */
    List<InventoryCheckConfig> lists(InventoryCheckConfigCondition condition);

    /**
     * 修改盘点计划状态
     *
     * @param id    盘点计划ID
     * @param state 状态值：0-禁用，1-启用，7-删除
     */
    void changeState(String id, Integer state);

    /**
     * 更新盘点计划时间为下一次计划时间
     * @param checkConfigId
     */
    void updateToNextPlanTime(String checkConfigId);
}