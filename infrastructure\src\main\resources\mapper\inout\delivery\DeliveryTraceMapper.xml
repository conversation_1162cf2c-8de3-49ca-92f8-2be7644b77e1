<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.delivery.DeliveryTraceMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.delivery.DeliveryTrace">
        <!--@Table B_DELIVERY_TRACE-->
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="deliveryId" column="DELIVERY_ID" jdbcType="VARCHAR"/>
        <result property="deliveryState" column="DELIVERY_STATE" jdbcType="INTEGER"/>
        <result property="attachments" column="ATTACHMENTS" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        DELIVERY_ID,
        DELIVERY_STATE,
        ATTACHMENTS,
        CREATE_TIME,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>


</mapper>

