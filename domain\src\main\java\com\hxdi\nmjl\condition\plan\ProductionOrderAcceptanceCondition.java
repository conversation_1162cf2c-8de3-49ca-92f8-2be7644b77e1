package com.hxdi.nmjl.condition.plan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.model.QueryCondition;
import com.hxdi.common.core.mybatis.annotation.Between;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.annotation.ORDER;
import com.hxdi.common.core.mybatis.base.support.OrderItem;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 生产订单验收查询条件
 * @author: 王贝强
 * @create: 2025-08-18 17:27
 */
@Getter
@Setter
@ApiModel("生产订单验收查询条件")
public class ProductionOrderAcceptanceCondition extends QueryCondition {

    @ApiModelProperty(value = "模糊查询条件：库点名称、客户名称、品种名称、生产批次号、订单编号")
    @LIKE(value = "STORE_NAME,CLIENT_NAME,CATALOG_NAME,BATCH_NO,ORDER_CODE", logic = "OR", join = true)
    private String keywords;

    @EQ
    @ApiModelProperty(value = "生产订单ID")
    private String orderId;

    @EQ
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @EQ
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @EQ
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @EQ
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    @EQ
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    @EQ
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private final Integer enabled = 1;

    @ApiModelProperty(value="创建时间")
    @Between
    private RangeBean<Date> createTime;

    @ORDER
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private final OrderItem orderItem = OrderItem.desc("create_time");




}
