package com.hxdi.nmjl.condition.specialproduct;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@ApiModel(description = "地方特色产品征集公告查询条件")
@Getter
@Setter
public class SpecialProductNoticeCondition extends QueryCondition {


    @ApiModelProperty(value = "公告标题（模糊查询）")
    private String title;

    @ApiModelProperty(value = "发布单位")
    private String orgName;

    @ApiModelProperty(value = "发布人")
    private String publisher;

    @ApiModelProperty(value = "创建时间开始")
    private LocalDateTime createStartTime;

    @ApiModelProperty(value = "创建时间结束")
    private LocalDateTime createEndTime;

    @ApiModelProperty(value = "发布状态")
    private Integer publishState;

}
