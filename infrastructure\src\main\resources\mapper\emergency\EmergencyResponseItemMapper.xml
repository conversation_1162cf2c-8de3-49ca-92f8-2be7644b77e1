<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyResponseItemMapper">
    <resultMap id="EmergencyResponseItemResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyResponseItem">
        <!-- 主键 -->
        <id property="id" column="ID" />
        <!-- 预案ID -->
        <result property="responseId" column="RESPONSE_ID" />
        <!-- 品种ID -->
        <result property="catalogId" column="CATALOG_ID" />
        <!-- 品种名称 -->
        <result property="catalogName" column="CATALOG_NAME" />
        <!-- 规格 -->
        <result property="specification" column="SPECIFICATION" />
        <!-- 质量等级 -->
        <result property="grade" column="GRADE" />
        <!-- 计划数量 -->
        <result property="planQty" column="PLAN_QTY" />
        <!-- 租户ID -->
        <result property="tenantId" column="TENANT_ID" />
        <!-- 组织 -->
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, RESPONSE_ID, CATALOG_ID, CATALOG_NAME, SPECIFICATION, GRADE, PLAN_QTY, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <!-- 可根据需要添加其他操作语句 -->
</mapper>
