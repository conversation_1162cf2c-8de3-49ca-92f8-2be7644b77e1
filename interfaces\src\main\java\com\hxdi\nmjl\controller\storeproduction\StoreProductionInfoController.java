package com.hxdi.nmjl.controller.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionInfo;
import com.hxdi.nmjl.service.storeproduction.StoreProductionInfoService;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionInfoCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 库存生产信息管理
 <AUTHOR>
 @version 1.0
 @since 2025/8/5
 */
@Api (tags = "库存生产信息管理")
@RestController
@RequestMapping ("/storeProductionInfo")
public class StoreProductionInfoController extends BaseController<StoreProductionInfoService, StoreProductionInfo> {

    @ApiOperation ("分页查询库存生产信息")
    @GetMapping ("/page")
    public ResultBody<Page<StoreProductionInfo>> page(StoreProductionInfoCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation ("列表查询库存生产信息")
    @GetMapping ("/list")
    public ResultBody<List<StoreProductionInfo>> list(StoreProductionInfoCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation ("保存/修改库存生产信息")
    @PostMapping ("/saveOrUpdate")
    public ResultBody saveOrUpdate (@RequestBody StoreProductionInfo info) {
        if (CommonUtils.isEmpty (info.getId ())) {
            bizService.create(info);
        } else {
            bizService.update(info);
        }
        return ResultBody.ok();
    }

    @ApiOperation ("查看库存生产信息详情")
    @GetMapping ("/getDetail")
    public ResultBody<StoreProductionInfo> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation ("删除库存生产信息")
    @PostMapping ("/remove")
    public ResultBody remove (@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }
}
