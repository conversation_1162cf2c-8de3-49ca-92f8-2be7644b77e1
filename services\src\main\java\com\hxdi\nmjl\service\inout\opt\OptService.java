package com.hxdi.nmjl.service.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.opt.OptInfo;
import com.hxdi.nmjl.condition.inout.OptCondition;

import java.util.List;

public interface OptService extends IBaseService<OptInfo> {
    /**
     * 新增
      * @param optInfo
     */
    void create(OptInfo optInfo);

    /**
     * 修改
     * @param optInfo
     */
    void updating(OptInfo optInfo);

    /**
     * 详情
     * @param optId
     * @return
     */
    OptInfo detail(String optId);

    /**
     * 删除
     * @param optId
     */
    void delete(String optId);

    /**
     * 分页查询
     * @param optCondition
     * @return
     */
    Page<OptInfo> getPage(OptCondition optCondition);

    /**
     * 列表查询
      * @param optCondition
     * @return
     */
    List<OptInfo> getList(OptCondition optCondition);
}
