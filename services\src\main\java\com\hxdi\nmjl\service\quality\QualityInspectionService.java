package com.hxdi.nmjl.service.quality;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.quality.QualityInspection;
import com.hxdi.nmjl.condition.quality.QualityInspectionCondition;
import com.hxdi.nmjl.vo.quality.QualityInspectionVO;

import java.io.Serializable;
import java.util.List;

public interface QualityInspectionService extends IBaseService<QualityInspection> {

    void SaveOrUpdate(QualityInspectionVO result);

    /**
     * 根据检验Id查询检验信息及详情
     * @param id
     * @return
     */
    QualityInspectionVO getDetail(Serializable id);

    /**
     * 根据条件查询检验信息列表（带用户权限控制）
     * @param condition
     * @return
     */
    List<QualityInspection> getList(QualityInspectionCondition condition);

    /**
     * 根据条件查询检验信息分页列表（带用户权限控制）
     * @param condition
     * @return
     */
    Page<QualityInspection> getPage(QualityInspectionCondition condition);

    /**
     * 根据检验Id删除检验信息
      * @param id
     */
    void removeid(String id);
}
