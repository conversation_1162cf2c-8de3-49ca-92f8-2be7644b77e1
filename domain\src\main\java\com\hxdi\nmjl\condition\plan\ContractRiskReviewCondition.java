package com.hxdi.nmjl.condition.plan;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.model.QueryCondition;
import com.hxdi.common.core.mybatis.annotation.Between;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.annotation.ORDER;
import com.hxdi.common.core.mybatis.base.support.OrderItem;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 合同风险查询条件
 * @author: 王贝强
 * @create: 2025-08-18 10:22
 */
@Getter
@Setter
@ApiModel(description = "合同风险查询条件")
public class ContractRiskReviewCondition extends QueryCondition {

    @EQ
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @EQ
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    @EQ
    @ApiModelProperty(value = "风险类型：1-供应商风险，2-合同条款风险，3-执行风险，4-财务风险，5-法律风险，9-其他风险")
    private String riskType;

    @EQ
    @ApiModelProperty(value = "风险等级：1-低风险，2-中风险，3-高风险，4-极高风险")
    private String riskLevel;

    @EQ
    @ApiModelProperty(value = "影响程度：1-轻微，2-一般，3-严重，4-非常严重")
    private String impactLevel;

    @EQ
    @ApiModelProperty(value = "发生概率：1-很低，2-较低，3-中等，4-较高，5-很高")
    private String probability;

    @EQ
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer enabled = 1;

    @ApiModelProperty(value = "模糊搜索： 合同编号、合同名称、客户名称、机构名称")
    @LIKE(value = "CONTRACT_CODE,CONTRACT_NAME,CLIENT_NAME,ORG_NAME", logic = "OR", join = true)
    private String search;

    @ApiModelProperty(value = "创建时间")
    @Between("CREATE_TIME")
    private RangeBean<Date> createTime;

    @ORDER
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private final OrderItem orderItem = OrderItem.desc("create_time");

}
