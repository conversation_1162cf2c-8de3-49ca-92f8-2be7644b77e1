package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.emergency.EmergencyResponseItem;
import com.hxdi.nmjl.domain.plan.OperatingPlanDetail;
import com.hxdi.nmjl.mapper.plan.OperatingPlanDetailMapper;
import com.hxdi.nmjl.service.plan.OperatingPlanDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class OperatingPlanDetailServiceImpl extends BaseServiceImpl<OperatingPlanDetailMapper, OperatingPlanDetail> implements OperatingPlanDetailService {
    @Override
    public List<OperatingPlanDetail> getList(List<String> planCode){
        return baseMapper.selectList(Wrappers.<OperatingPlanDetail>lambdaQuery().in(OperatingPlanDetail::getPlanCode, planCode));
    }

}
