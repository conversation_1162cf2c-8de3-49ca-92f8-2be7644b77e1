package com.hxdi.nmjl.mapper.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.model.ColumnKey;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.base.ClientInfo;
import com.hxdi.nmjl.condition.base.ClientInfoCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ClientInfoMapper extends SuperMapper<ClientInfo> {

    @DataPermission(alias = "r", column = "data_hierarchy_id", key = ColumnKey.PID_ORGID)
    List<ClientInfo> selectListV1(@Param("client") ClientInfoCondition condition);

    List<ClientInfo> selectListV2(@Param("client") ClientInfoCondition condition);

    @DataPermission(alias = "r", column = "data_hierarchy_id", key = ColumnKey.PID_ORGID)
    Page<ClientInfo> selectPageV1(@Param("page") Page<ClientInfo> page, @Param("client") ClientInfoCondition condition);

    Page<ClientInfo> selectPageV2(@Param("page") Page<ClientInfo> page, @Param("client") ClientInfoCondition condition);

    /**
     * 通过关键字（name, creditCode）搜索列表
     * @param condition
     * @return
     */
    List<ClientInfo> searchList(@Param("condition") ClientInfoCondition condition);

    /**
     * 逻辑删除
     * @param id
     * @param userId
     */
    void deleteSoft(@Param("clientId")String id, @Param("userId")String userId);


}
