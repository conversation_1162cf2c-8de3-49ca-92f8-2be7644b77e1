package com.hxdi.nmjl.mapper.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.common.WarningInfo;
import com.hxdi.nmjl.condition.common.WarningInfoCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WarningInfoMapper extends SuperMapper<WarningInfo> {

    @DataPermission
    Page<WarningInfo> getPageByCondition(@Param("page") IPage<WarningInfo> page, @Param("condition") WarningInfoCondition condition);

    @DataPermission
    List<WarningInfo> getListByCondition(@Param("condition") WarningInfoCondition condition);
}
