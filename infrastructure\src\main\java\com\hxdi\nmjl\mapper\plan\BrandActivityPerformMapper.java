package com.hxdi.nmjl.mapper.plan;

import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.BrandActivityPerform;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BrandActivityPerformMapper extends SuperMapper<BrandActivityPerform> {

    List<BrandActivityPerform> selectListV1(@Param("activityId") String activityId);
}
