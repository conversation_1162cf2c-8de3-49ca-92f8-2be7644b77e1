package com.hxdi.nmjl.service.inventory;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.TraceInfo;

/**
 * 追溯信息接口
 * @author: 王贝强
 * @create: 2025-03-10 15:12
 */
public interface TraceInfoService extends IBaseService<TraceInfo> {

    /**
     * 判断该批次是否已经存在
     * @param inventoryId
     * @param batchNum
     * @return
     */
    boolean exists(String inventoryId, String batchNum);
}
