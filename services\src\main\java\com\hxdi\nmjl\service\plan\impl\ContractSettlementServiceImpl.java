package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.ProductionOrderCondition;
import com.hxdi.nmjl.condition.plan.SaleCondition;
import com.hxdi.nmjl.domain.plan.ContractInfo;
import com.hxdi.nmjl.domain.plan.ContractSettlement;
import com.hxdi.nmjl.domain.plan.ProductionOrder;
import com.hxdi.nmjl.domain.plan.SaleOrder;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.ContractSettlementMapper;
import com.hxdi.nmjl.service.plan.ContractInfoService;
import com.hxdi.nmjl.service.plan.ContractSettlementService;
import com.hxdi.nmjl.service.plan.ProductionOrderService;
import com.hxdi.nmjl.service.plan.SaleOrderService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同结算服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ContractSettlementServiceImpl extends BaseServiceImpl<ContractSettlementMapper, ContractSettlement> implements ContractSettlementService {
    @Resource
    private ContractInfoService contractInfoService;

    @Resource
    private ProductionOrderService productionOrderService;

    @Resource
    private SaleOrderService saleOrderService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public void create(ContractSettlement contractSettlement) {
        // 获取合同信息
        ContractInfo contractInfo = contractInfoService.getById(Long.parseLong(contractSettlement.getContractId()));
        if (contractInfo == null) {
            throw new BaseException("合同信息不存在");
        }
        //生成编码
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("SETTLEMENT_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        contractSettlement.setSettlementCode((String) businessCode.getValue());

        contractSettlement.setEnabled(1);

        // 保存主表
        this.save(contractSettlement);
    }


    @Override
    public void submit(String contractSettlementId, String orderIds) {
        if (CommonUtils.isBlank(orderIds)) {
            return;
        }

        List<String> orderIdList = Arrays.asList(orderIds.split(","));
        ContractSettlement settlement = this.getById(contractSettlementId);
        if (settlement == null) {
            throw new BaseException("结算记录不存在");
        }

        BigDecimal settlementAmount;
        int settlementStatus;

        // 根据业务类型处理不同的订单
        if (settlement.getBizType().equals("1")) {
            // 处理生产订单
            settlementAmount = processProductionOrders(orderIdList);
            settlementStatus = calculateProductionSettlementStatus(settlement, orderIdList);
        } else {
            // 处理销售订单
            settlementAmount = processSaleOrders(orderIdList);
            settlementStatus = calculateSaleSettlementStatus(settlement, orderIdList);
        }

        // 更新结算信息
        settlement.setSettlementTotalAmount(settlementAmount);
        settlement.setSettlementProgress(calculateProgress(settlementAmount, settlement.getContractTotalAmount()));
        settlement.setSettlementStatus(settlementStatus);

        this.updateById(settlement);
    }

    @Override
    public ContractSettlement get(String contractId) {
        LambdaQueryWrapper<ContractSettlement> queryWrapper = new LambdaQueryWrapper<ContractSettlement>()
                .eq(ContractSettlement::getContractId,contractId)
                .eq(ContractSettlement::getEnabled,1);

        return this.getOne(queryWrapper);
    }

    /**
     * 处理生产订单结算
     */
    private BigDecimal processProductionOrders(List<String> orderIdList) {
        // 更新订单结算状态
        productionOrderService.updateSettlementStatus(String.join(",", orderIdList));

        // 计算结算金额
        return calculateProductionOrderAmount(orderIdList);
    }

    /**
     * 处理销售订单结算
     */
    private BigDecimal processSaleOrders(List<String> orderIdList) {
        // 更新订单结算状态
        saleOrderService.updateSettlementStatus(String.join(",", orderIdList));

        // 计算结算金额
        return calculateSaleOrderAmount(orderIdList);
    }

    /**
     * 计算生产订单结算状态
     */
    private int calculateProductionSettlementStatus(ContractSettlement settlement, List<String> orderIdList) {
        ProductionOrderCondition condition = new ProductionOrderCondition();
        condition.setSettlementState(0);
        condition.setContractCode(settlement.getContractCode());
        List<ProductionOrder> allOrders = productionOrderService.listV1(condition);

        List<String> allOrderIds = allOrders.stream().map(ProductionOrder::getId).collect(Collectors.toList());
        return new HashSet<>(orderIdList).containsAll(allOrderIds) ? 3 : 2;
    }

    /**
     * 计算销售订单结算状态
     */
    private int calculateSaleSettlementStatus(ContractSettlement settlement, List<String> orderIdList) {
        SaleCondition condition = new SaleCondition();
        condition.setSettlementState(0);
        condition.setSearch(settlement.getContractCode());
        List<SaleOrder> allOrders = saleOrderService.lists(condition);

        List<String> allOrderIds = allOrders.stream().map(SaleOrder::getId).collect(Collectors.toList());
        return new HashSet<>(orderIdList).containsAll(allOrderIds) ? 3 : 2;
    }

    /**
     * 计算生产订单结算金额
     */
    private BigDecimal calculateProductionOrderAmount(List<String> orderIdList) {
        return orderIdList.stream()
                .map(productionOrderService::getDetail)
                .filter(order -> order != null && order.getDetailList() != null)
                .flatMap(order -> order.getDetailList().stream())
                .filter(item -> item.getPrice() != null && item.getCompletedQty() != null)
                .map(item -> item.getPrice().multiply(item.getCompletedQty()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算销售订单结算金额
     */
    private BigDecimal calculateSaleOrderAmount(List<String> orderIdList) {
        return orderIdList.stream()
                .map(saleOrderService::getDetail)
                .filter(order -> order != null && order.getDetailList() != null)
                .flatMap(order -> order.getDetailList().stream())
                .filter(item -> item.getPrice() != null && item.getCompletedQty() != null)
                .map(item -> item.getPrice().multiply(item.getCompletedQty()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算结算进度
     */
    private BigDecimal calculateProgress(BigDecimal settlementAmount, BigDecimal contractTotalAmount) {
        if (contractTotalAmount == null || contractTotalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal progress = settlementAmount
                .divide(contractTotalAmount, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));

        // 限制进度最大值为100%，避免数据库字段溢出
        return progress.compareTo(new BigDecimal("100")) > 0 ? new BigDecimal("100") : progress;
    }

}







