package com.hxdi.nmjl.utils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/23 2:38 下午
 * @description 用于消除VO对象，可添加全局索引Key，实体分组
 */
public enum GlobalAdditionalKey {

    /**
     * 全局通用索引Key
     */
    EMM_NAME("emmName"),
    ALIAS("alias"),
    EMM_IMG_URL("imgUrl"),
    CLASSIFICATION_NAME("classificationName"),
    BRAND_NAME("brandName"),
    MODEL_NAME("modelName"),
    SPECIFICATION("specification"),
    EMM_UNIT("unit"),
    PROVIDER_NAME("providerName"),
    CLIENT_NAME("clientName"),
    WAREHOUSE_NAME("warehouseName"),
    ORGAN_NAME("organName"),
    PARENT_ORGAN_NAME("parentOrganName");

    public final String key;

    GlobalAdditionalKey(String key) {
        this.key = key;
    }

    /**
     * ----------针对前端展示需求，可以根据实体定义额外信息的键
     * 目的消除多余Bean定义
     */

    public enum ClientInfo {
        DAILY_PRODUCTION("dailyProduction");
        public final String key;

        ClientInfo(String key) {
            this.key = key;
        }
    }

    public enum Catalog {
        LEVEL0("level0"),
        LEVEL1("level1"),
        LEVEL2("level2"),
        LEVEL3("level3"),
        LEVEL4("level4");
        public final String key;

        Catalog(String key) {
            this.key = key;
        }
    }

    public enum Stock {
        EMM_NAME("emmName"),
        SPECIFICATIONS("specifications"),
        CLASSIFICATION_NAME("classificationName"),
        ENTERPRISE_NAME("enterpriseName"),
        ORGAN_NAME("organName"),
        WAREHOUSE_NAME("warehouseName"),
        PROVIDER_NAME("providerName"),
        DIFF_NUM("diffNum"),
        UNIT("unit"),
        ALIAS("alias"),
        MODEL("model"),
        BRAND("brand"),
        STORETIME("storeTime");
        public final String key;

        Stock(String key) {
            this.key = key;
        }
    }
}
