package com.hxdi.nmjl.service.emergency;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStat;
import com.hxdi.nmjl.domain.emergency.EmergencyStoreApply;

import java.util.List;

/**
 * 应急补库申请服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:10
 */
public interface EmergencyStoreApplyService extends IBaseService<EmergencyStoreApply> {


    /**
     * 审批
     * @param id
     * @param approveOpinion
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     * @param id
     * @param approveOpinion
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     * @param id
     */
    void submit(String id);

    /**
     * 完成
     * @param id
     */
    void complete(String id);

    /**
     * 结算
     * @param emergencyStoreApply
     */
    void accounted(EmergencyStoreApply emergencyStoreApply);

    /**
     * 查询应急损耗统计列表
     * @param storeId
     */
    List<EmergencyLossStat> queryLossStatList(String storeId);
}
