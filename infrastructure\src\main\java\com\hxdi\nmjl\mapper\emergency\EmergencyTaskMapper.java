package com.hxdi.nmjl.mapper.emergency;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.emergency.EmergencyTaskCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应急任务Mapper接口
 */
@Mapper
public interface EmergencyTaskMapper extends SuperMapper<EmergencyTask> {

    Page<EmergencyTask> selectPageV1(Page<EmergencyTask> page, @Param("condition") EmergencyTaskCondition condition);

    List<EmergencyTask> selectListV1(@Param("condition") EmergencyTaskCondition condition);
}
