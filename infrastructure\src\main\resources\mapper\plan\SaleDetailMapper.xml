<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.SaleDetailMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.SaleDetail">
    <!--@mbg.generated-->
    <!--@Table B_SALE_DETAIL-->
    <id column="ID" property="id" />
    <result column="CATALOG_ID" property="catalogId" />
    <result column="CATALOG_NAME" property="catalogName" />
    <result column="BRAND" property="brand" />
    <result column="GRADE" property="grade" />
    <result column="SPECIFICATION" property="specification" />
    <result column="PRODUCT_DATE" property="productDate" />
    <result column="CONTRACT_QTY" property="contractQty" />
    <result column="COMPLETED_QTY" property="completedQty" />
    <result column="RESERVE_LEVEL" property="reserveLevel" />
    <result column="PRICE" property="price" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONTRACT_ID, CATALOG_ID, "CATALOG_NAME", BRAND, GRADE, SPECIFICATION, PRODUCT_DATE,
    CONTRACT_QTY, COMPLETED_QTY, RESERVE_LEVEL, PRICE, TENANT_ID, DATA_HIERARCHY_ID
  </sql>

</mapper>
