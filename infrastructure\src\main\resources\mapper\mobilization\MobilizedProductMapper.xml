<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.mobilization.MobilizedProductMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.mobilization.MobilizedProduct">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="ENTERPRISE_ID" jdbcType="VARCHAR" property="enterpriseId" />
        <result column="CLASSIFICATION_ID" jdbcType="VARCHAR" property="classificationId" />
        <result column="CLASSIFICATION_NAME" jdbcType="VARCHAR" property="classificationName" />
        <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId" />
        <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
        <result column="GRADE" jdbcType="VARCHAR" property="grade" />
        <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification" />
        <result column="PRICE" jdbcType="DECIMAL" property="price" />
        <result column="ORIGIN" jdbcType="VARCHAR" property="origin" />
        <result column="UNIT" jdbcType="VARCHAR" property="unit" />
        <result column="AVG_PRODUCT_CAP" jdbcType="VARCHAR" property="avgProductCap" />
        <result column="AREA" jdbcType="VARCHAR" property="area" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="STATE" property="state"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, ENTERPRISE_ID, CLASSIFICATION_ID, CLASSIFICATION_NAME,
        CATALOG_ID, CATALOG_NAME, GRADE, SPECIFICATION, PRICE,
        ORIGIN, UNIT, AVG_PRODUCT_CAP, AREA, ENABLED, STATE,
        CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID,
        TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_MOBILIZED_PRODUCT
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                AND (CATALOG_NAME like concat('%', #{condition.keywords}, '%') or CLASSIFICATION_NAME like concat('%', #{condition.keywords}, '%'))
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseId)">
                AND ENTERPRISE_ID = #{condition.enterpriseId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.grade)">
                AND GRADE = #{condition.grade}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                AND STATE = #{condition.state}
            </if>
        </where>
    </select>
    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_MOBILIZED_PRODUCT
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                AND (CATALOG_NAME like concat('%', #{condition.keywords}, '%') or CLASSIFICATION_NAME like concat('%', #{condition.keywords}, '%'))
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.enterpriseId)">
                AND ENTERPRISE_ID = #{condition.enterpriseId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.grade)">
                AND GRADE = #{condition.grade}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.state)">
                AND STATE = #{condition.state}
            </if>
        </where>
    </select>
</mapper>
