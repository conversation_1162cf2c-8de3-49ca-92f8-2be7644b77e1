package com.hxdi.nmjl.domain.mobilization;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**

 动员协议
 */
@Getter
@Setter
@TableName (value = "B_MOBILIZED_CONTRACT")
public class MobilizedContract implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;
    /**
     动员企业 id
     */
    @TableField (value = "ENTERPRISE_ID")
    private String enterpriseId;
    /**
     动员企业名称
     */
    @TableField (value = "ENTERPRISE_NAME")
    private String enterpriseName;
    /**
     合同编号
     */
    @TableField (value = "CONTRACT_CODE")
    private String contractCode;
    /**
     原合同编号
     */
    @TableField (value = "ORIGIN_CODE")
    private String originCode;
    /**
     合同名称
     */
    @TableField (value = "NAME")
    private String name;
    /**
     业务类型：字典 DYXYLX
     */
    @TableField (value = "BIZ_TYPE")
    private String bizType;
    /**
     机构 id
     */
    @TableField (value = "ORG_ID")
    private String orgId;
    /**
     机构名称
     */
    @TableField (value = "ORG_NAME")
    private String orgName;
    /**
     计划数量
     */
    @TableField (value = "QTY")
    private BigDecimal qty;
    /**
     协议状态：0 - 未生效，1 - 已生效，2 - 已结束，3 - 已终止
     */
    @TableField (value = "STATE", fill = FieldFill.INSERT)
    private Integer state;
    /**
     金额
     */
    @TableField (value = "TOTAL_AMOUNT")
    private BigDecimal totalAmount;
    /**
     军供站 id
     */
    @TableField (value = "STORE_ID")
    private String storeId;
    /**
     军供站名称
     */
    @TableField (value = "STORE_NAME")
    private String storeName;
    /**
     备注
     */
    @TableField (value = "NOTES")
    private String notes;
    /**
     签订人
     */
    @TableField (value = "SIGNER")
    private String signer;
    /**
     签订人身份证号
     */
    @TableField (value = "SID_CARD")
    private String sidCard;
    /**
     签订时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "SIGNED_DATE")
    private Date signedDate;
    /**
     生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "START_DATE")
    private Date startDate;
    /**
     截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "END_DATE")
    private Date endDate;
    /**
     审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "APPROVE_TIME")
    private Date approveTime;
    /**
     审核状态
     */
    @TableField (value = "APPROVE_STATUS")
    private Integer approveStatus;
    /**
     审核人
     */
    @TableField (value = "APPROVER")
    private String approver;
    /**
     审核意见
     */
    @TableField (value = "APPROVE_OPINION")
    private String approveOpinion;
    /**
     状态：1 - 有效，0 - 删除
     */
    @TableField (value = "ENABLED")
    private Integer enabled;
    /**
     创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField (value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     创建 id
     */
    @TableField (value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;
    /**
     更新 id
     */
    @TableField (value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;
    /**
     租户 id
     */
    @TableField (value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;
    /**
     组织
     */
    @TableField (value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
    /**
     附件
     */
    @TableField (value = "ATTACHMENT")
    private String attachment;

    /**
     * ---------------------以下非实体字段---------------------
     *
     */

    /**
     * 计划详情
     */
    @TableField(exist = false)
    private List<MobilizedContractDetail> detailList;
}
