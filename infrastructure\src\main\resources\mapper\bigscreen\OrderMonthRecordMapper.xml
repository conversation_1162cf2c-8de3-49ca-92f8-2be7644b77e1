<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.bigscreen.OrderMonthRecordMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.bigscreen.OrderMonthRecord">
        <!--@Table B_ORDER_MONTH_RECORD-->
        <id column="ID" property="id"/>
        <result column="ORDER_CODE" property="orderCode"/>
        <result column="ORDER_TYPE" property="orderType"/>
        <result column="STATISTICAL_DATE" property="statisticalDate"/>
        <result column="ORG_ID" property="orgId"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="AREA_CODE" property="areaCode"/>
        <result column="STORE_ID" property="storeId"/>
        <result column="STORE_NAME" property="storeName"/>
        <result column="CLIENT_ID" property="clientId"/>
        <result column="CLIENT_NAME" property="clientName"/>
        <result column="CATALOG_ID" property="catalogId"/>
        <result column="CATALOG_NAME" property="catalogName"/>
        <result column="BRAND" property="brand"/>
        <result column="GRADE" property="grade"/>
        <result column="SPECIFICATION" property="specification"/>
        <result column="ORDER_QTY" property="orderQty"/>
        <result column="COMPLETED_QTY" property="completedQty"/>
        <result column="ORDER_PACK_QTY" property="orderPackQty"/>
        <result column="RESERVE_LEVEL" property="reserveLevel"/>
        <result column="PRICE" property="price"/>
        <result column="BATCH_NO" property="batchNo"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
    </resultMap>

    <!-- 新增：订单汇总统计查询 -->
    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.bigscreen.OrderMonthRecord">
        SELECT
        MAX(ID) as ID,
        ORDER_CODE,
        ORDER_TYPE,
        MAX(STATISTICAL_DATE) as STATISTICAL_DATE,
        ORG_ID,
        MAX(ORG_NAME) as ORG_NAME,
        AREA_CODE,
        STORE_ID,
        MAX(STORE_NAME) as STORE_NAME,
        CLIENT_ID,
        MAX(CLIENT_NAME) as CLIENT_NAME,
        CATALOG_ID,
        MAX(CATALOG_NAME) as CATALOG_NAME,
        MAX(BRAND) as BRAND,
        MAX(GRADE) as GRADE,
        MAX(SPECIFICATION) as SPECIFICATION,
        MAX(ORDER_QTY) as ORDER_QTY,
        SUM(COMPLETED_QTY) as COMPLETED_QTY,
        MAX(ORDER_PACK_QTY) as ORDER_PACK_QTY,
        MAX(RESERVE_LEVEL) as RESERVE_LEVEL,
        MAX(PRICE) as PRICE,
        MAX(BATCH_NO) as BATCH_NO,
        MAX(DATA_HIERARCHY_ID) as DATA_HIERARCHY_ID
        FROM B_ORDER_MONTH_RECORD
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
                AND STATISTICAL_DATE &gt;= #{condition.startTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
                AND STATISTICAL_DATE &lt;= #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orderType)">
                AND ORDER_TYPE = #{condition.orderType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientIds)">
                AND CLIENT_ID IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.clientIds)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeIds)">
                AND STORE_ID IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeIds)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgIds)">
                AND ORG_ID IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.orgIds)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogIds)">
                AND CATALOG_ID IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.catalogIds)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.areaCodes)">
                AND AREA_CODE IN
                <foreach item="item" index="index" collection="condition.codes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY ORDER_CODE
        ORDER BY STATISTICAL_DATE DESC
    </select>

    <!-- 新增：订单汇总统计分页查询 -->
    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT 
            MAX(ID) as ID,
            ORDER_CODE,
            ORDER_TYPE,
            MAX(STATISTICAL_DATE) as STATISTICAL_DATE,
            ORG_ID,
            MAX(ORG_NAME) as ORG_NAME,
            AREA_CODE,
            STORE_ID,
            MAX(STORE_NAME) as STORE_NAME,
            CLIENT_ID,
            MAX(CLIENT_NAME) as CLIENT_NAME,
            CATALOG_ID,
            MAX(CATALOG_NAME) as CATALOG_NAME,
            MAX(BRAND) as BRAND,
            MAX(GRADE) as GRADE,
            MAX(SPECIFICATION) as SPECIFICATION,
            MAX(ORDER_QTY) as ORDER_QTY,
            SUM(COMPLETED_QTY) as COMPLETED_QTY,
            MAX(ORDER_PACK_QTY) as ORDER_PACK_QTY,
            MAX(RESERVE_LEVEL) as RESERVE_LEVEL,
            MAX(PRICE) as PRICE,
            MAX(BATCH_NO) as BATCH_NO,
            MAX(DATA_HIERARCHY_ID) as DATA_HIERARCHY_ID
        FROM B_ORDER_MONTH_RECORD
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
                AND STATISTICAL_DATE &gt;= #{condition.startTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
                AND STATISTICAL_DATE &lt;= #{condition.endTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orderType)">
                AND ORDER_TYPE = #{condition.orderType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientIds)">
                AND CLIENT_ID IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.clientIds)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeIds)">
                AND STORE_ID IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeIds)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgIds)">
                AND ORG_ID IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.orgIds)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogIds)">
                AND CATALOG_ID IN
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.catalogIds)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.areaCodes)">
                AND AREA_CODE IN
                <foreach item="item" index="index" collection="condition.codes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY ORDER_CODE
        ORDER BY STATISTICAL_DATE DESC
    </select>
</mapper>