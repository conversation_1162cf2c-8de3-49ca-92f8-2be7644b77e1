package com.hxdi.nmjl.service.bigscreen;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.base.CatalogCondition;
import com.hxdi.nmjl.condition.base.ClassificationCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.Classification;
import com.hxdi.nmjl.domain.bigscreen.BigScreenConfig;
import com.hxdi.nmjl.enums.BigScreenEnum;
import com.hxdi.nmjl.mapper.bigscreen.BigScreenConfigMapper;
import com.hxdi.nmjl.service.base.CatalogService;
import com.hxdi.nmjl.service.base.ClassificationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: nmjl-service
 * @description: 大屏配置服务
 * @author: 王贝强
 * @create: 2025-07-29 13:49
 */
@Service
public class BigScreenConfigService extends BaseServiceImpl<BigScreenConfigMapper, BigScreenConfig> {

    @Resource
    private CatalogService catalogService;

    @Resource
    private ClassificationService classificationService;

    /**
     * 根据配置项枚举获取对应的配置值
     * @param bigScreenEnum
     * @return
     */
    public List<BigScreenConfig> getConfigListByEnum(BigScreenEnum bigScreenEnum) {
        return baseMapper.selectList(Wrappers.<BigScreenConfig>lambdaQuery()
                .eq(BigScreenConfig::getKey, bigScreenEnum.getKey())
                .eq(BigScreenConfig::getEnabled, 1));
    }

    /**
     * 根据配置项Key获取对应的配置值
     * @param bigScreenConfigKey
     * @return
     */
    public List<BigScreenConfig> getConfigListByKey(String bigScreenConfigKey) {
        return baseMapper.selectList(Wrappers.<BigScreenConfig>lambdaQuery()
                .eq(BigScreenConfig::getKey, bigScreenConfigKey)
                .eq(BigScreenConfig::getEnabled, 1));
    }

    /**
     * 构建品种分类配置
     * @return
     */
    public List<BigScreenConfig> buildCategoryClassification() {
        List<BigScreenConfig> classificationList = this.getConfigListByEnum(BigScreenEnum.CATEGORY_CLASSIFICATION);
        List<Classification> allClassificationList = classificationService.lists(new ClassificationCondition());
        List<Catalog> allCatalogList = catalogService.lists(new CatalogCondition());
        
        // 构建分类代码到分类ID的映射
        Map<String, String> classificationCodeToIdMap = allClassificationList.stream()
                .collect(Collectors.toMap(
                        Classification::getClassificationCode,
                        Classification::getId,
                        (existing, replacement) -> existing
                ));
        
        // 获取已配置的顶级分类ID集合（通过分类代码转换为ID）
        Set<String> configuredTopClassificationIds = classificationList.stream()
                .map(config -> classificationCodeToIdMap.get(config.getValue()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        // 构建分类的父子关系映射
        Map<String, List<Classification>> parentChildMap = allClassificationList.stream()
                .collect(Collectors.groupingBy(classification -> 
                        classification.getParentId() != null ? classification.getParentId() : "0"));
        
        // 获取每个顶级分类下的所有子分类ID（包括自己）
        Map<String, Set<String>> topClassToAllSubClassMap = configuredTopClassificationIds.stream()
                .collect(Collectors.toMap(
                        topClassId -> topClassId,
                        topClassId -> getAllSubClassificationIds(topClassId, parentChildMap)
                ));
        
        // 构建分类ID到品种ID的映射
        Map<String, List<String>> classificationToCatalogMap = allCatalogList.stream()
                .collect(Collectors.groupingBy(
                        Catalog::getClassificationId,
                        Collectors.mapping(Catalog::getId, Collectors.toList())
                ));
        
        // 按配置对需要的品种进行分类
        classificationList.forEach(config -> {
            String topClassId = classificationCodeToIdMap.get(config.getValue());
            if (topClassId != null) {
                Set<String> allSubClassIds = topClassToAllSubClassMap.get(topClassId);
                if (allSubClassIds != null) {
                    List<String> catalogIds = allSubClassIds.stream()
                            .flatMap(classId -> classificationToCatalogMap.getOrDefault(classId, Collections.emptyList()).stream())
                            .collect(Collectors.toList());
                    config.setCatalogId(catalogIds);
                }
            }
        });
        
        // 获取所有已配置的子分类ID
        Set<String> allConfiguredSubClassIds = topClassToAllSubClassMap.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        
        // 对于不在配置中的分类对应的品种，归为其他分类
        List<String> otherCatalogIds = allCatalogList.stream()
                .filter(catalog -> !allConfiguredSubClassIds.contains(catalog.getClassificationId()))
                .map(Catalog::getId)
                .collect(Collectors.toList());

        // 设置其他分类
        BigScreenConfig otherConfig = new BigScreenConfig();
        otherConfig.setKey(BigScreenEnum.CATEGORY_CLASSIFICATION.getKey());
        otherConfig.setValue("0000");
        otherConfig.setDesc("其它");
        otherConfig.setCatalogId(otherCatalogIds);
        classificationList.add(otherConfig);
        
        return classificationList;
    }

    /**
     * 递归获取指定分类下的所有子分类ID（包括自己）
     */
    private Set<String> getAllSubClassificationIds(String parentId, Map<String, List<Classification>> parentChildMap) {
        Set<String> result = new HashSet<>();
        result.add(parentId); // 包括自己
        
        List<Classification> children = parentChildMap.get(parentId);
        if (children != null && !children.isEmpty()) {
            for (Classification child : children) {
                result.addAll(getAllSubClassificationIds(child.getId(), parentChildMap));
            }
        }
        
        return result;
    }
}
