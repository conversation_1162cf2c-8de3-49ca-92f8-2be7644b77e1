package com.hxdi.nmjl.service.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.opt.ProcessRecord;
import com.hxdi.nmjl.condition.inout.ProcessRecordCondition;

import java.util.List;

/**
 * 加工工艺备案接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
public interface ProcessRecordService extends IBaseService<ProcessRecord> {
    /**
     * 保存或更新
     *
     * @param processRecord 加工工艺备案信息
     */
    void saveOrUpdateV1(ProcessRecord processRecord);


    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<ProcessRecord> pages(ProcessRecordCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<ProcessRecord> lists(ProcessRecordCondition condition);

    /**
     * 审核
     *
     * @param id   加工工艺备案id
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id   加工工艺备案id
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     *
     * @param id   加工工艺备案id
     */
    void submit(String id);

}
