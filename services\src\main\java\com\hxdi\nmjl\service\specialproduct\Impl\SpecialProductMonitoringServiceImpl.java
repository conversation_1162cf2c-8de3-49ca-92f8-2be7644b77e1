package com.hxdi.nmjl.service.specialproduct.Impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductMonitoring;
import com.hxdi.nmjl.mapper.specialproduct.SpecialProductMonitoringMapper;
import com.hxdi.nmjl.service.specialproduct.SpecialProductInfoService;
import com.hxdi.nmjl.service.specialproduct.SpecialProductMonitoringService;
import com.hxdi.nmjl.condition.specialproduct.SpecialProductMonitoringCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SpecialProductMonitoringServiceImpl extends BaseServiceImpl<SpecialProductMonitoringMapper, SpecialProductMonitoring> implements SpecialProductMonitoringService {

    @Resource
    private SpecialProductInfoService specialProductInfoService;

    @Override
    public void create(SpecialProductMonitoring monitoring) {
        if (monitoring.getProductId() == null) {
            throw new BaseException("产品ID不能为空");
        }
        SpecialProductInfo specialProductInfo = specialProductInfoService.getById(monitoring.getProductId());
        monitoring.setProductName(specialProductInfo.getProductName());
        // 保存报告
        this.save(monitoring);
    }

    @Override
    public void update(SpecialProductMonitoring monitoring) {
        // 校验报告是否存在
        SpecialProductMonitoring existReport = this.getById(monitoring.getId());
        if (existReport == null) {
            throw new BaseException("分析报告不存在，无法更新");
        }
        SpecialProductInfo specialProductInfo = specialProductInfoService.getById(monitoring.getProductId());
        if (specialProductInfo == null) {
            throw new BaseException("产品不存在，无法更新");
        }
        monitoring.setProductName(specialProductInfo.getProductName());
        // 更新报告
        this.updateById(monitoring);
    }

    @Override
    public void remove(String id) {
        // 校验报告是否存在
        SpecialProductMonitoring report = this.getById(id);
        if (report == null) {
            throw new BaseException("分析报告不存在，无法删除");
        }

        // 逻辑删除
        report.setEnabled(0);
        this.updateById(report);
    }

    @Override
    public SpecialProductMonitoring getDetail(String id) {
        SpecialProductMonitoring report = this.getById(id);
        if (report == null || report.getEnabled() != 1) {
            throw new BaseException("分析报告不存在或已删除");
        }
        return report;
    }

    @Override
    public Page<SpecialProductMonitoring> pages(SpecialProductMonitoringCondition condition) {
        Page<SpecialProductMonitoring> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<SpecialProductMonitoring> lists(SpecialProductMonitoringCondition condition) {
        return baseMapper.selectListV1(condition);
    }


}