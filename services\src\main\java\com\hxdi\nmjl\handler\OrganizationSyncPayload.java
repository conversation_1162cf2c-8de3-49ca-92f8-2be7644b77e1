package com.hxdi.nmjl.handler;

import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.base.Organization;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织机构同步消息载体 (Payload / DTO)
 * 包含 Organization 和 SystemOrgan 的共有或可映射字段，用于消息队列传递。
 * 目标是尽可能直接映射字段，减少转换逻辑。
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationSyncPayload implements Serializable {

    private static final long serialVersionUID = 1L;

    // --- 核心标识 ---
    private String id;          // 主键 (来自 Organization.id 和 SystemOrgan.id)
    private String code;        // 编码 (来自 Organization.orgCode 和 SystemOrgan.code)

    // --- 基本信息 ---
    private String name;        // 名称 (来自 Organization.orgName 和 SystemOrgan.name)
    private String parentId;    // 父级ID (来自 Organization.pid 和 SystemOrgan.parentId)

    /**
     * 类型。
     * 注意：Organization.orgType (1:管理单位, 2:军供站) 与 SystemOrgan.type (1:单位, 2:部门) 含义不同。
     * 此处直接传递值，接收方需要明确如何解释或映射此值。
     */
    private Integer type;       // 类型 (来自 Organization.orgType 或 SystemOrgan.type)

    // --- 联系信息 ---
    private String contactPerson; // 联系人 (来自 Organization.fzr 和 SystemOrgan.contact)
    private String contactPhone;  // 联系电话 (来自 Organization.mobile 和 SystemOrgan.tel)

    // --- 状态与排序 ---
    /**
     * 状态。
     * 映射规则:
     * Organization.enabled (1:启用) -> 1
     * Organization.enabled (0:禁用, 7:删除) -> 0
     * SystemOrgan.state (1:启用) -> 1
     * SystemOrgan.state (0:禁用) -> 0
     * 从 Payload 转换回实体时:
     * 1 -> Organization.enabled = 1, SystemOrgan.state = 1
     * 0 -> Organization.enabled = 0, SystemOrgan.state = 0
     * 注意：Organization 的删除状态(7) 在此简化映射中会丢失，同步时接收方可能需要特殊逻辑处理删除场景。
     */
    private Integer status;     // 状态 (来自 Organization.enabled 或 SystemOrgan.state)

    private Integer sortOrder;  // 排序 (来自 Organization.sorts 和 SystemOrgan.seq)

    // --- 路径与租户 ---
    /**
     * 组织路径。
     */
    private String path;        // 路径 (来自 Organization.paths 或 SystemOrgan.organPath)
    private String tenantId;    // 租户ID (来自 Organization.tenantId 和 SystemOrgan.tenantId)

    // --- 审计信息 ---
    private Date createTime;    // 创建时间
    private String createId;    // 创建者ID
    private Date updateTime;    // 更新时间
    private String updateId;    // 更新者ID


    // --- 转换方法 ---

    /**
     * 从 Organization 创建 Payload
     */
    public static OrganizationSyncPayload fromOrganization(Organization org) {
        if (org == null || CommonUtils.isEmpty(org.getId())) {
            return null;
        }
        OrganizationSyncPayload payload = new OrganizationSyncPayload();
        if(CommonUtils.isNotEmpty(org.getId())){
            payload.setId(org.getId());
        }
        if(CommonUtils.isNotEmpty(org.getOrgCode())){
            payload.setCode(org.getOrgCode());
        }
        if(CommonUtils.isNotEmpty(org.getOrgName())){
            payload.setName(org.getOrgName());
        }
        if(CommonUtils.isNotEmpty(org.getPid())){
            payload.setParentId(org.getPid());
        }
        payload.setType(CommonUtils.getOrElse(org.getOrgType(),1));
        if(CommonUtils.isNotEmpty(org.getFzr())){
            payload.setContactPerson(org.getFzr());
        }
        if(CommonUtils.isNotEmpty(org.getMobile())){
            payload.setContactPhone(org.getMobile());
        }
        // 状态映射: 1 -> 1; 0 或 7 -> 0
        payload.setStatus((org.getEnabled() != null && org.getEnabled() == 1) ? 1 : 0);
        payload.setSortOrder(CommonUtils.getOrElse(org.getSorts(),0));
        if(CommonUtils.isNotEmpty(org.getPaths())){
            payload.setPath(org.getPaths());
        }
        if(CommonUtils.isNotEmpty(org.getTenantId())){
            payload.setTenantId(org.getTenantId());
        }
        payload.setCreateTime(CommonUtils.getOrElse(org.getCreateTime(), new Date()));
        if(CommonUtils.isNotEmpty(org.getCreateId())){
            payload.setCreateId(org.getCreateId());
        }
        payload.setUpdateTime(CommonUtils.getOrElse(org.getUpdateTime(), new Date()));
        if(CommonUtils.isNotEmpty(org.getUpdateId())){
            payload.setUpdateId(org.getUpdateId());
        }
        return payload;
    }

    /**
     * 将 Payload 数据更新到目标 Organization 对象 (用于更新现有记录)
     * 注意：此方法修改传入的 Organization 对象。
     * 审计字段 (createV1/update time/id) 通常不应在此处设置，应由MyBatisPlus管理。
     */
    public void mergeIntoOrganization(Organization targetOrg) {
        if (targetOrg == null) {
            throw new IllegalArgumentException("Target Organization cannot be null");
        }
        // ID 通常用于查找，不在此更新
        if(CommonUtils.isNotEmpty(this.getCode())){
            targetOrg.setOrgCode(this.getId());
        }
        if(CommonUtils.isNotEmpty(this.getName())){
            targetOrg.setOrgName(this.getName());
        }
        if(CommonUtils.isNotEmpty(this.getParentId())){
            targetOrg.setPid(this.getParentId());
        }
        targetOrg.setOrgType(CommonUtils.getOrElse(this.getType(),1));
        if(CommonUtils.isNotEmpty(this.getContactPerson())){
            targetOrg.setFzr(this.getContactPerson());
        }
        if(CommonUtils.isNotEmpty(this.getContactPhone())){
            targetOrg.setMobile(this.getContactPhone());
        }
        // 状态映射: 1 -> 1; 0 -> 0. 如何处理原有的 7 状态需要在消费端决定
        if(CommonUtils.isNotEmpty(this.getStatus())){
            targetOrg.setEnabled(this.getStatus() == 1 ? 1 : 0);
        }
        targetOrg.setSorts(CommonUtils.getOrElse(this.getSortOrder(),0));
        if(CommonUtils.isNotEmpty(this.getPath())){
            targetOrg.setPaths(this.getPath());
        }
        if(CommonUtils.isNotEmpty(this.getTenantId())){
            targetOrg.setTenantId(this.getTenantId());
        }
        targetOrg.setUpdateTime(CommonUtils.getOrElse(this.getUpdateTime(),new Date()));
        if(CommonUtils.isNotEmpty(this.getUpdateId())){
            targetOrg.setUpdateId(this.getUpdateId());
        }
    }

    /**
     * 从 Payload 创建一个新的 Organization 对象 (用于插入新记录)
     */
    public Organization toOrganization() {
        Organization org = new Organization();
        // ID保持同步
        org.setId(this.getId());
        this.mergeIntoOrganization(org); // 复用填充逻辑
        // 在此设置创建时的审计信息
        org.setCreateId(CommonUtils.getOrEmpty(this.getCreateId()));
        org.setCreateTime(CommonUtils.getOrElse(this.getCreateTime(),new Date()));
        return org;
    }
}
