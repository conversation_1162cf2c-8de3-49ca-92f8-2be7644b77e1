package com.hxdi.nmjl.mapper.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.mobilization.MobilizedProductCatalogCondition;
import com.hxdi.nmjl.domain.mobilization.MobilizedProductCatalog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MobilizedProductCatalogMapper extends SuperMapper<MobilizedProductCatalog> {


    /**
     * 分页查询
     * @param page
     * @param condition
     */
    Page<MobilizedProductCatalog> selectPageV1(Page<MobilizedProductCatalog> page, @Param("condition") MobilizedProductCatalogCondition condition);

    /**
     * 列表查询
     * @param condition
     */
    List<MobilizedProductCatalog> selectListV1(@Param("condition") MobilizedProductCatalogCondition condition);
}
