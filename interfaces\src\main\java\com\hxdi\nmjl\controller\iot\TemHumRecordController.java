package com.hxdi.nmjl.controller.iot;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.iot.TemHumRecord;
import com.hxdi.nmjl.service.iot.TemHumRecordService;
import com.hxdi.nmjl.condition.iot.TemHumRecordCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 温湿度检测记录控制器
*
* <AUTHOR>
*/
@RestController
@Api(tags = "温湿度检测记录")
@RequestMapping("/temHumRecord")
public class TemHumRecordController extends BaseController<TemHumRecordService, TemHumRecord> {

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @ApiOperation("单条查询")
    @GetMapping("/selectOne")
    public TemHumRecord selectOne(Integer id) {
    return bizService.getById(id);
    }

    @ApiOperation("批量新增温湿度检测数据")
    @PostMapping("/insertBatch")
    public ResultBody insertBatch(@RequestBody List<TemHumRecord> record) {
        bizService.insertBatch(record);
        return ResultBody.ok();
    }

    @ApiOperation("根据条件查询列表")
    @GetMapping("/getList")
    public ResultBody<List<TemHumRecord>> getList(TemHumRecordCondition condition) {
        List<TemHumRecord> list = bizService.getList(condition);
        return ResultBody.ok().data(list);
    }

    @ApiOperation("根据条件查询分页列表")
    @GetMapping("/getPage")
    public ResultBody<Page<TemHumRecord>> getPage(TemHumRecordCondition condition) {
        Page<TemHumRecord> page = bizService.getPage(condition);
        return ResultBody.ok().data(page);
    }

}
