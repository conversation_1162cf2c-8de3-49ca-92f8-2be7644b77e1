<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.CaseInfoMapper">
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.CaseInfo">
        <id column="ID" property="id"/>
        <result column="CASE_NO" property="caseNo"/>
        <result column="CASE_NAME" property="caseName"/>
        <result column="PLAN_ID" property="planId"/>
        <result column="OCCUR_TIME" property="occurTime"/>
        <result column="FILING_TIME" property="filingTime"/>
        <result column="CLOSING_TIME" property="closingTime"/>
        <result column="ADDRESS" property="address"/>
        <result column="CASE_DESC" property="caseDesc"/>
        <result column="UNIT_NAME" property="unitName"/>
        <result column="LEGALS" property="legals"/>
        <result column="ORIGIN" property="origin"/>
        <result column="LAFX" property="lafx"/>
        <result column="STATE" property="state"/>
        <result column="ENABLED" property="enabled"/>
        <result column="ATTACHMENTS" property="attachments"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, CASE_NO, CASE_NAME, PLAN_ID, OCCUR_TIME, FILING_TIME, CLOSING_TIME,
        ADDRESS, CASE_DESC, UNIT_NAME, LEGALS, ORIGIN,
        LAFX, STATE, ENABLED,
        ATTACHMENTS, CREATE_TIME, UPDATE_TIME,
        CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_CASE_INFO
        <where>
            enabled = 1
            <if test="condition.startTime != null">
                and OCCUR_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and OCCUR_TIME &lt;= #{condition.endTime}
            </if>
            <if test="condition.state != null">
                and "STATE" = #{condition.state}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM B_CASE_INFO
        <where>
            enabled = 1
            <if test="condition.startTime != null">
                and OCCUR_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and OCCUR_TIME &lt;= #{condition.endTime}
            </if>
            <if test="condition.state != null">
                and "STATE" = #{condition.state}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
