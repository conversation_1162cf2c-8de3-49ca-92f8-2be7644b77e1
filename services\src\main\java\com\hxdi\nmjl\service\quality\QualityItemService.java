package com.hxdi.nmjl.service.quality;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.quality.QualityItem;

import java.io.Serializable;
import java.util.List;

public interface QualityItemService extends IBaseService<QualityItem> {

    /**
     * 根据质检方案id获取对应质检标准项列表
     * @param schemaId
     * @return
     */
    List<QualityItem> getList(Serializable schemaId);

    /**
     * 批量插入或更新质检标准项（根据Id判断）
     * @param data
     */
    void insertOrUpdate(List<QualityItem> data);
}
