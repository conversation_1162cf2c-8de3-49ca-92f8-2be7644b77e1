<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyLossMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyLoss">
    <!--@mbg.generated-->
    <!--@Table B_EMERGENCY_LOSS-->
    <id column="ID" property="id" />
    <result column="TASK_NAME" property="taskName" />
    <result column="TASK_CODE" property="taskCode" />
    <result column="LOSS_CODE" property="lossCode" />
    <result column="ORG_ID" property="orgId" />
    <result column="ORG_NAME" property="orgName" />
    <result column="UNIT_ID" property="unitId" />
    <result column="UNIT_NAME" property="unitName" />
    <result column="PROVINCE" property="province" />
    <result column="CITY" property="city" />
    <result column="COUNTY" property="county" />
    <result column="PROVINCE_CODE" property="provinceCode" />
    <result column="CITY_CODE" property="cityCode" />
    <result column="COUNTY_CODE" property="countyCode" />
    <result column="DETAIL_ADDR" property="detailAddr" />
    <result column="CLASSIFICATION_ID" property="classificationId" />
    <result column="CLASSIFICATION_NAME" property="classificationName" />
    <result column="LOSS_QTY" property="lossQty" />
    <result column="AMOUNT" property="amount" />
    <result column="STATISTICIAN" property="statistician" />
    <result column="STAT_MOBILE" property="statMobile" />
    <result column="FZR" property="fzr" />
    <result column="FZR_MOBILE" property="fzrMobile" />
    <result column="LOSS_DESC" property="lossDesc" />
    <result column="STATE" property="state" />
    <result column="ENABLED" property="enabled" />
    <result column="ATTACHMENT" property="attachment" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    <result column="REPORT_TYPE" property="reportType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LOSS_CODE, TASK_CODE, TASK_NAME, ORG_ID, ORG_NAME, UNIT_ID, UNIT_NAME, PROVINCE, CITY, COUNTY, PROVINCE_CODE,
    CITY_CODE, COUNTY_CODE, DETAIL_ADDR, CLASSIFICATION_ID, CLASSIFICATION_NAME, LOSS_QTY,
    AMOUNT, STATISTICIAN, STAT_MOBILE, FZR, FZR_MOBILE, LOSS_DESC, "STATE", ENABLED,
    ATTACHMENT, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,
    REPORT_TYPE
  </sql>

</mapper>
