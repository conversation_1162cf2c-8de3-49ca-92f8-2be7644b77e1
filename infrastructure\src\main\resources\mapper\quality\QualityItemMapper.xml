<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.quality.QualityItemMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.quality.QualityItem">
    <!--@mbg.generated-->
    <!--@Table B_QUALITY_ITEM-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SCHEMA_ID" jdbcType="VARCHAR" property="schemaId" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_SYMBOL" jdbcType="VARCHAR" property="itemSymbol" />
    <result column="ITEM_SIGN" jdbcType="VARCHAR" property="itemSign" />
    <result column="ITEM_SVAL" jdbcType="VARCHAR" property="itemSval" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCHEMA_ID, ITEM_NAME, ITEM_SYMBOL, ITEM_SIGN, ITEM_SVAL, TENANT_ID, DATA_HIERARCHY_ID
  </sql>
</mapper>