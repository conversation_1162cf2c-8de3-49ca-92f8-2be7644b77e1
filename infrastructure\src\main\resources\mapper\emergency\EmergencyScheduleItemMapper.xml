<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyScheduleItemMapper">

    <sql id="Base_Column_List">
        ID, SCHEDULE_ID, CATALOG_ID, CATALOG_NAME, SPECIFICATION, GRADE, PLAN_QTY,
    COMPLETED_QTY, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyScheduleItem">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="SCHEDULE_ID" jdbcType="VARCHAR" property="scheduleId"/>
        <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId"/>
        <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName"/>
        <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification"/>
        <result column="GRADE" jdbcType="VARCHAR" property="grade"/>
        <result column="PLAN_QTY" jdbcType="DECIMAL" property="planQty"/>
        <result column="COMPLETED_QTY" jdbcType="DECIMAL" property="completedQty"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_EMERGENCY_SCHEDULE_ITEM
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.scheduleId)">
                and SCHEDULE_ID = #{condition.scheduleId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                and CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogName)">
                and CATALOG_NAME like concat('%', #{condition.catalogName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.specification)">
                and SPECIFICATION like concat('%', #{condition.specification}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.grade)">
                and GRADE = #{condition.grade}
            </if>
        </where>
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_EMERGENCY_SCHEDULE_ITEM
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.scheduleId)">
                and SCHEDULE_ID = #{condition.scheduleId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogId)">
                and CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.catalogName)">
                and CATALOG_NAME like concat('%', #{condition.catalogName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.specification)">
                and SPECIFICATION like concat('%', #{condition.specification}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.grade)">
                and GRADE = #{condition.grade}
            </if>
        </where>
    </select>
</mapper>
