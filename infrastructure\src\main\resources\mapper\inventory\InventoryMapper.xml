<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryMapper">
    <resultMap id="ResultMap" type="com.hxdi.nmjl.domain.inventory.Inventory">
        <!--@mbg.generated-->
        <!--@Table B_INVENTORY-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName"/>
        <result column="ST_ID" jdbcType="VARCHAR" property="stId"/>
        <result column="ST_NAME" jdbcType="VARCHAR" property="stName"/>
        <result column="LOC_ID" jdbcType="VARCHAR" property="locId"/>
        <result column="LOC_NAME" jdbcType="VARCHAR" property="locName"/>
        <result column="BIG_TYPE" jdbcType="INTEGER" property="bigType"/>
        <result column="CLASSIFICATION_ID" jdbcType="VARCHAR" property="classificationId"/>
        <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId"/>
        <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName"/>
        <result column="SPECIFICATIONS" jdbcType="VARCHAR" property="specifications"/>
        <result column="GRADE" jdbcType="VARCHAR" property="grade"/>
        <result column="RESERVE_LEVEL" jdbcType="INTEGER" property="reserveLevel"/>
        <result column="PROVINCE" jdbcType="VARCHAR" property="province"/>
        <result column="CITY" jdbcType="VARCHAR" property="city"/>
        <result column="COUNTY" jdbcType="VARCHAR" property="county"/>
        <result column="PRODUCTION_DATE" jdbcType="TIMESTAMP" property="productionDate"/>
        <result column="MANAGE_UNIT_ID" jdbcType="VARCHAR" property="manageUnitId"/>
        <result column="MANAGE_UNIT_NAME" jdbcType="VARCHAR" property="manageUnitName"/>
        <result column="LOCK_QTY" jdbcType="DECIMAL" property="lockQty"/>
        <result column="INVENTORY_QTY" jdbcType="DECIMAL" property="inventoryQty"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>


    <sql id="Column_List">
        <!--@mbg.generated-->
        ID,
        STORE_ID,
        STORE_NAME,
        ST_ID,
        ST_NAME,
        LOC_ID,
        LOC_NAME,
        BIG_TYPE,
        CLASSIFICATION_ID,
        CATALOG_ID,
        CATALOG_NAME,
        SPECIFICATIONS,
        GRADE,
        RESERVE_LEVEL,
        PROVINCE,
        CITY,
        COUNTY,
        PRODUCTION_DATE,
        MANAGE_UNIT_ID,
        MANAGE_UNIT_NAME,
        LOCK_QTY,
        INVENTORY_QTY,
        ENABLED,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>

    <select id="selectCatalogInventoryByPage" resultMap="ResultMap">
        select STORE_ID,
               STORE_NAME,
               ST_ID,
               ST_NAME,
               CATALOG_ID,
               CATALOG_NAME,
               SUM(INVENTORY_QTY) AS INVENTORY_QTY
        from B_INVENTORY
        <where>
            <if test="condition.storeId != null and condition.storeId != ''">
                and STORE_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                and ST_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.stId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.bigType != null and condition.bigType != ''">
                and BIG_TYPE = #{condition.bigType}
            </if>
            <if test="condition.classificationId != null and condition.classificationId != ''">
                and CLASSIFICATION_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.classificationId)"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogId != null and condition.catalogId != ''">
                and CATALOG_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.catalogId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.reserveLevel != null and condition.reserveLevel != ''">
                and RESERVE_LEVEL = #{condition.reserveLevel}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                and GRADE = #{condition.grade}
            </if>
            <if test="condition.productionDateStart != null">
                and PRODUCTION_DATE &gt;= #{condition.productionDateStart}
            </if>
            <if test="condition.productionDateEnd != null">
                and PRODUCTION_DATE &lt;= #{condition.productionDateEnd}
            </if>
            <if test="condition.manageUnitId != null and condition.manageUnitId != ''">
                and MANAGE_UNIT_ID = #{condition.manageUnitId}
            </if>
            and ENABLED = 1
        </where>
        group by STORE_ID, ST_ID, CATALOG_ID
    </select>

    <select id="selectInventoryDetailByPage" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from B_INVENTORY
        <where>
            <if test="condition.storeId != null and condition.storeId != ''">
                and STORE_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                and ST_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.stId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.locId != null and condition.locId != ''">
                and LOC_ID = #{condition.locId}
            </if>
            <if test="condition.bigType != null and condition.bigType != ''">
                and BIG_TYPE = #{condition.bigType}
            </if>
            <if test="condition.classificationId != null and condition.classificationId != ''">
                and CLASSIFICATION_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.classificationId)"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogId != null and condition.catalogId != ''">
                and CATALOG_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.catalogId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogName != null and condition.catalogName != ''">
                and "CATALOG_NAME" = #{condition.catalogName}
            </if>
            <if test="condition.reserveLevel != null and condition.reserveLevel != ''">
                and RESERVE_LEVEL = #{condition.reserveLevel}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                and GRADE = #{condition.grade}
            </if>
            <if test="condition.productionDateStart != null">
                and PRODUCTION_DATE &gt;= #{condition.productionDateStart}
            </if>
            <if test="condition.productionDateEnd != null">
                and PRODUCTION_DATE &lt;= #{condition.productionDateEnd}
            </if>
            <if test="condition.manageUnitId != null and condition.manageUnitId != ''">
                and MANAGE_UNIT_ID = #{condition.manageUnitId}
            </if>
            and ENABLED = 1
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectInventoryDetailList" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from B_INVENTORY
        <where>
            <if test="condition.storeId != null and condition.storeId != ''">
                and STORE_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                and ST_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.stId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.locId != null and condition.locId != ''">
                and LOC_ID = #{condition.locId}
            </if>
            <if test="condition.bigType != null and condition.bigType != ''">
                and BIG_TYPE = #{condition.bigType}
            </if>
            <if test="condition.classificationId != null and condition.classificationId != ''">
                and CLASSIFICATION_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.classificationId)"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogId != null and condition.catalogId != ''">
                and CATALOG_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.catalogId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogName != null and condition.catalogName != ''">
                and CATALOG_NAME = #{condition.catalogName}
            </if>
            <if test="condition.reserveLevel != null and condition.reserveLevel != ''">
                and RESERVE_LEVEL = #{condition.reserveLevel}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                and GRADE = #{condition.grade}
            </if>
            <if test="condition.productionDateStart != null">
                and PRODUCTION_DATE &gt;= #{condition.productionDateStart}
            </if>
            <if test="condition.productionDateEnd != null">
                and PRODUCTION_DATE &lt;= #{condition.productionDateEnd}
            </if>
            <if test="condition.manageUnitId != null and condition.manageUnitId != ''">
                and MANAGE_UNIT_ID = #{condition.manageUnitId}
            </if>
            and ENABLED = 1
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectCatalogInventoryList" resultMap="ResultMap">
        select STORE_ID,
               STORE_NAME,
               ST_ID,
               ST_NAME,
               CATALOG_ID,
               CATALOG_NAME,
               SUM(INVENTORY_QTY) AS INVENTORY_QTY
        from B_INVENTORY
        <where>
            <if test="condition.storeId != null and condition.storeId != ''">
                and STORE_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                and ST_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.stId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.locId != null and condition.locId != ''">
                and LOC_ID = #{condition.locId}
            </if>
            <if test="condition.bigType != null and condition.bigType != ''">
                and BIG_TYPE = #{condition.bigType}
            </if>
            <if test="condition.classificationId != null and condition.classificationId != ''">
                and CLASSIFICATION_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.classificationId)"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogId != null and condition.catalogId != ''">
                and CATALOG_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.catalogId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogName != null and condition.catalogName != ''">
                and "CATALOG_NAME" = #{condition.catalogName}
            </if>
            <if test="condition.reserveLevel != null and condition.reserveLevel != ''">
                and RESERVE_LEVEL = #{condition.reserveLevel}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                and GRADE = #{condition.grade}
            </if>
            <if test="condition.productionDateStart != null">
                and PRODUCTION_DATE &gt;= #{condition.productionDateStart}
            </if>
            <if test="condition.productionDateEnd != null">
                and PRODUCTION_DATE &lt;= #{condition.productionDateEnd}
            </if>
            <if test="condition.manageUnitId != null and condition.manageUnitId != ''">
                and MANAGE_UNIT_ID = #{condition.manageUnitId}
            </if>
            and ENABLED = 1
        </where>
        group by STORE_ID, STORE_NAME, ST_ID, ST_NAME, CATALOG_ID, CATALOG_NAME
    </select>

    <select id="selectClassificationInventoryList" resultMap="ResultMap">
        select STORE_ID,
               STORE_NAME,
               ST_ID,
               ST_NAME,
               CLASSIFICATION_ID,
               SUM(INVENTORY_QTY) AS INVENTORY_QTY
        from B_INVENTORY
        <where>
            <if test="condition.storeId != null and condition.storeId != ''">
                and STORE_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                and ST_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.stId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.locId != null and condition.locId != ''">
                and LOC_ID = #{condition.locId}
            </if>
            <if test="condition.bigType != null and condition.bigType != ''">
                and BIG_TYPE = #{condition.bigType}
            </if>
            <if test="condition.classificationId != null and condition.classificationId != ''">
                and CLASSIFICATION_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.classificationId)"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogId != null and condition.catalogId != ''">
                and CATALOG_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.catalogId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogName != null and condition.catalogName != ''">
                and "CATALOG_NAME" = #{condition.catalogName}
            </if>
            <if test="condition.reserveLevel != null and condition.reserveLevel != ''">
                and RESERVE_LEVEL = #{condition.reserveLevel}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                and GRADE = #{condition.grade}
            </if>
            <if test="condition.productionDateStart != null">
                and PRODUCTION_DATE &gt;= #{condition.productionDateStart}
            </if>
            <if test="condition.productionDateEnd != null">
                and PRODUCTION_DATE &lt;= #{condition.productionDateEnd}
            </if>
            <if test="condition.manageUnitId != null and condition.manageUnitId != ''">
                and MANAGE_UNIT_ID = #{condition.manageUnitId}
            </if>
            and ENABLED = 1
        </where>
        group by STORE_ID, STORE_NAME, ST_ID, ST_NAME, CLASSIFICATION_ID
    </select>

    <select id="getInventoryStorehouseList" resultMap="ResultMap">
        select STORE_ID,
               STORE_NAME,
               ST_ID,
               ST_NAME,
               DATA_HIERARCHY_ID
        from B_INVENTORY
        where enabled = 1
          and STORE_ID = #{storeId}
        group by STORE_ID, STORE_NAME, ST_ID, ST_NAME, DATA_HIERARCHY_ID
    </select>

    <select id="getQuantity" resultType="java.lang.String">
        select sum(INVENTORY_QTY)
        from B_INVENTORY
        where LOC_ID = #{locId}
          and enabled = 1
    </select>

    <select id="selectInventorySummeryByPage" resultMap="ResultMap">
        select PROVINCE,
               CITY,
               COUNTY,
               STORE_ID,
               STORE_NAME,
               CLASSIFICATION_ID,
               ROUND(SUM(INVENTORY_QTY) / 1000, 3) AS INVENTORY_QTY
        from B_INVENTORY
        <where>
            <if test="condition.storeId != null and condition.storeId != ''">
                and STORE_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stId != null and condition.stId != ''">
                and ST_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.stId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.classificationId != null and condition.classificationId != ''">
                and CLASSIFICATION_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.classificationId)"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogId != null and condition.catalogId != ''">
                and CATALOG_ID IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.catalogId)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.catalogName != null and condition.catalogName != ''">
                and "CATALOG_NAME" = #{condition.catalogName}
            </if>
            <if test="condition.reserveLevel != null and condition.reserveLevel != ''">
                and RESERVE_LEVEL = #{condition.reserveLevel}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                and GRADE = #{condition.grade}
            </if>
            <if test="condition.areaCode != null and condition.areaCode != ''">
                and COUNTY IN
                <foreach item="item" index="index"
                         collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.areaCode)" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and ENABLED = 1
        </where>
        group by PROVINCE, CITY, COUNTY, STORE_ID, STORE_NAME, CLASSIFICATION_ID
        order by PROVINCE, CITY, COUNTY, STORE_ID, CLASSIFICATION_ID
    </select>

    <select id="selectInventorySummery" resultType="java.math.BigDecimal">
        select sum(INVENTORY_QTY)
        from B_INVENTORY
        <where>
            and ENABLED = 1
            <if test="areaCodeList != null and areaCodeList.size() > 0">
                and COUNTY IN
                <foreach item="item" index="index" collection="areaCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="catalogIdList != null and catalogIdList.size() > 0">
                and CATALOG_ID IN
                <foreach item="item" index="index" collection="catalogIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getInventorySumAndCapacity" resultType="com.hxdi.nmjl.vo.bigscreen.InventorySumAndCapacityVO">
        select COUNTY             as areaCode,
               SUM(INVENTORY_QTY) as inventorySum
        from B_INVENTORY
        <where>
            and ENABLED = 1
            <if test="areaCode != null and areaCode != ''">
                and COUNTY IN
                <foreach item="item" index="index" collection="areaCode" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by COUNTY
        order by COUNTY
    </select>

    <select id="getInventorySum" resultType="java.math.BigDecimal">
        select ROUND(SUM(INVENTORY_QTY) / 1000, 3) AS INVENTORY_QTY
        from B_INVENTORY
        where COUNTY = #{areaCode}
          and enabled = 1
    </select>

    <select id="getInventorySumAndCapacityPage" resultType="com.hxdi.nmjl.vo.bigscreen.InventorySumAndCapacityVO">
        select o.COUNTY                                           as areaCode,
               s.STORE_ID                                         as storeId,
               s.NAME                                             as storeName,
               s.ID                                               as StoreHouseId,
               s.NAME                                             as StoreHouseName,
               s.CAPACITY                                         as capacitySum,
               ROUND(COALESCE(SUM(i.INVENTORY_QTY), 0) / 1000, 3) as inventorySum
        from C_STORE_HOUSE s
                 left join B_ORGANIZATION o on s.STORE_ID = o.ID
                 left join B_INVENTORY i on s.ID = i.ST_ID and i.ENABLED = 1
        <where>
            and s.ENABLED = 1
            <choose>
                <when test="storeId != null and storeId != ''">
                    and s.STORE_ID = #{storeId}
                </when>
                <when test="areaCodeList != null and areaCodeList.size() > 0">
                    and o.COUNTY IN
                    <foreach item="item" index="index" collection="areaCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
        </where>
        group by o.COUNTY, s.STORE_ID, s.NAME, s.ID, s.NAME, s.CAPACITY
        order by o.COUNTY, s.STORE_ID, s.ID
    </select>
</mapper>
