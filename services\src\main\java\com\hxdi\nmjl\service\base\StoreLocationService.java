package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.StoreLocation;
import com.hxdi.nmjl.condition.base.StoreLocationCondition;

import java.util.List;

public interface StoreLocationService extends IBaseService<StoreLocation> {

    /**
     * 新增
     * @param location
     */
    StoreLocation create(StoreLocation location);

    /**
     * 更新
     * @param location
     */
    StoreLocation update(StoreLocation location);

    /**
     * 修改状态：启用、禁用、删除
     * @param id
     * @param state
     */
    StoreLocation changeState(String id, Integer state);

    /**
     * 查询详情
     * @param id
     * @return
     */
    StoreLocation getByUniqueKey(String id);

    /**
     * 分页
     * @param condition
     * @return
     */
    Page<StoreLocation> pages(StoreLocationCondition condition);

    /**
     * 列表
     * @param condition
     * @return
     */
    List<StoreLocation> lists(StoreLocationCondition condition);

    StoreLocation changeAssignState(String id, Integer assignState);
}
