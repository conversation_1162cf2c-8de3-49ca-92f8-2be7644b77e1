package com.hxdi.nmjl.service.inout.delivery;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.inout.FuelAnalysisCondition;
import com.hxdi.nmjl.domain.inout.delivery.FuelAnalysis;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【B_FUEL_ANALYSIS(油耗分析报表)】的数据库操作Service
 * @createDate 2025-08-05 15:12:43
 */
public interface FuelAnalysisService extends IBaseService<FuelAnalysis> {

    /**
     * 插入或更新数据
     *
     * @param fuelAnalysis
     */
    public void saveOrUpdateV1(FuelAnalysis fuelAnalysis);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<FuelAnalysis> pages(FuelAnalysisCondition condition);

    /**
     * 分页监控查询
     *
     * @param condition 查询条件
     * @return 分页数据(分组)
     */
    Page<FuelAnalysis> pages1(FuelAnalysisCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<FuelAnalysis> lists(FuelAnalysisCondition condition);

    /**
     * 根据车辆类型获取车牌号
     * @param kind
     * @return
     */
    List<String> getByKind(String kind);
}
