package com.hxdi.nmjl.domain.inout;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description="库存拆分记录")
@TableName(value = "B_DISPATCH_INFO")
public class DispatchInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 任务ID
     */
    @TableField(value = "TASK_ID")
    @ApiModelProperty(value="任务ID")
    private String taskId;

    /**
     * 任务单号
     */
    @TableField(value = "TASK_CODE")
    @ApiModelProperty(value="任务单号")
    private String taskCode;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value="库点名称")
    private String storeName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value="仓房ID")
    private String stId;

    /**
     * 仓房名称
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value="仓房名称")
    private String stName;

    /**
     * 货位ID
     */
    @TableField(value = "LOC_ID")
    @ApiModelProperty(value="货位ID")
    private String locId;

    /**
     * 货位名称
     */
    @TableField(value = "LOC_NAME")
    @ApiModelProperty(value="货位名称")
    private String locName;

    /**
     * 类型：字典CRKLX
     */
    @TableField(value = "DISPATCH_TYPE")
    @ApiModelProperty(value="类型：字典CRKLX")
    private String dispatchType;

    /**
     * 库存ID
     */
    @TableField(value = "INVENTORY_ID")
    @ApiModelProperty(value="库存ID")
    private String inventoryId;

    /**
     * 数量
     */
    @TableField(value = "QTY")
    @ApiModelProperty(value="数量")
    private BigDecimal qty;

    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value="备注")
    private String remarks;

    /**
     * 附件
     */
    @TableField(value = "ATTACHEMENTS")
    @ApiModelProperty(value="附件")
    private String attachements;

    /**
     * 状态：1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value="状态：1-有效，0-删除")
    private Integer enabled;

    /**
     * 执行状态：0-待提交 1-已提交 2-执行完成
     */
    @TableField(value = "STATUS")
    @ApiModelProperty(value="状态：0-待提交 1-已提交 2-执行完成")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}