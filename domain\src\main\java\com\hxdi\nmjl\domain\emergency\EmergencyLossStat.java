package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 应急损耗统计
 */
@Getter
@Setter
@TableName("B_EMERGENCY_LOSS_STAT")
public class EmergencyLossStat implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String taskCode;

    private String taskName;

    private String storeId;

    private String storeName;

    private BigDecimal lossTotal;

    private String classificationId;

    private String classificationName;

    private BigDecimal amount;

    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
    * 状态：0-待申请，1-已申请
    */
    @ApiModelProperty(value="状态：0-待申请，1-已申请")
    private Integer state;


    /********************以下非实体字段*********************/


    /**
     * 当前损耗数量
     */
    @TableField(exist = false)
    private BigDecimal currentLossQty;

    /**
     * 损耗ID
     */
    @TableField(exist = false)
    private String lossId;
}
