package com.hxdi.nmjl.domain.iot;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description="温湿度检测记录")
@TableName(value = "B_TEM_HUM_RECORD")
public class TemHumRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value="库点名称")
    private String storeName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value="仓房ID")
    private String stId;

    /**
     * 仓房
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value="仓房")
    private String stName;

    /**
     * 设备ID
     */
    @TableField(value = "SERIAL")
    @ApiModelProperty(value="设备序列号：唯一")
    private String serial;

    /**
     * 设备名称
     */
    @TableField(value = "DEVICE_NAME")
    @ApiModelProperty(value="设备名称")
    private String deviceName;

    /**
     * 设备状态
     */
    @TableField(value = "DEVICE_STATE")
    @ApiModelProperty(value="设备状态")
    private Integer deviceState;

    /**
     * 安装位置
     */
    @TableField(value = "AREA")
    @ApiModelProperty(value="安装位置")
    private String area;

    /**
     * 仓温
     */
    @TableField(value = "CW")
    @ApiModelProperty(value="仓温")
    private String cw;

    /**
     * 仓湿
     */
    @TableField(value = "CS")
    @ApiModelProperty(value="仓湿")
    private String cs;

    /**
     * 气温
     */
    @TableField(value = "QW")
    @ApiModelProperty(value="气温")
    private String qw;

    /**
     * 气湿
     */
    @TableField(value = "QS")
    @ApiModelProperty(value="气湿")
    private String qs;

    /**
     * 检测时间
     */
    @TableField(value = "INSPECT_TIME")
    @ApiModelProperty(value="检测时间")
    private Date inspectTime;

    /**
     * 是否异常：0-否，1-是
     */
    @TableField(value = "ERR_IS")
    @ApiModelProperty(value="是否异常：0-否，1-是")
    private Integer errIs;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 消息ID（同一批接收的消息ID相同）
     */
    @TableField(value = "MESSAGE_ID")
    @ApiModelProperty(value="消息ID（同一批接收的消息ID相同）")
    private String messageId;

    /**
     * 节点数量
     */
    @TableField(value = "NODE_ID")
    @ApiModelProperty(value="设备节点Id")
    private Integer nodeId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}