package com.hxdi.nmjl.domain.inout.opt;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**环境控制作业监测数据
 * @TableName B_OPT_DATA
 */
@ApiModel(description = "环境控制作业监测数据")
@TableName("B_OPT_DATA")
@Setter
@Getter
public class OptData implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("OPT_ID")
    @ApiModelProperty(value = "作业ID")
    private String optId;

    @TableField("DEVICE_ID")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @TableField("DEVICE_NAME")
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @TableField("DATA_ORIGIN")
    @ApiModelProperty(value = "数据来源")
    private String dataOrigin;

    @TableField("AREA_POINT")
    @ApiModelProperty(value = "检测点")
    private String areaPoint;

    @TableField("WD1")
    @ApiModelProperty(value = "作业前温度")
    private String wd1;

    @TableField("SD1")
    @ApiModelProperty(value = "作业前湿度")
    private String sd1;

    @TableField("WD2")
    @ApiModelProperty(value = "作业后温度")
    private String wd2;

    @TableField("SD2")
    @ApiModelProperty(value = "作业后湿度")
    private String sd2;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

}
