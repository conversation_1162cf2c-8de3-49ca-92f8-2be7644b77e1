<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ContractSettlementMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.ContractSettlement">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="SETTLEMENT_CODE" jdbcType="VARCHAR" property="settlementCode"/>
        <result column="CONTRACT_ID" jdbcType="VARCHAR" property="contractId"/>
        <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contractCode"/>
        <result column="CONTRACT_NAME" jdbcType="VARCHAR" property="contractName"/>
        <result column="BIZ_TYPE" jdbcType="VARCHAR" property="bizType"/>
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId"/>
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName"/>
        <result column="CLIENT_ID" jdbcType="VARCHAR" property="clientId"/>
        <result column="CLIENT_NAME" jdbcType="VARCHAR" property="clientName"/>
        <result column="CONTRACT_TOTAL_AMOUNT" jdbcType="DECIMAL" property="contractTotalAmount"/>
        <result column="SETTLEMENT_TOTAL_AMOUNT" jdbcType="DECIMAL" property="settlementTotalAmount"/>
        <result column="SETTLEMENT_PROGRESS" jdbcType="DECIMAL" property="settlementProgress"/>
        <result column="SETTLEMENT_STATUS" jdbcType="VARCHAR" property="settlementStatus"/>
        <result column="NOTES" jdbcType="VARCHAR" property="notes"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="ATTACHEMENTS" jdbcType="VARCHAR" property="attachements"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        SETTLEMENT_CODE,
        CONTRACT_ID,
        CONTRACT_CODE,
        CONTRACT_NAME,
        BIZ_TYPE,
        ORG_ID,
        ORG_NAME,
        CLIENT_ID,
        CLIENT_NAME,
        CONTRACT_TOTAL_AMOUNT,
        SETTLEMENT_TOTAL_AMOUNT,
        SETTLEMENT_PROGRESS,
        SETTLEMENT_STATUS,
        NOTES,
        ENABLED,
        ATTACHEMENTS,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>
</mapper>
