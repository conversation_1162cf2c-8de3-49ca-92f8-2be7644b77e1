package com.hxdi.nmjl.controller.iot;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.iot.TemHumWarningConfigCondition;
import com.hxdi.nmjl.domain.iot.TemHumWarningConfig;
import com.hxdi.nmjl.service.iot.TemHumWarningConfigService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 温湿度预警参数配置表控制层
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "温湿度预警参数配置")
@RequestMapping("/temHumWarningConfig")
public class TemHumWarningConfigController extends BaseController<TemHumWarningConfigService, TemHumWarningConfig> {

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/get")
    public ResultBody<TemHumWarningConfig> selectOne(String id) {
        return ResultBody.<TemHumWarningConfig>OK().data(bizService.getById(id));
    }

    @GetMapping("/list")
    public ResultBody<List<TemHumWarningConfig>> list(TemHumWarningConfigCondition condition) {
        return ResultBody.<List<TemHumWarningConfig>>OK().data(bizService.listV1(condition));
    }

    @GetMapping("/page")
    public ResultBody<IPage<TemHumWarningConfig>> page(TemHumWarningConfigCondition condition) {
        return ResultBody.<IPage<TemHumWarningConfig>>OK().data(bizService.pageV1(condition));
    }

    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody TemHumWarningConfig config) {
        bizService.saveOrUpdateV1(config);
        return ResultBody.OK();
    }

    @PostMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        bizService.removeById(id);
        return ResultBody.OK();
    }


}
