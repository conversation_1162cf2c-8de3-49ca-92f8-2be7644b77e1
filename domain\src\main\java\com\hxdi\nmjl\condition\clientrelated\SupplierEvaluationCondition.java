package com.hxdi.nmjl.condition.clientrelated;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "供应商评价查询参数")
@Setter
@Getter
public class SupplierEvaluationCondition extends QueryCondition {

    @TableField("SUPPLIER_NAME")
    private String supplierName;
}
