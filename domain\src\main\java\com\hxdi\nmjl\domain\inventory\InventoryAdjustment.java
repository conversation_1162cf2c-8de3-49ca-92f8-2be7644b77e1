package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 库存调整记录
 */
@ApiModel(description = "库存调整记录")
@Getter
@Setter
@TableName(value = "B_INVENTORY_ADJUSTMENT")
public class InventoryAdjustment implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;
    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;
    /**
     * 关联业务ID
     */
    @TableField(value = "BIZ_ID")
    @ApiModelProperty(value = "关联业务ID")
    private String bizId;
    /**
     * 业务类型
     */
    @TableField(value = "BIZ_TYPE")
    @ApiModelProperty(value = "关联业务类型: 1-盘点,2-其他")
    private String bizType;
    /**
     * 申请方式：字典KCTZLX
     */
    @TableField(value = "TYPE")
    @ApiModelProperty(value = "申请方式：字典KCTZLX")
    private String type;
    /**
     * 申请人
     */
    @TableField(value = "APPLICANT")
    @ApiModelProperty(value = "申请人")
    private String applicant;
    /**
     * 申请时间
     */
    @TableField(value = "APPLICATION_TIME")
    @ApiModelProperty(value = "申请时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applicationTime;
    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;
    /**
     * 审核状态：0-未审核，1-待审核，2-已审核，3-驳回
     */
    @TableField(value = "APPROVE_STATUS")
    @ApiModelProperty(value = "审核状态：0-未审核，1-待审核，2-已审核，3-驳回")
    private Integer approveStatus;
    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;
    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;
    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;
    /**
     * 状态：1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态：1-有效，0-删除")
    private Integer enabled;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;
    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;



    /*********************以下非实体字段*********************/

    /**
     * 库存调整明细列表
     */
    @TableField(exist = false)
    private List<InventoryAdjustmentDetail> detailList;
}
