package com.hxdi.nmjl.domain.inout.delivery;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 配送事件记录表实体类
 *
 * <AUTHOR>
 * @since 2025-04-23 10:23:26
 */
@Getter
@Setter
@TableName("B_DELIVERY_EVENT")
@ApiModel(description = "配送事件记录")
public class DeliveryEvent implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键Id")
    private String id;
    /**
     * 配送单ID
     */
    @TableField(value = "DELIVERY_ID")
    @ApiModelProperty(value = "配送单ID")
    private String deliveryId;
    /**
     * 配送单号
     */
    @TableField(value = "DELIVERY_CODE")
    @ApiModelProperty(value = "配送单号")
    private String deliveryCode;
    /**
     * 车牌号
     */
    @TableField(value = "VEHICLE_NO")
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    /**
     * 司机
     */
    @TableField(value = "DRIVER")
    @ApiModelProperty(value = "司机")
    private String driver;
    /**
     * 司机手机号
     */
    @TableField(value = "MOBILE")
    @ApiModelProperty(value = "司机手机号")
    private String mobile;
    /**
     * 事件开始时间
     */
    @TableField(value = "START_TIME")
    @ApiModelProperty(value = "事件开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 事件结束时间
     */
    @TableField(value = "END_TIME")
    @ApiModelProperty(value = "事件结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    /**
     * 事件类型
     */
    @TableField(value = "EVENT_TYPE")
    @ApiModelProperty(value = "事件类型")
    private String eventType;
    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 状态：0-删除，1-有效
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态：0-删除，1-有效")
    private Integer enabled;
    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;
    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    @TableField(value = "DELIVERER")
    @ApiModelProperty(value = "调度人员")
    private String deliverer;

    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点id")
    private String StoreId;

}

