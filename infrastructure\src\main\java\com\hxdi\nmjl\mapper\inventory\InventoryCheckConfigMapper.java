package com.hxdi.nmjl.mapper.inventory;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inventory.InventoryCheckConfig;
import com.hxdi.nmjl.condition.inventory.InventoryCheckConfigCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盘点任务管理数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@Mapper
public interface InventoryCheckConfigMapper extends SuperMapper<InventoryCheckConfig> {
    @DataPermission
    Page<InventoryCheckConfig> selectPageV1(Page<InventoryCheckConfig> page, @Param("condition") InventoryCheckConfigCondition condition);

    @DataPermission
    List<InventoryCheckConfig> selectListV1(@Param("condition") InventoryCheckConfigCondition condition);

}

