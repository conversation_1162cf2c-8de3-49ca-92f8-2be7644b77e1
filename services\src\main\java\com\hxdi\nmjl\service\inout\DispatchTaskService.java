package com.hxdi.nmjl.service.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.DispatchTask;
import com.hxdi.nmjl.condition.inout.DispatchCondition;

import java.util.List;

public interface DispatchTaskService extends IBaseService<DispatchTask> {
    /**
     * 创建或更新拆分调度计划（注意：无法修改已经开始执行的计划）
     * @param dispatchTask
     */
    void createOrUpdate(DispatchTask dispatchTask);

    void removeById(String id);

    /**
     * 查询拆分调度计划及对应的库存及品种信息
     * @param id
     * @return
     */
    DispatchTask getMore(String id);

    /**
     * 查询拆分调度计划（带权限控制）
     * @param condition
     * @return
     */
    List<DispatchTask> listV1(DispatchCondition condition);

    /**
     * 分页查询拆分调度计划（带权限控制）
     * @param condition
     * @return
     */
    Page<DispatchTask> PageV1(DispatchCondition condition);
}
