package com.hxdi.nmjl.domain.portal;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 包含文章、资料库、供应商信息等
 */
@Getter
@Setter
@TableName("CMS_RESOURCES")
public class CmsResources implements Serializable {
    private static final long serialVersionUID = 1L;


    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value="模块：1-公文类别，2-资料类别，3-供应商类别")
    private Integer module;

    /**
    * 所属栏目
    */
    @ApiModelProperty(value="所属栏目")
    private String categoryId;

    /**
    * 标题
    */
    @ApiModelProperty(value="标题")
    private String title;

    /**
    * 缩略图
    */
    @ApiModelProperty(value="缩略图")
    private String img;

    /**
    * 内容
    */
    @ApiModelProperty(value="内容")
    private String content;

    /**
    * 来源
    */
    @ApiModelProperty(value="来源")
    private String source;

    /**
    * 作者
    */
    @ApiModelProperty(value="作者")
    private String author;

    /**
    * 引用地址
    */
    @ApiModelProperty(value="引用地址")
    private String refUrl;

    /**
    * 是否公开:0-否，1-是
    */
    @ApiModelProperty(value="是否公开:0-否，1-是")
    private Integer publicIs;

    /**
    * 是否轮播展示：0-否，1-是
    */
    @ApiModelProperty(value="是否轮播展示：0-否，1-是")
    private Integer displayIs;

    /**
    * 发布时间
    */
    @ApiModelProperty(value="发布时间")
    private Date publishTime;

    /**
    * 排序
    */
    @ApiModelProperty(value="排序")
    private Integer sorts;

    /**
    * 点击次数
    */
    @ApiModelProperty(value="点击次数")
    private Integer hits;

    /**
    * 发布状态：0-否，1-是
    */
    @ApiModelProperty(value="发布状态：0-否，1-是")
    private Integer publishState;

    /**
    * 状态: 0-删除，1-有效
    */
    @ApiModelProperty(value="状态: 0-删除，1-有效")
    private Integer enabled;

    /**
    * 附件
    */
    @ApiModelProperty(value="附件")
    private String attachment;

    /**
     * 栏目路径: 用于增强检索
     */
    private String categoryPath;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
    * 更新时间
    */
    @ApiModelProperty(value="更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
    * 创建id
    */
    @ApiModelProperty(value="创建id")
    @TableField(fill = FieldFill.INSERT)
    private String createId;

    /**
    * 更新id
    */
    @ApiModelProperty(value="更新id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
    * 租户id
    */
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
    * 组织
    */
    @ApiModelProperty(value="组织")
    @TableField(fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**********************以下为扩展字段**********************/


    /**
     * 内容数据
     */
    @ApiModelProperty(value="内容数据")
    @TableField(exist = false)
    private String contentData;

    /**
     * 图片数据
     */
    @ApiModelProperty(value="图片数据")
    @TableField(exist = false)
    private String imgData;

    /**
     * 栏目信息
     */
    @TableField(exist = false)
    private CmsCategories cmsCategories;
}
