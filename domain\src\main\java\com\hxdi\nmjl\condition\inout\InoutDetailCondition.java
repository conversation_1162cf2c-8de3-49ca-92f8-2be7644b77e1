package com.hxdi.nmjl.condition.inout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "业务查询参数")
@Setter
@Getter
public class InoutDetailCondition extends QueryCondition {

    @ApiModelProperty(value = "出入库类型")
    private String inoutType;

    @ApiModelProperty(value = "业务类型")
    private String inoutBizType;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "状态：1-正常(默认查询)，0-作废")
    private Integer enabled = 1;
}
