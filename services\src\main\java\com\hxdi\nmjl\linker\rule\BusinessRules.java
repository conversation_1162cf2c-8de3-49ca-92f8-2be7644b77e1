package com.hxdi.nmjl.linker.rule;

import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.ProductionSupplier;

/**
 * <AUTHOR>
 * @date 2021/9/8 7:21 下午
 * @description 业务规则定义，提供系统逻辑约束与控制
 * 保证系统单向、简洁依赖关系
 * @version 1.0
 */
public interface BusinessRules {


    /**
     * 验证品种目录是否可删除
     * @param catalog
     * @return
     */
    boolean removeVerify(Catalog catalog);

    /**
     * 验证供应商是否可删除
     * @param productionSupplier
     * @return
     */
    boolean removeVerify(ProductionSupplier productionSupplier);
}
