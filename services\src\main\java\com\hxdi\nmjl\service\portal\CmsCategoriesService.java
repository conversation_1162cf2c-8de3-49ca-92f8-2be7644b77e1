package com.hxdi.nmjl.service.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.portal.CategoriesCondition;
import com.hxdi.nmjl.domain.portal.CmsCategories;

import java.util.List;


/**
 * 门户栏目管理接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/18 16:51
 */
public interface CmsCategoriesService extends IBaseService<CmsCategories> {

    /**
     * 新增
     * @param categories
     */
    void create(CmsCategories categories);

    /**
     * 修改
     * @param categories
     */
    void update(CmsCategories categories);

    /**
     * 删除
     * @param id
     */
    void remove(String id);


    /**
     * 分页查询
     * @param condition
     * @return
     */
    Page<CmsCategories> pages(CategoriesCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    List<CmsCategories> lists(CategoriesCondition condition);

    /**
     * 查询详情
     * @param categoryId
     * @return
     */
    CmsCategories getDetail(String categoryId);

    /**
     * 查询基础信息
     * @param categoryId
     * @return
     */
    CmsCategories getBaseDetail(String categoryId);

    /**
     * 查询树结构
     * @param module 模块: 1-公文类别，2-资料类别，3-供应商类别
     * @return
     */
    List<CmsCategories> tree(Integer module);
}
