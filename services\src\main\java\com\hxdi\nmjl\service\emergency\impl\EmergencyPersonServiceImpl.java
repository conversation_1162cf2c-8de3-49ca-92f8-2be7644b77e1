package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.emergency.EmergencyPersonCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyPerson;
import com.hxdi.nmjl.mapper.emergency.EmergencyPersonMapper;
import com.hxdi.nmjl.service.emergency.EmergencyPersonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应急人员管理服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class EmergencyPersonServiceImpl extends BaseServiceImpl<EmergencyPersonMapper, EmergencyPerson> implements EmergencyPersonService {

    @Resource
    private EmergencyPersonMapper emergencyPersonMapper;

    @Override
    public Page<EmergencyPerson> getPages(EmergencyPersonCondition condition) {
        Page<EmergencyPerson> page = condition.newPage();
        return emergencyPersonMapper.getPages(condition, page);
    }

    @Override
    public List<EmergencyPerson> getList(EmergencyPersonCondition condition) {
        return emergencyPersonMapper.getList(condition);
    }

    @Override
    public EmergencyPerson getDetail(String id) {
        return emergencyPersonMapper.selectById(id);
    }

    @Override
    public void add(EmergencyPerson emergencyPerson) {
        // 设置基础信息
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        String userId = baseUserDetails.getUserId();
        String tenantId = baseUserDetails.getTenantId();
        String dataHierarchyId = baseUserDetails.getDataHierarchyId();

        emergencyPerson.setCreateId(userId);
        emergencyPerson.setUpdateId(userId);
        emergencyPerson.setTenantId(tenantId);
        emergencyPerson.setDataHierarchyId(dataHierarchyId);

        if (!this.save(emergencyPerson)) {
            BizExp.pop("应急人员信息保存失败");
        }
    }

    @Override
    public void update(EmergencyPerson emergencyPerson) {
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        emergencyPerson.setUpdateId(baseUserDetails.getUserId());

        if (!this.updateById(emergencyPerson)) {
            BizExp.pop("应急人员信息更新失败");
        }
    }

    @Override
    public boolean delete(String id) {
        EmergencyPerson emergencyPerson = this.getById(id);
        if (emergencyPerson == null) {
            BizExp.pop("应急人员信息不存在");
        }
        if (emergencyPerson.getEnabled() == 0) {
            BizExp.pop("该应急人员已被删除");
        }

        return this.removeById(id);
    }
}
