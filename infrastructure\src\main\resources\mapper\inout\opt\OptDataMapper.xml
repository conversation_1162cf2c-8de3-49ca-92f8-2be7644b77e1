<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.opt.OptDataMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.opt.OptData">
        <result column="ID" property="id" />
        <result column="OPT_ID" property="optId" />
        <result column="DEVICE_ID" property="deviceId" />
        <result column="DATA_ORIGIN" property="dataOrigin" />
        <result column="AREA_POINT" property="areaPoint" />
        <result column="WD1" property="wd1" />
        <result column="SD1" property="sd1" />
        <result column="WD2" property="wd2" />
        <result column="SD2" property="sd2" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
        <result column="DEVICE_NAME" property="deviceName" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,OPT_ID,DEVICE_ID,DATA_ORIGIN,AREA_POINT,WD1,
        SD1,WD2,SD2,TENANT_ID,DATA_HIERARCHY_ID,DEVICE_NAME
    </sql>


</mapper>
