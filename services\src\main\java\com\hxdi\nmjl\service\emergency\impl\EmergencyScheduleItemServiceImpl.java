package com.hxdi.nmjl.service.emergency.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.emergency.EmergencyScheduleItemCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyScheduleItem;


import com.hxdi.nmjl.mapper.emergency.EmergencyScheduleItemMapper;
import com.hxdi.nmjl.service.emergency.EmergencyScheduleItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**

 紧急调度项目服务实现类
 */
@Service
@Transactional (rollbackFor = Exception.class)
public class EmergencyScheduleItemServiceImpl extends BaseServiceImpl<EmergencyScheduleItemMapper, EmergencyScheduleItem> implements EmergencyScheduleItemService {



    @Override
    public void removeBatch(String scheduleId) {
        QueryWrapper<EmergencyScheduleItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("SCHEDULE_ID", scheduleId);
        this.remove(queryWrapper);
    }

    @Override
    public EmergencyScheduleItem getDetail(String itemId) {
        return baseMapper.selectById(itemId);
    }
    @Override
    public Page<EmergencyScheduleItem> pages(EmergencyScheduleItemCondition condition) {
        Page<EmergencyScheduleItem> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }
    @Override
    public List<EmergencyScheduleItem> lists(EmergencyScheduleItemCondition condition) {
        return baseMapper.selectListV1(condition);
    }

}