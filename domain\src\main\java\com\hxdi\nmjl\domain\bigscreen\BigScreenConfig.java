package com.hxdi.nmjl.domain.bigscreen;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 大屏配置类,包含统计粮食品种配置
 * @author: 王贝强
 * @create: 2025-07-29 10:52
 */
@Setter
@Getter
@TableName(value = "B_BIG_SCREEN_CONFIG")
@ApiModel(description = "大屏配置类")
public class BigScreenConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    public Integer id;

    @ApiModelProperty(value = "配置项，见枚举BigScreenConfigKey")
    @TableField(value = "KEY")
    public String key;

    @ApiModelProperty(value = "配置值")
    @TableField(value = "VALUE")
    public String value;

    @ApiModelProperty(value = "配置描述")
    @TableField(value = "\"DESC\"")
    public String desc;

    @ApiModelProperty(value = "可选：扩展json字段")
    @TableField(value = "JSON_EXT")
    public String JsonExt;

    @ApiModelProperty(value = "状态:1-有效，0-删除")
    @TableField(value = "ENABLED")
    public Integer enabled;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    public Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    public Date updateTime;


    //------------非实体字段----------------
    @TableField(exist = false)
    @ApiModelProperty(value = "对应配置的品种列表 (只在key->categoryClassification时有效)")
    public List<String> catalogId;
}
