package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.model.Query;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.plan.InspectionItem;
import com.hxdi.nmjl.domain.plan.InspectionPlan;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.InspectionPlanMapper;
import com.hxdi.nmjl.service.plan.InspectionItemService;
import com.hxdi.nmjl.service.plan.InspectionPlanService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * <监督检查计划服务实现>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/13 18:51
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InspectionPlanServiceImpl extends BaseServiceImpl<InspectionPlanMapper, InspectionPlan> implements InspectionPlanService {

    @Resource
    private InspectionItemService inspectionItemService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public InspectionPlan getById(Serializable id) {
        InspectionPlan inspectionPlan = baseMapper.selectById(id);
        inspectionPlan.setDetailList(inspectionItemService.getListByPid((String) id));
        return inspectionPlan;
    }

    @Override
    public List<InspectionPlan> getList(Query condition) {
        List<InspectionPlan> list = super.getList(condition);
        if(CommonUtils.isNotEmpty(list)){
            for (InspectionPlan plan : list) {
                List<InspectionItem> detailList = inspectionItemService.getListByPid(plan.getId());
                detailList.forEach(item -> {
                    item.setResult(null);
                    item.setState(null);
                });
                plan.setDetailList(detailList);
            }
        }
        return list;
    }

    @Override
    public boolean saveOrUpdate(InspectionPlan entity) {
        if (CommonUtils.isEmpty(entity.getId())) {
            create(entity);
        } else {
            update(entity);
        }

        return true;
    }

    public void create(InspectionPlan plan) {
        //生成计划编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("INSPECTION_PLAN_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        plan.setPlanCode((String) businessCode.getValue());
        plan.setState(0);

        baseMapper.insert(plan);
        inspectionItemService.updateList(plan.getId(), plan.getDetailList());
    }

    public void update(InspectionPlan plan) {
        baseMapper.updateById(plan);
        inspectionItemService.updateList(plan.getId(), plan.getDetailList());
    }

    @Override
    public void softRemoveById(Serializable id) {
        InspectionPlan plan = this.getById(id);
        if (plan.getState() == 1 || plan.getState() == 2) {
            BizExp.pop("监督检查计划正在执行中不能删除！");
        }

        super.softRemoveById(id);
    }

    @Override
    public void cancel(String id) {
        InspectionPlan plan = this.getById(id);
        if (plan.getState() == 2) {
            BizExp.pop("监督检查计划执行完成不能取消！");
        }

        if (plan.getState() == 1) {
            plan.setState(3);
            baseMapper.updateById(plan);
        }
    }

    @Override
    public void updateState(String code, Integer state) {
        InspectionPlan plan = this.getOne(Wrappers.<InspectionPlan>lambdaQuery().eq(InspectionPlan::getPlanCode, code));

        InspectionPlan updatingPlan = null;
        if (state == 1 && plan.getState() == 0) {
            updatingPlan = new InspectionPlan();
            updatingPlan.setState(state);
        } else if (state == 2) {
            updatingPlan = new InspectionPlan();
            updatingPlan.setState(state);
        }

        if (updatingPlan != null) {
            updatingPlan.setId(plan.getId());
            baseMapper.updateById(updatingPlan);
        }
    }
}
