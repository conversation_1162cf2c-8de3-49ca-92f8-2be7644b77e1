package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.SaleCondition;
import com.hxdi.nmjl.domain.plan.SaleOrder;
import com.hxdi.nmjl.service.plan.SaleOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <销售订单管理接口>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/25 14:22
 */
@Api(tags = "销售订单管理")
@RestController
@RequestMapping("/sales")
public class SaleOrderController extends BaseController<SaleOrderService, SaleOrder> {

    @ApiOperation("分页查询(带权限控制)")
    @GetMapping("/page")
    public ResultBody<Page<SaleOrder>> pages(SaleCondition condition) {
        return ResultBody.<Page<SaleOrder>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("分页查询(销售订单->出库任务单->出库记录)(带权限控制)")
    @GetMapping("/pageV1")
    public ResultBody<Page<SaleOrder>> pagesV1(SaleCondition condition) {
        return ResultBody.<Page<SaleOrder>>OK().data(bizService.PagesV1(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<SaleOrder>> list(SaleCondition condition) {
        return ResultBody.<List<SaleOrder>>OK().data(bizService.lists(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/listV1")
    public ResultBody<List<SaleOrder>> listV1(SaleCondition condition) {
        return ResultBody.<List<SaleOrder>>OK().data(bizService.listsV1(condition));
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<SaleOrder> getDetail(@RequestParam String orderId) {
        return ResultBody.<SaleOrder>OK().data(bizService.getDetail(orderId));
    }

    @ApiOperation("保存/修改销售订单")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody SaleOrder saleOrder) {
        if (CommonUtils.isEmpty(saleOrder.getId())) {
            bizService.createV1(saleOrder);
        } else {
            bizService.updateV1(saleOrder);
        }
        return ResultBody.OK();
    }

    @ApiOperation("提交销售订单")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String orderId) {
        bizService.submitV1(orderId);
        return ResultBody.OK();
    }

    @ApiOperation("审批销售订单")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.OK();
    }

    @ApiOperation("驳回销售订单")
    @GetMapping("/reject")
    public ResultBody<Void> reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.OK();
    }

    @ApiOperation("删除销售订单")
    @GetMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String orderId) {
        bizService.removeV1(orderId);
        return ResultBody.OK();
    }

    @ApiOperation("更新订单结算状态")
    @GetMapping("/updateSettlementStatus")
    public ResultBody<Void> updateSettlementStatus(@RequestParam String orderIds) {
        bizService.updateSettlementStatus(orderIds);
        return ResultBody.OK();
    }

}
