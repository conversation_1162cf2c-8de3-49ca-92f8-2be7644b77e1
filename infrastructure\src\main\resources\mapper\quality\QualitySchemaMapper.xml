<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.quality.QualitySchemaMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.quality.QualitySchema">
    <!--@mbg.generated-->
    <!--@Table B_QUALITY_SCHEMA-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SCHEMA_NAME" jdbcType="VARCHAR" property="schemaName" />
    <result column="CLASSIFICATION_ID" jdbcType="VARCHAR" property="classificationId" />
    <result column="CLASSIFICATION_NAME" jdbcType="VARCHAR" property="classificationName" />
    <result column="ISDEFAULT" jdbcType="INTEGER" property="isdefault" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCHEMA_NAME, CLASSIFICATION_ID, CLASSIFICATION_NAME, ISDEFAULT, ENABLED, CREATE_TIME,
    UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
  </sql>

  <select id="selectDefaultByClassificationId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_QUALITY_SCHEMA
    <where>
      <if test="classificationId != null and classificationId != ''">
        and CLASSIFICATION_ID = #{classificationId}
      </if>
      and ENABLED = 1
      and ISDEFAULT = 1
    </where>
  </select>

  <select id="selectListV1" parameterType="com.hxdi.nmjl.condition.quality.QuatitySchemaCondition"
          resultMap="BaseResultMap">
    select distinct s.*
    from B_QUALITY_SCHEMA s
    left join C_CATALOG c on s.CLASSIFICATION_ID = c.CLASSIFICATION_ID
    <where>
      <if test="condition.schemaName != null and condition.schemaName != ''">
        and s."SCHEMA_NAME" like concat('%', #{condition.schemaName}, '%')
      </if>
      <if test="condition.catalogId != null and condition.catalogId != ''">
        and c.ID = #{condition.catalogId}
      </if>
      <if test="condition.catalogName != null and condition.catalogName != ''">
        and c.CATALOG_NAME like concat('%', #{condition.catalogName}, '%')
      </if>
      <if test="condition.classificationId != null and condition.classificationId != ''">
        and s.CLASSIFICATION_ID = #{condition.classificationId}
      </if>
      <if test="condition.classificationName != null and condition.classificationName != ''">
        and s.CLASSIFICATION_NAME like concat('%', #{condition.classificationName}, '%')
      </if>
      and s.ENABLED = 1
    </where>
    ORDER BY s.CREATE_TIME DESC
  </select>

  <select id="selectPageV1" parameterType="com.hxdi.nmjl.condition.quality.QuatitySchemaCondition"
          resultMap="BaseResultMap">
    select distinct s.*
    from B_QUALITY_SCHEMA s
    left join C_CATALOG c on s.CLASSIFICATION_ID = c.CLASSIFICATION_ID
    <where>
      <if test="condition.schemaName != null and condition.schemaName != ''">
        and s."SCHEMA_NAME" like concat('%', #{condition.schemaName}, '%')
      </if>
      <if test="condition.catalogId != null and condition.catalogId != ''">
        and c.ID = #{condition.catalogId}
      </if>
      <if test="condition.catalogName != null and condition.catalogName != ''">
        and c.CATALOG_NAME like concat('%', #{condition.catalogName}, '%')
      </if>
      <if test="condition.classificationId != null and condition.classificationId != ''">
        and s.CLASSIFICATION_ID = #{condition.classificationId}
      </if>
      <if test="condition.classificationName != null and condition.classificationName != ''">
        and s.CLASSIFICATION_NAME like concat('%', #{condition.classificationName}, '%')
      </if>
      and s.ENABLED in (1,0)
    </where>
    ORDER BY s.CREATE_TIME DESC
  </select>

  <update id="changeStatus">
    update B_QUALITY_SCHEMA set ENABLED = #{status}
    where ID = #{schemaId} and ENABLED in (1,0)
  </update>
</mapper>
