package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.ClientInfo;
import com.hxdi.nmjl.condition.base.ClientInfoCondition;

import java.util.List;

public interface ClientInfoService extends IBaseService<ClientInfo> {
    /**
     * 插入或更新 存在id则为更新，不存在则为插入（插入时若存在相似客户信息则转为更新）
     *
     * @param client
     * @return
     */
    ClientInfo updateV1(ClientInfo client, Boolean isSupplier);

    ClientInfo createV1(ClientInfo client);

    /**
     * 根据客户id删除
     * @param client
     */
    void remove(String client);

    /**
     * 根据供应商记录状态更新或删除客户信息
     * @param refId
     */
    String removeByRefId(String refId, Integer enabled);

    /**
     * 根据id查询单条数据
     * @param id
     * @return
     */
    ClientInfo selectOne(String id);
    /**
     * 分页查询
     * @param condition 查询条件
     */
    Page<ClientInfo> getPage(ClientInfoCondition condition);

    /**
     * 列表查询
     * @param condition 查询条件
     * @return
     */
    List<ClientInfo> getList(ClientInfoCondition condition);

    /**
     * 缓存加载时使用的列表查询
     * @return
     */
    List<ClientInfo> lists();

    /**
     * 搜索列表查询-无权限控制
     * @param condition
     * @return
     */
    List<ClientInfo> searchList(ClientInfoCondition condition);
}
