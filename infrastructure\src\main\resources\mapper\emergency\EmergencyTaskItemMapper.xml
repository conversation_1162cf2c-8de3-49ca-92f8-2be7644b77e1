<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.EmergencyTaskItemMapper">

    <sql id="Base_Column_List">
        ID, SCHEDULIE_ID, TASK_ID, CATALOG_ID, CATALOG_NAME,
        SPECIFICATION, GRADE, QTY, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.EmergencyTaskItem">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="SCHEDULIE_ID" jdbcType="VARCHAR" property="scheduleId" />
        <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
        <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId" />
        <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
        <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification" />
        <result column="GRADE" jdbcType="VARCHAR" property="grade" />
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_EMERGENCY_TASK_ITEM
        <where>
            <if test="condition.schedulieId != null and condition.schedulieId != ''">
                AND SCHEDULIE_ID = #{condition.schedulieId}
            </if>
            <if test="condition.taskId != null and condition.taskId != ''">
                AND TASK_ID = #{condition.taskId}
            </if>
            <if test="condition.catalogId != null and condition.catalogId != ''">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="condition.specification != null and condition.specification != ''">
                AND SPECIFICATION = #{condition.specification}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                AND GRADE = #{condition.grade}
            </if>
        </where>
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM B_EMERGENCY_TASK_ITEM
        <where>
            <if test="condition.schedulieId != null and condition.schedulieId != ''">
                AND SCHEDULIE_ID = #{condition.schedulieId}
            </if>
            <if test="condition.taskId != null and condition.taskId != ''">
                AND TASK_ID = #{condition.taskId}
            </if>
            <if test="condition.catalogId != null and condition.catalogId != ''">
                AND CATALOG_ID = #{condition.catalogId}
            </if>
            <if test="condition.specification != null and condition.specification != ''">
                AND SPECIFICATION = #{condition.specification}
            </if>
            <if test="condition.grade != null and condition.grade != ''">
                AND GRADE = #{condition.grade}
            </if>
        </where>
    </select>

</mapper>
