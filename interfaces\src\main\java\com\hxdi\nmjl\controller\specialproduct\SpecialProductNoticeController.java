package com.hxdi.nmjl.controller.specialproduct;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;

import com.hxdi.nmjl.domain.specialproduct.SpecialProductNotice;
import com.hxdi.nmjl.service.specialproduct.SpecialProductNoticeService;
import com.hxdi.nmjl.condition.specialproduct.SpecialProductNoticeCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 地方特色产品征集公告管理
 <AUTHOR>
 @version 1.0
 @since 2025/7/16
 */
@Api(tags = "地方特色产品征集公告管理")
@RestController
@RequestMapping("/specialProductNotice")
public class SpecialProductNoticeController extends BaseController<SpecialProductNoticeService, SpecialProductNotice> {

    @ApiOperation("分页查询公告")
    @GetMapping("/page")
    public ResultBody<Page<SpecialProductNotice>> page(SpecialProductNoticeCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }


    @ApiOperation("列表查询公告")
    @GetMapping("/list")
    public ResultBody<List<SpecialProductNotice>> list(SpecialProductNoticeCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }


    @ApiOperation("保存/修改公告")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody SpecialProductNotice notice) {
        if (CommonUtils.isEmpty(notice.getId())) {
            bizService.create(notice);
        } else {
            bizService.update(notice);
        }
        return ResultBody.ok();
    }


    @ApiOperation("查看公告详情")
    @GetMapping("/getDetail")
    public ResultBody<SpecialProductNotice> getDetail(@RequestParam String noticeId) {
        return ResultBody.ok().data(bizService.getDetail(noticeId));
    }


    @ApiOperation("删除公告")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String noticeId) {
        bizService.remove(noticeId);
        return ResultBody.ok();
    }


    @ApiOperation("发布公告")
    @PostMapping("/submit")
    public ResultBody publish(@RequestParam String noticeId) {
        bizService.publish(noticeId);
        return ResultBody.ok();
    }


    @ApiOperation("撤销公告")
    @PutMapping("/revoke")
    public ResultBody revoke(@RequestParam String noticeId) {
        bizService.revoke(noticeId);
        return ResultBody.ok();
    }

}
