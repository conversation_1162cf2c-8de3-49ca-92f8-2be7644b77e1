package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description="库存预警配置")
@TableName(value = "B_INVENTORY_ALARM_CONFIG")
public class InventoryAlarmConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="军供站ID")
    private String storeId;

    /**
     * 军供站名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value="军供站名称")
    private String storeName;

    /**
     * 品类ID
     */
    @TableField(value = "CLASSIFICATION_ID")
    @ApiModelProperty(value="品类ID")
    private String classificationId;

    /**
     * 品类名称
     */
    @TableField(value = "CLASSIFICATION_NAME")
    @ApiModelProperty(value="品类名称")
    private String classificationName;

    /**
     * 告警上限数量
     */
    @TableField(value = "MAX_LIMIT")
    @ApiModelProperty(value="告警上限数量")
    private BigDecimal maxLimit;

    /**
     * 告警下限数量
     */
    @TableField(value = "MIN_LIMIT")
    @ApiModelProperty(value="告警下限数量")
    private BigDecimal minLimit;

    @TableField(value = "RECEIVER")
    @ApiModelProperty(value="接收人列表(','分隔符)")
    private String receiver;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建日期")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}
