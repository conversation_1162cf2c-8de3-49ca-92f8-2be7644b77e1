package com.hxdi.nmjl.mapper.store;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.store.StoreApplyCondition;
import com.hxdi.nmjl.domain.store.StoreApply;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StoreApplyMapper extends SuperMapper<StoreApply> {

    Page<StoreApply> selectPageV1(Page<StoreApply> page, @Param("condition") StoreApplyCondition condition);

    List<StoreApply> selectListV1(@Param("condition") StoreApplyCondition condition);
}
