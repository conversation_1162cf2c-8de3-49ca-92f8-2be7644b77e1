package com.hxdi.nmjl.domain.quality;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(description="质检方案")
@TableName(value = "B_QUALITY_SCHEMA")
public class QualitySchema implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 方案名称
     */
    @TableField(value = "\"SCHEMA_NAME\"")
    @ApiModelProperty(value="方案名称")
    private String schemaName;

    /**
     * 品类ID
     */
    @TableField(value = "CLASSIFICATION_ID")
    @ApiModelProperty(value="品类ID")
    private String classificationId;

    /**
     * 品类名称
     */
    @TableField(value = "CLASSIFICATION_NAME")
    @ApiModelProperty(value="品类名称")
    private String classificationName;

    /**
     * 是否默认:0-否，1-是
     */
    @TableField(value = "ISDEFAULT")
    @ApiModelProperty(value="是否默认:0-否，1-是")
    private Integer isdefault;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value="状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建日期")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}