package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.plan.BrandInfo;
import com.hxdi.nmjl.service.plan.BrandInfoService;
import com.hxdi.nmjl.condition.plan.BrandInfoCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 品牌信息管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/7
 */
@Api(tags = "品牌信息管理")
@RestController
@RequestMapping("/brand/info")
public class BrandInfoController extends BaseController<BrandInfoService, BrandInfo> {

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<BrandInfo>> list(BrandInfoCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<BrandInfo>> page(BrandInfoCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation("企业详情查询")
    @GetMapping("/getDetail")
    public ResultBody<BrandInfo> detail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("保存/修改企业信息")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody BrandInfo brandInfo) {
        if (CommonUtils.isEmpty(brandInfo.getId())) {
            bizService.create(brandInfo);
        } else {
            bizService.update(brandInfo);
        }
        return ResultBody.ok();
    }

    @ApiOperation("删除")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

}
