package com.hxdi.nmjl.condition.base;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 供应商沟通记录查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-11
 */
@Getter
@Setter
@ApiModel(description = "供应商沟通记录查询条件")
public class SupplierCommunicationCondition extends QueryCondition {

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称（模糊查询）")
    private String supplierName;

    @ApiModelProperty(value = "沟通类型：1-在线报价，2-即时交互，3-消息通知，4-文件传输，5-其他")
    private Integer communicationType;

    @ApiModelProperty(value = "沟通标题（模糊查询）")
    private String title;

    @ApiModelProperty(value = "优先级：1-低，2-中，3-高，4-紧急")
    private Integer priority;

    @ApiModelProperty(value = "发起方组织ID")
    private String orgId;

    @ApiModelProperty(value = "发起方组织名称")
    private String orgName;

    @ApiModelProperty(value = "沟通发起方ID")
    private String initiatorId;

    @ApiModelProperty(value = "沟通发起方名称（模糊查询）")
    private String initiatorName;

    @ApiModelProperty(value = "沟通状态：1-待处理，2-处理中，3-已处理")
    private Integer status;

    @ApiModelProperty(value = "处理人ID")
    private String handlerId;

    @ApiModelProperty(value = "处理人名称（模糊查询）")
    private String handlerName;

    @ApiModelProperty(value = "创建开始时间")
    private Date startCreateTime;

    @ApiModelProperty(value = "创建结束时间")
    private Date endCreateTime;

    @ApiModelProperty(value = "处理开始时间")
    private Date startHandleTime;

    @ApiModelProperty(value = "处理结束时间")
    private Date endHandleTime;

    @ApiModelProperty(value = "模糊查询：沟通标题、沟通内容、处理结果")
    private String keywords;
}
