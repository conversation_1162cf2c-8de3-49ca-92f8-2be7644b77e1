package com.hxdi.nmjl.controller.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.InoutDetailCondition;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.service.inout.InoutDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/inout/detail")
@Api(tags = "出入库记录接口")
@Validated
public class InoutDetailController extends BaseController<InoutDetailService, InoutDetail> {


    @ApiOperation(value = "新增/编辑")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody InoutDetail inoutDetail) {
        if (CommonUtils.isEmpty(inoutDetail.getId())) {
            bizService.create(inoutDetail);
        } else {
            bizService.updating(inoutDetail);
        }
        return ResultBody.OK();
    }

    @ApiOperation(value = "提交")
    @PostMapping("/commit")
    public ResultBody<Void> commit(@RequestParam String id) {
        bizService.commit(id);
        return ResultBody.OK();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.OK();
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/query")
    public ResultBody<InoutDetail> query(@RequestParam String id) {
        return ResultBody.<InoutDetail>OK().data(bizService.detail(id));
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<InoutDetail>> page(InoutDetailCondition taskCondition) {
        return ResultBody.<Page<InoutDetail>>OK().data(bizService.getPage(taskCondition));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<InoutDetail>> list(InoutDetailCondition taskCondition) {
        return ResultBody.<List<InoutDetail>>OK().data(bizService.getList(taskCondition));
    }

    @ApiOperation(value = "生成销售、生产订单统计数据(定时任务接口,请勿手动调用)")
    @PostMapping("/generateOrderStatistics")
    public ResultBody<Void> generateOrderStatistics() {
        bizService.generateOrderStatistics();
        return ResultBody.OK();
    }

}
