package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.Classification;
import com.hxdi.nmjl.dto.base.ClassificationTree;
import com.hxdi.nmjl.condition.base.ClassificationCondition;

import java.util.List;

public interface ClassificationService extends IBaseService<Classification> {

    /**
     * 创建
     * @param classification
     */
    Classification create(Classification classification);

    /**
     * 更新
     * @param classification
     */
    Classification update(Classification classification);

    /**
     * 删除一个对象，若存在子节点则不允许删除
     * @param id
     */
    void remove(String id);

    /**
     * 根据编号\ID查询
     * @param uniqueKey
     * @return
     */
    Classification getByUniqueKey(String uniqueKey);

    /**
     * 分类树
     * @return
     */
    List<ClassificationTree> classTree();

    /**
     * 分类树，包含商品目录
     * @return
     */
    List<ClassificationTree> catalogTree();

    /**
     * 分页查询
     * @param condition
     * @return
     */
    Page<Classification> pages(ClassificationCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    List<Classification> lists(ClassificationCondition condition);


}
