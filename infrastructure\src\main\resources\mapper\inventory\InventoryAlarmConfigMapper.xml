<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryAlarmConfigMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryAlarmConfig">
    <!--@mbg.generated-->
    <!--@Table B_INVENTORY_ALARM_CONFIG-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
    <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
    <result column="CLASSIFICATION_ID" jdbcType="VARCHAR" property="classificationId" />
    <result column="CLASSIFICATION_NAME" jdbcType="VARCHAR" property="classificationName" />
    <result column="MAX_LIMIT" jdbcType="DECIMAL" property="maxLimit" />
    <result column="MIN_LIMIT" jdbcType="DECIMAL" property="minLimit" />
    <result column="RECEIVER" jdbcType="VARCHAR" property="receiver"/>
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, STORE_ID, STORE_NAME, CLASSIFICATION_ID, CLASSIFICATION_NAME, MAX_LIMIT, MIN_LIMIT,RECEIVER,
    CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
  </sql>

  <select id="listV1" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_INVENTORY_ALARM_CONFIG
    <where>
      <if test="condition.storeId != null">
        and STORE_ID = #{condition.storeId}
      </if>
      <if test="condition.storeName != null">
        and STORE_NAME like concat('%',#{condition.storeName},'%')
      </if>
      <if test="condition.classificationId != null">
        and CLASSIFICATION_ID = #{condition.classificationId}
      </if>
      <if test="condition.classificationName != null">
        and CLASSIFICATION_NAME like concat('%',#{condition.classificationName},'%')
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>

  <select id="pageV1" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_INVENTORY_ALARM_CONFIG
    <where>
      <if test="condition.storeId != null">
        and STORE_ID = #{condition.storeId}
      </if>
      <if test="condition.storeName != null">
        and STORE_NAME like concat('%',#{condition.storeName},'%')
      </if>
      <if test="condition.classificationId != null">
        and CLASSIFICATION_ID = #{condition.classificationId}
      </if>
      <if test="condition.classificationName != null">
        and CLASSIFICATION_NAME like concat('%',#{condition.classificationName},'%')
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>
