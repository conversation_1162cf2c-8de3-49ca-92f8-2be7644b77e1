package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.CatalogCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.service.base.CatalogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "品种目录管理")
@RestController
@RequestMapping("/catalog")
public class CatalogController extends BaseController<CatalogService, Catalog> {

    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody Catalog catalog) {
        if (CommonUtils.isEmpty(catalog.getId())) {
            bizService.create(catalog);
        } else {
            bizService.update(catalog);
        }
        return ResultBody.OK();
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResultBody<Void> delete(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.OK();
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<Catalog> getCatalog(String uniqueKey) {
        return ResultBody.<Catalog>OK().data(bizService.getByUniqueKey(uniqueKey));
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<Catalog>> pages(CatalogCondition condition) {
        return ResultBody.<Page<Catalog>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<Catalog>> lists(CatalogCondition condition) {
        return ResultBody.<List<Catalog>>OK().data(bizService.lists(condition));
    }
}
