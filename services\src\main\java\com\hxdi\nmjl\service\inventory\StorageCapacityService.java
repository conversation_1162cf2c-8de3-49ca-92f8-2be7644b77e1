package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.StorageCapacity;
import com.hxdi.nmjl.condition.inventory.StorageCapacityCondition;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @Data 2025/7/19 15:37
 * @Description: 库容管理接口
 */
public interface StorageCapacityService extends IBaseService<StorageCapacity> {

    /**
     * 查询库容详情
     *
     * @param id 库容ID
     * @return StorageCapacity
     */
    StorageCapacity getDetail(String id);

    /**
     * 分页查询库容信息
     *
     * @param condition 查询条件
     * @return Page<StorageCapacity>
     */
    Page<StorageCapacity> getPages(StorageCapacityCondition condition);

    /**
     * 列表查询库容信息
     *
     * @param condition 查询条件
     * @return List<StorageCapacity>
     */
    List<StorageCapacity> getList(StorageCapacityCondition condition);

    /**
     * 创建库容
     *
     * @param storageCapacity 库容信息
     */
    void add(StorageCapacity storageCapacity);

    /**
     * 修改库容
     *
     * @param storageCapacity 库容信息
     */
    void update(StorageCapacity storageCapacity);

    /**
     * 删除库容信息
     *
     * @param id 库容ID
     * @return boolean
     */
    boolean delete(String id);

}
