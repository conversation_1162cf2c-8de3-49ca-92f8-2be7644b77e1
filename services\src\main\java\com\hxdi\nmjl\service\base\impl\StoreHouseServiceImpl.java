package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.OrganizationCondition;
import com.hxdi.nmjl.condition.base.StorehouseCondition;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.dto.base.StoreCapacityDTO;
import com.hxdi.nmjl.mapper.base.StoreHouseMapper;
import com.hxdi.nmjl.service.base.OrganizationService;
import com.hxdi.nmjl.service.base.StoreHouseService;
import com.hxdi.nmjl.service.bigscreen.InfoAreaService;
import com.hxdi.nmjl.utils.RedisKeys;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <仓房管理实现>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/12 14:01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StoreHouseServiceImpl extends BaseServiceImpl<StoreHouseMapper, StoreHouse> implements StoreHouseService {


    @Resource
    private OrganizationService organizationService;

    @Resource
    private InfoAreaService infoAreaService;

    @Override
    @CachePut(value = RedisKeys.Prefix.STORE_HOUSE, key = "#storehouse.id", unless = "#result == null")
    public StoreHouse create(StoreHouse storehouse) {
        verifyStorehouse(storehouse, 0);
        storehouse.setId(IdWorker.getIdStr(storehouse));
        storehouse.setStCode(storehouse.getId());

        if (SecurityHelper.isTenantAdminUser()) {
            storehouse.setDataHierarchyId(storehouse.getStoreId());
        }

        baseMapper.insert(storehouse);
        return getByUniqueKey(storehouse.getId());
    }

    @Override
    @CachePut(value = RedisKeys.Prefix.STORE_HOUSE, key = "#storehouse.id", unless = "#result == null")
    public StoreHouse update(StoreHouse storehouse) {
        verifyStorehouse(storehouse, 1);
        baseMapper.updateById(storehouse);

        return getByUniqueKey(storehouse.getId());
    }

    @Override
    @CacheEvict(value = RedisKeys.Prefix.STORE_HOUSE, key = "#id", condition = "#state == 7 OR #state == 0")
    @CachePut(value = RedisKeys.Prefix.STORE_HOUSE, key = "#id", condition = "#state == 1")
    public StoreHouse changeState(String id, Integer state) {
        StoreHouse storehouse = getByUniqueKey(id);
        if (StrPool.State.DELETE == storehouse.getEnabled().intValue()) {
            return null;
        }

        StoreHouse updatingStorehouse = new StoreHouse();
        updatingStorehouse.setId(id);
        updatingStorehouse.setEnabled(state);
        baseMapper.updateById(updatingStorehouse);

        return getByUniqueKey(id);
    }

    @Override
    public List<StoreCapacityDTO> getCapacityByStoreId(List<String> storeId) {
        return baseMapper.getCapacityByStoreId(storeId);
    }

    @Override
    public Map<String, String> getStorehouseListByAreaCodeList(String areaCode) {
        if (CommonUtils.isEmpty(areaCode)) {
            areaCode = "150000"; // 默认内蒙古自治区
        }

        // 查询直接下属的地区编码（用于最终分组统计）
        List<String> firstLevelAreaCodes = infoAreaService.getAreaCodesByParentId(areaCode);

        // 确定要统计的地区编码列表
        List<String> targetAreaCodes;
        if (firstLevelAreaCodes.isEmpty()) {
            // 没有下级地区，统计当前地区
            targetAreaCodes = Collections.singletonList(areaCode);
        } else {
            targetAreaCodes = firstLevelAreaCodes;
        }

        // 查询该地区下属的所有层级地区编码（用于查询军供站）
        List<String> allChildAreaCodes = infoAreaService.getAllChildAreaCodes(areaCode);
        if (allChildAreaCodes.isEmpty()) {
            // 没有下级地区，只查询当前地区
            allChildAreaCodes = Collections.singletonList(areaCode);
        }

        // 一次性查询所有相关地区的军供站
        OrganizationCondition orgCondition = new OrganizationCondition();
        orgCondition.setOrgType(2); // 军供站类型
        orgCondition.setAreaCode(String.join(",", allChildAreaCodes)); // 批量查询所有层级地区

        List<Organization> organizations = organizationService.lists(orgCondition);

        if (organizations.isEmpty()) {
            // 如果没有军供站，返回各一级地区容量为0
            return targetAreaCodes.stream()
                    .collect(Collectors.toMap(code -> code, code -> "0"));
        }

        // 提取所有军供站ID
        List<String> storeIds = organizations.stream()
                .map(Organization::getId)
                .collect(Collectors.toList());

        // 一次性查询所有军供站下的仓房
        StorehouseCondition storeCondition = new StorehouseCondition();
        storeCondition.setStoreId(String.join(",", storeIds)); // 批量查询多个军供站

        List<StoreHouse> storeHouses = this.lists(storeCondition);

        // 构建军供站ID到地区编码的映射
        Map<String, String> storeIdToAreaCodeMap = organizations.stream()
                .collect(Collectors.toMap(Organization::getId, Organization::getCounty));

        // 构建地区编码到下一级地区编码的映射
        Map<String, String> areaCodeToFirstLevelMap = buildAreaCodeToFirstLevelMap(areaCode, targetAreaCodes, allChildAreaCodes);

        // 按直属地区编码分组统计仓房容量
        Map<String, BigDecimal> areaCapacityMap = storeHouses.stream()
                .filter(storeHouse -> storeHouse.getCapacity() != null)
                .filter(storeHouse -> {
                    String orgAreaCode = storeIdToAreaCodeMap.get(storeHouse.getStoreId());
                    return areaCodeToFirstLevelMap.containsKey(orgAreaCode);
                })
                .collect(Collectors.groupingBy(
                        storeHouse -> {
                            String orgAreaCode = storeIdToAreaCodeMap.get(storeHouse.getStoreId());
                            return areaCodeToFirstLevelMap.get(orgAreaCode);
                        },
                        Collectors.reducing(BigDecimal.ZERO,
                                StoreHouse::getCapacity,
                                BigDecimal::add)
                ));

        // 转换为String类型的结果，确保所有直属地区都有返回值
        return targetAreaCodes.stream()
                .collect(Collectors.toMap(
                        code -> code,
                        code -> areaCapacityMap.getOrDefault(code, BigDecimal.ZERO).toString()
                ));
    }

    /**
     * 构建地区编码到一级地区编码的映射
     */
    private Map<String, String> buildAreaCodeToFirstLevelMap(String parentAreaCode, List<String> firstLevelAreaCodes, List<String> allChildAreaCodes) {
        Map<String, String> mapping = new HashMap<>();

        // 如果没有一级地区，直接映射到父地区
        if (firstLevelAreaCodes.isEmpty()) {
            for (String childCode : allChildAreaCodes) {
                mapping.put(childCode, parentAreaCode);
            }
            return mapping;
        }

        // 为每个一级地区查询其下属的所有地区
        for (String firstLevelCode : firstLevelAreaCodes) {
            List<String> subAreaCodes = infoAreaService.getAllChildAreaCodes(firstLevelCode);
            // 一级地区本身
            mapping.put(firstLevelCode, firstLevelCode);
            // 一级地区的所有下属地区
            for (String subCode : subAreaCodes) {
                mapping.put(subCode, firstLevelCode);
            }
        }

        return mapping;
    }

    @Override
    @Cacheable(value = RedisKeys.Prefix.STORE_HOUSE, key = "#id", unless = "#result == null")
    public StoreHouse getByUniqueKey(String id) {
        return getById(id);
    }

    @Override
    public Page<StoreHouse> pages(StorehouseCondition condition) {
        Page<StoreHouse> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);

        page.getRecords().forEach(item -> item.setStoreName((organizationService.getByUniqueKey(item.getStoreId())).getOrgName()));
        return page;
    }

    @Override
    public List<StoreHouse> lists(StorehouseCondition condition) {
        return baseMapper.selectListV1(condition);
    }


    /**
     * 校验仓房信息有效性
     *
     * @param storeHouse  仓房实体
     * @param expectCount 预期存在的条目
     */
    private void verifyStorehouse(StoreHouse storeHouse, int expectCount) {
        long count = baseMapper.selectCount(Wrappers.<StoreHouse>lambdaQuery()
                .eq(CommonUtils.isNotEmpty(storeHouse.getId()), StoreHouse::getId, storeHouse.getId())
                .or(r -> r.eq(StoreHouse::getName, storeHouse.getName())
                        .eq(StoreHouse::getStoreId, storeHouse.getStoreId())
                ));

        if (count > expectCount) {
            throw new BaseException("仓房信息已存在！");
        }
    }
}
