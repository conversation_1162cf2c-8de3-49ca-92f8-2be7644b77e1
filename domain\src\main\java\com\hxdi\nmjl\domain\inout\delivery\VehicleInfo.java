package com.hxdi.nmjl.domain.inout.delivery;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * (BVehicleInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-22 14:52:10
 */
@Getter
@Setter
@ApiModel(description = "车辆管理")
@TableName("B_VEHICLE_INFO")
public class VehicleInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "车辆管理Id")
    private String id;
    /**
     * 所有权类型
     */
    @TableField(value = "OWNER_TYPE")
    @ApiModelProperty(value = "所有权类型")
    private String ownerType;
    /**
     * 所属机构
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "所属机构")
    private String orgId;
    /**
     * 机构名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "机构名称")
    private String orgName;
    /**
     * 发动机编号
     */
    @TableField(value = "VIN")
    @ApiModelProperty(value = "发动机编号")
    private String vin;
    /**
     * 车牌号
     */
    @TableField(value = "VEHICLE_NO")
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    /**
     * 车辆类型
     */
    @TableField(value = "VEHICLE_TYPE")
    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;
    /**
     * 装载量
     */
    @TableField(value = "LOAD_CAP")
    @ApiModelProperty(value = "装载量")
    private String loadCap;
    /**
     * 车辆状态 1-空闲,2-在途,3-维修,4-停用
     */
    @TableField(value = "VEHICLE_STATE")
    @ApiModelProperty(value = "车辆状态")
    private Integer vehicleState;
    /**
     * 品牌
     */
    @TableField(value = "BRAND")
    @ApiModelProperty(value = "品牌")
    private String brand;
    /**
     * 型号
     */
    @TableField(value = "MODELS")
    @ApiModelProperty(value = "型号")
    private String models;
    /**
     * 购置日期
     */
    @TableField(value = "PURCHASE_DATE")
    @ApiModelProperty(value = "购置日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date purchaseDate;
    /**
     * 车主
     */
    @TableField(value = "OWNER")
    @ApiModelProperty(value = "车主")
    private String owner;
    /**
     * 车主联系方式
     */
    @TableField(value = "OWNER_PHONE")
    @ApiModelProperty(value = "车主联系方式")
    private String ownerPhone;
    /**
     * 司机
     */
    @TableField(value = "DRIVER")
    @ApiModelProperty(value = "司机")
    private String driver;
    /**
     * 司机身份证编号
     */
    @TableField(value = "ID_CARD")
    @ApiModelProperty(value = "司机身份证编号")
    private String idCard;
    /**
     * 司机联系电话
     */
    @TableField(value = "MOBILE")
    @ApiModelProperty(value = "司机联系电话")
    private String mobile;
    /**
     * 状态：0-删除，1-有效
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态：0-删除，1-有效")
    private Integer enabled;
    /**
     * 附件
     */
    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;
    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;


}

