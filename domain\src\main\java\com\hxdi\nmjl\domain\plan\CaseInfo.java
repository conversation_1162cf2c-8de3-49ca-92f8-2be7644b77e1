package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 案件管理信息
 */
@ApiModel(description = "案件管理信息")
@Getter
@Setter
@TableName("B_CASE_INFO") // 表名映射
public class CaseInfo implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("CASE_NO")
    @ApiModelProperty(value = "案件编号")
    private String caseNo;

    @TableField("CASE_NAME")
    @ApiModelProperty(value = "案件名称")
    private String caseName;

    @TableField("PLAN_ID")
    @ApiModelProperty(value = "监查计划ID")
    private String planId;

    @TableField("PLAN_CODE")
    @ApiModelProperty(value = "监察计划编号")
    private String planCode;

    @TableField("OCCUR_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "发生时间")
    private Date occurTime;

    @TableField("FILING_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "立案时间")
    private Date filingTime;

    @TableField("CLOSING_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结案时间")
    private Date closingTime;

    @TableField("ADDRESS")
    @ApiModelProperty(value = "地点")
    private String address;

    @TableField("CASE_DESC")
    @ApiModelProperty(value = "案件描述")
    private String caseDesc;

    @TableField("UNIT_NAME")
    @ApiModelProperty(value = "涉案企业")
    private String unitName;

    @TableField("LEGALS")
    @ApiModelProperty(value = "执法人员")
    private String legals;

    @TableField("ORIGIN")
    @ApiModelProperty(value = "案件来源: 1-监督检查")
    private Integer origin;

    @TableField("LAFX")
    @ApiModelProperty(value = "立案分析")
    private String lafx;

    @TableField("STATE")
    @ApiModelProperty(value = "业务状态：0-未执行，1-执行中，2-已立案，3-已结案")
    private Integer state;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    @TableField("ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;









    /********************************以下非实体字段********************************/

    @ApiModelProperty(value = "案件处理信息列表")
    @TableField(exist = false)
    List<CaseDealInfo> caseDealList;
}
