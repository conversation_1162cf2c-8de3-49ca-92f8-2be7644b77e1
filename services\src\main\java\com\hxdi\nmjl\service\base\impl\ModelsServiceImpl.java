package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.base.Models;
import com.hxdi.nmjl.mapper.base.ModelsMapper;
import com.hxdi.nmjl.service.base.ModelsService;
import com.hxdi.nmjl.condition.base.ModelsCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class ModelsServiceImpl extends BaseServiceImpl<ModelsMapper, Models> implements ModelsService {

    @Override
    public void saveOrUpdates(Models model) {
        saveOrUpdate(model);
    }

    @Override
    public void remove(String modelId) {
        baseMapper.deleteById(modelId);
    }

    @Override
    public Page<Models> pages(ModelsCondition condition) {
        Page<Models> page = condition.newPage();
        baseMapper.selectPage(page, Wrappers.<Models>lambdaQuery()
                .eq(Models::getClassificationCode, condition.getClassificationId()));

        return page;
    }

    @Override
    public List<Models> lists(ModelsCondition condition) {
        if (CommonUtils.isBlank(condition.getClassificationId())){
            return Collections.EMPTY_LIST;
        }

        List<Models> models = baseMapper.selectList(Wrappers.<Models>lambdaQuery()
                .eq(Models::getClassificationCode, condition.getClassificationId()));

        return models;
    }
}
