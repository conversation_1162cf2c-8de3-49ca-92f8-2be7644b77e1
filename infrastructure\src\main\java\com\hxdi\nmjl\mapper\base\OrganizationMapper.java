package com.hxdi.nmjl.mapper.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.condition.base.OrganizationCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrganizationMapper extends SuperMapper<Organization> {

    /**
     * 分页查询
     * @param page
     * @param condition
     * @return
     */
    @DataPermission
    Page<Organization> selectPageV1(Page<Organization> page, @Param("condition") OrganizationCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    @DataPermission
    List<Organization> selectListV1(@Param("condition") OrganizationCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    List<Organization> selectListV2(@Param("condition") OrganizationCondition condition);

    /**
     * 查询机构当前层级最大的排序数字
     * @param pid
     * @return
     */
    Integer selectMaxSeqNumber(@Param("pid") String pid);
}
