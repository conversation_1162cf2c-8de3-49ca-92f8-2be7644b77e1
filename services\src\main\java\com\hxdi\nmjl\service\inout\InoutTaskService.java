package com.hxdi.nmjl.service.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.InoutTask;
import com.hxdi.nmjl.condition.inout.InoutTaskCondition;

import java.math.BigDecimal;
import java.util.List;


public interface InoutTaskService extends IBaseService<InoutTask> {


    /**
     * 新增
     * @param inoutTask
     * @return
     */
    void create(InoutTask inoutTask);

    /**
     * 修改
     * @param task
     * @return
     */
    void updating(InoutTask task);

    /**
     * 查询
     * @param uniqueKey 主键或者编号
     * @return
     */
    InoutTask detail(String uniqueKey);

    /**
     * 分页查询
     * @param inoutTaskCondition
     * @return
     */
    Page<InoutTask> getPage(InoutTaskCondition inoutTaskCondition);

    /**
     * 查询列表
     * @param inoutTaskCondition
     * @return
     */
    List<InoutTask> getList(InoutTaskCondition inoutTaskCondition);


    /**
     * 删除
     * @param taskId
     * @return
     */
    void remove(String taskId);

    /**
     * 根据任务编号查询详情
     * @param taskCode
     * @return
     */
    InoutTask getByCode(String taskCode);

    /**
     * 更新完成数量
     * @param taskCode
     * @param changeQty
     */
    void updateCompletedQty(String taskCode, BigDecimal changeQty);

    /**
     * 获取当前用户选中的订单中已选择的品种
     * @return
     */
    List<String> getCatalogIds(String orderId,String inoutType);
}
