package com.hxdi.nmjl.service.emergency.impl;

import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.emergency.EmergencyLoss;
import com.hxdi.nmjl.domain.emergency.EmergencyLossStat;
import com.hxdi.nmjl.linker.ContextService;
import com.hxdi.nmjl.mapper.emergency.EmergencyLossMapper;
import com.hxdi.nmjl.service.emergency.EmergencyLossService;
import com.hxdi.nmjl.service.emergency.EmergencyLossStatService;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <应急损耗管理服务实现>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/15 15:49
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class EmergencyLossServiceImpl extends BaseServiceImpl<EmergencyLossMapper, EmergencyLoss> implements EmergencyLossService {


    @Autowired
    private ContextService contextService;

    @Autowired
    private EmergencyLossStatService emergencyLossStatService;

    @Override
    public boolean saveOrUpdate(EmergencyLoss entity) {
        if (CommonUtils.isEmpty(entity.getId())) {
            create(entity);
        } else {
            update(entity);
        }
        return true;
    }

    public void create(EmergencyLoss entity) {
        entity.setLossCode((String) contextService.generateCode("emergency_loss_code"));
        entity.setState(0);

        if (entity.getReportType() == 1) {
            String orgId = CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), entity.getUnitId(), Organization::getId);
            Organization parentOrg = CacheProvider.getCacheObject(RedisKeys.ORGANIZATION.key(), orgId);
            if (parentOrg != null) {
                entity.setOrgId(parentOrg.getId());
                entity.setOrgName(parentOrg.getOrgName());
            }
        }

        baseMapper.insert(entity);
    }

    public void update(EmergencyLoss entity) {
        if (entity.getState() == 1) {
            BizExp.pop("当前状态不能进行修改操作");
        }

        baseMapper.updateById(entity);
    }


    @Override
    public void confirm(String lossId) {
        EmergencyLoss loss = getById(lossId);
        if (loss.getState() == 0) {
            loss.setState(1);
            this.updateById(loss);

            EmergencyLossStat stat = new EmergencyLossStat();
            stat.setStoreId(loss.getUnitId());
            stat.setStoreName(loss.getUnitName());
            stat.setCurrentLossQty(loss.getLossQty());
            stat.setAmount(loss.getAmount());
            stat.setClassificationId(loss.getClassificationId());
            stat.setClassificationName(loss.getClassificationName());
            stat.setLossId(lossId);
            stat.setTaskCode(loss.getTaskCode());
            stat.setTaskName(loss.getTaskName());
            emergencyLossStatService.updateStat(stat);
        }
    }
}
