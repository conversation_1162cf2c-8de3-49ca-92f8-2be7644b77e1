<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.clientrelated.SupplierCreditMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.clientrelated.SupplierCredit">
        <!--@mbg.generated-->
        <!--@Table B_SUPPLIER_CREDIT-->
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="SUPPLIER_ID" jdbcType="VARCHAR" property="supplierId" />
        <result column="CHECK_UNIT" jdbcType="VARCHAR" property="checkUnit" />
        <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
        <result column="PROBLEM" jdbcType="VARCHAR" property="problem" />
        <result column="CHECK_MAN" jdbcType="VARCHAR" property="checkMan" />
        <result column="OPINION" jdbcType="VARCHAR" property="opinion" />
        <result column="PROBLEM_DESC" jdbcType="VARCHAR" property="problemDesc" />
        <result column="HANDLED_TIME" jdbcType="TIMESTAMP" property="handledTime" />
        <result column="IS_HANDLED" jdbcType="INTEGER" property="isHandled" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, SUPPLIER_ID, CHECK_UNIT, CHECK_TIME, PROBLEM, CHECK_MAN, OPINION, PROBLEM_DESC,
        HANDLED_TIME, IS_HANDLED, ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID,
        TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <update id="changeState" parameterType="com.hxdi.nmjl.domain.clientrelated.SupplierCredit">
        update B_SUPPLIER_CREDIT
        set ENABLED = #{enabled}
        where ID = #{id}
    </update>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_SUPPLIER_CREDIT
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.problem)">
                AND problem  LIKE CONCAT('%', #{condition.problem}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.supplierId)">
                AND supplier_id = #{condition.supplierId}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_SUPPLIER_CREDIT
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.problem)">
                AND problem  LIKE CONCAT('%', #{condition.problem}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.supplierId)">
                AND supplier_id = #{condition.supplierId}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>
</mapper>
