package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

/**
 * 规格
 */
@TableName(value = "C_MODELS")
@Getter
@Setter
public class Models extends Entity<Models> {

    private static final long serialVersionUID = 8622217399896031530L;

    /**
     * 规格ID
     */
    @TableField(value = "MODEL_ID")
    private String modelId;

    /**
     * 物资分类编码
     */
    @TableField(value = "CLASSIFICATION_CODE")
    private String classificationCode;

    /**
     * 规格名称
     */
    @TableField(value = "MODEL_NAME")
    private String modelName;


    /**
     * -------
     */

    @TableField(exist = false)
    private String classificationName;

    public Models() {
    }

    public Models(String classificationCode, String modelName) {
        this.classificationCode = classificationCode;
        this.modelName = modelName;
    }
}
