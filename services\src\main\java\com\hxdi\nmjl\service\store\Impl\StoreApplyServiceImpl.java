package com.hxdi.nmjl.service.store.Impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.store.StoreApplyCondition;
import com.hxdi.nmjl.domain.store.StoreApply;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.store.StoreApplyMapper;
import com.hxdi.nmjl.service.store.StoreApplyService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 店铺申请服务实现类
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class StoreApplyServiceImpl extends BaseServiceImpl<StoreApplyMapper, StoreApply> implements StoreApplyService {

    @Autowired
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public void create(StoreApply storeApply) {
        // 生成申请编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("STORE_APPLY_NO");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        storeApply.setApplyNo((String) businessCode.getValue());
        BaseUserDetails user = SecurityHelper.getUser();
        storeApply.setOrgId(user.getOrganId());
        storeApply.setOrgName(user.getOrganName());
        this.save(storeApply);
    }

    @Override
    public void update(StoreApply storeApply) {
        // 查询原申请信息
        StoreApply savedApply = this.getById(storeApply.getId());
        if (savedApply == null) {
            throw new BaseException("申请不存在");
        }

        this.updateById(storeApply);
    }

    @Override
    public StoreApply getDetail(String id) {
        StoreApply storeApply = baseMapper.selectById(id);
        if (storeApply == null || storeApply.getEnabled() == 0) {
            throw new BaseException("店铺申请不存在或已删除");
        }
        return storeApply;
    }

    @Override
    public Page<StoreApply> pages(StoreApplyCondition condition) {
        Page<StoreApply> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<StoreApply> lists(StoreApplyCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void approve(String id, Integer approveStatus, String opinion) {
        StoreApply storeApply = baseMapper.selectById(id);
        if (storeApply == null || storeApply.getEnabled() == 0) {
            throw new BaseException("店铺申请不存在或已删除");
        }
        // 校验审核状态
        if (storeApply.getApproveStatus() == 1) {
            throw new BaseException("该申请已审核，不可重复操作");
        }
        // 设置审核信息
        BaseUserDetails user = SecurityHelper.obtainUser();
        storeApply.setApproveStatus(approveStatus);
        storeApply.setApprover(user.getNickName());
        storeApply.setApproveTime(new Date());
        storeApply.setApproveOpinion(opinion);

        this.updateById(storeApply);
    }

    @Override
    public void remove(String id) {
        StoreApply storeApply = baseMapper.selectById(id);
        if (storeApply == null) {
            throw new BaseException("申请不存在");
        }

        // 逻辑删除
        storeApply.setEnabled(0);
        baseMapper.updateById(storeApply);
    }

    @Override
    public void submit(String id) {
        StoreApply storeApply = baseMapper.selectById(id);
        storeApply.setApproveStatus(0);
        baseMapper.updateById(storeApply);
    }

}