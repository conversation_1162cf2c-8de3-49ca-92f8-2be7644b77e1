<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.SaleOrderMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.SaleOrder">
    <!--@mbg.generated-->
    <!--@Table B_SALE_ORDER-->
    <id column="ID" property="id" />
    <result column="ORDER_CODE" property="orderCode" />
    <result column="PID" property="pid" />
    <result column="ORIGIN" property="origin" />
    <result column="CONTRACT_ID" property="contractId" />
    <result column="CONTRACT_CODE" property="contractCode" />
    <result column="ORDER_TIME" property="orderTime" />
    <result column="ORG_ID" property="orgId" />
    <result column="STORE_ID" property="storeId" />
    <result column="STORE_NAME" property="storeName" />
    <result column="CLIENT_ID" property="clientId" />
    <result column="CLIENT_NAME" property="clientName" />
    <result column="PAYMENT_PERIOD" property="paymentPeriod" />
    <result column="PAYMENT_END_TIME" property="paymentEndTime" />
    <result column="TOTAL_AMOUNT" property="totalAmount" />
    <result column="DISCOUNT_RATE" property="discountRate" />
    <result column="DISCOUNT_AMOUNT" property="discountAmount" />
    <result column="DELIVERY_METHOD" property="deliveryMethod" />
    <result column="STATE" property="state" />
    <result column="SETTLEMENT_STATUS" property="settlementStatus" />
    <result column="APPROVER" property="approver" />
    <result column="APPROVE_STATUS" property="approveStatus" />
    <result column="APPROVE_OPINION" property="approveOpinion" />
    <result column="APPROVE_TIME" property="approveTime" />
    <result column="PICKUP_CODE" property="pickupCode" />
    <result column="RCV_ADDR" property="rcvAddr" />
    <result column="RCV_DETAIL_ADDR" property="rcvDetailAddr" />
    <result column="NOTES" property="notes" />
    <result column="ENABLED" property="enabled" />
    <result column="ATTACHEMENTS" property="attachements" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ORDER_CODE, PID, ORIGIN, CONTRACT_ID, CONTRACT_CODE, ORDER_TIME, ORG_ID, STORE_ID, 
    STORE_NAME, CLIENT_ID, CLIENT_NAME, PAYMENT_PERIOD, PAYMENT_END_TIME, TOTAL_AMOUNT, 
    DISCOUNT_RATE, DISCOUNT_AMOUNT, DELIVERY_METHOD, "STATE", PICKUP_CODE, RCV_ADDR, 
    RCV_DETAIL_ADDR, NOTES, ENABLED, ATTACHEMENTS, CREATE_TIME, UPDATE_TIME, CREATE_ID, 
    UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID,SETTLEMENT_STATUS,APPROVER,APPROVE_STATUS,APPROVE_OPINION,APPROVE_TIME
  </sql>

  <select id="selectPageV1" resultType="com.hxdi.nmjl.domain.plan.SaleOrder">
    SELECT <include refid="Base_Column_List"/>
    FROM B_SALE_ORDER
    <where>
      ENABLED = 1
      <if test="@plugins.OGNL@isNotEmpty(condition.search)">
        AND (ORDER_CODE LIKE CONCAT('%', #{condition.search}, '%') OR CONTRACT_CODE LIKE CONCAT('%', #{condition.search}, '%'))
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.origin)">
        AND ORIGIN = #{condition.origin}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
        AND STORE_ID = #{condition.storeId}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
        AND CLIENT_ID = #{condition.clientId}
      </if>
      <if test="condition.clientName != null and condition.clientName != ''">
        AND CLIENT_NAME LIKE CONCAT('%', #{condition.clientName}, '%')
      </if>
      <if test="condition.deliveryMethod != null and condition.deliveryMethod != ''">
        AND CLIENT_NAME = #{condition.deliveryMethod}
      </if>
      <if test="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.orderState) !=null">
        AND STATE IN
        <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.orderState)" item="state" open="(" separator="," close=")">
          #{state}
        </foreach>
      </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
            AND APPROVE_STATUS = #{condition.approveStatus}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.settlementState)">
            AND SETTLEMENT_STATUS = #{condition.settlementState}
        </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
        AND ORDER_TIME &gt;= #{condition.startTime}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
        AND ORDER_TIME &lt;= #{condition.endTime}
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>

  <select id="selectListV1" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM B_SALE_ORDER
    <where>
      ENABLED = 1
      <if test="@plugins.OGNL@isNotEmpty(condition.search)">
        AND (ORDER_CODE LIKE CONCAT('%', #{condition.search}, '%') OR CONTRACT_CODE LIKE CONCAT('%', #{condition.search}, '%'))
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.origin)">
        AND ORIGIN = #{condition.origin}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
        AND STORE_ID = #{condition.storeId}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
        AND CLIENT_ID = #{condition.clientId}
      </if>
      <if test="condition.clientName != null and condition.clientName != ''">
        AND CLIENT_NAME LIKE CONCAT('%', #{condition.clientName}, '%')
      </if>
      <if test="condition.deliveryMethod != null and condition.deliveryMethod != ''">
        AND CLIENT_NAME = #{condition.deliveryMethod}
      </if>
      <if test="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.orderState) !=null">
        AND STATE IN
        <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.orderState)" item="state" open="(" separator="," close=")">
          #{state}
        </foreach>
      </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.approveStatus)">
            AND APPROVE_STATUS = #{condition.approveStatus}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.settlementState)">
            AND SETTLEMENT_STATUS = #{condition.settlementState}
        </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
        AND ORDER_TIME &gt;= #{condition.startTime}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
        AND ORDER_TIME &lt;= #{condition.endTime}
      </if>
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>