package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.ClassificationCondition;
import com.hxdi.nmjl.domain.base.Classification;
import com.hxdi.nmjl.dto.base.ClassificationTree;
import com.hxdi.nmjl.service.base.ClassificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "品类管理")
@RestController
@RequestMapping("/classification")
public class ClassificationController extends BaseController<ClassificationService, Classification> {


    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody Classification classification) {
        if (CommonUtils.isEmpty(classification.getId())) {
            create(classification);
        } else {
            update(classification);
        }
        return ResultBody.OK();
    }

    @ApiOperation("新增")
    @PostMapping("/add")
    public ResultBody<Void> create(@RequestBody Classification classification) {
        bizService.create(classification);
        return ResultBody.OK();
    }

    @ApiOperation("更新")
    @PostMapping("/update")
    public ResultBody<Void> update(@RequestBody Classification classification) {
        bizService.update(classification);
        return ResultBody.OK();
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResultBody<Void> delete(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.OK();
    }

    @ApiOperation("查询详情")
    @GetMapping("/get")
    public ResultBody<Classification> getClassification(String uniqueKey) {
        return ResultBody.<Classification>OK().data(bizService.getByUniqueKey(uniqueKey));
    }

    @ApiOperation("查询树结构")
    @GetMapping("/tree")
    public ResultBody<List<ClassificationTree>> tree(@RequestParam(required = false, value = "isOnlyClass", defaultValue = "0") Integer isOnlyClass) {
        if (isOnlyClass == 0) {
            return ResultBody.<List<ClassificationTree>>OK().data(bizService.classTree());
        } else {
            return ResultBody.<List<ClassificationTree>>OK().data(bizService.catalogTree());
        }

    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<Classification>> pages(ClassificationCondition condition) {
        return ResultBody.<Page<Classification>>OK().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<Classification>> lists(ClassificationCondition condition) {
        return ResultBody.<List<Classification>>OK().data(bizService.lists(condition));
    }
}
