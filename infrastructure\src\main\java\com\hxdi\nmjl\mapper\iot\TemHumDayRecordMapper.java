package com.hxdi.nmjl.mapper.iot;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.iot.TemHumDayRecordCondition;
import com.hxdi.nmjl.domain.iot.TemHumDayRecord;
import com.hxdi.nmjl.domain.iot.TemHumRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TemHumDayRecordMapper extends SuperMapper<TemHumDayRecord> {
    @DataPermission
    List<TemHumDayRecord> selectListV1(@Param("condition") TemHumDayRecordCondition condition);

    @DataPermission
    Page<TemHumDayRecord> selectPageV1(@Param("condition") TemHumDayRecordCondition condition, @Param("page") Page<TemHumDayRecord> page);

    /**
     * 查询指定时间段内指定仓房的温湿度数据
     * @param condition 查询条件
     * @param row_num 每row_num行取一行数据
     * @return
     */
    List<TemHumRecord> selectV2(@Param("condition") TemHumDayRecordCondition condition,@Param("row_num") int row_num);
}