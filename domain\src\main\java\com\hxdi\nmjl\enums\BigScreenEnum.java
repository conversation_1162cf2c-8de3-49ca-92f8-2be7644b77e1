package com.hxdi.nmjl.enums;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * @program: nmjl-service
 * @description: 大屏配置Key枚举
 * @author: 王贝强
 * @create: 2025-07-29 10:57
 */
@Getter
public enum BigScreenEnum {
    /**
     * 大屏配置Key
     */
    CATEGORY_CLASSIFICATION("categoryClassification","品种分类",false);

    @ApiModelProperty(value = "配置项")
    private final String key;
    @ApiModelProperty(value = "配置描述")
    private final String label;
    @ApiModelProperty(value = "是否使用扩展json字段")
    private final boolean isUseJsonExt;

    BigScreenEnum(String key, String label,boolean isUseJsonExt) {
        this.key = key;
        this.label = label;
        this.isUseJsonExt = isUseJsonExt;
    }
}
