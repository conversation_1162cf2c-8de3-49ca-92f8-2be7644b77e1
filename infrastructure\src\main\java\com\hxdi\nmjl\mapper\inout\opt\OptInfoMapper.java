package com.hxdi.nmjl.mapper.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.opt.OptInfo;
import com.hxdi.nmjl.condition.inout.OptCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_OPT_INFO(包含控制作业、防虫作业等)】的数据库操作Mapper
* @createDate 2025-04-18 09:55:20
* @Entity com.hxdi.nmjl.domain.inout.opt.OptInfo
*/
@Mapper
public interface OptInfoMapper extends SuperMapper<OptInfo> {


    /**
     * 列表查询
     * @param optCondition
     * @return
     */
    @DataPermission
    List<OptInfo> selectListV1(@Param("condition") OptCondition optCondition);

    /**
     * 分页查询
     * @param pages
     * @param optCondition
     * @return
     */
    @DataPermission
    Page<OptInfo> selectPageV1(Page<OptInfo> pages, @Param("condition") OptCondition optCondition);
}
