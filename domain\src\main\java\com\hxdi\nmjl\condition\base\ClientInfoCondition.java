package com.hxdi.nmjl.condition.base;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: nmjl-service
 * @description: 客户信息查询
 * @author: 王贝强
 * @create: 2025-04-15 10:34
 */
@ApiModel(description = "客户信息查询条件")
@Getter
@Setter
public class ClientInfoCondition extends QueryCondition {

    @ApiModelProperty(value = "关键字查询(名称/信用代码)")
    private String keywords;

    @ApiModelProperty(value = "客户名称")
    private String name;

    /**
     * 客户类型：1-供应商，2-质检机构，9-其他
     */
    @ApiModelProperty(value = "客户类型：1-供应商，2-质检机构，9-其他")
    private String clientType;

    /**
     * 企业性质
     */
    @ApiModelProperty(value = "企业性质")
    private String companyType;

    /**
     * 所在省id
     */
    @ApiModelProperty(value = "所在省id")
    private String provinceId;

    /**
     * 所在市id
     */
    @ApiModelProperty(value = "所在市id")
    private String cityId;

    /**
     * 所在县区id
     */
    @ApiModelProperty(value = "所在县区id")
    private String countyId;
}
