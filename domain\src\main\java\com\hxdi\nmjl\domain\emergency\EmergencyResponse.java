package com.hxdi.nmjl.domain.emergency;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 应急预案
 */
@ApiModel(description = "应急预案信息")
@Getter
@Setter
@TableName("B_EMERGENCY_RESPONSE") // 表名映射
public class EmergencyResponse extends BModel {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @TableField("RESPONSE_CODE")
    @ApiModelProperty(value = "预案编号")
    private String responseCode;

    @TableField("RESPONSE_NAME")
    @ApiModelProperty(value = "预案名称")
    private String responseName;

    @TableField("RESPONSE_NUMBER")
    @ApiModelProperty(value = "预案文号")
    private String responseNumber;

    @TableField("RESP_LEVEL")
    @ApiModelProperty(value = "预案级别：1-一级，2-二级，3-三级")
    private Integer responseLevel;

    @TableField("RESP_TYPE")
    @ApiModelProperty(value = "预案类别：字典YJYALB")
    private Integer responseType;

    @TableField("ORG_NAME")
    @ApiModelProperty(value = "主管部门名称")
    private String orgName;

    @TableField("ORG_ID")
    @ApiModelProperty(value = "主管部门ID")
    private String orgId;

    @TableField("PUBLISH_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "制定时间")
    private Date publishTime;

    @TableField("PUBLISH_ORG_ID")
    @ApiModelProperty(value = "印发单位ID")
    private String publishOrgId;

    @TableField("PUBLISH_ORG_NAME")
    @ApiModelProperty(value = "印发单位名称")
    private String publishOrgName;

    @TableField("PLAN_AMOUNT")
    @ApiModelProperty(value = "应急投入资金")
    private BigDecimal planAmount;

    @TableField("GROUP_NO")
    @ApiModelProperty(value = "小组编号")
    private String groupNo;

    @TableField("GROUP_NAME")
    @ApiModelProperty(value = "小组名称")
    private String groupName;

    @TableField("GROUP_TEL")
    @ApiModelProperty(value = "值班电话")
    private String groupTel;

    @TableField("LEADER_ID")
    @ApiModelProperty(value = "组长ID")
    private String leaderId;

    @TableField("LEADER")
    @ApiModelProperty(value = "组长")
    private String leader;

    @TableField("MOBILE")
    @ApiModelProperty(value = "组长电话")
    private String mobile;

    @TableField("MEMBER_IDS")
    @ApiModelProperty(value = "小组成员IDS")
    private String memberIds;

    @TableField("MEMBERS")
    @ApiModelProperty(value = "小组成员")
    private String members;

    @TableField("CREATE_NAME")
    @ApiModelProperty(value = "录入人")
    private String createName;

    @TableField("PUBLISH_STATE")
    @ApiModelProperty(value = "发布状态:0-待发布，1-已发布")
    private Integer publishState;

    @TableField("APPROVE_TIME")
    @ApiModelProperty(value = "审核时间")
    private Date approveTime;

    @TableField("APPROVE_STATUS")
    @ApiModelProperty(value = "审核状态")
    private Integer approveStatus;

    @TableField("APPROVER")
    @ApiModelProperty(value = "审核人")
    private String approver;

    @TableField("APPROVE_OPINION")
    @ApiModelProperty(value = "审核意见")
    private String approveOpinion;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    @TableField("ATTACHMENT")
    @ApiModelProperty(value = "附件")
    private String attachment;


    //明细
    @TableField(exist = false)
    private List<EmergencyResponseItem> detailList;

    @TableField(exist = false)
    @ApiModelProperty(value = "小组成员姓名列表")
    private List<String> memberNameList;

    @TableField(exist = false)
    @ApiModelProperty(value = "小组成员ID列表")
    private List<String> memberIdList;

}
