package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 订单明细
 */
@ApiModel(description="订单明细")
@Getter
@Setter
@TableName("B_SALE_DETAIL")
public class SaleDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 订单ID
    */
    @ApiModelProperty(value="订单ID")
    private String orderId;

    /**
    * 品种ID
    */
    @ApiModelProperty(value="品种ID")
    private String catalogId;

    /**
    * 品种名称
    */
    @ApiModelProperty(value="品种名称")
    private String catalogName;

    /**
    * 品牌名称
    */
    @ApiModelProperty(value="品牌名称")
    private String brand;

    /**
    * 质量等级：字典：YZLDJ/LZLDJ
    */
    @ApiModelProperty(value="质量等级：字典：YZLDJ/LZLDJ")
    private String grade;

    /**
    * 规格
    */
    @ApiModelProperty(value="规格")
    private String specification;

    /**
    * 生产日期
    */
    @ApiModelProperty(value="生产日期")
    private Date productDate;

    /**
    * 订单数量
    */
    @ApiModelProperty(value="订单数量")
    private BigDecimal contractQty;

    /**
    * 完成数量
    */
    @ApiModelProperty(value="完成数量")
    private BigDecimal completedQty;

    /**
    * 储备性质:字典CBXZ
    */
    @ApiModelProperty(value="储备性质:字典CBXZ")
    private String reserveLevel;

    /**
    * 单价
    */
    @ApiModelProperty(value="单价")
    private BigDecimal price;

    /**
    * 租户id
    */
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
    * 组织
    */
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;


}
