package com.hxdi.nmjl.service.clientrelated.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.clientrelated.ClientSaleConfigCondition;
import com.hxdi.nmjl.domain.clientrelated.ClientSaleConfig;
import com.hxdi.nmjl.mapper.clientrelated.ClientSaleConfigMapper;
import com.hxdi.nmjl.service.clientrelated.ClientSaleConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客户销售配置服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ClientSaleConfigServiceImpl extends BaseServiceImpl<ClientSaleConfigMapper, ClientSaleConfig> implements ClientSaleConfigService {

    @Resource
    private ClientSaleConfigMapper clientSaleConfigMapper;

    @Override
    public Page<ClientSaleConfig> getPages(ClientSaleConfigCondition condition) {
        Page<ClientSaleConfig> page = condition.newPage();
        return clientSaleConfigMapper.getPages(condition, page);
    }

    @Override
    public List<ClientSaleConfig> getList(ClientSaleConfigCondition condition) {
        return clientSaleConfigMapper.getList(condition);
    }

    @Override
    public ClientSaleConfig getDetail(String id) {
        return clientSaleConfigMapper.selectById(id);
    }

    @Override
    public void add(ClientSaleConfig clientSaleConfig) {
        // 校验是否重复配置（可选逻辑）
        verifyConfig(clientSaleConfig);

        BaseUserDetails baseUserDetails = SecurityHelper.getUser();

        String tenantId = baseUserDetails.getTenantId(); // 租户ID
        String dataHierarchyId = baseUserDetails.getDataHierarchyId(); // 组织层级
        String userId = baseUserDetails.getUserId(); // 用户ID

        clientSaleConfig.setCreateId(userId);
        clientSaleConfig.setUpdateId(userId);
        clientSaleConfig.setTenantId(tenantId);
        clientSaleConfig.setDataHierarchyId(dataHierarchyId);

        if (!this.save(clientSaleConfig)) {
            BizExp.pop("客户销售配置保存失败");
        }
    }

    @Override
    public void update(ClientSaleConfig clientSaleConfig) {
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        clientSaleConfig.setUpdateId(baseUserDetails.getUserId());

        if (!this.updateById(clientSaleConfig)) {
            BizExp.pop("客户销售配置更新失败");
        }
    }

    @Override
    public boolean delete(String id) {
        ClientSaleConfig config = this.getById(id);
        if (config == null) {
            BizExp.pop("客户销售配置不存在");
        }

        // 可以添加业务状态判断，比如是否启用中等，不能删除某些状态的数据
        return this.removeById(id);
    }

    /**
     * 校验是否存在重复配置
     *
     * @param clientSaleConfig 配置信息
     */
    private void verifyConfig(ClientSaleConfig clientSaleConfig) {
        if (CommonUtils.isNotEmpty(clientSaleConfig.getClientId())) {
            long count = baseMapper.selectCount(Wrappers.<ClientSaleConfig>lambdaQuery()
                    .eq(ClientSaleConfig::getClientId, clientSaleConfig.getClientId())
                    .eq(ClientSaleConfig::getEnabled, StrPool.State.ENABLE));

            if (count > 0) {
                BizExp.pop(String.format("客户ID %s 已存在且处于启用状态", clientSaleConfig.getClientId()));
            }
        }
    }
}
