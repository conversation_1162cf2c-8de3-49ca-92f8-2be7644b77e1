package com.hxdi.nmjl.service.storeproduction.Impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.storeproduction.StoreRawMaterialPurchaseCondition;
import com.hxdi.nmjl.domain.storeproduction.StoreRawMaterialPurchase;
import com.hxdi.nmjl.mapper.storeproduction.StoreRawMaterialPurchaseMapper;
import com.hxdi.nmjl.service.storeproduction.StoreRawMaterialPurchaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 原料采购服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class StoreRawMaterialPurchaseServiceImpl extends BaseServiceImpl<StoreRawMaterialPurchaseMapper, StoreRawMaterialPurchase> implements StoreRawMaterialPurchaseService {
    

    @Override
    public void create(StoreRawMaterialPurchase purchase) {
        BaseUserDetails user = SecurityHelper.getUser();
        purchase.setStoreId(user.getOrganId());
        purchase.setStoreName(user.getOrganName());
        this.save(purchase);
    }

    @Override
    public void update(StoreRawMaterialPurchase purchase) {
        // 查询原始记录
        StoreRawMaterialPurchase original = this.getById(purchase.getId());
        if (original == null) {
            throw new BaseException("采购记录不存在");
        }
        this.updateById(purchase);
    }

    @Override
    public StoreRawMaterialPurchase getDetail(String id) {
        StoreRawMaterialPurchase purchase = baseMapper.selectById(id);
        if (purchase == null || purchase.getEnabled() != 1) {
            throw new BaseException("采购记录不存在或已删除");
        }
        return purchase;
    }

    @Override
    public Page<StoreRawMaterialPurchase> pages(StoreRawMaterialPurchaseCondition condition) {
        Page<StoreRawMaterialPurchase> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<StoreRawMaterialPurchase> lists(StoreRawMaterialPurchaseCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void remove(String id) {
        StoreRawMaterialPurchase purchase = this.getById(id);
        if (purchase == null) {
            throw new BaseException("采购记录不存在");
        }

        // 逻辑删除（修改状态）
        purchase.setEnabled(0);
        this.updateById(purchase);
    }
}
