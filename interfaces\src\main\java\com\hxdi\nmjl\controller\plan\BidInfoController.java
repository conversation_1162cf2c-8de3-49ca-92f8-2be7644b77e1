package com.hxdi.nmjl.controller.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.plan.BidInfo;
import com.hxdi.nmjl.service.plan.BidInfoService;
import com.hxdi.nmjl.condition.plan.BidInfoCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <招标信息管理接口>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25 14:22
 */
@Api(tags = "招标信息管理")
@RestController
@RequestMapping("/bids")
public class BidInfoController extends BaseController<BidInfoService, BidInfo> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<BidInfo>> getPages(BidInfoCondition condition) {
        return ResultBody.<Page<BidInfo>>OK().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<BidInfo>> getList(BidInfoCondition condition) {
        return ResultBody.<List<BidInfo>>OK().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<BidInfo> getDetail(@RequestParam String bidId) {
        return ResultBody.<BidInfo>OK().data(bizService.getDetail(bidId));
    }

    @ApiOperation("保存/修改招标信息")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody BidInfo bidInfo) {
        if(CommonUtils.isEmpty(bidInfo.getId())) {
            bizService.add(bidInfo);
        } else {
            bizService.updateV1(bidInfo);
        }
        return ResultBody.OK();
    }

    @ApiOperation("根据选择的筹措计划查询已招标的标的物（品类+质量等级）")
    @GetMapping("/getExistingBidObject")
    public ResultBody<List<String>> getExistingBidObject(@RequestParam String planId) {
        return ResultBody.<List<String>>OK().data(bizService.getExistingBidObject(planId));
    }

    @ApiOperation("中标")
    @GetMapping("/winBid")
    public ResultBody<Void> winBid(@RequestParam String id) {
        bizService.winBid(id);
        return ResultBody.OK();
    }

    @ApiOperation("流标")
    @GetMapping("/flow")
    public ResultBody<Void> flow(@RequestParam String id) {
        bizService.flow(id);
        return ResultBody.OK();
    }

    @ApiOperation("删除招标信息")
    @GetMapping("/delete")
    public ResultBody<Void> delete(@RequestParam String bidId) {
        bizService.deleteV1(bidId);
        return ResultBody.OK();
    }

    @ApiOperation("改变招标流程状态")
    @GetMapping("/changeFlowState")
    public ResultBody<Void> changeFlowState(@RequestParam String id, @RequestParam Integer flowState) {
        bizService.changeFlowState(id, flowState);
        return ResultBody.OK();
    }

    @ApiOperation(value = "审核")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.OK();
    }

    @ApiOperation(value = "驳回")
    @GetMapping("/reject")
    public ResultBody<Void> reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.OK();
    }

    @ApiOperation(value = "提交")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.OK();
    }

}
