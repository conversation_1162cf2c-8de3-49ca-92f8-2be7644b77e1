package com.hxdi.nmjl.service.inout.opt.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.AdditiveConsumingCondition;
import com.hxdi.nmjl.domain.inout.opt.AdditiveConsuming;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inout.opt.AdditiveConsumingMapper;
import com.hxdi.nmjl.service.inout.opt.AdditiveConsumingService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 添加剂管理Service实现类
 *
 * <AUTHOR>
 * @since 2025-04-22 10:02:10
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class AdditiveConsumingServiceImpl extends BaseServiceImpl<AdditiveConsumingMapper, AdditiveConsuming> implements AdditiveConsumingService {

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public void saveOrUpdateV1(AdditiveConsuming additiveConsuming) {
        if (CommonUtils.isEmpty(additiveConsuming.getId())) {
            verifyRecord(additiveConsuming);
            //添加剂领用编号
            BusinessCodeParams params = new BusinessCodeParams();
            params.setCode("CONSUMING_CODE");
            params.setDt(DataType.STRING);
            BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
            additiveConsuming.setConsumingCode((String) businessCode.getValue());
            baseMapper.insert(additiveConsuming);
        } else {
            AdditiveConsuming existing = this.getById(additiveConsuming.getId());
            if (existing.getApproveStatus() == 1 || existing.getApproveStatus() == 2) {
                throw new BaseException("该单据正在审核中，无法更新");
            }
            baseMapper.updateById(additiveConsuming);
        }
    }

    @Override
    public Page<AdditiveConsuming> pages(AdditiveConsumingCondition condition) {
        Page<AdditiveConsuming> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<AdditiveConsuming> lists(AdditiveConsumingCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void approve(String id, String approveOpinion) {
        //审核状态：0-未审核，1-待审核，2-已审核，3-驳回
        changeApproveStatus(id, 2, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 3, approveOpinion);
    }

    @Override
    public void submit(String id) {
        changeApproveStatus(id, 1, null);
    }

    @Override
    public void remove(String id) {
        AdditiveConsuming additiveConsuming = getById(id);
        // 检查是否可以删除（只能删除未审核、驳回的）
        if (additiveConsuming.getApproveStatus() == 1 || additiveConsuming.getApproveStatus() == 2) {
            throw new BaseException("该单据正在审批中，无法删除");
        }
        AdditiveConsuming updatingAdditive = new AdditiveConsuming();
        updatingAdditive.setId(id);
        updatingAdditive.setEnabled(0);
        this.updateById(updatingAdditive);
    }

    /**
     * 更新审核状态
     *
     * @param id             添加剂管理ID
     * @param approveStatus  审核状态
     * @param approveOpinion 审核意见
     */
    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        AdditiveConsuming additiveConsuming = new AdditiveConsuming();
        additiveConsuming.setId(id);
        additiveConsuming.setApproveStatus(approveStatus);
        if (approveStatus == 2 || approveStatus == 3) {
            additiveConsuming.setApprover(SecurityHelper.obtainUser().getNickName());
            additiveConsuming.setApproveTime(new Date());
            additiveConsuming.setApproveOpinion(approveOpinion);
        }
        baseMapper.updateById(additiveConsuming);
    }

    /**
     * 验证数据有效性
     *
     * @param additiveConsuming 添加剂管理
     */
    private void verifyRecord(AdditiveConsuming additiveConsuming) {
        long count = baseMapper.selectCount(Wrappers.<AdditiveConsuming>lambdaQuery()
                .eq(AdditiveConsuming::getStoreId, additiveConsuming.getStoreId())
                .eq(AdditiveConsuming::getCatalogId, additiveConsuming.getCatalogId())
                .eq(AdditiveConsuming::getEnabled, 1));

        if (count > 0) {
            throw new BaseException("该库点已存在该品种的添加剂管理记录，请勿重复创建");
        }
    }
}
