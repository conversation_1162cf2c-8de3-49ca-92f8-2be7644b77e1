<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.BidObjectMapper">
    <resultMap id="BidObjectResultMap" type="com.hxdi.nmjl.domain.plan.BidObject">
        <!-- 主键 -->
        <id property="id" column="ID" />
        <!-- 招标ID -->
        <result property="bidId" column="BID_ID" />
        <!-- 品类 -->
        <result property="classificationId" column="CLASSIFICATION_ID" />
        <!-- 品类名称 -->
        <result property="classificationName" column="CLASSIFICATION_NAME" />
        <!-- 规格 -->
        <result property="specifications" column="SPECIFICATIONS" />
        <!-- 质量等级 -->
        <result property="grade" column="GRADE" />
        <!-- 需求上限 -->
        <result property="maxLimit" column="MAX_LIMIT" />
        <!-- 需求下限 -->
        <result property="minLimit" column="MIN_LIMIT" />
        <!-- 产地及年份要求 -->
        <result property="requirement" column="REQUIREMENT" />
        <!-- 供货频次 -->
        <result property="supplyFrequency" column="SUPPLY_FREQUENCY" />
        <!-- 首次交货日期 -->
        <result property="firstDeliveryTime" column="FIRST_DELIVERY_TIME" />
        <!-- 创建时间 -->
        <result property="createTime" column="CREATE_TIME" />
        <!-- 更新时间 -->
        <result property="updateTime" column="UPDATE_TIME" />
        <!-- 创建ID -->
        <result property="createId" column="CREATE_ID" />
        <!-- 更新ID -->
        <result property="updateId" column="UPDATE_ID" />
        <!-- 租户ID -->
        <result property="tenantId" column="TENANT_ID" />
        <!-- 组织 -->
        <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, BID_ID, CLASSIFICATION_ID, CLASSIFICATION_NAME, SPECIFICATIONS, GRADE, MAX_LIMIT, MIN_LIMIT, REQUIREMENT, SUPPLY_FREQUENCY, FIRST_DELIVERY_TIME, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
    </sql>
</mapper>