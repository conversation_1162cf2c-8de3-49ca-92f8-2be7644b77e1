package com.hxdi.nmjl.domain.bigscreen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: nmjl-service
 * @description: 机构地图标点返回类
 * @author: 王贝强
 * @create: 2025-08-01 16:55
 */
@Getter
@Setter
@ApiModel(description = "机构地图标点返回类")
public class OrgPoint {
    @ApiModelProperty(value = "机构名称")
    String orgName;
    @ApiModelProperty(value = "机构类型")
    String orgType;
    @ApiModelProperty(value = "机构功能")
    String orgFunctional;
    @ApiModelProperty(value = "经度")
    String lon;
    @ApiModelProperty(value = "纬度")
    String lat;
    @ApiModelProperty(value = "库容")
    private String capacity;
    @ApiModelProperty(value = "负责人")
    private String fzr;
    @ApiModelProperty(value = "联系电话")
    private String mobile;
}
