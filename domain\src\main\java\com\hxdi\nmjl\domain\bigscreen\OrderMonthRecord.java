package com.hxdi.nmjl.domain.bigscreen;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @program: nmjl-service
 * @description: 采购、销售订单统计（按月统计：定时任务每月1号凌晨生成上月数据）
 * @author: 王贝强
 * @create: 2025-07-28 14:04
 */
@Setter
@Getter
@TableName(value = "B_ORDER_MONTH_RECORD")
@ApiModel(description = "采购、销售订单统计（按月统计：定时任务每月1号凌晨生成上月数据）")
public class OrderMonthRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 订单号
     */
    @TableField(value = "ORDER_CODE")
    @ApiModelProperty(value = "订单号")
    private String orderCode;

    /**
     * 订单类型
     */
    @TableField(value = "ORDER_TYPE")
    @ApiModelProperty(value = "订单类型 1->采购订单 2->销售订单")
    private String orderType;

    /**
     * 统计日期（YYYY-MM-DD）
     */
    @TableField(value = "STATISTICAL_DATE")
    @ApiModelProperty(value = "统计日期（YYYY-MM-DD）")
    private String statisticalDate;


    /**
     * 单位ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "单位ID")
    private String orgId;

    /**
     * 单位名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "单位名称")
    private String orgName;

    /**
     * 单位区域编码
     */
    @TableField(value = "AREA_CODE")
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "军供站ID")
    private String storeId;

    /**
     * 军供站名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "军供站名称")
    private String storeName;

    /**
     * 客户ID
     */
    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    /**
     * 客户名称
     */
    @TableField(value = "CLIENT_NAME")
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND")
    @ApiModelProperty(value = "品牌名称")
    private String brand;

    /**
     * 质量等级
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value = "质量等级")
    private String grade;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    @ApiModelProperty(value = "规格")
    private String specification;

    /**
     * 订单数量
     */
    @TableField(value = "ORDER_QTY")
    @ApiModelProperty(value = "订单数量")
    private String orderQty;

    /**
     * 完成数量
     */
    @TableField(value = "COMPLETED_QTY")
    @ApiModelProperty(value = "完成数量")
    private String completedQty;

    /**
     * 订单包装数量
     */
    @TableField(value = "ORDER_PACK_QTY")
    @ApiModelProperty(value = "订单包装数量")
    private String orderPackQty;


    /**
     * 储备性质
     */
    @TableField(value = "RESERVE_LEVEL")
    @ApiModelProperty(value = "储备性质")
    private String reserveLevel;

    /**
     * 单价
     */
    @TableField(value = "PRICE")
    @ApiModelProperty(value = "单价")
    private String price;

    /**
     * 生产批次号
     */
    @TableField(value = "BATCH_NO")
    @ApiModelProperty(value = "生产批次号")
    private String batchNo;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
