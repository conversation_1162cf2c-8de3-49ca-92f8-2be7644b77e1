package com.hxdi.nmjl.service.inout.delivery.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.FuelAnalysisCondition;
import com.hxdi.nmjl.condition.inout.VehicleInfoCondition;
import com.hxdi.nmjl.domain.inout.delivery.FuelAnalysis;
import com.hxdi.nmjl.domain.inout.delivery.VehicleInfo;
import com.hxdi.nmjl.mapper.inout.delivery.FuelAnalysisMapper;
import com.hxdi.nmjl.service.inout.delivery.FuelAnalysisService;
import com.hxdi.nmjl.service.inout.delivery.VehicleInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【B_FUEL_ANALYSIS(油耗分析报表)】的数据库操作Service实现
 * @createDate 2025-08-05 15:12:43
 */
@Service
public class FuelAnalysisServiceImpl extends BaseServiceImpl<FuelAnalysisMapper, FuelAnalysis>
        implements FuelAnalysisService {

    @Resource
    private VehicleInfoService vehicleInfoService;

    @Override
    public void saveOrUpdateV1(FuelAnalysis fuelAnalysis) {
        if (CommonUtils.isEmpty(fuelAnalysis.getId())) {
            transFuelAndVehicleKind(fuelAnalysis);
            fuelAnalysis.setStatTime(new Date());
        }

        BigDecimal totalDistance = fuelAnalysis.getTotalDistance();
        BigDecimal refuelQuantity = fuelAnalysis.getRefuelQuantity();

        if (totalDistance != null && totalDistance.compareTo(BigDecimal.ZERO) != 0 && refuelQuantity != null) {
            BigDecimal fuelConsumptionActual = refuelQuantity
                    .multiply(BigDecimal.valueOf(100))
                    .divide(totalDistance, 2, BigDecimal.ROUND_HALF_UP);

            fuelAnalysis.setFuelConsumptionActual(fuelConsumptionActual);
            fuelAnalysis.setFuelConsumption(fuelConsumptionActual.add(BigDecimal.valueOf(5)));

            if (fuelConsumptionActual.compareTo(fuelAnalysis.getFuelConsumption()) <= 0) {
                fuelAnalysis.setMark("正常");
            } else {
                fuelAnalysis.setMark("超标预警");
            }
        } else {
            fuelAnalysis.setFuelConsumptionActual(BigDecimal.ZERO);
        }

        if (CommonUtils.isEmpty(fuelAnalysis.getId())) {
            baseMapper.insert(fuelAnalysis);
        } else {
            String confirmRemark = fuelAnalysis.getConfirmRemark();
            if (CommonUtils.isNotEmpty(confirmRemark)) {
                Integer alertCount = fuelAnalysis.getAlertCount();
                fuelAnalysis.setAlertCount(alertCount != null ? alertCount + 1 : 1);
            }
            baseMapper.updateById(fuelAnalysis);
        }

    }



    @Override
    public Page<FuelAnalysis> pages(FuelAnalysisCondition condition) {
        Page<FuelAnalysis> page = condition.newPage();
        Page<FuelAnalysis> page1 = baseMapper.selectPageV1(page, condition);
        page1.getRecords().forEach(fuelAnalysis -> {
            String fuelType = fuelAnalysis.getFuelType();
            switch (fuelType) {
                case "01":
                    fuelAnalysis.setFuelType("市区");
                    break;
                case "02":
                    fuelAnalysis.setFuelType("高速");
                    break;
                case "03":
                    fuelAnalysis.setFuelType("混合");
                    break;
                default:
                    break;
            }
        });
        return page1;
    }

    @Override
    public Page<FuelAnalysis> pages1(FuelAnalysisCondition condition) {
        Page<FuelAnalysis> page = condition.newPage();
        return baseMapper.selectPageV2(page, condition);
    }

    @Override
    public List<FuelAnalysis> lists(FuelAnalysisCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public List<String> getByKind(String kind) {
        VehicleInfoCondition vehicleInfoCondition = new VehicleInfoCondition();
        vehicleInfoCondition.setVehicleType(kind);
        List<VehicleInfo> lists = vehicleInfoService.lists(vehicleInfoCondition);
        List<String> collect = lists.stream().map(VehicleInfo::getVehicleNo).collect(Collectors.toList());
        return collect;
    }


    public void transFuelAndVehicleKind(FuelAnalysis fuelAnalysis) {

        String fuelType = fuelAnalysis.getFuelType();
        String vehicleKind = fuelAnalysis.getVehicleKind();
        if(vehicleKind.equals("01")){
            fuelAnalysis.setVehicleKind("平板式货车");
        }
        if(vehicleKind.equals("02")){
            fuelAnalysis.setVehicleKind("栏板式货车");
        }
        if(vehicleKind.equals("03")){
            fuelAnalysis.setVehicleKind("厢式货车");
        }
        if(vehicleKind.equals("04")){
            fuelAnalysis.setVehicleKind("仓栅式货车");
        }
        if(vehicleKind.equals("05")){
            fuelAnalysis.setVehicleKind("罐式车");
        }
        if(vehicleKind.equals("06")){
            fuelAnalysis.setVehicleKind("自卸车");
        }

    }
}




