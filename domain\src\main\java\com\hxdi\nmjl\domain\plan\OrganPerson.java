package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <监督检查机构人员管理>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/12 16:03
 */
@Getter
@Setter
@TableName("B_ORGAN_PERSON")
public class OrganPerson extends BModel {

    private static final long serialVersionUID = 3308368789069720146L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private Integer sex;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private String edu;

    /**
     * 岗位
     */
    @ApiModelProperty(value = "岗位")
    private String jobTitle;

    /**
     * 职责描述
     */
    @ApiModelProperty(value = "职责描述")
    private String jobDesc;

}
