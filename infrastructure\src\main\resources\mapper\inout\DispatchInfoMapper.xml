<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inout.DispatchInfoMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inout.DispatchInfo">
    <!--@mbg.generated-->
    <!--@Table B_DISPATCH_INFO-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
    <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
    <result column="ST_ID" jdbcType="VARCHAR" property="stId" />
    <result column="ST_NAME" jdbcType="VARCHAR" property="stName" />
    <result column="LOC_ID" jdbcType="VARCHAR" property="locId" />
    <result column="LOC_NAME" jdbcType="VARCHAR" property="locName" />
    <result column="DISPATCH_TYPE" jdbcType="VARCHAR" property="dispatchType" />
    <result column="INVENTORY_ID" jdbcType="VARCHAR" property="inventoryId" />
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="ATTACHEMENTS" jdbcType="VARCHAR" property="attachements" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="TASK_CODE" jdbcType="VARCHAR" property="taskCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TASK_ID, STORE_ID, STORE_NAME, ST_ID, ST_NAME, LOC_ID, LOC_NAME, DISPATCH_TYPE, 
    INVENTORY_ID, QTY, REMARKS, ATTACHEMENTS, ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, 
    UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, STATUS, TASK_CODE
  </sql>

  <select id="listV1" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_DISPATCH_INFO
    <where>
      <if test="condition.status != null and condition.status != ''">
        and STATUS = #{condition.status}
      </if>
      <if test="condition.startTime != null and condition.startTime != ''">
        and UPDATE_TIME &gt;= #{condition.startTime}
      </if>
      <if test="condition.endTime != null and condition.endTime != ''">
        and UPDATE_TIME &lt;= #{condition.endTime}
      </if>
      <if test="condition.taskCode != null and condition.taskCode != ''">
        and TASK_CODE like concat('%',#{condition.taskCode},'%')
      </if>
      and enabled = 1
    </where>
    ORDER BY CREATE_TIME DESC
    </select>

  <select id="pageV1" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_DISPATCH_INFO
    <where>
      <if test="condition.status != null and condition.status != ''">
        and STATUS = #{condition.status}
      </if>
      <if test="condition.startTime != null and condition.startTime != ''">
        and UPDATE_TIME &gt;= #{condition.startTime}
      </if>
      <if test="condition.endTime != null and condition.endTime != ''">
        and UPDATE_TIME &lt;= #{condition.endTime}
      </if>
      <if test="condition.taskCode != null and condition.taskCode != ''">
        and TASK_CODE like concat('%',#{condition.taskCode},'%')
      </if>
      and enabled = 1
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>