package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 整体经营计划
 */
@ApiModel(description = "整体经营计划信息")
@Getter
@Setter
@TableName("B_OPERATING_PLAN")
public class OperatingPlan implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("PID")
    @ApiModelProperty(value = "父计划(默认为0)")
    private String pid = "0";

    @TableField("PLAN_CODE")
    @ApiModelProperty(value = "计划编号")
    private String planCode;

    @TableField("PLAN_NAME")
    @ApiModelProperty(value = "计划名称")
    private String planName;

    @TableField("ORG_ID")
    @ApiModelProperty(value = "管理单位ID")
    private String orgId;

    @TableField("ORG_NAME")
    @ApiModelProperty(value = "管理单位名称")
    private String orgName;

    @TableField("STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @TableField("STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @TableField("PLAN_YEAR")
    @ApiModelProperty(value = "计划年度")
    private String planYear;

    @TableField("OBJECTIVE_DESC")
    @ApiModelProperty(value = "总体目标描述")
    private String objectiveDesc;

    @TableField("CHARGE_PERSON")
    @ApiModelProperty(value = "总负责人")
    private String chargePerson;

    @TableField("STATE")
    @ApiModelProperty(value = "计划状态：1未生效，2已生效，3已完成")
    private Integer state;

    @TableField("APPROVE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "审核时间")
    private Date approveTime;

    @TableField("APPROVE_STATUS")
    @ApiModelProperty(value = "审核状态:0：未审核；1审核通过；2驳回")
    private Integer approveStatus;

    @TableField("APPROVER")
    @ApiModelProperty(value = "审核人")
    private String approver;

    @TableField("APPROVE_OPINION")
    @ApiModelProperty(value = "审核意见")
    private String approveOpinion;

    @TableField("ENABLED")
    @ApiModelProperty(value = "启用状态0：未启用 1：启用")
    private Integer enabled = 1;

    @TableField("ATTACHMENT")
    @ApiModelProperty(value = "附件")
    private String attachment;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    @TableField("DEPT_NAME")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @TableField("FATHER_PLAN_CODE")
    @ApiModelProperty(value = "整体经营计划编号")
    private String fatherPlanCode;

    @TableField("FINISH_PERCENT")
    @ApiModelProperty(value = "完成进度")
    private String finishPercent;

    @TableField("SUMMARIZE")
    @ApiModelProperty(value = "总结")
    private String summarize;

    @TableField("PROBLEM")
    @ApiModelProperty(value = "问题")
    private String problem;

    @TableField("MEASURE")
    @ApiModelProperty(value = "措施")
    private String measure;


    @TableField(exist = false)
    private List<OperatingPlanDetail> detailList;
}
