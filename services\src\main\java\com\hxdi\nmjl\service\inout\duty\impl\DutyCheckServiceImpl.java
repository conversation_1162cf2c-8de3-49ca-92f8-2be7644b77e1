package com.hxdi.nmjl.service.inout.duty.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.DutyCondition;
import com.hxdi.nmjl.domain.inout.duty.DutyCheck;
import com.hxdi.nmjl.domain.inout.duty.DutyPlan;
import com.hxdi.nmjl.mapper.inout.duty.DutyCheckMapper;
import com.hxdi.nmjl.service.inout.duty.DutyCheckService;
import com.hxdi.nmjl.service.inout.duty.DutyPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class DutyCheckServiceImpl extends BaseServiceImpl<DutyCheckMapper, DutyCheck> implements DutyCheckService {

    @Resource
    private DutyPlanService dutyPlanService;

    @Override
    public void create(DutyCheck dutyCheck) {
        if (CommonUtils.isNotEmpty(dutyCheck.getDutyPlanId())) {
            DutyPlan dutyPlan = dutyPlanService.getById(dutyCheck.getDutyPlanId());
            dutyCheck.setDutyName(dutyPlan.getName());
        }
        this.save(dutyCheck);
    }

    @Override
    public void updating(DutyCheck dutyCheck) {
        this.updateById(dutyCheck);
    }

    @Override
    public DutyCheck detail(String dutyId) {
        return this.getById(dutyId);
    }

    @Override
    public void delete(String dutyId) {
        this.removeById(dutyId);
    }

    @Override
    public Page<DutyCheck> getPage(DutyCondition dutyCondition) {
        Page<DutyCheck> pages = dutyCondition.newPage();
        baseMapper.selectPageV1(pages, dutyCondition);
        List<DutyCheck> records = pages.getRecords();
        Map<DutyCheck, String> maps = records.stream().collect(Collectors.toMap(c -> c, DutyCheck::getDutyPlanId));
        Collection<String> ids = maps.values();
        List<DutyPlan> dutyPlans = dutyPlanService.listByIds(ids);

        Map<String, String> dutyPlanMap = dutyPlans.stream()
                .collect(Collectors.toMap(DutyPlan::getId, DutyPlan::getDutyPlanCode));
        records.forEach(c -> c.setDutyPlanCode(dutyPlanMap.get(c.getDutyPlanId())));
        pages.setRecords(records);
        return pages;
    }

    @Override
    public List<DutyCheck> getList(DutyCondition dutyCondition) {
        List<DutyCheck> dutyChecks = baseMapper.selectListV1(dutyCondition);
        List<String> collects = dutyChecks.stream()
                .map(DutyCheck::getDutyPlanId)
                .filter(CommonUtils::isNotEmpty)
                .collect(Collectors.toList());
        List<DutyPlan> dutyPlans = dutyPlanService.listByIds(collects);
        Map<String, String> dutyPlanMap = dutyPlans.stream()
                .collect(Collectors.toMap(DutyPlan::getId, DutyPlan::getDutyPlanCode));
        dutyChecks.forEach(c -> c.setDutyPlanCode(dutyPlanMap.get(c.getDutyPlanId())));
        return dutyChecks;
    }
}
