#\u5E94\u7528\u4FE1\u606F
app.port=8244
profile.name=test
config.server-addr=10.13.4.20:8848
discovery.server-addr=10.13.4.20:8848
config.namespace=412b80a2-ee56-4ae5-804d-d1d1dee0a681
config.group=DEFAULT_GROUP
auth.username=
auth.password=

#\u7CFB\u7EDF\u65E5\u5FD7\u914D\u7F6E
application.name=${artifactId}
log.path=logs/${artifactId}
log.level=debug

api.debug=true

#\u89C6\u9891\u8F6C\u7801
hx.videoDecode.rtmpUrl=rtmp://121.37.202.125:11935/live/
#\u89C6\u9891live Html\u9875\u9762
hx.liveVideo.liveHtmlUrl=http://10.13.4.31:8810/#/video?plateNo=
hx.liveVideo.recordHtmlUrl=http://10.13.4.31:8810/#/video?channelId=

#\u5927\u534E
dahua.url=https://121.37.202.125
dahua.userName=system
dahua.passWord=1qazCDE#
dahua.authorizeUrl=/admin/API/accounts/authorize
dahua.startVideoUrl=/admin/API/MTS/Video/StartVideo
dahua.getGpsDetailInfoUrl=/vehicleServer/api/getGpsDetailInfo
dahua.liveVideoRtspUrl=rtsp://121.37.202.125:9090/dss/monitor/params?
dahua.deviceRecordsVideoRtspUrl=rtsp://121.37.202.125:9320/dss/playback/pu?
dahua.getDevicesUrl=/admin/API/tree/devices
dahua.queryAlarmsUrl=/admin/API/BRM/Alarm/QueryAlarms
