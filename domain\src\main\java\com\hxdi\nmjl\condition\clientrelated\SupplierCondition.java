package com.hxdi.nmjl.condition.clientrelated;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SupplierCondition extends QueryCondition {

    @ApiModelProperty(value = "供应商名称")
    private String name;

    @ApiModelProperty(value = "开始时间")
    private String startFilledDate;
    @ApiModelProperty(value = "截止时间")
    private String endFilledDate;

    @ApiModelProperty(value = "产地所在省")
    private Integer provinceId;
    @ApiModelProperty(value = "产地所在市")
    private Integer cityId;
    @ApiModelProperty(value = "产地所在县区")
    private Integer countyId;

    @ApiModelProperty(value = "供应商状态")
    private Integer approveStatus;
}
