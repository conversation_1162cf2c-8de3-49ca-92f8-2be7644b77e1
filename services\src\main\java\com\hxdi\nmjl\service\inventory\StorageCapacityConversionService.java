package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.inventory.StorageCapacityCondition;
import com.hxdi.nmjl.condition.inventory.StorageCapacityConversionCondition;
import com.hxdi.nmjl.domain.inventory.StorageCapacity;
import com.hxdi.nmjl.domain.inventory.StorageCapacityConversion;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @Data 2025/7/19 15:37
 * @Description: 库容转换记录服务接口
 */
public interface StorageCapacityConversionService extends IBaseService<StorageCapacityConversion> {
    /**
     * 列表查询库容转换信息
     *
     * @param condition 查询条件
     * @return List<StorageCapacity>
     */
    List<StorageCapacityConversion> getList(StorageCapacityConversionCondition condition);

    /**
     * 查询库容详情
     *
     * @param id 库容ID
     * @return StorageCapacity
     */
    StorageCapacityConversion getDetail(String id);

    /**
     * 分页查询库容信息
     *
     * @param condition 查询条件
     * @return Page<StorageCapacity>
     */
    Page<StorageCapacityConversion> getPages(StorageCapacityConversionCondition condition);


    /**
     * 创建调配信息
     *
     * @param storageCapacityConversion
     */
    void add(StorageCapacityConversion storageCapacityConversion);

    /**
     * 修改调配信息
     *
     * @param storageCapacityConversion 库容信息
     */
    void update(StorageCapacityConversion storageCapacityConversion);

    /**
     * 删除信息
     *
     * @param id 库容ID
     * @return boolean
     */
    boolean delete(String id);


    /**
     * 提交
     *
     * @param id 库容ID
     */
    void submit(String id);

    /**
     * 回收租界库容
     *
     * @param id 库容ID
     */
    void recycle(String id);

    /**
     * 修改审批状态
     *
     * @param id
     * @param approveStatus
     * @param approveOpinion
     */
    void changeApproveStatus(String id, int approveStatus, String approveOpinion);
}

