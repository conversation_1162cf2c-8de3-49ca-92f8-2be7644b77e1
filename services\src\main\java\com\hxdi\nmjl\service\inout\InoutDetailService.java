package com.hxdi.nmjl.service.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.condition.inout.InoutDetailCondition;

import java.util.List;


public interface InoutDetailService extends IBaseService<InoutDetail> {

    /**
     * 新增
     * @param inoutDetail
     */
    void create(InoutDetail inoutDetail);

    /**
     * 更新
     * @param inoutDetail
     */
    void updating(InoutDetail inoutDetail);

    /**
     * 提交保存
     * @param id
     */
    void commit(String id);

    /**
     * 查询详情
     * @param id
     * @return
     */
    InoutDetail detail(String id);

    /**
     * 删除操作需要回滚相关状态：任务单、库存等
     * @param id
     */
    void remove(String id);

    /**
     * 分页查询
     * @param taskCondition
     * @return
     */
    Page<InoutDetail> getPage(InoutDetailCondition taskCondition);

    /**
     * 列表查询
     * @param taskCondition
     * @return
     */
    List<InoutDetail> getList(InoutDetailCondition taskCondition);


    /**
     * 查询合同、订单号
     * @param bizId
     * @return
     */
    String getOrderId(String bizId);

    /**
     * 定时任务接口
     * <p>
     * 生成前一天的销售、生产订单统计数据
     */
    void generateOrderStatistics();
}
