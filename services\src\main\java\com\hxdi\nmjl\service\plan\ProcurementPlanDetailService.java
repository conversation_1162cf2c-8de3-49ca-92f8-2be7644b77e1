package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.plan.ProcurementPlanDetailCondition;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlanDetail;

import java.util.List;

/**
 * 粮食筹措计划详情服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/26 15:30
 */
public interface ProcurementPlanDetailService extends IBaseService<GrainProcurementPlanDetail> {

    /**
     * 获取列表
     *
     * @param planId 计划ID
     * @return 计划详情列表
     */
    List<GrainProcurementPlanDetail> getList(String planId);

    /**
     * 修改计划详情
     *
     * @param planDetail 计划详情
     */
    void update(GrainProcurementPlanDetail planDetail);

    /**
     * 删除计划详情
     *
     * @param planId 计划详情ID
     */
    void remove(String planId);

    /**
     * 获取计划详情分页列表
     *
     * @param condition 查询条件
     * @return 分页列表
     */
    Page<GrainProcurementPlanDetail> getPage(ProcurementPlanDetailCondition condition);

    /**
     * 检查指定计划的所有明细是否都有对应的招标信息
     *
     * @param planId 计划ID
     * @return true 如果所有明细都有招标信息，否则返回 false
     */
    boolean checkAllDetailsHaveBids(String planId);
}