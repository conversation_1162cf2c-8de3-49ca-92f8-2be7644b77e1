package com.hxdi.nmjl.condition.iot;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: nmjl-service
 * @description: 温湿度预警配置查询条件
 * @author: 王贝强
 * @create: 2025-07-21 17:23
 */
@Setter
@Getter
@ApiModel(description = "温湿度预警参数配置查询条件")
public class TemHumWarningConfigCondition extends QueryCondition {

    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @ApiModelProperty(value = "仓房ID")
    private String stId;

    @ApiModelProperty(value = "指标")
    private String indexName;
}
