package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlan;
import com.hxdi.nmjl.condition.plan.ProcurementPlanCondition;
import com.hxdi.nmjl.vo.bigscreen.ProcurementPlanSummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProcurementPlanMapper extends SuperMapper<GrainProcurementPlan> {

    /**
     * 分页查询
     * @param page
     * @param condition
     */
    @DataPermission
    Page<GrainProcurementPlan> selectPageV1(Page<GrainProcurementPlan> page, @Param("condition") ProcurementPlanCondition condition);

    /**
     * 列表查询
     * @param condition
     */
    @DataPermission
    List<GrainProcurementPlan> selectListV1(@Param("condition") ProcurementPlanCondition condition);

    /**
     * 获取筹措计划总览统计
     * @param areaCodeList
     * @return
     */
    @DataPermission(alias = "p")
    ProcurementPlanSummaryVO getPlanSummary(@Param("areaCodeList") List<String> areaCodeList);

    /**
     * 获取筹措计划总览统计分页
     * @param areaCodeList
     * @return
     */
    @DataPermission(alias = "p")
    Page<ProcurementPlanSummaryVO> getPlanSummaryPage(@Param("condition") ProcurementPlanCondition condition,Page<ProcurementPlanSummaryVO> page);

}
