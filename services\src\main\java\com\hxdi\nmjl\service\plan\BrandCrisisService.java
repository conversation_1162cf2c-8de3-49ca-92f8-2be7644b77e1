package com.hxdi.nmjl.service.plan;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.BrandCrisis;
import com.hxdi.nmjl.condition.plan.BrandCrisisCondition;

import java.util.List;

/**
 * 品牌危机服务类
 * <AUTHOR>
 */
public interface BrandCrisisService extends IBaseService<BrandCrisis> {

    /**
     * 分页查询
     * @param condition 查询条件
     * @return
     */
    Page<BrandCrisis> pages(BrandCrisisCondition condition);

    /**
     * 查询列表
     * @param condition 查询条件
     * @return
     */
    List<BrandCrisis> lists(BrandCrisisCondition condition);

    /**
     * 查询详情
     * @param id
     * @return
     */
    BrandCrisis getDetail(String id);

    /**
     * 删除
     * @param id
     */
    void remove(String id);

    /**
     * 新增
     * @param crisis
     */
    void create(BrandCrisis crisis);

    /**
     * 修改
     * @param crisis
     */
    void update(BrandCrisis crisis);
}
