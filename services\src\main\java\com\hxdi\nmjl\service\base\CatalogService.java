package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.condition.base.CatalogCondition;

import java.util.List;

public interface CatalogService extends IBaseService<Catalog> {

    /**
     * 新增
     * @param catalog
     */
    Catalog create(Catalog catalog);

    /**
     * 更新
     * @param catalog
     */
    Catalog update(Catalog catalog);

    /**
     * 根据ID/CODE查询详情
     * @param uniqueKey
     * @return
     */
    Catalog getByUniqueKey(String uniqueKey);

    /**
     * 分页
     * @param condition
     * @return
     */
    Page<Catalog> pages(CatalogCondition condition);

    /**
     * 列表
     * @param condition
     * @return
     */
    List<Catalog> lists(CatalogCondition condition);

    /**
     * 删除
     * @param id
     */
    void remove(String id);

    /**
     * 根据分类ID集合查询分类列表
     * @param leafIds
     * @return
     */
    List<Catalog> getListByClassIds(List<String> leafIds);
}
