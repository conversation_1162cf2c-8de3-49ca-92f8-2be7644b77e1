package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 招标信息
 */
@ApiModel(description = "招标信息")
@Getter
@Setter
@TableName("B_BID_INFO")
public class BidInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @TableField(value = "PROJECT_NAME")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @TableField(value = "YEAR")
    @ApiModelProperty(value = "年份")
    private String year;

    @TableField(value = "PLAN_ID")
    @ApiModelProperty(value = "关联计划ID")
    private String planId;

    @TableField(value = "PLAN_CODE")
    @ApiModelProperty(value = "关联计划CODE")
    private String planCode;

    @TableField(value = "UNIT_ID")
    @ApiModelProperty(value = "招标单位ID")
    private String unitId;

    @TableField(value = "UNIT_NAME")
    @ApiModelProperty(value = "招标单位名称")
    private String unitName;

    @TableField(value = "BID_START_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "招标开始时间")
    private Date bidStartTime;

    @TableField(value = "BID_END_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "招标结束时间")
    private Date bidEndTime;

    @TableField(value = "SUPPLY_START_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "供货期限开始时间")
    private Date supplyStartTime;

    @TableField(value = "SUPPLY_END_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "供货期限结束时间")
    private Date supplyEndTime;

    @TableField(value = "BID_TYPE")
    @ApiModelProperty(value = "招标组织形式:1委托招标,2自行招标")
    private Integer bidType;

    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "中标企业ID")
    private String clientId;

    @TableField(value = "CLIENT_NAME")
    @ApiModelProperty(value = "中标企业名称")
    private String clientName;

    @TableField(value = "TRADE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "成交日期")
    private Date tradeTime;

    @TableField(value = "BUDGET_AMOUNT")
    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetAmount;

    @TableField(value = "TRADE_DEPOSIT")
    @ApiModelProperty(value = "交易保证金")
    private BigDecimal tradeDeposit;

    @TableField(value = "PERFORMANCE_BOND_AMOUNT")
    @ApiModelProperty(value = "履约保证金")
    private BigDecimal performanceBondAmount;

    @TableField(value = "STATE")
    @ApiModelProperty(value = "状态：0-未开始，1-进行中，2-已完成，3-流标")
    private Integer state;

    /**
     * 招标内部流程状态：1-招标 2-投标 3-评标 4-中标
     */
    @TableField(value = "FLOW_STATE")
    @ApiModelProperty(value = "招标状态：1-招标 2-投标 3-评标 4-中标")
    private Integer flowState;

    @TableField(value = "NOTES")
    @ApiModelProperty(value = "备注")
    private String notes;

    @TableField(value = "BID_DESC")
    @ApiModelProperty(value = "投标信息描述")
    private String bidDesc;

    @TableField(value = "JUDGE_DESC")
    @ApiModelProperty(value = "评标信息描述")
    private String judgeDesc;

    @TableField(value = "ZBGG")
    @ApiModelProperty(value = "中标公告附件")
    private String zbgg;

    @TableField(value = "ZBWJ")
    @ApiModelProperty(value = "招标文件附件")
    private String zbwj;

    @TableField(value = "PBJL")
    @ApiModelProperty(value = "评标记录附件")
    private String pbjl;

    @TableField(value = "ATTACHMENTS")
    @ApiModelProperty(value = "其他附件")
    private String attachments;

    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    @TableField(value = "APPROVE_STATUS")
    @ApiModelProperty(value = "0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建ID")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新ID")
    private String updateId;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    //招标明细
    @TableField(exist = false)
    private List<BidObject> detailList;
}
