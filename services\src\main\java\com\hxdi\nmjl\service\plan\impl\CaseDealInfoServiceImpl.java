package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.plan.CaseDealInfo;
import com.hxdi.nmjl.mapper.plan.CaseDealInfoMapper;
import com.hxdi.nmjl.service.plan.CaseDealInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class CaseDealInfoServiceImpl extends BaseServiceImpl<CaseDealInfoMapper, CaseDealInfo> implements CaseDealInfoService {

    @Override
    public List<CaseDealInfo> getList(String caseId) {
        return baseMapper.selectList(Wrappers.<CaseDealInfo>lambdaQuery()
                .eq(CaseDealInfo::getCaseId, caseId)
                .orderByAsc(CaseDealInfo::getDealTime));
    }

    @Override
    public void updateList(String caseId, List<CaseDealInfo> caseDealList) {
        if (caseDealList == null || caseDealList.isEmpty()) {
            return;
        }
        caseDealList.forEach(deal -> deal.setCaseId(caseId));

        removeByCaseId(caseId);

        caseDealList.forEach(item -> item.setCaseId(caseId));
        this.saveOrUpdateBatch(caseDealList);
    }

    private void removeByCaseId(String caseId) {
        baseMapper.delete(Wrappers.<CaseDealInfo>lambdaQuery().eq(CaseDealInfo::getCaseId, caseId));
    }
}
