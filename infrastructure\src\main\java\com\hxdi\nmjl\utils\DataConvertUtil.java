package com.hxdi.nmjl.utils;

import com.google.common.collect.Lists;
import com.hxdi.common.core.utils.CommonUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @program: nmjl-service
 * @description: 数据转换工具类(用于处理前端不方便传递数组的情况)
 * @author: 王贝强
 * @create: 2025-07-18 09:55
 */
@Getter
@Setter
public class DataConvertUtil {

    /**
     * 对象转list
     * @param params（使用','分割的字符串）
     * @return
     */
    public static List<String> ObjToList(Object params){
        String paramStr = (String) params;
        if(CommonUtils.isBlank(paramStr)){
            return null;
        }
        return Lists.newArrayList(paramStr.split(","));
    }
}
