package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.plan.CaseInfoCondition;
import com.hxdi.nmjl.domain.plan.CaseDealInfo;
import com.hxdi.nmjl.domain.plan.CaseInfo;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.CaseInfoMapper;
import com.hxdi.nmjl.service.plan.CaseDealInfoService;
import com.hxdi.nmjl.service.plan.CaseInfoService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class CaseInfoServiceImpl extends BaseServiceImpl<CaseInfoMapper, CaseInfo> implements CaseInfoService {

    @Autowired
    private SystemNumberRuleClient systemNumberRuleClient;

    @Autowired
    private CaseDealInfoService caseDealInfoService;

    @Override
    public Page<CaseInfo> getPages(CaseInfoCondition condition) {
        Page<CaseInfo> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<CaseInfo> getList(CaseInfoCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public CaseInfo getDetail(String id) {
        CaseInfo caseInfo = this.getById(id);
        List<CaseDealInfo> caseDealList = caseDealInfoService.getList(caseInfo.getId());
        caseInfo.setCaseDealList(caseDealList);
        return caseInfo;
    }

    @Override
    public void add(CaseInfo caseInfo) {
        //生成计划编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("CASE_INFO_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        caseInfo.setCaseNo((String) businessCode.getValue());
        //判断当前状态
        checkCaseState(caseInfo);
        baseMapper.insert(caseInfo);
        caseDealInfoService.updateList(caseInfo.getId(), caseInfo.getCaseDealList());
    }

    @Override
    public void update(CaseInfo caseInfo) {
        checkCaseState(caseInfo);
        baseMapper.updateById(caseInfo);
        caseDealInfoService.updateList(caseInfo.getId(), caseInfo.getCaseDealList());
    }

    /**
     * 根据数据内容，更新案件状态
     *
     * @param caseInfo
     */
    private void checkCaseState(CaseInfo caseInfo) {
        if (caseInfo.getFilingTime() != null && caseInfo.getClosingTime() != null) {
            caseInfo.setState(3);
        } else if (caseInfo.getFilingTime() != null && !caseInfo.getCaseDealList().isEmpty()) {
            caseInfo.setState(2);
        } else if (caseInfo.getFilingTime() != null) {
            caseInfo.setState(1);
        } else {
            caseInfo.setState(0);
        }
    }

    @Override
    public boolean delete(String id) {
        CaseInfo caseInfo = this.getById(id);
        if (caseInfo.getState() == 3) {
            BizExp.pop("案件处理状态已结案，不能删除");
        }

        CaseInfo deletingCase = new CaseInfo();
        deletingCase.setId(id);
        deletingCase.setEnabled(StrPool.State.DISABLE);
        baseMapper.updateById(deletingCase);
        return true;
    }
}
