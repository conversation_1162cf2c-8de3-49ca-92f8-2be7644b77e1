package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合同结算主表
 */
@Getter
@Setter
@TableName(value = "B_CONTRACT_SETTLEMENT")
@ApiModel(description = "合同结算表")
public class ContractSettlement extends BModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 结算单号
     */
    @TableField(value = "SETTLEMENT_CODE")
    @ApiModelProperty(value = "结算单号")
    private String settlementCode;

    /**
     * 合同ID
     */
    @TableField(value = "CONTRACT_ID")
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 合同编号
     */
    @TableField(value = "CONTRACT_CODE")
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 合同名称
     */
    @TableField(value = "CONTRACT_NAME")
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 业务类型：1-采购合同，2-销售合同
     */
    @TableField(value = "BIZ_TYPE")
    @ApiModelProperty(value = "业务类型：1-采购合同，2-销售合同")
    private String bizType;

    /**
     * 机构ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    /**
     * 机构名称
     */
    @TableField(value = "ORG_NAME")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 客户ID
     */
    @TableField(value = "CLIENT_ID")
    @ApiModelProperty(value = "客户ID")
    private String clientId;

    /**
     * 客户名称
     */
    @TableField(value = "CLIENT_NAME")
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    /**
     * 合同总金额
     */
    @TableField(value = "CONTRACT_TOTAL_AMOUNT")
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal contractTotalAmount;

    /**
     * 结算总金额
     */
    @TableField(value = "SETTLEMENT_TOTAL_AMOUNT")
    @ApiModelProperty(value = "结算总金额")
    private BigDecimal settlementTotalAmount;

    /**
     * 结算进度百分比
     */
    @TableField(value = "SETTLEMENT_PROGRESS")
    @ApiModelProperty(value = "结算进度百分比")
    private BigDecimal settlementProgress;

    /**
     * 结算状态：基于结算数据计算
     */
    @TableField(value = "SETTLEMENT_STATUS")
    @ApiModelProperty(value = "结算状态：1-未结算，2-部分结算，3-已结算")
    private Integer settlementStatus;

    /**
     * 备注
     */
    @TableField(value = "NOTES")
    @ApiModelProperty(value = "备注")
    private String notes;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHEMENTS")
    @ApiModelProperty(value = "附件")
    private String attachements;
}
