package com.hxdi.nmjl.domain.inout;

import java.io.Serializable;

import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
*
* @TableName B_INOUT_ITEM
*/
@Getter
@Setter
@ApiModel(description = "出入库明细表")
@TableName("B_INOUT_ITEM")
public class InoutItem implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "出入库记录ID")
    @TableField(value = "INOUT_DETAIL_ID")
    private String inoutDetailId;

    @ApiModelProperty(value = "品种ID")
    @TableField(value = "CATALOG_ID")
    private String catalogId;

    @ApiModelProperty(value = "品种名称")
    @TableField(value = "CATALOG_NAME")
    private String catalogName;

    @ApiModelProperty(value = "品牌名称")
    @TableField(value = "BRAND")
    private String brand;

    @ApiModelProperty(value = "质量等级")
    @TableField(value = "GRADE")
    private String grade;

    @ApiModelProperty(value = "规格")
    @TableField(value = "SPECIFICATION")
    private String specification;

    @ApiModelProperty(value = "生产日期")
    @TableField(value = "PRODUCT_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productDate;

    @ApiModelProperty(value = "数量")
    @TableField(value = "QTY")
    private BigDecimal qty;

    @ApiModelProperty(value = "仓房")
    @TableField(value = "ST_ID")
    private String stId;

    @ApiModelProperty(value = "仓房名称")
    @TableField(value = "ST_NAME")
    private String stName;

    @ApiModelProperty(value = "货位")
    @TableField(value = "LOC_ID")
    private String locId;

    @ApiModelProperty(value = "货位名称")
    @TableField(value = "LOC_NAME")
    private String locName;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "组织")
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    private String dataHierarchyId;

    }
