package com.hxdi.nmjl.mapper.inout.duty;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.duty.DutyRecord;
import com.hxdi.nmjl.condition.inout.DutyCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_DUTY_RECORD】的数据库操作Mapper
* @createDate 2025-04-16 09:16:20
* @Entity com.hxdi.nmjl.domain.inout.duty.BDutyRecord
*/
public interface DutyRecordMapper extends SuperMapper<DutyRecord> {


    /**
     * 分页查询
     *
     * @param pages
     * @param dutyCondition
     * @return
     */
    @DataPermission
    Page<DutyRecord> selectPageV1(Page<DutyRecord> pages, @Param("condition") DutyCondition dutyCondition);

    /**
     * 列表查询
     * @param dutyCondition
     * @return
     */
    @DataPermission
    List<DutyRecord> selectListV1(@Param("condition") DutyCondition dutyCondition);
}
