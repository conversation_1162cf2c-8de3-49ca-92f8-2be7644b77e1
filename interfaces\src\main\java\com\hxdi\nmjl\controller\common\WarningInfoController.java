package com.hxdi.nmjl.controller.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.common.WarningInfoCondition;
import com.hxdi.nmjl.domain.common.WarningInfo;
import com.hxdi.nmjl.service.common.WarningInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 告警信息控制层
 * 所有的告警信息都在此处进行统一管理
 * 根据类型区分对应的告警信息
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "告警信息管理")
@RequestMapping("/warningInfo")
public class WarningInfoController extends BaseController<WarningInfoService, WarningInfo> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<WarningInfo>> page(WarningInfoCondition condition) {
        return ResultBody.ok().data(bizService.getPageByCondition(condition));
    }

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public ResultBody<List<WarningInfo>> list(WarningInfoCondition condition) {
        return ResultBody.ok().data(bizService.getListByCondition(condition));
    }

    @PostMapping("/handle")
    @ApiOperation("处理告警信息")
    public ResultBody<Void> handle(@RequestParam String id, @RequestParam String disposeDesc) {
        bizService.handle(id, disposeDesc);
        return ResultBody.ok();
    }

    @GetMapping("/get")
    @ApiOperation("查看告警信息详情")
    public ResultBody<WarningInfo> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

}
