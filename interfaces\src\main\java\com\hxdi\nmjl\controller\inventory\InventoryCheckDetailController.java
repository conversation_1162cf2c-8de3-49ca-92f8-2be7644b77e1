package com.hxdi.nmjl.controller.inventory;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.domain.inventory.InventoryCheckDetail;
import com.hxdi.nmjl.service.inventory.InventoryCheckDetailService;
import com.hxdi.nmjl.condition.inventory.InventoryCheckDetailCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 盘点记录明细接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Api(tags = "盘点记录明细")
@RestController
@RequestMapping("/inventoryCheckDetail")
public class InventoryCheckDetailController extends BaseController<InventoryCheckDetailService, InventoryCheckDetail> {

    @ApiOperation(value = "查询盘点明细")
    @PostMapping("/generateDetail")
    public ResultBody<List<InventoryCheckDetail>> generateDetail(@RequestParam String checkId, @RequestParam String stId) {
        List<InventoryCheckDetail> inventoryCheckDetail = bizService.generateDetail(checkId, stId);
        return ResultBody.ok().data(inventoryCheckDetail);
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResultBody<Page<InventoryCheckDetail>> pages(InventoryCheckDetailCondition condition) {
        Page<InventoryCheckDetail> list = bizService.pages(condition);
        return ResultBody.ok().data(list);
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public ResultBody<List<InventoryCheckDetail>> lists(String checkId) {
        List<InventoryCheckDetail> list = bizService.lists(checkId);
        return ResultBody.ok().data(list);
    }


}
