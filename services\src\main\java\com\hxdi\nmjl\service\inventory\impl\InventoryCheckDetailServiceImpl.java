package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.inventory.InventoryCheckDetailCondition;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.domain.base.StoreLocation;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inventory.InventoryCheckDetail;
import com.hxdi.nmjl.mapper.inventory.InventoryCheckDetailMapper;
import com.hxdi.nmjl.service.inventory.InventoryBaseService;
import com.hxdi.nmjl.service.inventory.InventoryCheckDetailService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 盘点记录明细服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryCheckDetailServiceImpl extends BaseServiceImpl<InventoryCheckDetailMapper, InventoryCheckDetail> implements InventoryCheckDetailService {

    @Resource
    private InventoryService inventoryService;

    @Resource
    private InventoryBaseService inventoryBaseService;

    @Override
    public List<InventoryCheckDetail> generateDetail(String checkId, String stId) {
        if (verifyConfig(checkId)) {
            InventoryCondition inventoryCondition = new InventoryCondition();
            inventoryCondition.setStId(stId);
            List<Inventory> inventoryList = inventoryService.getList(inventoryCondition);

            if (CollectionUtils.isEmpty(inventoryList)) {
                throw new BaseException("库存记录为空");
            }

            List<InventoryCheckDetail> detailList = new ArrayList<>(inventoryList.size());
            for (Inventory inventory : inventoryList) {
                InventoryCheckDetail detail = new InventoryCheckDetail();
                detail.setCheckId(checkId);
                detail.setInventoryId(inventory.getId());
                detail.setCurrentInventoryQty(inventory.getInventoryQty());
                detailList.add(detail);
            }
            saveBatch(detailList);
        }
        return lists(checkId);
    }

    @Override
    public Page<InventoryCheckDetail> pages(InventoryCheckDetailCondition condition) {
        Page<InventoryCheckDetail> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        page.getRecords().forEach(item -> {
            item.getInventory().setStoreName(CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), item.getInventory().getStoreId(), Organization::getOrgName));
            item.getInventory().setStName(CacheProvider.getValue(RedisKeys.STORE_HOUSE.key(), item.getInventory().getStId(), StoreHouse::getName));
            item.getInventory().setLocName(CacheProvider.getValue(RedisKeys.STORE_LOCATION.key(), item.getInventory().getLocId(), StoreLocation::getName));
        });
        return page;
    }

    @Override
    public List<InventoryCheckDetail> lists(String checkId) {
        List<InventoryCheckDetail> detailList = baseMapper.selectListV1(checkId);
        detailList.forEach(detail -> detail.setInventory(inventoryBaseService.get(detail.getInventoryId()).toInventory()));
        return detailList;
    }

    /**
     * 验证数据是否已生成
     */
    private boolean verifyConfig(String checkId) {
        Long count = baseMapper.selectCount(Wrappers.<InventoryCheckDetail>lambdaQuery()
                .eq(InventoryCheckDetail::getCheckId, checkId));

        return count == 0;
    }
}
