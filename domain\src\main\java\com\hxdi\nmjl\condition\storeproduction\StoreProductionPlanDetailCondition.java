package com.hxdi.nmjl.condition.storeproduction;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "门店生产计划明细查询条件")
@Getter
@Setter
public class StoreProductionPlanDetailCondition extends QueryCondition {

    @ApiModelProperty(value = "明细ID")
    private String id;

    @ApiModelProperty(value = "生产计划ID")
    private String planId;

    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    @ApiModelProperty(value = "分类ID")
    private String classificationId;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "组织ID")
    private String dataHierarchyId;
}

