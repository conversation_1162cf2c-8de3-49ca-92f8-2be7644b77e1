package com.hxdi.nmjl.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.InventoryCheckDetail;
import com.hxdi.nmjl.condition.inventory.InventoryCheckDetailCondition;

import java.util.List;

/**
 * 盘点记录明细接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
public interface InventoryCheckDetailService extends IBaseService<InventoryCheckDetail> {

    /**
     * 新增明细并回显
     *
     * @param checkId 盘点id
     * @return 盘点明细列表
     */
    List<InventoryCheckDetail> generateDetail(String checkId, String stId);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 明细列表
     */
    Page<InventoryCheckDetail> pages(InventoryCheckDetailCondition condition);

    /**
     * 列表查询
     *
     * @param checkId 盘点id
     * @return 盘点明细列表
     */
    List<InventoryCheckDetail> lists(String checkId);
}
