package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description="库存更新日志")
@Getter
@Setter
@TableName(value = "B_INVENTORY_LOG")
public class InventoryLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 库存ID
     */
    @TableField(value = "INVENTORY_ID")
    @ApiModelProperty(value="库存ID")
    private String inventoryId;

    /**
     * 主业务ID
     */
    @TableField(value = "MAIN_BIZ_ID")
    @ApiModelProperty(value="主业务ID")
    private String mainBizId;

    /**
     * 子业务ID
     */
    @TableField(value = "SUB_BIZ_ID")
    @ApiModelProperty(value="子业务ID")
    private String subBizId;

    /**
     * 业务类型
     */
    @TableField(value = "BIZ_TYPE")
    @ApiModelProperty(value="业务类型")
    private String bizType;

    /**
     * 出入库类型
     */
    @TableField(value = "INOUT_TYPE")
    @ApiModelProperty(value="出入库类型")
    private String inoutType;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value="军供站ID")
    private String storeId;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value="仓房ID")
    private String stId;

    /**
     * 货位ID
     */
    @TableField(value = "LOC_ID")
    @ApiModelProperty(value="货位ID")
    private String locId;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value="品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value="品种名称")
    private String catalogName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATIONS")
    @ApiModelProperty(value="规格")
    private String specifications;

    /**
     * 质量等级
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value="质量等级")
    private String grade;

    /**
     * 储备性质
     */
    @TableField(value = "RESERVE_LEVEL")
    @ApiModelProperty(value="储备性质")
    private Integer reserveLevel;

    /**
     * 省
     */
    @TableField(value = "PROVINCE")
    @ApiModelProperty(value="省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "CITY")
    @ApiModelProperty(value="市")
    private String city;

    /**
     * 区
     */
    @TableField(value = "COUNTY")
    @ApiModelProperty(value="区县")
    private String county;

    /**
     * 生产日期
     */
    @TableField(value = "PRODUCTION_DATE")
    @ApiModelProperty(value="生产日期")
    private Date productionDate;

    /**
     * 管理机构id
     */
    @TableField(value = "MANAGE_UNIT_ID")
    @ApiModelProperty(value="管理机构id")
    private String manageUnitId;

    /**
     * 更新数量
     */
    @TableField(value = "CHANGE_QTY")
    @ApiModelProperty(value="更新数量")
    private BigDecimal changeQty;

    /**
     * 更新前库存数量
     */
    @TableField(value = "BEFORE_INVENTORY_QTY")
    @ApiModelProperty(value="更新前库存数量")
    private BigDecimal beforeInventoryQty;

    /**
     * 单价
     */
    @TableField(value = "PRICE")
    @ApiModelProperty(value="单价")
    private BigDecimal price;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value="状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 创建日期（只保留日期部分）
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建日期")
    private Date createTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建id")
    private String createId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}
