<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.clientrelated.SupplierProductMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.clientrelated.SupplierProduct">
        <!--@mbg.generated-->
        <!--@Table B_SUPPLIER_PRODUCT-->
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="SUPPLIER_ID" jdbcType="VARCHAR" property="supplierId" />
        <result column="FOODCATEGORY_ID" jdbcType="VARCHAR" property="foodCategoryId" />
        <result column="QUALITY_LEVEL" jdbcType="VARCHAR" property="qualityLevel" />
        <result column="CAPACITY" jdbcType="DECIMAL" property="capacity" />
        <result column="PRODUCE_DATE" jdbcType="TIMESTAMP" property="produceDate" />
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
        <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
        <result column="SPEC" jdbcType="VARCHAR" property="spec" />
        <result column="SUPPLIER_PRICE" jdbcType="DECIMAL" property="supplierPrice" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
        <result column="FOODCATEGORY_NAME" jdbcType="VARCHAR" property="foodCategoryName" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, SUPPLIER_ID, FOODCATEGORY_ID, QUALITY_LEVEL, CAPACITY, PRODUCE_DATE, REMARKS, ENABLED,
        SPEC, SUPPLIER_PRICE, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID,
        DATA_HIERARCHY_ID, FOODCATEGORY_NAME
    </sql>
    <update id="changeState" parameterType="com.hxdi.nmjl.domain.clientrelated.SupplierProduct">
        UPDATE B_SUPPLIER_PRODUCT
        SET ENABLED = #{enabled}
        WHERE ID = #{id}
    </update>
    <select id="selectPageV1" resultType="com.hxdi.nmjl.domain.clientrelated.SupplierProduct">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_SUPPLIER_PRODUCT
        <where>
        enabled = 1
        <if test="@plugins.OGNL@isNotEmpty(condition.supplierId)">
            AND SUPPLIER_ID = #{condition.supplierId}
        </if>
        <if test="@plugins.OGNL@isNotEmpty(condition.foodCategoryId )">
            AND FOODCATEGORY_ID = #{condition.foodCategoryId}
        </if>
        </where>
        order by CREATE_TIME DESC
    </select>
    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.clientrelated.SupplierProduct">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_SUPPLIER_PRODUCT
        <where>
        enabled = 1
        <if test="condition.supplierId != null">
            AND SUPPLIER_ID = #{condition.supplierId}
        </if>
        <if test="condition.foodCategoryId != null">
            AND FOODCATEGORY_ID = #{condition.foodCategoryId}
        </if>
        </where>
        order by CREATE_TIME DESC
    </select>

</mapper>
