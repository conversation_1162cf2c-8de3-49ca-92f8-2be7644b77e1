package com.hxdi.nmjl.service.inout.opt.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.inout.ReceiptCondition;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.inout.opt.ReceiptDetail;
import com.hxdi.nmjl.domain.inout.opt.ReceiptOrder;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inout.receipt.ReceiptOrderMapper;
import com.hxdi.nmjl.service.inout.opt.ReceiptDetailService;
import com.hxdi.nmjl.service.inout.opt.ReceiptService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.utils.RedisKeys;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class ReciptServiceImpl extends BaseServiceImpl<ReceiptOrderMapper, ReceiptOrder> implements ReceiptService {


    @Resource
    private InventoryService inventoryService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private ReceiptDetailService receiptDetailService;


    @Override
    public void create(ReceiptOrder receiptOrder) {
        // 生成领用单号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("RECIPT_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        receiptOrder.setReceiptCode((String) businessCode.getValue());

        // 保存领用单
        save(receiptOrder);
        receiptOrder.getDetailList().forEach(receiptDetail -> {
            receiptDetail.setReceiptId(receiptOrder.getId());
            //todo 库存ID
            receiptDetailService.save(receiptDetail);
        });


    }

    @Override
    public void updating(ReceiptOrder receiptOrder) {
        //修改后默认回到待审批状态
        receiptOrder.setApproveStatus(0);
        this.updateById(receiptOrder);

        receiptDetailService.delete(receiptOrder.getId());
        receiptOrder.getDetailList().forEach(receiptDetail -> {
            receiptDetail.setReceiptId(receiptOrder.getId());
            //todo 库存ID
            receiptDetailService.save(receiptDetail);
        });


    }

    @Override
    public ReceiptOrder detail(String id) {
        ReceiptOrder receiptOrder = this.getById(id);

        List<ReceiptDetail> receiptDetailList = receiptDetailService.getlists(id);
        receiptOrder.setDetailList(receiptDetailList);
        return receiptOrder;

    }

    @Override
    public void delete(String id) {
        ReceiptOrder receiptOrder = this.getById(id);
        if (receiptOrder.getApproveStatus() == 0) {
            this.removeById(id);
            receiptDetailService.delete(id);
        } else {
            BizExp.pop("当前领用申请单正在审批中，无法删除");
        }
    }

    @Override
    public Page<ReceiptOrder> getPage(ReceiptCondition receiptCondition) {
        Page<ReceiptOrder> pages = receiptCondition.newPage();
        baseMapper.selectPageV1(pages, receiptCondition);
        return pages;
    }

    @Override
    public List<ReceiptOrder> getList(ReceiptCondition receiptCondition) {
        return baseMapper.selectListV1(receiptCondition);
    }

    @Override
    public void approve(String id, String approveOpinion) {
        ReceiptOrder receiptOrder = this.getById(id);
        if (receiptOrder.getApproveStatus() == 0) {
            receiptOrder.setApproveStatus(1);
            receiptOrder.setApproveTime(new Date());
            receiptOrder.setApprover(SecurityHelper.obtainUser().getNickName());
            receiptOrder.setApproveOpinion(approveOpinion);
            baseMapper.updateById(receiptOrder);
        } else {
            BizExp.pop("当前领用申请单已审核，无法驳回");
        }

    }

    @Override
    public void rejected(String id, String approveOpinion) {
        ReceiptOrder receiptOrder = this.getById(id);
        if (receiptOrder.getApproveStatus() == 0) {
            receiptOrder.setApproveStatus(2);
            receiptOrder.setApproveTime(new Date());
            receiptOrder.setApproveOpinion(approveOpinion);
            receiptOrder.setApprover(SecurityHelper.obtainUser().getNickName());
            baseMapper.updateById(receiptOrder);
        } else {
            BizExp.pop("当前领用申请单已审核，无法驳回");
        }
    }

    @Override
    public List<Inventory> getlists(InventoryCondition dto) {
        List<Inventory> inventories = inventoryService.getListWithGroupByCatalog(dto);
        inventories.forEach(inventory -> {
            Optional<Catalog> catalog = CacheProvider.optional(RedisKeys.CATALOG.key(), inventory.getCatalogId());
            inventory.setBrand(CommonUtils.getOptionalValue(catalog, Catalog::getBrand));
            inventory.setSpecifications(CommonUtils.getOptionalValue(catalog, Catalog::getSpecification));
            inventory.setUnit(CommonUtils.getOptionalValue(catalog, Catalog::getUnit));
        });
        return inventories;
    }


}
