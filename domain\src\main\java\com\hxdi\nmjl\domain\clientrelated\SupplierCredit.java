package com.hxdi.nmjl.domain.clientrelated;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 供应商信用表
 */
@Getter
@Setter
@TableName("B_SUPPLIER_CREDIT")
public class SupplierCredit extends Entity<SupplierCredit> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 供应商ID
     */
    @TableField("SUPPLIER_ID")
    private String supplierId;

    /**
     * 巡检单位
     */
    @TableField("CHECK_UNIT")
    private String checkUnit;

    /**
     * 巡检时间
     */
    @TableField("CHECK_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate checkTime;

    /**
     * 记录问题
     */
    @TableField("PROBLEM")
    private String problem;

    /**
     * 巡检人员
     */
    @TableField("CHECK_MAN")
    private String checkMan;

    /**
     * 问题处理意见
     */
    @TableField("OPINION")
    private String opinion;

    /**
     * 问题处理说明
     */
    @TableField("PROBLEM_DESC")
    private String problemDesc;

    /**
     * 处理完成时间
     */
    @TableField("HANDLED_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate handledTime;

    /**
     * 是否处理（0-未处理，1-已处理）
     */
    @TableField("IS_HANDLED")
    private Integer isHandled;

    /**
     * 状态（1-正常，0-删除）
     */
    @TableField("ENABLED")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建者ID
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新者ID
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 数据权限字段
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
