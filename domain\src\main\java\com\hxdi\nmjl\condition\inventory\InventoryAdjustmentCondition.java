package com.hxdi.nmjl.condition.inventory;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @Author: chenhui
 * @CreateTime: 2025-04-18
 * @Description: 库存调整记录条件
 */
@Getter
@Setter
@ApiModel(description = "库存调整记录查询条件")
public class InventoryAdjustmentCondition extends QueryCondition {
    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    private Date applyStartTime;
    /**
     * 申请结束时间
     */
    @ApiModelProperty(value = "申请结束时间")
    private Date applyEndTime;
    /**
     * 处置方式
     */
    @ApiModelProperty(value = "处置方式")
    private String type;
    /**
     * 审核状态
     */
    @ApiModelProperty(value = "审核状态")
    private Integer approveStatus;
}
