<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.CommPlanMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.CommPlan">
            <id property="ID" column="ID" jdbcType="VARCHAR"/>
            <result property="SCHEMA_ID" column="SCHEMA_ID" jdbcType="VARCHAR"/>
            <result property="NAME" column="NAME" jdbcType="VARCHAR"/>
            <result property="MAIN_PARTICIPANTS" column="MAIN_PARTICIPANTS" jdbcType="VARCHAR"/>
            <result property="CONTENT" column="CONTENT" jdbcType="VARCHAR"/>
            <result property="TIME_DESC" column="TIME_DESC" jdbcType="VARCHAR"/>
            <result property="COMM_MODE" column="COMM_MODE" jdbcType="VARCHAR"/>
            <result property="STATUS" column="STATUS" jdbcType="INTEGER"/>
            <result property="ENABLED" column="ENABLED" jdbcType="INTEGER"/>
            <result property="CREATE_TIME" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="UPDATE_TIME" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="CREATE_ID" column="CREATE_ID" jdbcType="VARCHAR"/>
            <result property="UPDATE_ID" column="UPDATE_ID" jdbcType="VARCHAR"/>
            <result property="TENANT_ID" column="TENANT_ID" jdbcType="VARCHAR"/>
            <result property="DATA_HIERARCHY_ID" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SCHEMA_ID,NAME,
        MAIN_PARTICIPANTS,CONTENT,TIME_DESC,
        COMM_MODE,STATUS,ENABLED,
        CREATE_TIME,UPDATE_TIME,CREATE_ID,
        UPDATE_ID,TENANT_ID,DATA_HIERARCHY_ID
    </sql>
</mapper>
