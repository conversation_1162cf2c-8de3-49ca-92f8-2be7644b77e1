package com.hxdi.nmjl.vo.quality;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.hxdi.nmjl.domain.quality.QualityInspectionDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 质量检验结果返回VO
 * @author: 王贝强
 * @create: 2025-04-18 15:55
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "质量检验结果返回VO")
public class QualityInspectionVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "检验编号")
    private String inspectionNo;

    @ApiModelProperty(value = "检验名称")
    private String name;

    @ApiModelProperty(value = "检验方案ID")
    private String schemaId;

    @ApiModelProperty(value = "检验方案名称")
    private String schemaName;

    @ApiModelProperty(value = "检验报告类型:字典JYBGLX")
    private String inspectType;

    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构代码")
    private String creditCode;

    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @ApiModelProperty(value = "合同CODE")
    private String contractCode;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "订单编码")
    private String orderCode;

    @ApiModelProperty(value = "生产批次")
    private String batchNum;

    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    @ApiModelProperty(value = "质量等级：字典：YZLDJ/LZLDJ")
    private String grade;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "品牌名称")
    private String brand;

    @ApiModelProperty(value = "样品编号")
    private String sampleNo;

    @ApiModelProperty(value = "抽样日期")
    private String samplingDate;

    @ApiModelProperty(value = "抽样人")
    private String sampler;

    @ApiModelProperty(value = "样品数量")
    private BigDecimal sampleQty;

    @ApiModelProperty(value = "检验机构ID")
    private String qualityOrgId;

    @ApiModelProperty(value = "检验机构名称")
    private String qualityOrgName;

    @ApiModelProperty(value = "检测日期")
    private Date inspectTime;

    @ApiModelProperty(value = "检验人")
    private String inspectPerson;

    @ApiModelProperty(value = "检验结果:1-合格，0-不合格")
    private String inspectResult;

    @ApiModelProperty(value = "检验说明")
    private String inspectDesc;

    @ApiModelProperty(value = "附件")
    private String attachments;

    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    @ApiModelProperty(value = "质检结果检测项列表")
    private List<QualityInspectionDetail> detailList;


    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID")
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID")
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID")
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
