<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.quality.QualityInspectionMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.quality.QualityInspection">
    <!--@mbg.generated-->
    <!--@Table B_QUALITY_INSPECTION-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="INSPECTION_NO" jdbcType="VARCHAR" property="inspectionNo" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="SCHEMA_ID" jdbcType="VARCHAR" property="schemaId" />
    <result column="SCHEMA_NAME" jdbcType="VARCHAR" property="schemaName" />
    <result column="INSPECT_TYPE" jdbcType="VARCHAR" property="inspectType" />
    <result column="STORE_ID" jdbcType="VARCHAR" property="storeId" />
    <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName" />
    <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="CONTRACT_ID" jdbcType="VARCHAR" property="contractId" />
    <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contractCode" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="ORDER_CODE" jdbcType="VARCHAR" property="orderCode" />
    <result column="BATCH_NUM" jdbcType="VARCHAR" property="batchNum" />
    <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId" />
    <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
    <result column="GRADE" jdbcType="VARCHAR" property="grade" />
    <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification" />
    <result column="PRODUCT_DATE" jdbcType="TIMESTAMP" property="productDate" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="SAMPLE_NO" jdbcType="VARCHAR" property="sampleNo" />
    <result column="SAMPLING_DATE" jdbcType="VARCHAR" property="samplingDate" />
    <result column="SAMPLER" jdbcType="VARCHAR" property="sampler" />
    <result column="SAMPLE_QTY" jdbcType="DECIMAL" property="sampleQty" />
    <result column="QUALITY_ORG_ID" jdbcType="VARCHAR" property="qualityOrgId" />
    <result column="QUALITY_ORG_NAME" jdbcType="VARCHAR" property="qualityOrgName" />
    <result column="INSPECT_TIME" jdbcType="TIMESTAMP" property="inspectTime" />
    <result column="INSPECT_PERSON" jdbcType="VARCHAR" property="inspectPerson" />
    <result column="INSPECT_RESULT" jdbcType="VARCHAR" property="inspectResult" />
    <result column="INSPECT_DESC" jdbcType="VARCHAR" property="inspectDesc" />
    <result column="ATTACHMENTS" jdbcType="VARCHAR" property="attachments" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    <result column="CREDIT_CODE" jdbcType="VARCHAR" property="creditCode" />

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INSPECTION_NO, "NAME", SCHEMA_ID, "SCHEMA_NAME", INSPECT_TYPE, STORE_ID, STORE_NAME, 
    ORG_ID, ORG_NAME,CREDIT_CODE, CONTRACT_ID,CONTRACT_CODE, BATCH_NUM, CATALOG_ID, "CATALOG_NAME", GRADE, SPECIFICATION,
    PRODUCT_DATE, BRAND, SAMPLE_NO, SAMPLING_DATE, SAMPLER, SAMPLE_QTY, QUALITY_ORG_ID, 
    QUALITY_ORG_NAME, INSPECT_TIME, INSPECT_PERSON, INSPECT_RESULT, INSPECT_DESC, ATTACHMENTS, 
    ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, ORDER_ID, ORDER_CODE
  </sql>
  
  <sql id="condition">
    <if test="condition.inspectionNo != null and condition.inspectionNo != ''">
      and INSPECTION_NO like concat('%',#{condition.inspectionNo},'%')
    </if>
    <if test="condition.batchNum != null and condition.batchNum != ''">
      and INSPECTION_NO = #{condition.batchNum}
    </if>
    <if test="condition.name != null and condition.name != ''">
      and NAME = #{condition.name}
    </if>
    <if test="condition.inspectType != null and condition.inspectType != ''">
      and INSPECT_TYPE = #{condition.inspectType}
    </if>
    <if test="condition.storeId != null and condition.storeId != ''">
      and STORE_ID = #{condition.storeId}
    </if>
    <if test="condition.batchNum != null and condition.batchNum != ''">
      and BATCH_NUM = #{condition.batchNum}
    </if>
    <if test="condition.catalogId != null and condition.catalogId != ''">
      and CATALOG_ID = #{condition.catalogId}
    </if>
    <if test="condition.catalogName != null and condition.catalogName != ''">
      and CATALOG_NAME like concat('%',#{condition.catalogName},'%')
    </if>
    <if test="condition.contractId != null and condition.contractId != ''">
      and CONTRACT_ID = #{condition.contractId}
    </if>
    <if test="condition.orderId != null and condition.orderId != ''">
      and ORDER_ID = #{condition.orderId}
    </if>
    <if test="condition.inspectTimeStart != null">
      and INSPECT_TIME &gt;= #{condition.inspectTimeStart}
    </if>
    <if test="condition.inspectTimeEnd != null">
      and INSPECT_TIME &lt;= #{condition.inspectTimeEnd}
    </if>
  </sql>

  <select id="getList" parameterType="com.hxdi.nmjl.condition.quality.QualityInspectionCondition"
          resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_QUALITY_INSPECTION
    <where>
      <include refid="condition" />
      and ENABLED = 1
    </where>
    ORDER BY CREATE_TIME DESC
  </select>

  <select id="getPage" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from B_QUALITY_INSPECTION
    <where>
      <include refid="condition" />
      and ENABLED = 1
    </where>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>