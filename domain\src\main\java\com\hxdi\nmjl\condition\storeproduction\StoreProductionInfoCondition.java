package com.hxdi.nmjl.condition.storeproduction;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "库存生产信息查询条件")
@Getter
@Setter
public class StoreProductionInfoCondition extends QueryCondition {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "计划编号")
    private String planNo;

    @ApiModelProperty(value = "库点ID")
    private String storeId;

    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "组织ID")
    private String dataHierarchyId;

}

