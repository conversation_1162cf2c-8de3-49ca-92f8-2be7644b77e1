package com.hxdi.nmjl.mapper.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.opt.ProcessRecord;
import com.hxdi.nmjl.condition.inout.ProcessRecordCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 加工工艺备案数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Mapper
public interface ProcessRecordMapper extends SuperMapper<ProcessRecord> {

    /**
     * 分页查询加工工艺备案
     *
     * @param page      分页对象
     * @param condition 查询条件
     * @return 分页结果
     */
    @DataPermission
    Page<ProcessRecord> selectPageV1(Page<ProcessRecord> page, @Param("condition") ProcessRecordCondition condition);

    /**
     * 列表查询加工工艺备案
     *
     * @param condition 查询条件
     * @return 列表结果
     */
    List<ProcessRecord> selectListV1(@Param("condition") ProcessRecordCondition condition);
}
