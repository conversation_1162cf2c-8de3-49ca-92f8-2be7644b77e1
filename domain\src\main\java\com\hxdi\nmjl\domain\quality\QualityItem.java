package com.hxdi.nmjl.domain.quality;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description="质检标准项")
@TableName(value = "B_QUALITY_ITEM")
public class QualityItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 方案ID
     */
    @TableField(value = "SCHEMA_ID")
    @ApiModelProperty(value="方案ID")
    @NotBlank(message = "方案ID不能为空")
    private String schemaId;

    /**
     * 质检项名称
     */
    @TableField(value = "ITEM_NAME")
    @ApiModelProperty(value="质检项名称")
    @NotBlank(message = "质检项名称不能为空")
    private String itemName;

    /**
     * 比较符
     */
    @TableField(value = "ITEM_SYMBOL")
    @ApiModelProperty(value="比较符 见枚举QualityItemComparisonType值")
    @NotBlank(message = "比较符不能为空")
    private String itemSymbol;

    /**
     * 单位符号
     */
    @TableField(value = "ITEM_SIGN")
    @ApiModelProperty(value="单位符号")
    @NotBlank(message = "单位符号不能为空")
    private String itemSign;

    /**
     * 标准值
     */
    @TableField(value = "ITEM_SVAL")
    @ApiModelProperty(value="标准值")
    @NotBlank(message = "标准值不能为空")
    private String itemSval;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;
}