package com.hxdi.nmjl.service.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inout.opt.ReceiptOrder;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.condition.inout.ReceiptCondition;

import java.util.List;

public interface ReceiptService  extends IBaseService<ReceiptOrder> {
    /**
     * 创建
     * @param receiptOrder
     */
    void create(ReceiptOrder receiptOrder);

    /**
     * 更新
     * @param receiptOrder
     */
    void updating(ReceiptOrder receiptOrder);

    /**
     * 查询单个
     * @param id
     * @return
     */
    Object detail(String id);

    /**
     * 删除
      * @param id
     */
    void delete(String id);

    /**
     * 分页查询
     * @param receiptCondition
     * @return
     */
    Page<ReceiptOrder> getPage(ReceiptCondition receiptCondition);

    /**
     * 列表查询
      * @param receiptCondition
     * @return
     */
    List<ReceiptOrder> getList(ReceiptCondition receiptCondition);

    /**
     * 审核
      * @param id
     */
    void approve(String id,String approveOpinion);

    /**
     * 获取商品列表
     *
     * @return
     */
    List<Inventory> getlists(InventoryCondition dto);

    /**
     * 驳回
      * @param id
     */
    void rejected(String id,String approveOpinion);
}
