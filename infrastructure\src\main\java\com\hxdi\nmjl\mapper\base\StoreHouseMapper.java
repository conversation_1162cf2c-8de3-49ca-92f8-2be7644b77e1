package com.hxdi.nmjl.mapper.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.condition.base.StorehouseCondition;
import com.hxdi.nmjl.dto.base.StoreCapacityDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface StoreHouseMapper extends SuperMapper<StoreHouse> {
    /**
     * 分页查询
     * @param page
     * @param condition
     * @return
     */
    @DataPermission
    Page<StoreHouse> selectPageV1(Page<StoreHouse> page, @Param("condition") StorehouseCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    @DataPermission
    List<StoreHouse> selectListV1(@Param("condition") StorehouseCondition condition);

    /**
     * 根据军供站ID查询仓房容量
     *
     * @param storeId
     * @return
     */
    List<StoreCapacityDTO> getCapacityByStoreId(@Param("storeId") List<String> storeId);
}
