package com.hxdi.nmjl.domain.specialproduct;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 地方特色产品征集公告
 */
@Getter
@Setter
@TableName("B_SPECIAL_PRODUCT_NOTICE")
public class SpecialProductNotice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 公告标题
     */
    @TableField("TITLE")
    private String title;

    /**
     * 公告内容
     */
    @TableField("CONTENT")
    private String content;

    /**
     * 发布单位
     */
    @TableField("ORG_NAME")
    private String orgName;

    /**
     * 发布人
     */
    @TableField("PUBLISHER")
    private String publisher;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("PUBLISH_TIME")
    private Date publishTime;

    /**
     * 联系电话
     */
    @TableField("MOBILE")
    private String mobile;

    /**
     * 截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("END_TIME")
    private Date endTime;

    /**

     发布状态：0 - 待发布，1 - 已发布
     */
    @TableField (value = "PUBLISH_STATE")
    private Integer publishState;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
