<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.clientrelated.SupplierCommunicationMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.clientrelated.SupplierCommunication">
        <!--@Table B_SUPPLIER_COMMUNICATION-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="SUPPLIER_ID" jdbcType="VARCHAR" property="supplierId"/>
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName"/>
        <result column="COMMUNICATION_TYPE" jdbcType="INTEGER" property="communicationType"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="CONTENT" jdbcType="LONGVARCHAR" property="content"/>
        <result column="PRIORITY" jdbcType="INTEGER" property="priority"/>
        <result column="ATTACHMENTS" jdbcType="LONGVARCHAR" property="ATTACHMENTS"/>
        <result column="INITIATOR_ID" jdbcType="VARCHAR" property="initiatorId"/>
        <result column="INITIATOR_NAME" jdbcType="VARCHAR" property="initiatorName"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="RESULT" jdbcType="LONGVARCHAR" property="result"/>
        <result column="HANDLER_ID" jdbcType="VARCHAR" property="handlerId"/>
        <result column="HANDLER_NAME" jdbcType="VARCHAR" property="handlerName"/>
        <result column="HANDLE_TIME" jdbcType="TIMESTAMP" property="handleTime"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId"/>
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        SUPPLIER_ID,
        SUPPLIER_NAME,
        COMMUNICATION_TYPE,
        TITLE,
        CONTENT,
        PRIORITY,
        ATTACHMENTS,
        INITIATOR_ID,
        INITIATOR_NAME,
        STATUS,
        RESULT,
        HANDLER_ID,
        HANDLER_NAME,
        HANDLE_TIME,
        REMARKS,
        ENABLED,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID,
        ORG_ID,
        ORG_NAME
    </sql>

    <update id="changeStatus">
        UPDATE B_SUPPLIER_COMMUNICATION
        SET ENABLED = #{enabled}
        WHERE ID = #{id}
    </update>

    <update id="batchUpdateStatus">
        UPDATE B_SUPPLIER_COMMUNICATION
        SET STATUS = #{status}
        WHERE ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="countBySupplierId" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM B_SUPPLIER_COMMUNICATION
        WHERE SUPPLIER_ID = #{supplierId}
          AND ENABLED = 1
    </select>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_SUPPLIER_COMMUNICATION
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.supplierId)">
                AND SUPPLIER_ID = #{condition.supplierId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.supplierName)">
                AND SUPPLIER_NAME LIKE CONCAT('%', #{condition.supplierName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.communicationType)">
                AND COMMUNICATION_TYPE = #{condition.communicationType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.title)">
                AND TITLE LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.priority)">
                AND PRIORITY = #{condition.priority}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                AND ORG_NAME LIKE CONCAT('%', #{condition.orgName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.initiatorId)">
                AND INITIATOR_ID = #{condition.initiatorId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.initiatorName)">
                AND INITIATOR_NAME LIKE CONCAT('%', #{condition.initiatorName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.status)">
                AND STATUS = #{condition.status}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.handlerId)">
                AND HANDLER_ID = #{condition.handlerId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.handlerName)">
                AND HANDLER_NAME LIKE CONCAT('%', #{condition.handlerName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startCreateTime)">
                AND CREATE_TIME &gt;= #{condition.startCreateTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endCreateTime)">
                AND CREATE_TIME &lt;= #{condition.endCreateTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startHandleTime)">
                AND HANDLE_TIME &gt;= #{condition.startHandleTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endHandleTime)">
                AND HANDLE_TIME &lt;= #{condition.endHandleTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                AND (TITLE LIKE CONCAT('%', #{condition.keywords}, '%')
                    OR CONTENT LIKE CONCAT('%', #{condition.keywords}, '%')
                    OR RESULT LIKE CONCAT('%', #{condition.keywords}, '%'))
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_SUPPLIER_COMMUNICATION
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.supplierId)">
                AND SUPPLIER_ID = #{condition.supplierId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.supplierName)">
                AND SUPPLIER_NAME LIKE CONCAT('%', #{condition.supplierName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.communicationType)">
                AND COMMUNICATION_TYPE = #{condition.communicationType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.title)">
                AND TITLE LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.priority)">
                AND PRIORITY = #{condition.priority}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgId)">
                AND ORG_ID = #{condition.orgId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgName)">
                AND ORG_NAME LIKE CONCAT('%', #{condition.orgName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.initiatorId)">
                AND INITIATOR_ID = #{condition.initiatorId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.initiatorName)">
                AND INITIATOR_NAME LIKE CONCAT('%', #{condition.initiatorName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.status)">
                AND STATUS = #{condition.status}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.handlerId)">
                AND HANDLER_ID = #{condition.handlerId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.handlerName)">
                AND HANDLER_NAME LIKE CONCAT('%', #{condition.handlerName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startCreateTime)">
                AND CREATE_TIME &gt;= #{condition.startCreateTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endCreateTime)">
                AND CREATE_TIME &lt;= #{condition.endCreateTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startHandleTime)">
                AND HANDLE_TIME &gt;= #{condition.startHandleTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endHandleTime)">
                AND HANDLE_TIME &lt;= #{condition.endHandleTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                AND (TITLE LIKE CONCAT('%', #{condition.keywords}, '%')
                    OR CONTENT LIKE CONCAT('%', #{condition.keywords}, '%')
                    OR RESULT LIKE CONCAT('%', #{condition.keywords}, '%'))
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>
