package com.hxdi.nmjl.service.common;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.common.BarCode;
import com.hxdi.nmjl.domain.common.BarInfo;
import com.hxdi.nmjl.enums.BusinessType;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @program: nmjl-service
 * @description: 系统条码服务
 * @author: 王贝强
 * @create: 2025-08-20 15:20
 */
public interface BarCodeService extends IBaseService<BarCode> {
    /**
     * 获取条码详情
     *
     * @param info 条码信息
     * @return 条码详情的JSON字符串
     */
    String getDetail(BarInfo info);

    /**
     * 根据外链获取条码详情
     *
     * @param openId 外链标识
     * @return 条码详情的JSON字符串
     */
    String getDetailByUrl(String openId);

    /**
     * 生成业务数据对应的条码
     *
     * @param businessId   业务ID
     * @param businessType 业务类型
     * @param ext          扩展信息
     * @return 生成的条码字符串
     */
    void generateBarCode(String businessId, BusinessType businessType, String ext);

    /**
     * 获取条码字符串(Base64编码)
     *
     * @param businessId       业务ID
     * @param businessTypeCode 业务类型代码
     * @return 条码字符串
     */
    String getBarCode(String businessId, String businessTypeCode);

    /**
     * 获取条码外链URL(Base64编码)
     *
     * @param businessId       业务ID
     * @param businessTypeCode 业务类型代码
     * @return 条码外链URL
     */
    String getBarUrl(String businessId, String businessTypeCode);

    /**
     * 获取条码图片
     *
     * @param businessId       业务ID
     * @param businessTypeCode 业务类型代码
     * @return 条码图片的Base64编码字符串
     */
    void getBarCodeImage(String businessId, String businessTypeCode, HttpServletRequest request, HttpServletResponse response);

    /**
     * 获取条码外链URL图片
     *
     * @param businessId       业务ID
     * @param businessTypeCode 业务类型代码
     * @return 条码外链URL图片的Base64编码字符串
     */
    void getBarUrlImage(String businessId, String businessTypeCode, HttpServletRequest request, HttpServletResponse response);
}
