package com.hxdi.nmjl.service.inout.opt;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.opt.ReceiptDetail;

import java.util.List;

public interface ReceiptDetailService extends IBaseService<ReceiptDetail> {
    /**
     * 删除
      * @param id
     */
    void delete(String id);


    /**
     * 获取list
     * @param id
     * @return
     */
    List<ReceiptDetail> getlists(String id);
}
