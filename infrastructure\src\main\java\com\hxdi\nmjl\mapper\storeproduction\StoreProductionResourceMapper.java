package com.hxdi.nmjl.mapper.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionResourceCondition;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产资源调配Mapper接口
 */
public interface StoreProductionResourceMapper extends SuperMapper<StoreProductionResource> {

    Page<StoreProductionResource> selectPageV1(Page<StoreProductionResource> page, @Param("condition") StoreProductionResourceCondition condition);

    List<StoreProductionResource> selectListV1(@Param("condition") StoreProductionResourceCondition condition);
}
