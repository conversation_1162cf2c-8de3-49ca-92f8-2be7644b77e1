package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 库存盘点明细
 */
@ApiModel(description = "库存盘点明细")
@Getter
@Setter
@TableName(value = "B_INVENTORY_CHECK_DETAIL")
public class InventoryCheckDetail implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 盘点ID
     */
    @TableField(value = "CHECK_ID")
    @ApiModelProperty(value = "盘点ID")
    private String checkId;
    /**
     * 库存ID
     */
    @TableField(value = "INVENTORY_ID")
    @ApiModelProperty(value = "库存ID")
    private String inventoryId;
    /**
     * 当前库存数量
     */
    @TableField(value = "CURRENT_INVENTORY_QTY")
    @ApiModelProperty(value = "当前库存数量")
    private BigDecimal currentInventoryQty;
    /**
     * 盘点数量
     */
    @TableField(value = "CHECK_QTY")
    @ApiModelProperty(value = "盘点数量")
    private BigDecimal checkQty;
    /**
     * 损溢数量
     */
    @TableField(value = "DIFF_QTY")
    @ApiModelProperty(value = "损溢数量")
    private BigDecimal diffQty;
    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;

    /*************************扩展字段******************************/

    /**
     * 库存静态数据
     */
    @ApiModelProperty(value = "库存静态数据")
    @TableField(exist = false)
    private Inventory inventory;

    /**
     * 盘点时间
     */
    @ApiModelProperty(value = "盘点时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date startTime;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    @TableField(exist = false)
    private Integer approveStatus;

    /**
     * 盘点状态：0-未盘点，1-已盘点
     */
    @ApiModelProperty(value = "盘点状态：0-未盘点，1-已盘点")
    @TableField(exist = false)
    private Integer checkStatus;
}
