package com.hxdi.nmjl.domain.specialproduct;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 地方特色产品审批
 */
@Getter
@Setter
@TableName (value = "B_SPECIAL_PRODUCT_APPROVAL")
public class SpecialProductApproval implements Serializable {

    private static final long serialVersionUID = 1L;

    /**

     特色产品 id（主键）
     */
    @TableId (value = "PRODUCT_ID", type = IdType.ASSIGN_ID)
    private String productId;

    /**

     一级审核状态：0 - 未审核，1 - 已审核，2 - 驳回
     */
    @TableField (value = "APPROVE_STATUS1")
    private Integer approveStatus1;

    /**

     一级审批人
     */
    @TableField (value = "APPROVER1")
    private String approver1;

    /**

     一级审批时间
     */
    @TableField (value = "APPROVE_TIME1")
    private Date approveTime1;

    /**

     一级审批意见
     */
    @TableField (value = "APPROVE_OPINION1")
    private String approveOpinion1;

    /**

     二级审核状态：0 - 未审核，1 - 已审核，2 - 驳回
     */
    @TableField (value = "APPROVE_STATUS2")
    private Integer approveStatus2;

    /**

     二级审批人
     */
    @TableField (value = "APPROVER2")
    private String approver2;

    /**

     二级审批时间
     */
    @TableField (value = "APPROVE_TIME2")
    private Date approveTime2;

    /**

     二级审批意见
     */
    @TableField (value = "APPROVE_OPINION2")
    private String approveOpinion2;

    /**

     三级审核状态：0 - 未审核，1 - 已审核，2 - 驳回
     */
    @TableField (value = "APPROVE_STATUS3")
    private Integer approveStatus3;

    /**

     三级审批人
     */
    @TableField (value = "APPROVER3")
    private String approver3;

    /**

     三级审批时间
     */
    @TableField (value = "APPROVE_TIME3")
    private Date approveTime3;

    /**

     三级审批意见
     */
    @TableField (value = "APPROVE_OPINION3")
    private String approveOpinion3;
}