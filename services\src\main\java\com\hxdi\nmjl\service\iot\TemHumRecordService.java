package com.hxdi.nmjl.service.iot;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.iot.TemHumRecord;
import com.hxdi.nmjl.condition.iot.TemHumRecordCondition;

import java.util.List;

public interface TemHumRecordService extends IBaseService<TemHumRecord>{
    /**
     * 批量插入温湿度检测结果
     * @param record
     */
    void insertBatch(List<TemHumRecord> record);

    List<TemHumRecord> getList(TemHumRecordCondition condition);

    Page<TemHumRecord> getPage(TemHumRecordCondition condition);
}
