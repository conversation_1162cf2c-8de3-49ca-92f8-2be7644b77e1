package com.hxdi.nmjl.condition.mobilization;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "动员协议查询条件")
@Getter
@Setter
public class MobilizedContractCondition extends QueryCondition {

    @ApiModelProperty(value = "协议类型：字典DYXYLX")
    private String bizType;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "合同状态：0-未生效，1-已生效，2-已结束，3-已终止")
    private Integer state;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "军供站id")
    private String storeId;

    @ApiModelProperty(value = "审核状态")
    private Integer approveStatus;

    @ApiModelProperty(value = "创建开始时间")
    private Date createStartTime;

    @ApiModelProperty(value = "创建结束时间")
    private Date createEndTime;

    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
