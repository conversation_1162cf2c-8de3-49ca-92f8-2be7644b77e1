<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ProductionOrderMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.ProductionOrder">
        <!--@mbg.generated-->
        <!--@Table B_PRODUCTION_ORDER-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="ORDER_CODE" jdbcType="VARCHAR" property="orderCode"/>
        <result column="PID" jdbcType="VARCHAR" property="pid"/>
        <result column="BATCH_NO" jdbcType="VARCHAR" property="batchNo"/>
        <result column="CONTRACT_ID" jdbcType="VARCHAR" property="contractId"/>
        <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contractCode"/>
        <result column="ORDER_TIME" jdbcType="TIMESTAMP" property="orderTime"/>
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId"/>
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName"/>
        <result column="CLIENT_ID" jdbcType="VARCHAR" property="clientId"/>
        <result column="CLIENT_NAME" jdbcType="VARCHAR" property="clientName"/>
        <result column="START_DATE" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="EXPECTED_COMPLETION_DATE" jdbcType="TIMESTAMP" property="expectedCompletionDate"/>
        <result column="PLAN_QTY" jdbcType="DECIMAL" property="planQty"/>
        <result column="COMPLETED_QTY" jdbcType="DECIMAL" property="completedQty"/>
        <result column="DELIVERY_METHOD" jdbcType="INTEGER" property="deliveryMethod"/>
        <result column="RCV_ADDR" jdbcType="VARCHAR" property="rcvAddr"/>
        <result column="RCV_DETAIL_ADDR" jdbcType="VARCHAR" property="rcvDetailAddr"/>
        <result column="STATE" jdbcType="INTEGER" property="state"/>
        <result column="SETTLEMENT_STATUS" jdbcType="INTEGER" property="settlementStatus"/>
        <result column="NOTES" jdbcType="VARCHAR" property="notes"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="ATTACHEMENTS" jdbcType="VARCHAR" property="attachements"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        ORDER_CODE,
        PID,
        BATCH_NO,
        CONTRACT_ID,
        CONTRACT_CODE,
        ORDER_TIME,
        ORG_ID,
        ORG_NAME,
        STORE_ID,
        STORE_NAME,
        CLIENT_ID,
        CLIENT_NAME,
        START_DATE,
        EXPECTED_COMPLETION_DATE,
        PLAN_QTY,
        COMPLETED_QTY,
        DELIVERY_METHOD,
        RCV_ADDR,
        RCV_DETAIL_ADDR,
        "STATE",
        NOTES,
        ENABLED,
        ATTACHEMENTS,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID,
        SETTLEMENT_STATUS
    </sql>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_PRODUCTION_ORDER
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.contractId)">
                AND CONTRACT_ID = #{condition.contractId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.contractCode)">
                AND CONTRACT_CODE = #{condition.contractCode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="condition.storeName != null and condition.storeName != ''">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
                AND CLIENT_ID = #{condition.clientId}
            </if>
            <if test="condition.clientName != null and condition.clientName != ''">
                AND CLIENT_NAME LIKE CONCAT('%', #{condition.clientName}, '%')
            </if>
            <if test="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.state) != null">
                AND STATE IN
                <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.state)" item="state"
                         open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.settlementState)">
                AND SETTLEMENT_STATUS = #{condition.settlementState}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
                AND START_DATE &gt;= #{condition.startTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
                AND START_DATE &lt;= #{condition.endTime}
            </if>
            <if test="condition.search != null and condition.search != ''">
                AND (ORDER_CODE LIKE CONCAT('%', #{condition.search}, '%') OR
                     BATCH_NO LIKE CONCAT('%', #{condition.search}, '%'))
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_PRODUCTION_ORDER
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.contractId)">
                AND CONTRACT_ID = #{condition.contractId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.contractCode)">
                AND CONTRACT_CODE = #{condition.contractCode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="condition.storeName != null and condition.storeName != ''">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
                AND CLIENT_ID = #{condition.clientId}
            </if>
            <if test="condition.clientName != null and condition.clientName != ''">
                AND CLIENT_NAME LIKE CONCAT('%', #{condition.clientName}, '%')
            </if>
            <if test="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.state) != null">
                AND STATE IN
                <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.state)" item="state"
                         open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.settlementState)">
                AND SETTLEMENT_STATUS = #{condition.settlementState}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
                AND START_DATE &gt;= #{condition.startTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
                AND START_DATE &lt;= #{condition.endTime}
            </if>
            <if test="condition.search != null and condition.search != ''">
                AND (ORDER_CODE LIKE CONCAT('%', #{condition.search}, '%') OR
                     BATCH_NO LIKE CONCAT('%', #{condition.search}, '%'))
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="selectListV2" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_PRODUCTION_ORDER
        <where>
            ENABLED = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.contractId)">
                AND CONTRACT_ID = #{condition.contractId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.contractCode)">
                AND CONTRACT_CODE = #{condition.contractCode}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="condition.storeName != null and condition.storeName != ''">
                AND STORE_NAME LIKE CONCAT('%', #{condition.storeName}, '%')
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.clientId)">
                AND CLIENT_ID = #{condition.clientId}
            </if>
            <if test="condition.clientName != null and condition.clientName != ''">
                AND CLIENT_NAME LIKE CONCAT('%', #{condition.clientName}, '%')
            </if>
            <if test="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.state) != null">
                AND STATE IN
                <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.state)" item="state"
                         open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.settlementState)">
                AND settlementState = #{condition.settlementState}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
                AND START_DATE &gt;= #{condition.startTime}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
                AND START_DATE &lt;= #{condition.endTime}
            </if>
            <if test="condition.search != null and condition.search != ''">
                AND (ORDER_CODE LIKE CONCAT('%', #{condition.search}, '%') OR
                     BATCH_NO LIKE CONCAT('%', #{condition.search}, '%'))
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>
</mapper>