package com.hxdi.nmjl.event;

import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.model.DataSyncMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @program: nmjl-service
 * @description: 数据变更事件监听器
 * @author: 王贝强
 * @create: 2025-03-27 09:47
 */
@Component
@Slf4j
public class DataChangeEventListener {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @EventListener
    public void handleDataChangeEvent(DataChangeEvent event) {
        log.info("收到Spring数据变更事件: {} {} {}", event.getOperation(), event.getEntityType(), event.getEntityId());

        // 只处理特定实体类型的事件，目前只有一个
        if (!"organization".equals(event.getEntityType())) {
            return;
        }

        try {
            // 将 Spring Event 转换为 RabbitMQ Message DTO
            DataSyncMessage message = convertMessage(event);

            // 发送到 RabbitMQ
            rabbitTemplate.convertAndSend(CommonConstants.MQ_DATASYNC_EXCHANGE,CommonConstants.MQ_BASE_DATASYNC_KEY, message);

            log.info("向RabbitMQ发送数据同步消息 [交换机: {}, 路由键: {}]: 消息id={}",
                    CommonConstants.MQ_DATASYNC_EXCHANGE,CommonConstants.MQ_BASE_DATASYNC_KEY, message.getMessageId());

        } catch (Exception e) {
            log.error("向RabbitMQ发送数据同步消息失败: {} {} {}",
                    event.getOperation(), event.getEntityType(), event.getEntityId(), e);
            // 考虑添加错误处理逻辑，例如记录失败事件到数据库以便重试
        }
    }
    private DataSyncMessage convertMessage(DataChangeEvent event){
        DataSyncMessage message = new DataSyncMessage();
        message.setEntityType(event.getEntityType());
        message.setEntityId(event.getEntityId());
        message.setOperation(event.getOperation());
        message.setData(event.getData());
        message.setVersion(event.getVersion());
        return message;
    }
}
