package com.hxdi.nmjl.condition.emergency;

import com.hxdi.common.core.model.QueryCondition;
import com.hxdi.common.core.mybatis.annotation.Between;
import com.hxdi.common.core.mybatis.annotation.EQ;
import com.hxdi.common.core.mybatis.annotation.LIKE;
import com.hxdi.common.core.mybatis.base.support.RangeBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


@Getter
@Setter
public class CommPlanCondition extends QueryCondition {
    @Between("create_time")
    private RangeBean<Date> createTime;

    @ApiModelProperty(value = "方案标题")
    @LIKE
    private String title;

    @ApiModelProperty(value = "方案状态")
    @EQ
    private String statusRecord;

    @ApiModelProperty(value = "方案计划状态")
    @EQ
    private String status;

    @ApiModelProperty(value = "方案ID")
    @EQ
    private String schemaId;

}