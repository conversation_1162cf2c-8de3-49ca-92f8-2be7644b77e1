package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 组织机构：管理单位与军供站
 */
@Getter
@Setter
@TableName(value = "B_ORGANIZATION")
public class Organization extends Entity<Organization> {

    private static final long serialVersionUID = -9202133504558634939L;

    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    /**
     * 机构编号
     */
    @TableField(value = "ORG_CODE")
    private String orgCode;

    /**
     * 机构名称
     */
    @TableField(value = "ORG_NAME")
    private String orgName;

    /**
     * 简称
     */
    @TableField(value = "ABBR_NAME")
    private String abbrName;

    @NotEmpty(message = "请选择上级单位")
    @TableField(value = "PID")
    private String pid;

    /**
     * 机构类型：1-管理单位，2-军供站
     */
    @TableField(value = "ORG_TYPE")
    private Integer orgType;

    /**
     * 统一社会信用代码
     */
    @TableField(value = "CREDIT_CODE")
    private String creditCode;

    /**
     * 机构性质：字典JGXZ
     */
    @TableField(value = "ORG_NATURE")
    private String orgNature;

    /**
     * 省
     */
    @TableField(value = "PROVINCE")
    private String province;

    /**
     * 市
     */
    @TableField(value = "CITY")
    private String city;

    /**
     * 区县
     */
    @TableField(value = "COUNTY")
    private String county;

    /**
     * 区划地址
     */
    @TableField(value = "AREA")
    private String area;

    /**
     * 详细地址
     */
    @TableField(value = "DETAIL_ADDR")
    private String detailAddr;

    /**
     * 邮编
     */
    @TableField(value = "POST_CODE")
    private String postCode;

    /**
     * 成立时间
     */
    @TableField(value = "REGISTRE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registreDate;

    /**
     * 法人
     */
    @TableField(value = "FR")
    private String fr;

    /**
     * 负责人
     */
    @TableField(value = "FZR")
    private String fzr;

    /**
     * 联系电话
     */
    @TableField(value = "MOBILE")
    private String mobile;

    /**
     * 企业邮箱
     */
    @TableField(value = "EMAIL")
    private String email;

    /**
     * 经度
     */
    @TableField(value = "LON")
    private String lon;

    /**
     * 纬度
     */
    @TableField(value = "LAT")
    private String lat;

    /**
     * 功能分类：1-保障基地，2-军供站
     */
    @TableField(value = "FUNCTIONAL")
    private Integer functional;

    /**
     * 是否承储：0-否，1-是
     */
    @TableField(value = "STORE_IS")
    private Integer storeIs;

    /**
     * 市场业务: 字典SCYW
     */
    @TableField(value = "BIZ_TYPE")
    private String bizType;

    /**
     * 排序
     */
    @TableField(value = "SORTS")
    private Integer sorts;

    /**
     * 是否启用:0-禁用，1-启用，7-删除
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    @TableField(value = "PATHS")
    private String paths;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 数据权限字段
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * ------扩展分割线------
     */

    @TableField(exist = false)
    private String parentOrgName;

    /**
     * 子机构
     */
    @TableField(exist = false)
    private List<Organization> children;
}
