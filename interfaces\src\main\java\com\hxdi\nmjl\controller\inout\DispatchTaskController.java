package com.hxdi.nmjl.controller.inout;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.nmjl.domain.inout.DispatchTask;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.service.inout.DispatchTaskService;
import com.hxdi.nmjl.condition.inout.DispatchCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 库存拆分计划控制层
*
* <AUTHOR>
*/
@RestController
@Api(tags = "库存拆分计划")
@RequestMapping("/dispatchTask")
public class DispatchTaskController extends BaseController<DispatchTaskService,DispatchTask> {

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("/get")
    @ApiOperation(value = "单条查询")
    public ResultBody<DispatchTask> selectOne(String id) {
    return ResultBody.ok().data(bizService.getById(id));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/getMore")
    @ApiOperation(value = "单条查询（包含计划对应的库存及品种信息）")
    public ResultBody<DispatchTask> selectOneV1(String id) {
        return ResultBody.ok().data(bizService.getMore(id));
    }


    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "创建或更新拆分调度计划（注意：无法修改已经开始执行的计划）")
    public ResultBody<DispatchTask> createOrUpdate(@RequestBody DispatchTask dispatchTask) {
    bizService.createOrUpdate(dispatchTask);
    return ResultBody.ok().data(dispatchTask);
    }

    @GetMapping("/remove")
    @ApiOperation(value = "删除拆分调度计划")
    public ResultBody remove(@RequestParam("id") String id) {
        bizService.removeById(id);
        return ResultBody.ok();
    }

    @GetMapping("/list")
    @ApiOperation(value = "根据条件查询计划列表（带权限控制）")
    public ResultBody<List<DispatchTask>> list(DispatchCondition condition) {
    return ResultBody.ok().data(bizService.listV1(condition));
    }

    @GetMapping("/page")
    @ApiOperation(value = "根据条件查询计划分页列表（带权限控制）")
    public ResultBody<Page<DispatchTask>> page(DispatchCondition condition) {
    return ResultBody.ok().data(bizService.PageV1(condition));
    }

}
