package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.plan.BidObject;
import com.hxdi.nmjl.mapper.plan.BidObjectMapper;
import com.hxdi.nmjl.service.plan.BidObjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class BidObjectServiceImpl extends BaseServiceImpl<BidObjectMapper, BidObject> implements BidObjectService {
    @Override
    public List<BidObject> getList(List<String> bidIdList){
        return baseMapper.selectList(Wrappers.<BidObject>lambdaQuery().in(BidObject::getBidId, bidIdList));
    }
}
