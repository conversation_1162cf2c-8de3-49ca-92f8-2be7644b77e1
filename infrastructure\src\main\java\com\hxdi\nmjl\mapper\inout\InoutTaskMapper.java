package com.hxdi.nmjl.mapper.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.InoutTask;
import com.hxdi.nmjl.condition.inout.InoutTaskCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 出入库任务单数据访问层
* @createDate 2025-04-08 16:01:28
* @Entity com.hxdi.nmjl.domain.inventory.BInoutTask
*/
public interface InoutTaskMapper extends SuperMapper<InoutTask> {

    /**
     * 分页查询
     * @return
     */
    @DataPermission
    Page<InoutTask> selectPageV1(Page<InoutTask> page, @Param("condition") InoutTaskCondition condition);

    /**
     * 列表查询
     * @return
     */
    @DataPermission
    List<InoutTask> selectListV1(@Param("condition") InoutTaskCondition condition);
}




