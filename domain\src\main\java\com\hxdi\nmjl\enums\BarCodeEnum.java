package com.hxdi.nmjl.enums;

import lombok.Getter;

/**
 * @program: nmjl-service
 * @description: 系统条码枚举
 * @author: 王贝强
 * @create: 2025-08-20 14:21
 */
@Getter
public enum BarCodeEnum {
    
    QR_CODE("001", "二维码"),
    BAR_CODE("002", "条形码");

    private final String code;
    private final String label;

    BarCodeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }
}
