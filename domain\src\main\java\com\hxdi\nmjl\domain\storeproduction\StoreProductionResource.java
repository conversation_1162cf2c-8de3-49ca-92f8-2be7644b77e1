package com.hxdi.nmjl.domain.storeproduction;


import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 门店生产资源表
 */
@Getter
@Setter
@TableName(value = "B_STORE_PRODUCTION_RESOURCE")
public class StoreProductionResource implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 计划编号
     */
    @TableField(value = "PLAN_NO")
    private String planNo;

    /**
     * 资源类型：1-设备，2-人员，3-原材料
     */
    @TableField(value = "RESOURCE_TYPE")
    private Integer resourceType;

    /**
     * 资源名称（设备名/人员姓名/原材料名）
     */
    @TableField(value = "RESOURCE_NAME")
    private String resourceName;

    /**
     * 调配数量
     */
    @TableField(value = "ALLOCATION_QUANTITY")
    private BigDecimal allocationQuantity;

    /**
     * 资源描述
     */
    @TableField(value = "RESOURCE_DESC")
    private String resourceDesc;

    /**
     * 状态（1-有效 0-删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;



}

