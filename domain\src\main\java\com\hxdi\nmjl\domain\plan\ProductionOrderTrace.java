package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@TableName(value = "B_PRODUCTION_ORDER_TRACE")
@ApiModel(description = "生产订单跟踪记录")
public class ProductionOrderTrace extends Entity<ProductionOrderTrace> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 订单ID
     */
    @TableField(value = "ORDER_ID")
    @ApiModelProperty(value = "订单ID")
    private String orderId;

    /**
     * 订单明细ID
     */
    @TableField(value = "ORDER_ITEM_ID")
    @ApiModelProperty(value = "订单明细ID")
    private String orderItemId;

    /**
     * 上报时间
     */
    @TableField(value = "REPORT_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "上报时间")
    private Date reportTime;

    /**
     * 上报人
     */
    @TableField(value = "REPORTER")
    @ApiModelProperty(value = "上报人")
    private String reporter;

    /**
     * 生产日期
     */
    @TableField(value = "PRODUCT_DATE")
    @NotEmpty
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    /**
     * 累计生产件数
     */
    @TableField(value = "TOTAL_PACK_QTY")
    @ApiModelProperty(value = "累计生产件数")
    private Integer totalPackQty;

    /**
     * 累计完成数量
     */
    @TableField(value = "TOTAL_QTY")
    @ApiModelProperty(value = "累计完成数量")
    private BigDecimal totalQty;

    /**
     * 生产批次状态:0-进心中， 2-已结束
     */
    @TableField(value = "BATCH_STATE")
    @ApiModelProperty(value = "生产批次状态:0-进行中， 2-已结束")
    private Integer batchState;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}