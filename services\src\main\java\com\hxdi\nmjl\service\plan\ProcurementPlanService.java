package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.condition.plan.ProcurementPlanCondition;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlan;
import com.hxdi.nmjl.vo.bigscreen.ProcurementPlanSummaryVO;

import java.util.List;

/**
 * 粮食筹措计划服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/26 15:30
 */
public interface ProcurementPlanService extends IBaseService<GrainProcurementPlan> {

    /**
     * 新增筹措计划
     *
     * @param plan 筹措计划实体
     */
    GrainProcurementPlan createV1(GrainProcurementPlan plan);

    /**
     * 修改筹措计划计划
     *
     * @param plan 筹措计划实体
     */
    void update(GrainProcurementPlan plan);

    /**
     * 获取计划详情
     *
     * @param planId 计划ID
     * @return 筹措计划实体
     */
    GrainProcurementPlan getDetail(String planId);

    /**
     * 分页查询计划
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<GrainProcurementPlan> pages(ProcurementPlanCondition condition);

    /**
     * 列表查询计划
     *
     * @param condition 查询条件
     * @return 计划列表
     */
    List<GrainProcurementPlan> lists(ProcurementPlanCondition condition);

    /**
     * 列表查询计划和详情
     *
     * @param condition 查询条件
     * @return 计划列表
     */
    List<GrainProcurementPlan> listAndDetail(ProcurementPlanCondition condition);

    /**
     * 审批计划（状态变更）
     *
     * @param planId        计划ID
     * @param approveStatus 审批状态
     * @param opinion       审批意见
     */
    void approve(String planId, Integer approveStatus, String opinion);

    /**
     * 删除计划
     *
     * @param planId 计划ID
     */
    void remove(String planId);

    /**
     * 提交计划（状态变更）
     *
     * @param planId 计划ID
     */
    void submit(String planId);

    /**
     * 筹措计划状态变更
     * state 计划状态：0-未上报，1-已上报、2-招标中，3-采购中，4-已完成
     *
     * @param planId 计划ID
     */
    void updatePlanState(String planId,Integer State);

    /**
     * 手动完成计划（状态变更）
     *
     * @param planId 计划ID
     */
    void completePlan(String planId);

    /**
     * 获取可以委托的单位
     *
     * @return 可以委托的单位列表
     */
    List<String> getAvailableDelegatedUnits();

    /**
     * 判断计划是否可以新增
     *
     * @return 是否可以创建
     */
    boolean CanBeCreated();

    /**
     * 大屏统计：获取随机城市的筹措计划统计
     * 传入当前用户地区编码，获取当前地区的筹措计划统计
     *
     * @param areaCode 当前用户地区编码
     * @return
     */
    ProcurementPlanSummaryVO getPlanSummary(String areaCode);

    /**
     * 大屏统计：获取指定地区下的各个军供站的各个品类的筹措计划
     *
     * @param condition 查询条件
     * @return
     */
    Page<ProcurementPlanSummaryVO> getPlanSummaryPage(ProcurementPlanCondition condition);
}