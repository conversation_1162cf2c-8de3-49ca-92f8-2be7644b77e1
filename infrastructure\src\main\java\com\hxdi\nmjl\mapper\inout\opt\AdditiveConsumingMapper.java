package com.hxdi.nmjl.mapper.inout.opt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.opt.AdditiveConsuming;
import com.hxdi.nmjl.condition.inout.AdditiveConsumingCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 添加剂管理数据访问层
 *
 * <AUTHOR>
 * @since 2025-04-22 10:02:10
 */
@Mapper
public interface AdditiveConsumingMapper extends SuperMapper<AdditiveConsuming> {

    /**
     * 分页查询添加剂管理
     *
     * @param page      分页对象
     * @param condition 查询条件
     * @return 分页结果
     */
    @DataPermission
    Page<AdditiveConsuming> selectPageV1(Page<AdditiveConsuming> page, @Param("condition") AdditiveConsumingCondition condition);

    /**
     * 列表查询添加剂管理
     *
     * @param condition 查询条件
     * @return 列表结果
     */
    @DataPermission
    List<AdditiveConsuming> selectListV1(@Param("condition") AdditiveConsumingCondition condition);
}
