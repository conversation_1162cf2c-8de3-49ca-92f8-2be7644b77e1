package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 粮食筹措计划主表
 */
@Getter
@Setter
@ApiModel(description = "粮食筹措计划主表")
@TableName(value = "B_GRAIN_PROCUREMENT_PLAN")
public class GrainProcurementPlan extends Entity<GrainProcurementPlan> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 父计划
     */
    @TableField(value = "PID")
    @ApiModelProperty(value = "父计划ID")
    private String pid;

    @TableField(value = "P_CODE")
    @ApiModelProperty(value = "父计划编号")
    private String pCode;

    @TableField(value = "PARENT_FLAG")
    @ApiModelProperty(value = "是否为父计划标识：0-否，1-是")
    private Integer parentFlag;

    /**
     * 计划编号
     */
    @TableField(value = "PLAN_CODE")
    @ApiModelProperty(value = "计划编号")
    private String planCode;

    /**
     * 年份：YYYY
     */
    @TableField(value = "YEAR")
    @ApiModelProperty(value = "年份")
    private Integer year;

    /**
     * 计划类型：1-委托采购，2-自行采购
     */
    @TableField(value = "PLAN_TYPE")
    @ApiModelProperty(value = "计划类型")
    private Integer planType;

    /**
     * 管理单位ID
     */
    @TableField(value = "MANAGE_UNIT_ID")
    @ApiModelProperty(value = "管理单位ID")
    private String manageUnitId;

    /**
     * 管理单位名称
     */
    @TableField(value = "MANAGE_NAME")
    @ApiModelProperty(value = "管理单位名称")
    private String manageName;

    /**
     * 军供站ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "军供站ID")
    private String storeId;

    /**
     * 军供站名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "军供站名称")
    private String storeName;

    /**
     * 委托单位ID
     */
    @TableField(value = "ENTRUSTED_UNIT_ID")
    @ApiModelProperty(value = "委托单位ID")
    private String entrustedUnitId;

    /**
     * 委托单位名称
     */
    @TableField(value = "ENTRUSTED_UNIT_NAME")
    @ApiModelProperty(value = "委托单位名称")
    private String entrustedUnitName;

    /**
     * 区域编码
     */
    @TableField(value = "AREA_CODE")
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    /**
     * 计划状态：0-未上报，1-已上报、2-招标中，3-采购中，4-已完成
     */
    @TableField(value = "STATE")
    @ApiModelProperty(value = "计划状态")
    private Integer state;

    /**
     * 上报人
     */
    @TableField(value = "REPORTER")
    @ApiModelProperty(value = "上报人")
    private String reporter;

    /**
     * 上报时间
     */
    @TableField(value = "REPORT_TIME")
    @ApiModelProperty(value = "上报时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @ApiModelProperty(value = "审核状态")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;

    /**
     * 备注
     */
    @TableField(value = "NOTES")
    @ApiModelProperty(value = "备注")
    private String notes;

    /**
     * 交货地址
     */
    @TableField(value = "RCV_ADDR")
    @ApiModelProperty(value = "交货地址")
    private String rcvAddr;

    /**
     * 详细地址
     */
    @TableField(value = "RCV_DETAIL_ADDR")
    @ApiModelProperty(value = "详细地址")
    private String rcvDetailAddr;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 附件
     */
    @TableField(value = "ATTACHEMENTS")
    @ApiModelProperty(value = "附件")
    private String attachements;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * 计划详情
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "计划详情")
    private List<GrainProcurementPlanDetail> detailList;
}
