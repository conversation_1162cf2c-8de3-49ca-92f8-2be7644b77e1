package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.plan.OperatingPlan;
import com.hxdi.nmjl.condition.plan.OperatingPlanCondition;

import java.util.List;

/**
 * 整体经营计划管理服务接口
 */
public interface OperatingPlanService extends IBaseService<OperatingPlan> {

    /**
     * 查询整体经营计划详情
     *
     * @param id 整体经营计划ID
     * @return OperatingPlan
     */
    OperatingPlan getDetail(String id);

    /**
     * 分页查询整体经营计划信息
     *
     * @param condition 查询条件
     * @return Page<OperatingPlan>
     */
    Page<OperatingPlan> getPages(OperatingPlanCondition condition);

    /**
     * 列表查询整体经营计划信息
     *
     * @param condition 查询条件
     * @return List<OperatingPlan>
     */
    List<OperatingPlan> getList(OperatingPlanCondition condition);

    /**
     * 新增整体经营计划信息
     *
     * @param operatingPlan 整体经营计划信息
     */
    void add(OperatingPlan operatingPlan);

    /**
     * 更新整体经营计划信息
     *
     * @param operatingPlan 整体经营计划信息
     */
    void update(OperatingPlan operatingPlan);

    /**
     * 拆分
     *
     * @param operatingPlan 整体经营计划ID
     */
    void split(OperatingPlan operatingPlan);

    /**
     * 删除整体经营计划信息
     *
     * @param id 整体经营计划ID
     * @return boolean
     */
    boolean delete(String id);

    /**
     * 审核
     *
     * @param id             调整ID
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id             调整ID
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     *
     * @param id 调整ID
     */
    void submit(String id);
    /**
     * 已完结
     *
     * @param id 完结ID
     */
    void finished(String id);
    /**
     * 下达
     *
     * @param id 下达ID
     */
    void issue(String id);
}
