package com.hxdi.nmjl.condition.emergency;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "应急事件查询参数")
@Getter
@Setter

public class EmergencyEventCondition extends QueryCondition {

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "事件级别")
    private Integer eventLevel;

    @ApiModelProperty(value = "预案编号")
    private String respCode;

    @ApiModelProperty(value = "事件编号")
    private String eventCode;

    /**
     * 所在省id
     */
    @ApiModelProperty(value = "所在省id")
    private String provinceId;

    /**
     * 所在市id
     */
    @ApiModelProperty(value = "所在市id")
    private String cityId;

    /**
     * 所在县区id
     */
    @ApiModelProperty(value = "所在县区id")
    private String countyId;
}
