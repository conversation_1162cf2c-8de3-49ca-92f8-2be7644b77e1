<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.specialproduct.SpecialProductMonitoringMapper">

    <sql id="Base_Column_List">
        ID, PRODUCT_ID, PRODUCT_NAME, REPORT_DATE, REPORTER, SALES_VOLUMN, CUSTOMER_SCORE,
    SALES_TREND, INFLUENCE_FACTORS, NOTES, MARKEY_SHARE, ENABLED,
    CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID,
    DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.specialproduct.SpecialProductMonitoring">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PRODUCT_ID" jdbcType="VARCHAR" property="productId"/>
        <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
        <result column="REPORT_DATE" jdbcType="TIMESTAMP" property="reportDate"/>
        <result column="REPORTER" jdbcType="VARCHAR" property="reporter"/>
        <result column="SALES_VOLUMN" jdbcType="DECIMAL" property="salesVolumn"/>
        <result column="CUSTOMER_SCORE" jdbcType="DECIMAL" property="customerScore"/>
        <result column="SALES_TREND" jdbcType="LONGVARCHAR" property="salesTrend"/>
        <result column="INFLUENCE_FACTORS" jdbcType="LONGVARCHAR" property="influenceFactors"/>
        <result column="NOTES" jdbcType="VARCHAR" property="notes"/>
        <result column="MARKEY_SHARE" jdbcType="DECIMAL" property="marketShare"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from B_SPECIAL_PRODUCT_MONITORING
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.productId)">
                and PRODUCT_ID = #{condition.productId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.reportStartDate)">
                and REPORT_DATE >= #{condition.reportStartDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.reportEndDate)">
                and REPORT_DATE &lt;= #{condition.reportEndDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.reporter)">
                and REPORTER like concat('%', #{condition.reporter}, '%')
            </if>
        </where>
        order by REPORT_DATE desc
    </select>
    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.specialproduct.SpecialProductMonitoring">
        select <include refid="Base_Column_List"/>
        from B_SPECIAL_PRODUCT_MONITORING
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.productId)">
                and PRODUCT_ID = #{condition.productId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.reportStartDate)">
                and REPORT_DATE >= #{condition.reportStartDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.reportEndDate)">
                and REPORT_DATE &lt;= #{condition.reportEndDate}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.reporter)">
                and REPORTER like concat('%', #{condition.reporter}, '%')
            </if>
        </where>
        order by REPORT_DATE desc
    </select>
</mapper>
