<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.emergency.CommEvaluationMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.emergency.CommEvaluation">
            <id property="ID" column="ID" jdbcType="VARCHAR"/>
            <result property="TITLE" column="TITLE" jdbcType="VARCHAR"/>
            <result property="EVALUATION_OBJECT" column="EVALUATION_OBJECT" jdbcType="VARCHAR"/>
            <result property="EVALUATION_TIME" column="EVALUATION_TIME" jdbcType="TIMESTAMP"/>
            <result property="SCORE" column="SCORE" jdbcType="INTEGER"/>
            <result property="EVALUATE_STATUS" column="EVALUATE_STATUS" jdbcType="INTEGER"/>
            <result property="COMMON_ISSUES" column="COMMON_ISSUES" jdbcType="VARCHAR"/>
            <result property="IMPROVEMENT_MEASURES" column="IMPROVEMENT_MEASURES" jdbcType="VARCHAR"/>
            <result property="ENABLED" column="ENABLED" jdbcType="INTEGER"/>
            <result property="CREATE_TIME" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="UPDATE_TIME" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="CREATE_ID" column="CREATE_ID" jdbcType="VARCHAR"/>
            <result property="UPDATE_ID" column="UPDATE_ID" jdbcType="VARCHAR"/>
            <result property="TENANT_ID" column="TENANT_ID" jdbcType="VARCHAR"/>
            <result property="DATA_HIERARCHY_ID" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,TITLE,EVALUATION_OBJECT,EVALUATE_STATUS,
        EVALUATION_TIME,SCORE,COMMON_ISSUES,
        IMPROVEMENT_MEASURES,ENABLED,CREATE_TIME,
        UPDATE_TIME,CREATE_ID,UPDATE_ID,
        TENANT_ID,DATA_HIERARCHY_ID
    </sql>
</mapper>
