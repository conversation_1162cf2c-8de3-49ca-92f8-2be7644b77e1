package com.hxdi.nmjl.service.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.mobilization.MobilizedEnterprise;
import com.hxdi.nmjl.condition.mobilization.MobilizedEnterpriseCondition;

import java.util.List;

/**
 * 动员企业管理服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/1
 */
public interface MobilizedEnterpriseService extends IBaseService<MobilizedEnterprise> {

    /**
     * 分页查询企业
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<MobilizedEnterprise> pages(MobilizedEnterpriseCondition condition);

    /**
     * 列表查询企业
     * @param condition 查询条件
     * @return 企业列表
     */
    List<MobilizedEnterprise> lists(MobilizedEnterpriseCondition condition);

    /**
     * 修改企业信息
     * @param mobilizedEnterprise 企业信息实体
     */
    void update(MobilizedEnterprise mobilizedEnterprise);

    /**
     * 新增企业
     * @param mobilizedEnterprise 企业信息实体
     */
    void create(MobilizedEnterprise mobilizedEnterprise);

    /**
     * 删除企业
     * @param enterpriseId 企业ID
     */
    void remove(String enterpriseId);

    /**
     * 变更企业状态
     * @param enterpriseId 企业ID
     * @param state 企业状态
     */
    void changeState(String enterpriseId, Integer state);

    /**
     * 审核
     * @param id
     * @param approveOpinion
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     * @param id
     * @param approveOpinion
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     * @param id
     */
    void submit(String id);
}
