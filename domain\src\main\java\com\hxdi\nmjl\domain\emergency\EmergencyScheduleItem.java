package com.hxdi.nmjl.domain.emergency;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 紧急调度项目明细表
 */
@Getter
@Setter
@TableName(value = "B_EMERGENCY_SCHEDULE_ITEM")
public class EmergencyScheduleItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调度ID
     */
    @TableField(value = "SCHEDULE_ID")
    private String scheduleId;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    private String catalogName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    private String specification;

    /**
     * 质量等级
     */
    @TableField(value = "GRADE")
    private String grade;

    /**
     * 计划数量
     */
    @TableField(value = "PLAN_QTY")
    private BigDecimal planQty;

    /**
     * 完成数量
     */
    @TableField(value = "COMPLETED_QTY")
    private BigDecimal completedQty;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织ID
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
