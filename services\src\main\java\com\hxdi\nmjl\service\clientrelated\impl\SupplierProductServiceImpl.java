package com.hxdi.nmjl.service.clientrelated.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.clientrelated.SupplierProductCondition;
import com.hxdi.nmjl.domain.clientrelated.SupplierProduct;
import com.hxdi.nmjl.mapper.clientrelated.SupplierProductMapper;
import com.hxdi.nmjl.service.clientrelated.SupplierProductService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 供应商产品管理Service实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SupplierProductServiceImpl extends BaseServiceImpl<SupplierProductMapper, SupplierProduct> implements SupplierProductService {


    @Override
    public void create(SupplierProduct product) {
        verifyProduct(product);
        baseMapper.insert(product);
    }

    @Override
    public void update(SupplierProduct product) {
        baseMapper.updateById(product);
    }

    @Override
    public void changeState(String id, Integer enabled) {
        baseMapper.changeState(id, enabled);
    }

    @Override
    public SupplierProduct getByUniqueKey(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public List<SupplierProduct> getDetail(String supplierId) {
        //根据主表id查询
        SupplierProductCondition condition = new SupplierProductCondition();
        condition.setSupplierId(supplierId);
        return baseMapper.selectListV1(condition);
    }

    @Override
    public Page<SupplierProduct> pages(SupplierProductCondition condition) {
        Page<SupplierProduct> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public List<SupplierProduct> lists(SupplierProductCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public void removeByMainId(String mainId) {
        baseMapper.delete(Wrappers.<SupplierProduct>lambdaQuery().eq(SupplierProduct::getSupplierId, mainId));
    }

    /**
     * 验证数据有效性
     * 名称可以重复
     *
     * @param product 供应商产品
     */
    public void verifyProduct(SupplierProduct product) {
        long count = baseMapper.selectCount(Wrappers.<SupplierProduct>lambdaQuery()
                .eq(SupplierProduct::getSupplierId, product.getSupplierId())
                .eq(SupplierProduct::getFoodCategoryId, product.getFoodCategoryId()));
        if (count > 0) {
            throw new BaseException("已存在相似的供应商产品，请检查录入数据");
        }
    }
}

