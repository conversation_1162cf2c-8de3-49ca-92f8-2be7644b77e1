<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hxdi</groupId>
        <artifactId>hxdicloud_2.0</artifactId>
        <version>1.0.0</version>
    </parent>
    <groupId>com.hxdi.nmjl</groupId>
    <artifactId>nmjl-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>

    <modules>
        <module>domain</module>
        <module>infrastructure</module>
        <module>client</module>
        <module>services</module>
        <module>interfaces</module>
        <module>nmjl-service-starter</module>
    </modules>


    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <package.environment>dev</package.environment>
                <tag>SNAPSHOT</tag>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <package.environment>test</package.environment>
                <tag>ALPHA</tag>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>release</id>
            <properties>
                <package.environment>release</package.environment>
                <tag>RELEASE</tag>
            </properties>
        </profile>
        <!-- 本地环境 -->
        <profile>
            <id>local</id>
            <properties>
                <package.environment>local</package.environment>
                <tag>SNAPSHOT</tag>
            </properties>
        </profile>
    </profiles>


    <properties>
        <!-- 当前项目版本 -->
        <afood-project.version>0.0.1-SNAPSHOT</afood-project.version>
        <!-- mvn编译版本 -->
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

</project>
