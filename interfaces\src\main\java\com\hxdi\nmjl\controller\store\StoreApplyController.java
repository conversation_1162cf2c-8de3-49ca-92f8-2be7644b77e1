package com.hxdi.nmjl.controller.store;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.store.StoreApply;
import com.hxdi.nmjl.service.store.StoreApplyService;
import com.hxdi.nmjl.condition.store.StoreApplyCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 申请管理
 <AUTHOR>
 @version 1.0
 @since 2025/8/11
 */
@Api(tags = "申请管理")
@RestController
@RequestMapping("/storeApply")
public class StoreApplyController extends BaseController<StoreApplyService, StoreApply> {

    @ApiOperation("分页查询申请")
    @GetMapping("/page")
    public ResultBody<Page<StoreApply>> page(StoreApplyCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }


    @ApiOperation("列表查询申请")
    @GetMapping("/list")
    public ResultBody<List<StoreApply>> list(StoreApplyCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }


    @ApiOperation("保存/修改申请")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody StoreApply storeApply) {
        if(CommonUtils.isEmpty(storeApply.getId())) {
            bizService.create(storeApply);
        } else {
            bizService.update(storeApply);
        }
        return ResultBody.ok();
    }


    @ApiOperation("查看申请详情")
    @GetMapping("/getDetail")
    public ResultBody<StoreApply> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }


    @ApiOperation("审核申请")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String id,
                              @RequestParam Integer approveStatus,
                              @RequestParam(required = false) String opinion) {
        bizService.approve(id, approveStatus, opinion);
        return ResultBody.ok();
    }


    @ApiOperation("删除申请")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation("提交审核")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }
}
