package com.hxdi.nmjl.mapper.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.base.StoreLocation;
import com.hxdi.nmjl.condition.base.StoreLocationCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StoreLocationMapper extends SuperMapper<StoreLocation> {
    /**
     * 分页查询
     * @param page
     * @param condition
     * @return
     */
    @DataPermission
    Page<StoreLocation> selectPageV1(Page<StoreLocation> page, @Param("condition") StoreLocationCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    @DataPermission
    List<StoreLocation> selectListV1(@Param("condition") StoreLocationCondition condition);
}
