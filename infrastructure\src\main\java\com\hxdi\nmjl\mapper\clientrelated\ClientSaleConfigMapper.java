package com.hxdi.nmjl.mapper.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.clientrelated.ClientSaleConfig;
import com.hxdi.nmjl.condition.clientrelated.ClientSaleConfigCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户销售配置数据访问接口
 */
public interface ClientSaleConfigMapper extends SuperMapper<ClientSaleConfig> {
    @DataPermission
    List<ClientSaleConfig> getList(@Param("condition") ClientSaleConfigCondition condition);
    @DataPermission
    Page<ClientSaleConfig> getPages(@Param("condition") ClientSaleConfigCondition condition, @Param("page") Page<ClientSaleConfig> page);
}
