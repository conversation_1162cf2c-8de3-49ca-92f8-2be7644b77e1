package com.hxdi.nmjl.cache;

import com.hxdi.nmjl.condition.base.*;
import com.hxdi.nmjl.domain.base.*;
import com.hxdi.nmjl.service.base.*;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 缓存预热服务
 * 系统启动时预加载基础数据到缓存
 */
@Component
@Slf4j
public class CachePreloadRunner implements CommandLineRunner {

    @Resource
    private CatalogService catalogService;
    
    @Resource
    private ClassificationService classificationService;
    
    @Resource
    private OrganizationService organizationService;
    
    @Resource
    private StoreHouseService storeHouseService;
    
    @Resource
    private StoreLocationService storeLocationService;

    @Resource
    private ClientInfoService clientInfoService;
    
    @Resource
    private CacheManager cacheManager;

    // 自定义线程池，避免使用默认的ForkJoinPool
    private final ExecutorService cachePreloadExecutor = new ThreadPoolExecutor(
            6, // 核心线程数
            10, // 最大线程数
            60L, TimeUnit.SECONDS, // 空闲线程存活时间
            new LinkedBlockingQueue<>(100), // 任务队列
            r -> {
                Thread t = new Thread(r, "cache-preload-" + System.currentTimeMillis());
                t.setDaemon(true);
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者执行
    );

    // 批量操作大小
    private static final int BATCH_SIZE = 100;
    
    @Override
    public void run(String... args) {

        try {
            log.info("应用启动，开始执行缓存预热...");

            // 清除所有旧的基础数据缓存
            cleanedAll();

            // 基础数据预加载
            preloadAll();

            log.info("缓存预热完成，应用准备就绪");
        } catch (Exception e) {
            log.error("缓存预热过程中发生异常，应用可能无法正常工作", e);
            // 不抛出异常，允许应用继续启动，避免因缓存问题导致整个应用无法启动
        }
    }



    /**
     * 启动时清除所有旧的缓存数据
     */
    private void cleanedAll() {
        log.info("旧缓存数据清除中...");
        long startTime = System.currentTimeMillis();
        int clearedCacheCount = 0;

        try {
            for (String cacheName : cacheManager.getCacheNames()) {
                try {
                    Cache cache = cacheManager.getCache(cacheName);
                    if (cache != null) {
                        cache.clear();
                        clearedCacheCount++;
                        log.debug("已清除缓存: {}", cacheName);
                    }
                } catch (Exception e) {
                    log.warn("清除缓存{}时发生异常: {}", cacheName, e.getMessage());
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("旧缓存数据清除完成，共清除{}个缓存，耗时: {}ms", clearedCacheCount, duration);
        } catch (Exception e) {
            log.error("清除缓存过程中发生异常", e);
            throw e; // 重新抛出异常，让上层方法处理
        }
    }

    /**
     * 预加载基础数据
     * 支持手动触发预加载
     */
    public void preloadAll() {
        log.info("开始预热基础数据缓存...");

        long startTime = System.currentTimeMillis();

        // 使用自定义线程池执行异步任务
        CompletableFuture<Void> catalogFuture = CompletableFuture.runAsync(this::loadCacheDataForCatalog, cachePreloadExecutor);
        CompletableFuture<Void> classificationFuture = CompletableFuture.runAsync(this::loadCacheDataForClassification, cachePreloadExecutor);
        CompletableFuture<Void> organizationFuture = CompletableFuture.runAsync(this::loadCacheDataForOrganization, cachePreloadExecutor);
        CompletableFuture<Void> storeHouseFuture = CompletableFuture.runAsync(this::loadCacheDataForStoreHouse, cachePreloadExecutor);
        CompletableFuture<Void> storeLocationFuture = CompletableFuture.runAsync(this::loadCacheDataForStoreLocation, cachePreloadExecutor);
        CompletableFuture<Void> clientInfoFuture = CompletableFuture.runAsync(this::loadCacheDataForClientInfo, cachePreloadExecutor);

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                catalogFuture, classificationFuture, organizationFuture, storeHouseFuture, storeLocationFuture, clientInfoFuture
        );

        try {
            // 等待所有任务完成，设置超时时间
            allFutures.get(30, TimeUnit.SECONDS);
            log.info("缓存预热完毕! 耗时：{}秒", (System.currentTimeMillis() - startTime) / 1000.0);
        } catch (TimeoutException e) {
            log.error("缓存预热超时", e);
        } catch (Exception e) {
            log.error("缓存预热发生异常", e);
        }
    }

    /**
     * 手动触发缓存加载
     *
     * @param type 缓存类型
     */
    public void LoadCache(String type){
        log.info("异步加载缓存开始, 类型: {}", type);
        long startTime = System.currentTimeMillis();
        
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                switch (type){
                    case RedisKeys.Prefix.CATALOG:
                        loadCacheDataForCatalog();
                        break;
                    case RedisKeys.Prefix.CLASSIFICATION:
                        loadCacheDataForClassification();
                        break;
                    case RedisKeys.Prefix.ORGANIZATION:
                        loadCacheDataForOrganization();
                        break;
                    case RedisKeys.Prefix.STORE_HOUSE:
                        loadCacheDataForStoreHouse();
                        break;
                    case RedisKeys.Prefix.STORE_LOCATION:
                        loadCacheDataForStoreLocation();
                        break;
                    case RedisKeys.Prefix.CLIENT_INFO:
                        loadCacheDataForClientInfo();
                        break;
                    default:
                        log.warn("不支持的缓存类型刷新: {}", type);
                        break;
                }
            } catch (Exception e) {
                log.error("异步加载缓存失败, 类型: {}", type, e);
                throw e;
            }
        }, cachePreloadExecutor);

        try {
            // 设置超时时间，避免无限等待
            future.get(30, TimeUnit.SECONDS);
            log.info("缓存加载完成, 类型: {}, 耗时: {}秒", type, (System.currentTimeMillis() - startTime) / 1000.0);
        } catch (TimeoutException e) {
            log.error("缓存加载超时, 类型: {}", type, e);
        } catch (Exception e) {
            log.error("缓存加载异常, 类型: {}", type, e);
        }
    }

    /**
     * 预热品种目录数据
     */
    private void loadCacheDataForCatalog() {
        loadCacheDataWithBatch("品种目录", RedisKeys.CATALOG.key(),
                () -> catalogService.lists(new CatalogCondition()).stream()
                .collect(Collectors.toMap(Catalog::getId, item->item)));
    }

    /**
     * 预热粮油品类数据
     */
    private void loadCacheDataForClassification() {
        loadCacheDataWithBatch("粮油品类", RedisKeys.CLASSIFICATION.key(),
                () -> classificationService.lists(new ClassificationCondition()).stream()
                .collect(Collectors.toMap(Classification::getId, item->item)));
    }

    /**
     * 预热仓房/油罐数据
     */
    private void loadCacheDataForStoreHouse() {
        loadCacheDataWithBatch("仓房/油罐", RedisKeys.STORE_HOUSE.key(), () -> {
            StorehouseCondition condition = new StorehouseCondition();
            return storeHouseService.lists(condition).stream()
                    .collect(Collectors.toMap(StoreHouse::getId, item->item));
        });
    }

    /**
     * 预热货位数据
     */
    private void loadCacheDataForStoreLocation() {
        loadCacheDataWithBatch("货位", RedisKeys.STORE_LOCATION.key(), () -> {
            StoreLocationCondition condition = new StoreLocationCondition();
            return storeLocationService.lists(condition).stream()
                    .collect(Collectors.toMap(StoreLocation::getId, item->item));
        });
    }

    /**
     * 预热组织机构数据
     */
    private void loadCacheDataForOrganization() {
        loadCacheDataWithBatch("组织机构", RedisKeys.ORGANIZATION.key(), () -> {
            OrganizationCondition condition = new OrganizationCondition();
            return organizationService.lists(condition).stream()
                    .collect(Collectors.toMap(Organization::getId, item->item));
        });
    }

    private void loadCacheDataForClientInfo() {
        loadCacheDataWithBatch("客户信息", RedisKeys.CLIENT_INFO.key(), () -> clientInfoService.lists().stream()
                .collect(Collectors.toMap(ClientInfo::getId, item->item)));
    }

    /**
     * 通用的批量缓存加载方法
     *
     * @param dataType 数据类型名称，用于日志
     * @param cacheKey 缓存键
     * @param dataSupplier 数据提供者，返回Map<String, Object>
     */
    private void loadCacheDataWithBatch(String dataType, String cacheKey,
                                        Supplier<Map<String, Object>> dataSupplier) {
        log.info("预热{}数据中...", dataType);
        long startTime = System.currentTimeMillis();

        try {
            // 获取数据
            Map<String, Object> dataMap = dataSupplier.get();

            if (dataMap == null || dataMap.isEmpty()) {
                log.warn("{}数据为空，跳过预热", dataType);
                return;
            }

            Cache cache = cacheManager.getCache(cacheKey);
            if (cache == null) {
                log.error("缓存{}不存在", cacheKey);
                return;
            }

            // 批量写入缓存
            int totalSize = dataMap.size();
            int processedCount = 0;

            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                try {
                    cache.put(entry.getKey(), entry.getValue());
                    processedCount++;

                    // 每处理BATCH_SIZE个记录输出一次进度
                    if (processedCount % BATCH_SIZE == 0) {
                        log.debug("{}数据预热进度: {}/{}", dataType, processedCount, totalSize);
                    }
                } catch (Exception e) {
                    log.warn("{}数据预热失败，key: {}, error: {}", dataType, entry.getKey(), e.getMessage());
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("{}数据预热完成，数据大小: {}，耗时: {}ms", dataType, processedCount, duration);

        } catch (Exception e) {
            log.error("预热{}数据失败", dataType, e);
            throw new RuntimeException("预热" + dataType + "数据失败", e);
        }
    }
}