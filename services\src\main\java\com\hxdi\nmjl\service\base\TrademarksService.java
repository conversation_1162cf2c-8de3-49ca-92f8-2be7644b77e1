package com.hxdi.nmjl.service.base;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.Trademarks;
import com.hxdi.nmjl.condition.base.TrademarkCondition;

import java.util.List;

public interface TrademarksService extends IBaseService<Trademarks> {

    /**
     * 保存更新
     * @param trademark
     */
    void saveOrUpdates(Trademarks trademark);

    /**
     * 删除
     * @param brandId
     */
    void remove(String brandId);

    /**
     * 分页
     * @param condition
     * @return
     */
    Page<Trademarks> pages(TrademarkCondition condition);

    /**
     * 列表
     * @param condition
     * @return
     */
    List<Trademarks> lists(TrademarkCondition condition);
}
