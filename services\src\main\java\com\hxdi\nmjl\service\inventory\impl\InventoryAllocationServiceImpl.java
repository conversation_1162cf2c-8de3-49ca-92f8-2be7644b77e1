package com.hxdi.nmjl.service.inventory.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inventory.InventoryAllocationCondition;
import com.hxdi.nmjl.domain.inventory.InventoryAllocation;
import com.hxdi.nmjl.domain.inventory.InventoryAllocationDetail;
import com.hxdi.nmjl.domain.plan.ProductionPackageApply;
import com.hxdi.nmjl.enums.TaskStatus;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inventory.InventoryAllocationMapper;
import com.hxdi.nmjl.service.inventory.InventoryAllocationDetailService;
import com.hxdi.nmjl.service.inventory.InventoryAllocationService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【B_INVENTORY_ALLOCATION(库存调配管理)】的数据库操作Service实现
 * @createDate 2025-07-31 09:45:14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryAllocationServiceImpl extends BaseServiceImpl<InventoryAllocationMapper, InventoryAllocation>
        implements InventoryAllocationService {
    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private InventoryAllocationDetailService inventoryAllocationDetailService;


    @Override
    public void createOrUpdate(InventoryAllocation inventoryAllocation) {
        if (CommonUtils.isNotEmpty(inventoryAllocation.getId())) {
            if (!Objects.equals(TaskStatus.UNEXECUTED.getCode(), inventoryAllocation.getAllocateState())) {
                throw new BaseException("无法更新已经开始作业的调度计划！");
            }
            baseMapper.updateById(inventoryAllocation);
            if (inventoryAllocation.getDetailList() != null) {
                //批量删除调度计划详情
                inventoryAllocationDetailService.removeV1(inventoryAllocation.getId());
                inventoryAllocation.getDetailList().forEach(detail -> detail.setAllocateId(inventoryAllocation.getId()));
                //重新插入新的调度计划详情
                inventoryAllocationDetailService.saveBatch(inventoryAllocation.getDetailList());
            }
        } else {
            // 生成调度计划编号
            BusinessCodeParams params = new BusinessCodeParams();
            params.setCode("INVENTORY_ALLOCATION_CODE");
            params.setDt(DataType.STRING);
            BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
            inventoryAllocation.setAllocateCode((String) businessCode.getValue());
            //新增调度计划
            inventoryAllocation.setAllocateState(TaskStatus.UNEXECUTED.getCode());
            this.save(inventoryAllocation);
            //新增调度计划详情
            inventoryAllocation.getDetailList().forEach(detail -> detail.setAllocateId(inventoryAllocation.getId()));
            inventoryAllocationDetailService.saveBatch(inventoryAllocation.getDetailList());
        }
    }

    @Override
    public void removeById(String id) {
        InventoryAllocation task = baseMapper.selectById(id);
        if (CommonUtils.isNotEmpty(task)) {
            if (!Objects.equals(TaskStatus.UNEXECUTED.getCode(), task.getAllocateState())) {
                throw new BaseException("无法删除已经开始作业的调度计划！");
            }
            //删除调度计划(逻辑删除)
            task.setEnabled(StrPool.State.DISABLE);
            baseMapper.updateById(task);
        }
    }

    @Override
    public InventoryAllocation getMore(String id) {
        InventoryAllocation task;
        task = baseMapper.selectById(id);
        if (CommonUtils.isEmpty(task)) {
            throw new BaseException("查询为空对象,查询错误!");
        }
        //拿到详情列表
        List<InventoryAllocationDetail> inventoryAllocationDetailList = inventoryAllocationDetailService.getListV1(id);
        task.setDetailList(inventoryAllocationDetailList);
        return task;
    }


    @Override
    @DataPermission
    public List<InventoryAllocation> listV1(InventoryAllocationCondition condition) {
        List<InventoryAllocation> inventoryAllocations = baseMapper.listV1(condition);
        if(!inventoryAllocations.isEmpty()){
            // 3.1 提取主表ID
            List<String> ids = inventoryAllocations.stream()
                    .map(InventoryAllocation::getId)
                    .collect(Collectors.toList());
            // 3.2 批量查询详情（使用Stream API简化分组逻辑）
            Map<String, List<InventoryAllocationDetail>> detailMap = inventoryAllocationDetailService.getBatchIds(ids)
                    .stream()
                    .collect(Collectors.groupingBy(InventoryAllocationDetail::getAllocateId));

            // 3.3 关联详情到主记录（使用getOrDefault避免空指针）
            inventoryAllocations.forEach(record -> {
                List<InventoryAllocationDetail> details = detailMap.getOrDefault(record.getId(), Collections.emptyList());
                record.setDetailList(details);
            });
        }
        return inventoryAllocations;
    }

    @Override
    public Page<InventoryAllocation> PageV1(InventoryAllocationCondition condition) {
        // 1. 创建分页对象
        Page<InventoryAllocation> page = condition.newPage();
        // 2. 执行主表分页查询
        return baseMapper.PageV1(condition, page);
    }

    @Override
    public void removeV1(String id) {
        //逻辑删除
        this.update(Wrappers.<InventoryAllocation>lambdaUpdate()
                .eq(InventoryAllocation::getId, id)
                .set(InventoryAllocation::getEnabled, 0));
    }

    @Override
    public void approveV1(String id, Integer approveStatus, String opinion) {
        InventoryAllocation inventoryAllocation = this.getById(id);
        if (inventoryAllocation == null) {
            throw new BaseException("包装申请不存在");
        }
        // 校验状态：已审核的不能重复审核
        if (inventoryAllocation.getApproveStatus() != 0) {
            throw new BaseException("该申请已审核，无法重复操作");
        }
        inventoryAllocation.setApproveStatus(approveStatus);
        inventoryAllocation.setApproveOpinion(opinion);
        inventoryAllocation.setApprover(SecurityHelper.obtainUser().getNickName());
        inventoryAllocation.setApproveTime(new Date());
        this.updateById(inventoryAllocation);
    }

    @Override
    public void submitV1(String id) {
        InventoryAllocation productionOrder = this.getById(id);
        if (productionOrder == null) {
            throw new BaseException("包装申请不存在");
        }
        if (productionOrder.getApproveStatus() != null && productionOrder.getApproveStatus() != 2) {
            throw new BaseException("该申请已提交，无法重复操作");
        }
        productionOrder.setApproveStatus(0);
        productionOrder.setApproveOpinion("");
        productionOrder.setApprover("");
        this.updateById(productionOrder);
    }
}




