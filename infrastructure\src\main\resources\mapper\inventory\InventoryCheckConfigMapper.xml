<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryCheckConfigMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryCheckConfig">
        <!--@Table B_INVENTORY_CHECK_CONFIG-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName"/>
        <result column="PERIODS" jdbcType="INTEGER" property="periods"/>
        <result column="TIME_UNIT" jdbcType="INTEGER" property="timeUnit"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="ENABLED" jdbcType="INTEGER" property="enabled"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_ID" jdbcType="VARCHAR" property="createId"/>
        <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId"/>
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        "NAME",
        STORE_ID,
        STORE_NAME,
        PERIODS,
        TIME_UNIT,
        START_TIME,
        ENABLED,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>
    <select id="selectPageV1" resultType="com.hxdi.nmjl.domain.inventory.InventoryCheckConfig">
        SELECT <include refid="Base_Column_List"/> FROM B_INVENTORY_CHECK_CONFIG
        <where>
            enabled != 7
            <if test="@plugins.OGNL@isNotEmpty(condition.name)">
                AND "NAME" LIKE CONCAT('%', #{condition.name}, '%')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 列表查询 -->
    <select id="selectListV1" resultType="com.hxdi.nmjl.domain.inventory.InventoryCheckConfig">
        SELECT <include refid="Base_Column_List"/> FROM B_INVENTORY_CHECK_CONFIG
        <where>
            enabled != 7
            <if test="@plugins.OGNL@isNotEmpty(condition.name)">
                AND "NAME" LIKE CONCAT('%', #{condition.name}, '%')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>


</mapper>

