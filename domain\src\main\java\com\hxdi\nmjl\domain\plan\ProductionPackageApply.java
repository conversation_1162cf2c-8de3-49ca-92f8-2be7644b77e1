package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@ApiModel(description = "包装申请")
@TableName(value = "B_PRODUCTION_PACKAGE_APPLY")
public class ProductionPackageApply extends Entity<ProductionPackageApply> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 订单ID
     */
    @TableField(value = "ORDER_ID")
    @ApiModelProperty(value = "订单ID")
    private String orderId;

    /**
     * 订单编号
     */
    @TableField(value = "ORDER_CODE")
    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    /**
     * 申请编号
     */
    @TableField(value = "APPLY_CODE")
    @ApiModelProperty(value = "申请编号")
    private String applyCode;

    /**
     * 申请企业
     */
    @TableField(value = "APPLY_COMPANY_NAME")
    @ApiModelProperty(value = "申请企业")
    private String applyCompanyName;

    /**
     * 申请日期
     */
    @TableField(value = "APPLY_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请日期")
    private Date applyDate;

    /**
     * 申请人
     */
    @TableField(value = "APPLICANT")
    @ApiModelProperty(value = "申请人")
    private String applicant;

    /**
     * 申请人联系电话
     */
    @TableField(value = "APPLICANT_TEL")
    @ApiModelProperty(value = "申请人联系电话")
    private String applicantTel;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value = "品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value = "品种名称")
    private String catalogName;

    /**
     * 净含量
     */
    @TableField(value = "NET_WEIGHT")
    @ApiModelProperty(value = "净含量")
    private String netWeight;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    @ApiModelProperty(value = "规格")
    private String specification;

    /**
     * 质量等级
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value = "质量等级")
    private String grade;

    /**
     * 保质期/天
     */
    @TableField(value = "EXPIRATION_DATE")
    @ApiModelProperty(value = "保质期/天")
    private Integer expirationDate;

    /**
     * 产地
     */
    @TableField(value = "ORIGIN")
    @ApiModelProperty(value = "产地")
    private String origin;

    /**
     * 配料表
     */
    @TableField(value = "INGREDIENTS")
    @ApiModelProperty(value = "配料表")
    private String ingredients;

    /**
     * 食品生产许可证号
     */
    @TableField(value = "FOOD_LICENSE_NO")
    @ApiModelProperty(value = "食品生产许可证号")
    private String foodLicenseNo;

    /**
     * 产品标准
     */
    @TableField(value = "PRODUCTION_DESC")
    @ApiModelProperty(value = "产品标准")
    private String productionDesc;

    /**
     * 存储方法
     */
    @TableField(value = "STORAGE_METHOD")
    @ApiModelProperty(value = "存储方法")
    private String storageMethod;

    /**
     * 使用方法
     */
    @TableField(value = "USE_METHOD")
    @ApiModelProperty(value = "使用方法")
    private String useMethod;

    /**
     * 生产地址
     */
    @TableField(value = "PRODUCT_ADDR")
    @ApiModelProperty(value = "生产地址")
    private String productAddr;

    /**
     * 生产厂家
     */
    @TableField(value = "FACTORY_NAME")
    @ApiModelProperty(value = "生产厂家")
    private String factoryName;

    /**
     * 生产厂家电话
     */
    @TableField(value = "FACTORY_TEL")
    @ApiModelProperty(value = "生产厂家电话")
    private String factoryTel;

    /**
     * 网址
     */
    @TableField(value = "FACTORY_SITE_URL")
    @ApiModelProperty(value = "网址")
    private String factorySiteUrl;

    /**
     * 包装样式描述
     */
    @TableField(value = "PACKAGE_DESC")
    @ApiModelProperty(value = "包装样式描述")
    private String packageDesc;

    /**
     * 材质工艺要求
     */
    @TableField(value = "PACKAGE_TECHNICS")
    @ApiModelProperty(value = "材质工艺要求")
    private String packageTechnics;

    /**
     * 是否采用专用LOGO
     */
    @TableField(value = "ISUSE_LOGO")
    @ApiModelProperty(value = "是否采用专用LOGO")
    private Integer isuseLogo;

    /**
     * 是否包含质量查询二维码
     */
    @TableField(value = "ISUSE_QUALITY_QRCODE")
    @ApiModelProperty(value = "是否包含质量查询二维码")
    private Integer isuseQualityQrcode;

    /**
     * 是否包含专用标签
     */
    @TableField(value = "ISUSE_TAG")
    @ApiModelProperty(value = "是否包含专用标签")
    private Integer isuseTag;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @TableField(value = "APPROVE_STATUS")
    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value = "审批意见")
    private String approveOpinion;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    /**
     * 产品检验报告
     */
    @TableField(value = "PRODUCT_QUALITY_REPORT")
    @ApiModelProperty(value = "产品检验报告")
    private String productQualityReport;

    /**
     * 营养成分检验报告
     */
    @TableField(value = "PRODUCT_COMPOSITION_REPORT")
    @ApiModelProperty(value = "营养成分检验报告")
    private String productCompositionReport;

    /**
     * 其他附件
     */
    @TableField(value = "ATTACHEMENTS")
    @ApiModelProperty(value = "其他附件")
    private String attachements;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}