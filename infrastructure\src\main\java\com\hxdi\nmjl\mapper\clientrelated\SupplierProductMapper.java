package com.hxdi.nmjl.mapper.clientrelated;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.clientrelated.SupplierProduct;
import com.hxdi.nmjl.condition.clientrelated.SupplierProductCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商产品管理数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/21
 */
@Mapper
public interface SupplierProductMapper extends SuperMapper<SupplierProduct> {

    void changeState(@Param("id") String id, @Param("enabled") Integer enabled);

    Page<SupplierProduct> selectPageV1(Page<SupplierProduct> page, @Param("condition") SupplierProductCondition condition);

    List<SupplierProduct> selectListV1(@Param("condition") SupplierProductCondition condition);
}
