package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 品牌传播活动执行记录
 */
@Getter
@Setter
@TableName(value = "B_BRAND_ACTIVITY_PERFORM")
public class BrandActivityPerform implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 活动id
     */
    @TableField(value = "ACTIVITY_ID")
    private String activityId;

    /**
     * 活动步骤名称
     */
    @TableField(value = "NAME")
    private String name;

    /**
     * 费用
     */
    @TableField(value = "AMOUNT")
    private BigDecimal amount;

    /**
     * 参与人数
     */
    @TableField(value = "DATA")
    private Integer data;

    /**
     * 备注
     */
    @TableField(value = "MEMO")
    private String memo;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "PERFORM_TIME")
    private Date performTime;
}