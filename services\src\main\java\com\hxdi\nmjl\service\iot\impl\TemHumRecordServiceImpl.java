package com.hxdi.nmjl.service.iot.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.iot.TemHumRecordCondition;
import com.hxdi.nmjl.domain.iot.DeviceInfo;
import com.hxdi.nmjl.domain.iot.TemHumRecord;
import com.hxdi.nmjl.mapper.iot.TemHumRecordMapper;
import com.hxdi.nmjl.service.iot.DeviceInfoService;
import com.hxdi.nmjl.service.iot.TemHumRecordService;
import com.hxdi.nmjl.service.iot.TemHumWarningConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TemHumRecordServiceImpl extends BaseServiceImpl<TemHumRecordMapper, TemHumRecord> implements TemHumRecordService {

    @Resource
    DeviceInfoService deviceInfoService;

    @Resource
    TemHumWarningConfigService configService;

    @Override
    public void insertBatch(List<TemHumRecord> record) {
        //批量插入时，每一批次生成一个messageId，用于后续查询等操作
        String messageId = IdWorker.getIdStr();

        // 过滤并设置设备信息
        List<TemHumRecord> validRecords = record.stream()
                .filter(rec -> !CommonUtils.isEmpty(rec.getSerial()))
                .map(rec -> {
                    String serial = rec.getSerial();
                    //根据设备Id从设备信息接口中获取库点、仓房等信息
                    DeviceInfo device = deviceInfoService.findBySerial(serial);
                    //忽略没有对应设备的记录
                    if (CommonUtils.isEmpty(device)) {
                        log.warn("设备序列号 {} 未找到对应的设备信息", serial);
                        return null;
                    }
                    rec.setStoreId(device.getStoreId());
                    rec.setStId(device.getStId());
                    rec.setStoreName(device.getStoreName());
                    rec.setStName(device.getStName());
                    rec.setDeviceName(device.getDeviceName());
                    rec.setDeviceState(device.getDeviceState());
                    rec.setMessageId(messageId);
                    rec.setDataHierarchyId(device.getDataHierarchyId());
                    rec.setArea(device.getArea());
                    rec.setErrIs(0); //先取0，后面根据返回数据判断
                    return rec;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (validRecords.isEmpty()) {
            log.warn("批次 {} 中没有有效的温湿度记录", messageId);
            return;
        }

        //按仓房分组生成预警信息
        Map<String, List<TemHumRecord>> recordsByStId = validRecords.stream()
                .filter(rec -> !CommonUtils.isEmpty(rec.getStId()))
                .collect(Collectors.groupingBy(TemHumRecord::getStId));

        // 为每个仓房单独生成预警信息
        recordsByStId.forEach((stId, recordList) -> {
            try {
                configService.generateWarningMsg(stId, recordList);
            } catch (Exception e) {
                log.error("仓房 {} 生成预警信息失败: {}", stId, e.getMessage(), e);
            }
        });

        //批量插入检测结果
        this.saveBatch(validRecords);
        log.info("成功插入 {} 条温湿度记录，涉及 {} 个仓房", validRecords.size(), recordsByStId.size());
    }

    @Override
    public List<TemHumRecord> getList(TemHumRecordCondition condition) {
        return baseMapper.getList(condition);
    }

    @Override
    public Page<TemHumRecord> getPage(TemHumRecordCondition condition) {
        Page<TemHumRecord> page = condition.newPage();
        return baseMapper.getPage(condition, page);
    }
}
