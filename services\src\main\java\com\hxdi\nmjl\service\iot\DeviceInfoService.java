package com.hxdi.nmjl.service.iot;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.iot.DeviceInfo;
import com.hxdi.nmjl.condition.iot.DeviceInfoCondition;

import java.io.Serializable;
import java.util.List;

public interface DeviceInfoService extends IBaseService<DeviceInfo> {
    /**
     * 根据设备唯一序列号查询设备信息
     * @param serial
     * @return
     */
    DeviceInfo findBySerial(String serial);

    /**
     * 新增或更新设备信息
     * @param deviceInfo
     */
    void insertOrUpdate(DeviceInfo deviceInfo);

    /**
     * 修改设备状态（字典SBZT）
     * @param id
     * @param state
     */
    void changeDeviceState(Serializable id,String serial, Integer state);

    /**
     * 根据查询条件获取设备信息列表（带权限用户控制）
     * @return
     */
    List<DeviceInfo> getList(DeviceInfoCondition condition);

    /**
     * 根据查询条件获取设备信息分页列表（带权限用户控制）
     * @return
     */
    Page<DeviceInfo> getPage(DeviceInfoCondition condition);
}
