package com.hxdi.nmjl.controller.plan;

import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.plan.OrganPersonCondition;
import com.hxdi.nmjl.domain.plan.OrganPerson;
import com.hxdi.nmjl.service.plan.OrganPersonService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <机构人员管理>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/12 16:10
 */
@Api(tags = "机构人员管理")
@RestController
@RequestMapping("/person")
public class OrganPersonController extends CommonApiController<OrganPersonService, OrganPerson, OrganPersonCondition> {


}
