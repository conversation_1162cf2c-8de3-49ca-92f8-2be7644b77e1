<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hxdi.nmjl.mapper.mobilization.MobilizedContractDetailMapper">

    <sql id="Base_Column_List">
        ID, CONTRACT_ID, CLASSIFICATION_ID, CLASSIFICATION_NAME, CATALOG_ID, CATALOG_NAME,
    GRADE, SPECIFICATION, PRICE, ORIGIN, UNIT, QTY, TENANT_ID, DATA_HIERARCHY_ID
    </sql>

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.mobilization.MobilizedContractDetail">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="CONTRACT_ID" jdbcType="VARCHAR" property="contractId" />
        <result column="CLASSIFICATION_ID" jdbcType="VARCHAR" property="classificationId" />
        <result column="CLASSIFICATION_NAME" jdbcType="VARCHAR" property="classificationName" />
        <result column="CATALOG_ID" jdbcType="VARCHAR" property="catalogId" />
        <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName" />
        <result column="GRADE" jdbcType="VARCHAR" property="grade" />
        <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification" />
        <result column="PRICE" jdbcType="DECIMAL" property="price" />
        <result column="ORIGIN" jdbcType="VARCHAR" property="origin" />
        <result column="UNIT" jdbcType="VARCHAR" property="unit" />
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
        <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    </resultMap>
</mapper>