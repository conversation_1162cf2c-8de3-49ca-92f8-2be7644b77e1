package com.hxdi.nmjl.mapper.storeproduction;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.storeproduction.StoreProductionPlan;
import com.hxdi.nmjl.condition.storeproduction.StoreProductionPlanCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 门店生产计划Mapper接口
 */
public interface StoreProductionPlanMapper extends SuperMapper<StoreProductionPlan> {

    Page<StoreProductionPlan> selectPageV1(Page<StoreProductionPlan> page, @Param("condition") StoreProductionPlanCondition condition);

    List<StoreProductionPlan> selectListV1(@Param("condition") StoreProductionPlanCondition condition);
}