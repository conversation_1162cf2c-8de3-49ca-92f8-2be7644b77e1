package com.hxdi.nmjl.controller.plan;

import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.plan.InspectionPlanCondition;
import com.hxdi.nmjl.domain.plan.InspectionPlan;
import com.hxdi.nmjl.service.plan.InspectionPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <监督检查计划管理接口>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "监督检查计划管理")
@RestController
@RequestMapping("/inspectionPlan")
public class InspectionPlanController extends CommonApiController<InspectionPlanService, InspectionPlan, InspectionPlanCondition> {


    @ApiOperation("取消")
    @PostMapping({"/cancel"})
    @Log(value = "取消",saveReqParam = true)
    public ResultBody cancel(String id) {
        this.bizService.cancel(id);
        return ResultBody.OK();
    }
}
