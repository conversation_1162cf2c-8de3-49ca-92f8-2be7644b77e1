package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 客户信息表（注意：此表中的权限字段未生效，权限查询请走【客户——组织关系表】判断）
 */
@Getter
@Setter
@TableName(value = "B_CLIENT_INFO")
public class ClientInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 客户名称
     */
    @TableField(value = "NAME")
    private String name;

    /**
     * 客户类型：1-供应商，2-质检机构，9-其他
     */
    @TableField(value = "CLIENT_TYPE")
    @NotBlank(message = "客户类型不能为空")
    private String clientType;

    /**
     * 企业性质
     */
    @TableField(value = "COMPANY_TYPE")
    private String companyType;

    /**
     * 来源：1-手动新增，2-同步
     */
    @TableField(value = "ORIGIN")
    private Integer origin;

    /**
     * 关联ID
     */
    @TableField(value = "REF_ID")
    private String refId;

    /**
     * 所在省id
     */
    @TableField(value = "PROVINCE_ID")
    private String provinceId;

    /**
     * 所在省
     */
    @TableField(value = "PROVINCE")
    private String province;

    /**
     * 所在市id
     */
    @TableField(value = "CITY_ID")
    private String cityId;

    /**
     * 所在市
     */
    @TableField(value = "CITY")
    private String city;

    /**
     * 所在县区id
     */
    @TableField(value = "COUNTY_ID")
    private String countyId;

    /**
     * 所在县区
     */
    @TableField(value = "COUNTY")
    private String county;

    /**
     * 详细地址
     */
    @TableField(value = "DETAIL_ADDR")
    private String detailAddr;

    /**
     * 统一社会信用代码
     */
    @TableField(value = "CREDIT_CODE")
    private String creditCode;

    /**
     * 登记注册类型
     */
    @TableField(value = "REGISTER_TYPE")
    private String registerType;

    /**
     * 工商登记注册号
     */
    @TableField(value = "BUSINESS_NO")
    private String businessNo;

    /**
     * 法定代表人
     */
    @TableField(value = "LEGAL")
    private String legal;

    /**
     * 联系电话
     */
    @TableField(value = "PHONE")
    private String phone;

    /**
     * 传真
     */
    @TableField(value = "FAX")
    private String fax;

    /**
     * 电子邮箱
     */
    @TableField(value = "MAIL")
    private String mail;

    /**
     * 邮政编码
     */
    @TableField(value = "POST_CODE")
    private String postCode;

    /**
     * 开户银行
     */
    @TableField(value = "BANK")
    private String bank;

    /**
     * 银行账号
     */
    @TableField(value = "ACCOUNT")
    private String account;

    /**
     * 银行信用等级
     */
    @TableField(value = "BANK_CREDIT_LEVEL")
    private String bankCreditLevel;

    /**
     * 固定资产
     */
    @TableField(value = "FIXED_ASSETS")
    private String fixedAssets;

    /**
     * 注册资本(万元)
     */
    @TableField(value = "REGISTERED_CAPITAL")
    private BigDecimal registeredCapital;

    /**
     * 企业从业人数
     */
    @TableField(value = "EMPLOYMENTS")
    private Integer employments;

    /**
     * 描述
     */
    @TableField(value = "REMARKS")
    private String remarks;

    /**
     * 评级
     */
    @TableField(value = "GRADE")
    private Integer grade;

    /**
     * 状态（0禁用，1有效，7删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id（不填充权限字段，改为关系表维护）
     */
    @TableField(value = "CREATE_ID",fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id（不填充权限字段，改为关系表维护）
     */
    @TableField(value = "UPDATE_ID",fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id（不填充权限字段，改为关系表维护）
     */
    @TableField(value = "TENANT_ID")
    private String tenantId;

    /**
     * 组织（不填充权限字段，改为关系表维护）
     */
    @TableField(value = "DATA_HIERARCHY_ID",fill = FieldFill.INSERT)
    private String dataHierarchyId;
}
