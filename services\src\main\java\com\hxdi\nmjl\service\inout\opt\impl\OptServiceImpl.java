package com.hxdi.nmjl.service.inout.opt.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inout.OptCondition;
import com.hxdi.nmjl.domain.inout.opt.OptData;
import com.hxdi.nmjl.domain.inout.opt.OptInfo;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inout.opt.OptInfoMapper;
import com.hxdi.nmjl.service.inout.opt.OptDataService;
import com.hxdi.nmjl.service.inout.opt.OptService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class OptServiceImpl extends BaseServiceImpl<OptInfoMapper, OptInfo> implements OptService {


    @Resource
    private OptDataService optDataService;

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public void create(OptInfo optInfo) {
        // 生成业务编号
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("OPT_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        optInfo.setOptCode((String) businessCode.getValue());

        baseMapper.insert(optInfo);
        if (CommonUtils.isNotEmpty(optInfo.getOptDataList())) {
            optInfo.getOptDataList().forEach(optData -> {
                optData.setOptId(optInfo.getId());
                optDataService.save(optData);
            });
        }
    }

    @Override
    public void updating(OptInfo optInfo) {
        baseMapper.updateById(optInfo);
        optDataService.remove(new LambdaQueryWrapper<OptData>().eq(OptData::getOptId, optInfo.getId()));
        if (CommonUtils.isNotEmpty(optInfo.getOptDataList())) {
            optInfo.getOptDataList().forEach(optData -> {
                optData.setOptId(optInfo.getId());
                optDataService.save(optData);
            });
        }

    }

    @Override
    public OptInfo detail(String optId) {
        OptInfo optInfo = baseMapper.selectById(optId);
        optInfo.setOptDataList(optDataService.list(new LambdaQueryWrapper<OptData>().eq(OptData::getOptId, optId)));
        return optInfo;
    }

    @Override
    public void delete(String optId) {
        baseMapper.deleteById(optId);
        optDataService.remove(new LambdaQueryWrapper<OptData>().eq(OptData::getOptId, optId));
    }

    @Override
    public Page<OptInfo> getPage(OptCondition optCondition) {
        Page<OptInfo> pages = optCondition.newPage();
        baseMapper.selectPageV1(pages, optCondition);
        List<OptInfo> records = pages.getRecords();
        records.forEach(optInfo -> optInfo.setOptDataList(optDataService.list(new LambdaQueryWrapper<OptData>().eq(OptData::getOptId, optInfo.getId()))));
        pages.setRecords(records);
        return pages;
    }

    @Override
    public List<OptInfo> getList(OptCondition optCondition) {
        List<OptInfo> optInfos = baseMapper.selectListV1(optCondition);
        optInfos.forEach(optInfo -> optInfo.setOptDataList(optDataService.list(new LambdaQueryWrapper<OptData>().eq(OptData::getOptId, optInfo.getId()))));
        return optInfos;
    }
}
