package com.hxdi.nmjl.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.emergency.EmergencyTaskItemCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyTaskItem;
import com.hxdi.nmjl.mapper.emergency.EmergencyTaskItemMapper;
import com.hxdi.nmjl.service.emergency.EmergencyTaskItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 紧急任务项业务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class EmergencyTaskItemServiceImpl extends BaseServiceImpl<EmergencyTaskItemMapper, EmergencyTaskItem> implements EmergencyTaskItemService {

    @Override
    public void create(EmergencyTaskItem taskItem) {
        BaseUserDetails user = SecurityHelper.obtainUser();
        // 设置租户ID和组织信息（从当前登录用户获取）
        taskItem.setTenantId(user.getTenantId());
        taskItem.setDataHierarchyId(user.getOrganId());
        // 保存紧急任务项
        this.save(taskItem);
    }

    @Override
    public void create(EmergencyTaskItem taskItem, String s) {

    }

    @Override
    public void update(EmergencyTaskItem taskItem) {
        // 查询原任务项
        EmergencyTaskItem savedItem = this.getById(taskItem.getId());
        if (savedItem == null) {
            throw new BaseException("紧急任务项不存在");
        }
        // 此处可添加状态校验逻辑（如：只有特定状态允许修改）
        this.updateById(taskItem);
    }

    @Override
    public EmergencyTaskItem getDetail(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Page<EmergencyTaskItem> pages(EmergencyTaskItemCondition condition) {
        Page<EmergencyTaskItem> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        return page;
    }

    @Override
    public List<EmergencyTaskItem> lists(EmergencyTaskItemCondition condition) {
        return baseMapper.selectListV1(condition);
    }


    @Override
    public void removeBatch(String taskId) {
        QueryWrapper<EmergencyTaskItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TASK_ID", taskId);
        this.remove(queryWrapper);
    }

}
