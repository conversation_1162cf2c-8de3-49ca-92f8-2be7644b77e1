package com.hxdi.nmjl.mapper.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.condition.base.CatalogCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CatalogMapper extends SuperMapper<Catalog> {

    /**
     * 分页
     * @param page
     * @param condition
     * @return
     */
    Page<Catalog> selectPageV1(Page<Catalog> page, @Param("condition") CatalogCondition condition);

    /**
     * 列表
     * @param condition
     * @return
     */
    List<Catalog> selectListV1(@Param("condition") CatalogCondition condition);

    /**
     * 查询已存在条目数量
     * @param catalog
     * @return
     */
    Long selectCountV1(@Param("catalog") Catalog catalog);

    /**
     * 逻辑删除
     * @param id
     */
    void softDeleteById(String id);

    /**
     * 根据分类id集合查询商品目录列表
     * @param classificationIds
     * @return
     */
    List<Catalog> selectListByClassIds(@Param("classificationIds") List<String> classificationIds);
}
