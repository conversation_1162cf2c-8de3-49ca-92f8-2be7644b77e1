package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hxdi.nmjl.domain.inventory.InventoryBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InventoryBaseMapper extends BaseMapper<InventoryBase> {

    /**
     * 将当前有效库存更新成历史库存
     * @param
     */
    void updateHistory(@Param("locId") String locId);


}
