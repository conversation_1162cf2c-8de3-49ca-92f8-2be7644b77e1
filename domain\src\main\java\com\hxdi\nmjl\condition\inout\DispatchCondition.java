package com.hxdi.nmjl.condition.inout;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
* @program: nmjl-service
*
* @description: 库存拆分记录查询条件
*
* @author: 王贝强
*
* @create: 2025-04-23 15:16
*/
@Getter
@Setter
@ApiModel(description="库存拆分记录查询条件")
public class DispatchCondition extends QueryCondition {
    @ApiModelProperty(value="开始时间")
    private String startTime;

    @ApiModelProperty(value="结束时间")
    private String endTime;

    @ApiModelProperty(value="执行状态")
    private String status;

    @ApiModelProperty(value="任务单号")
    private String taskCode;
}
