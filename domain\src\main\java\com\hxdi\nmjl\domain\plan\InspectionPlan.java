package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.mybatis.base.entity.BModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 监督检查计划
 */
@ApiModel(description = "监督检查计划")
@Getter
@Setter
@TableName("B_INSPECTION_PLAN")
public class InspectionPlan extends BModel {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @TableField("PLAN_CODE")
    @ApiModelProperty(value = "计划编号")
    private String planCode;

    @TableField("PLAN_NAME")
    @ApiModelProperty(value = "计划名称")
    private String planName;

    @TableField("INSPECT_UNIT_ID")
    @ApiModelProperty(value = "被检查单位ID")
    private String inspectUnitId;

    @TableField("INSPECT_UNIT_NAME")
    @ApiModelProperty(value = "被检查单位")
    private String inspectUnitName;

    @TableField("INSPECT_TYPE")
    @ApiModelProperty(value = "检查类型")
    private String inspectType;

    @TableField("PURPOSE")
    @ApiModelProperty(value = "检查目的")
    private String purpose;

    @TableField("INSPECTORS")
    @ApiModelProperty(value = "检查人员")
    private String inspectors;

    @TableField("START_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @TableField("END_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @TableField("STATE")
    @ApiModelProperty(value = "业务状态：0-未执行，1-执行中，2-已完成，3-已取消")
    private Integer state;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态:1-有效，0-删除")
    private Integer enabled;

    @TableField("ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;



    /*****************************以下非实体字段*****************************/


    @TableField(exist = false)
    private List<InspectionItem> detailList;
}
