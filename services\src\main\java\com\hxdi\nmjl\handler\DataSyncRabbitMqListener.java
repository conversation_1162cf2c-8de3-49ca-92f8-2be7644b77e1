package com.hxdi.nmjl.handler;

import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.model.DataSyncMessage;
import com.hxdi.common.core.utils.RedisUtil;
import com.hxdi.nmjl.service.base.OrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @program: nmjl-service
 * @description: RabbtiMQ消息队列监听器
 * @author: 王贝强
 * @create: 2025-03-27 16:16
 */


@Component
@Slf4j
public class DataSyncRabbitMqListener {

    private static final String IDEMPOTENCY_KEY_PREFIX = "nmjl:business_msg:idempotency:";
    private static final long EXPIRATION_DAYS = 3; //缓存过期时间，单位为天
    @Resource
    private RedisUtil<String> redisUtil;
    @Resource
    private OrganizationService organizationService; // 注入处理数据的服务

    // 监听入站队列，队列名称与 RabbitMqConfiguration 中的一致
    @RabbitListener(queues = CommonConstants.MQ_BUSINESS_QUEUE_BUS)
    public void handleIncomingDataSync(DataSyncMessage message) {
        String messageId = message.getMessageId();
        log.info("收到来自RabbitMQ的DataSyncMessage消息 [Queue: {}]: MessageId={}", CommonConstants.MQ_BUSINESS_QUEUE_BUS, messageId);

        try {
            // 1. 幂等性检查
            if (isProcessed(messageId)) {
                log.warn("消息已处理, 跳过: {}", messageId);
                return;
            }

            // 2. 根据实体类型和操作处理数据
            if ("organization".equals(message.getEntityType())) {
                organizationService.handleMessage(message);
            } else {
                log.warn("收到未知类型的消息: {}", message.getEntityType());
            }

            // 3. 标记消息为已处理（在成功处理后）
            markAsProcessed(messageId);
            log.info("已成功处理并将消息标记为已处理: {}", messageId);

        } catch (Exception e) {
            log.error("处理DataSyncMessage时出错: {}", messageId, e);
            // 异常处理：根据需要决定是否 nack 让消息重试，或记录错误
            // 如果配置了死信队列，处理不了的消息最终会进入死信队列
            throw new AmqpRejectAndDontRequeueException("消息处理失败: " + messageId, e); //直接拒绝并不重回队列
        }
    }

    /**
     * 检查消息是否已经处理过
     */
    public boolean isProcessed(String messageId) {
        String key = IDEMPOTENCY_KEY_PREFIX + messageId;
        return redisUtil.hasKey(key);
    }

    /**
     * 标记消息为已处理
     */
    public void markAsProcessed(String messageId) {
        String key = IDEMPOTENCY_KEY_PREFIX + messageId;
        redisUtil.set(key, "1", EXPIRATION_DAYS, TimeUnit.DAYS);
        // 设置过期时间，避免Redis无限增长
    }

}
