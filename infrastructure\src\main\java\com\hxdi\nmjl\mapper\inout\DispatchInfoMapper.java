package com.hxdi.nmjl.mapper.inout;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inout.DispatchInfo;
import com.hxdi.nmjl.condition.inout.DispatchCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DispatchInfoMapper extends SuperMapper<DispatchInfo> {

    @DataPermission
    List<DispatchInfo> listV1(@Param("condition") DispatchCondition condition);

    @DataPermission
    Page<DispatchInfo> pageV1(@Param("condition") DispatchCondition condition,@Param("page") Page<DispatchInfo> page);
}