package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.base.Entity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 粮食采购计划详情
 */
@Getter
@Setter
@TableName(value = "B_GRAIN_PROCUREMENT_PLAN_DETAIL")
public class GrainProcurementPlanDetail extends Entity<GrainProcurementPlanDetail> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 计划ID
     */
    @TableField(value = "PLAN_ID")
    private String planId;

    /**
     * 品类
     */
    @TableField(value = "CLASSIFICATION_ID")
    private String classificationId;

    /**
     * 品类名称
     */
    @TableField(value = "CLASSIFICATION_NAME")
    private String classificationName;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATIONS")
    private String specifications;

    /**
     * 质量等级
     */
    @TableField(value = "GRADE")
    private String grade;

    /**
     * 需求上限
     */
    @TableField(value = "MAX_LIMIT")
    private BigDecimal maxLimit;

    /**
     * 需求下限
     */
    @TableField(value = "MIN_LIMIT")
    private BigDecimal minLimit;

    /**
     * 产地及年份要求
     */
    @TableField(value = "REQUIREMENT")
    private String requirement;

    /**
     * 供货频次:1次/月
     */
    @TableField(value = "SUPPLY_FREQUENCY")
    private String supplyFrequency;

    /**
     * 首次交货日期
     */
    @TableField(value = "FIRST_DELIVERY_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date firstDeliveryTime;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;
    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;


    /**
     * 获取需求范围描述
     *
     * @return 需求范围字符串
     */
    public String getRequirementRange() {
        return minLimit + " - " + maxLimit;
    }
}