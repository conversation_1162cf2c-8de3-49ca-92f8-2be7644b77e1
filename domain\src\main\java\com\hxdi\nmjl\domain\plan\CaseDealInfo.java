package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <案件处理过程信息>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/12 11:10
 */
@Getter
@Setter
@TableName("B_CASE_DEAL_INFO") // 表名映射
public class CaseDealInfo implements Serializable {

    private static final long serialVersionUID = 862631550422380048L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("CASE_ID")
    @ApiModelProperty(value = "案件ID")
    private String caseId;

    @TableField("DEAL_PHASE")
    @ApiModelProperty(value = "处理阶段")
    private String dealPhase;

    @TableField("DEAL_TIME")
    @ApiModelProperty(value = "处理时间")
    private Date dealTime;

    @TableField("DEAL_DESC")
    @ApiModelProperty(value = "处理描述")
    private String dealDesc;

    @TableField("ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
