<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.OrganizationMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.Organization">
        <!--@mbg.generated-->
        <!--@Table B_ORGANIZATION-->
        <id column="ID" property="id" />
        <result column="ORG_CODE" property="orgCode" />
        <result column="ORG_NAME" property="orgName" />
        <result column="ABBR_NAME" property="abbrName" />
        <result column="PID" property="pid" />
        <result column="ORG_TYPE" property="orgType" />
        <result column="CREDIT_CODE" property="creditCode" />
        <result column="ORG_NATURE" property="orgNature" />
        <result column="PROVINCE" property="province" />
        <result column="CITY" property="city" />
        <result column="COUNTY" property="county" />
        <result column="AREA" property="area" />
        <result column="DETAIL_ADDR" property="detailAddr" />
        <result column="POST_CODE" property="postCode" />
        <result column="REGISTRE_DATE" property="registreDate" />
        <result column="FR" property="fr" />
        <result column="FZR" property="fzr" />
        <result column="MOBILE" property="mobile" />
        <result column="EMAIL" property="email" />
        <result column="LON" property="lon" />
        <result column="LAT" property="lat" />
        <result column="FUNCTIONAL" property="functional" />
        <result column="STORE_IS" property="storeIs" />
        <result column="BIZ_TYPE" property="bizType" />
        <result column="SORTS" property="sorts" />
        <result column="ENABLED" property="enabled" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_ID" property="createId" />
        <result column="UPDATE_ID" property="updateId" />
        <result column="TENANT_ID" property="tenantId" />
        <result column="PATHS" property="paths" />
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, ORG_CODE, ORG_NAME, ABBR_NAME, PID, ORG_TYPE, CREDIT_CODE, ORG_NATURE, PROVINCE,
        CITY, COUNTY, AREA, DETAIL_ADDR, POST_CODE, REGISTRE_DATE, FR, FZR, MOBILE, EMAIL,
        LON, LAT, FUNCTIONAL, STORE_IS, BIZ_TYPE, SORTS, ENABLED, CREATE_TIME, UPDATE_TIME,
        CREATE_ID, UPDATE_ID, TENANT_ID, PATHS, DATA_HIERARCHY_ID
    </sql>

    <select id="selectListV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from b_organization
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                and (org_name like concat('%', #{condition.keywords} ,'%') or credit_code like concat(#{condition.keywords}, '%'))
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgType)">
                and org_type = #{condition.orgType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.pid)">
                and pid = #{condition.pid}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.areaCode)">
                    and county in
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.areaCode)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by PATHS
    </select>

    <select id="selectPageV1" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from b_organization
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                and (org_name like concat('%', #{condition.keywords} ,'%') or credit_code like concat(#{condition.keywords}, '%'))
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgType)">
                and org_type = #{condition.orgType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.pid)">
                and pid = #{condition.pid}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.areaCode)">
                    and area_code in
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.areaCode)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by PATHS
    </select>

    <select id="selectMaxSeqNumber" resultType="java.lang.Integer">
        select MAX(sorts) from b_organization where pid = #{pid}
    </select>

    <select id="selectListV2" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from b_organization
        <where>
            enabled = 1
            <if test="@plugins.OGNL@isNotEmpty(condition.keywords)">
                and (org_name like concat('%', #{condition.keywords} ,'%') or credit_code like concat(#{condition.keywords}, '%'))
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.orgType)">
                and org_type = #{condition.orgType}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.pid)">
                and pid = #{condition.pid}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.areaCode)">
                and county in
                <foreach item="item" index="index" collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.areaCode)" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by PATHS
    </select>
</mapper>
