<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.plan.ContractRiskReviewMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.ContractRiskReview">
    <!--@mbg.generated-->
    <!--@Table B_CONTRACT_RISK_REVIEW-->
    <id column="ID" property="id" />
    <result column="CONTRACT_ID" property="contractId" />
    <result column="CONTRACT_CODE" property="contractCode" />
    <result column="CONTRACT_NAME" property="contractName" />
    <result column="CLIENT_ID" property="clientId" />
    <result column="CLIENT_NAME" property="clientName" />
    <result column="CREDIT_CODE" property="creditCode" />
    <result column="RISK_TYPE" property="riskType" />
    <result column="RISK_DESCRIPTION" property="riskDescription" />
    <result column="RISK_LEVEL" property="riskLevel" />
    <result column="IMPACT_LEVEL" property="impactLevel" />
    <result column="PROBABILITY" property="probability" />
    <result column="USER_ID" property="userId" />
    <result column="USER_NAME" property="userName" />
    <result column="DISPOSE_TIME" property="disposeTime" />
    <result column="DISPOSE_STATE" property="disposeState" />
    <result column="DISPOSE_DESC" property="disposeDesc" />
    <result column="ORG_ID" property="orgId" />
    <result column="ORG_NAME" property="orgName" />
    <result column="ENABLED" property="enabled" />
    <result column="ATTACHMENTS" property="attachments" />
    <result column="REMARKS" property="remarks" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_ID" property="createId" />
    <result column="UPDATE_ID" property="updateId" />
    <result column="TENANT_ID" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONTRACT_ID, CONTRACT_CODE, CONTRACT_NAME, CLIENT_ID, CLIENT_NAME, CREDIT_CODE, 
    RISK_TYPE, RISK_DESCRIPTION, RISK_LEVEL, IMPACT_LEVEL, PROBABILITY, USER_ID, USER_NAME, 
    DISPOSE_TIME, DISPOSE_STATE, DISPOSE_DESC, ORG_ID, ORG_NAME, ENABLED, ATTACHMENTS, 
    REMARKS, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID
  </sql>
</mapper>