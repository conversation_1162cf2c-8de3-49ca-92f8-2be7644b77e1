package com.hxdi.nmjl.enums;

import lombok.Getter;

/**
 * @program: nmjl-service
 * @description: 系统业务类型
 * @author: 王贝强
 * @create: 2025-08-20 15:06
 */
@Getter
public enum BusinessType {
    //-------------- 业务类型枚举定义 --------------
    NOOP("000", "无对应业务类型"),
    STORE_LOCATION("001", "货位信息"),
    QUALITY_TRACE("002", "质量追溯"),;

    private final String code;
    private final String label;

    BusinessType(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static BusinessType fromCode(String businessTypeCode) {
        for (BusinessType type : BusinessType.values()) {
            if (type.getCode().equals(businessTypeCode)) {
                return type;
            }
        }
        return NOOP; // 默认返回无对应业务类型
    }
}
