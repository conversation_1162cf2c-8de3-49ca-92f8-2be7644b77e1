package com.hxdi.nmjl.controller.plan;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.ProcurementPlanCondition;
import com.hxdi.nmjl.domain.plan.GrainProcurementPlan;
import com.hxdi.nmjl.service.plan.ProcurementPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 粮食筹措计划管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/26
 */
@Api(tags = "粮食筹措计划管理")
@RestController
@RequestMapping("/procurement")
public class ProcurementPlanController extends BaseController<ProcurementPlanService, GrainProcurementPlan> {

    @ApiOperation("分页查询计划")
    @GetMapping("/page")
    public ResultBody<Page<GrainProcurementPlan>> page(ProcurementPlanCondition condition) {
        return ResultBody.<Page<GrainProcurementPlan>>OK().data(bizService.pages(condition));
    }


    @ApiOperation("列表查询计划")
    @GetMapping("/list")
    public ResultBody<List<GrainProcurementPlan>> list(ProcurementPlanCondition condition) {
        return ResultBody.<List<GrainProcurementPlan>>OK().data(bizService.lists(condition));
    }

    @ApiOperation("列表查询计划及详情")
    @GetMapping("/listAndDetail")
    public ResultBody<List<GrainProcurementPlan>> listAndDetail(ProcurementPlanCondition condition) {
        return ResultBody.<List<GrainProcurementPlan>>OK().data(bizService.listAndDetail(condition));
    }


    @ApiOperation("保存/修改计划")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody GrainProcurementPlan plan) {
        if (CommonUtils.isEmpty(plan.getId())) {
            bizService.createV1(plan);
        } else {
            bizService.update(plan);
        }
        return ResultBody.OK();
    }

    @ApiOperation("查看不能选择的委托单位")
    @GetMapping("/getNotDelegatedUnits")
    public ResultBody<List<String>> getAvailableDelegatedUnits() {
        return ResultBody.<List<String>>OK().data(bizService.getAvailableDelegatedUnits());
    }

    @ApiOperation("判断是否可以新增")
    @GetMapping("/canBeCreated")
    public ResultBody<Boolean> canBeCreated() {
        return ResultBody.<Boolean>OK().data(bizService.CanBeCreated());
    }


    @ApiOperation("查看计划详情")
    @GetMapping("/getDetail")
    public ResultBody<GrainProcurementPlan> getDetail(@RequestParam String planId) {
        return ResultBody.<GrainProcurementPlan>OK().data(bizService.getDetail(planId));
    }


    @ApiOperation("审批计划")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id,
                                    @RequestParam Integer approveStatus,
                                    @RequestParam(required = false) String approveOpinion) {
        bizService.approve(id, approveStatus, approveOpinion);
        return ResultBody.OK();
    }


    @ApiOperation("删除计划")
    @GetMapping("/remove")
    public ResultBody<Void> remove(@RequestParam String planId) {
        bizService.remove(planId);
        return ResultBody.OK();
    }


    @ApiOperation("提交计划")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String planId) {
        bizService.submit(planId);
        return ResultBody.OK();
    }

    @ApiOperation("完成计划")
    @GetMapping("/complete")
    public ResultBody<Void> complete(@RequestParam String planId) {
        bizService.completePlan(planId);
        return ResultBody.OK();
    }
}
