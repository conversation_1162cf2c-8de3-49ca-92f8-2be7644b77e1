package com.hxdi.nmjl.controller.emergency;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.CommonApiController;
import com.hxdi.nmjl.condition.emergency.CommEvaluationCondition;
import com.hxdi.nmjl.domain.emergency.CommEvaluation;
import com.hxdi.nmjl.domain.emergency.CommRecord;
import com.hxdi.nmjl.service.emergency.CommEvaluationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "控制沟通接口")
@RequestMapping("/commEvaluation")
@RestController
public class CommEvaluationController extends CommonApiController<CommEvaluationService, CommEvaluation, CommEvaluationCondition> {


    @ApiOperation(value = "提交")
    @GetMapping("/submit")
    public ResultBody submit(String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }
}
