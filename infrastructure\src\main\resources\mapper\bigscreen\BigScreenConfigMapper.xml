<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.bigscreen.BigScreenConfigMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.bigscreen.BigScreenConfig">
        <!--@Table B_BIG_SCREEN_CONFIG-->
        <id column="ID" property="id"/>
        <result column="KEY" property="key"/>
        <result column="VALUE" property="value"/>
        <result column="DESC" property="desc"/>
        <result column="JSON_EXT" property="jsonExt"/>
        <result column="ENABLED" property="enabled"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, "KEY", "VALUE", "DESC", JSON_EXT, ENABLED, CREATE_TIME, UPDATE_TIME
    </sql>
</mapper>