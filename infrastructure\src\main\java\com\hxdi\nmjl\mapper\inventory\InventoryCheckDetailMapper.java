package com.hxdi.nmjl.mapper.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.inventory.InventoryCheckDetail;
import com.hxdi.nmjl.condition.inventory.InventoryCheckDetailCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盘点记录明细数据访问层
 *
 * <AUTHOR>
 * @since 2025-04-14 14:16:23
 */
@Mapper
public interface InventoryCheckDetailMapper extends SuperMapper<InventoryCheckDetail> {


    /**
     * 分页查询盘点记录明细
     *
     * @param page      分页参数
     * @param condition 查询条件
     * @return 分页结果
     */
    @DataPermission(alias = "d")
    Page<InventoryCheckDetail> selectPageV1(Page<InventoryCheckDetail> page, @Param("condition") InventoryCheckDetailCondition condition);

    /**
     * 根据条件查询盘点明细列表
     *
     * @param checkId 盘点ID
     * @return 盘点明细列表
     */
    @DataPermission
    List<InventoryCheckDetail> selectListV1(@Param("checkId") String checkId);
}

